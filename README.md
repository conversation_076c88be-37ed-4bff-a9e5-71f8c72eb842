[![SonarCloud](https://sonarcloud.io/images/project_badges/sonarcloud-white.svg)](https://sonarcloud.io/dashboard?id=wexl_retail-service)
# Introduction 
Retail Service is the version of application which consists of Parent, Student and Teacher services.

# Getting Started
Its a standard maven spring boot project.  Import this into your IDE
This  project takes care of the following:
1. Project Structure - with Controller, Service, Repository layers
1. Test Case implementations
1. Error Code Management
1. Managing Correlation IDs while interacting across many services
1. Exception Handling & Generic Failure Responses
1. Logging Request and Responses
1. Structured Logging
1. AWS Parameter Store Integration for managing application configurations
1. Docker, Helm Chart support for deployment to kubernetes

# Next Steps
1. Authentication support
1. RBA for authorization
1. Multi Tenant Support

# Build and Test
Install JDK 11.  Preferably using a tool like [SDKMAN](https://sdkman.io/)  
**Note:** If working on `IntelliJ Idea` - install `lombok` plugin

```shell script
./mvnw clean install 
````

# Java Style Guide
We are using [fmt-maven-plugin](https://github.com/coveooss/fmt-maven-plugin) to maintain google java style guide. This plugin comes following goals

* Use `./mvnw fmt:format` to **automatically format** code base using google style guide
* Use `./mvnw fmt:check` to verify code base style. This goal is already part of our build process

Along with [fmt-maven-plugin](https://github.com/coveooss/fmt-maven-plugin), we also use [maven-checkstyle-plugin](http://maven.apache.org/plugins/maven-checkstyle-plugin/).

:warning: `./mvnw fmt:format` doesn't add an empty line for method which has JavaDoc, this needs to done manually(Refer [Signature.java](application/src/main/java/com/wexl/retail/util/Signature.java)). The status of this issue can be tracked [here](https://github.com/google/google-java-format/issues/399).

# Secret Management
We will be using [AWS Parameter Store](https://docs.aws.amazon.com/systems-manager/latest/userguide/systems-manager-parameter-commerceStore.html) for managing the yaml configurations for the spring boot apps.  This will be used on AWS environment.  As part of the dev environment setup, you don't have to worry about using this.

# Encrypted Secrets
Never check-in secrets in plain text in "yaml" configuration files.  For that run the following command and use it in yaml as shown below

``
./mvnw jasypt:encrypt-value -Djasypt.encryptor.password="the password" -Djasypt.plugin.value="theValueYouWantToEncrypt"
``

And you need to provide the password (given above) as an environment variable and then run the Spring Boot Application 

``
export JASYPT_ENCRYPTOR_PASSWORD=<ask_team_for_password>
``
#for changes in PR