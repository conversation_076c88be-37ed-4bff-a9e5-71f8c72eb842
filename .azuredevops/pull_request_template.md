Thank you for your contribution to the repo. 
Before submitting this PR, please make sure:

- [ ] Your code builds clean without any errors or warnings

DTO Layer
--------
- [ ] You are using @JsonProperty to have underscores in request and response
- [ ] I am using Java 17 Records in DTOs.  All DTOs are in the standard class of that particular feature

Service Layer
------------


Repository Layer
--------------

Controller Layer
----------------
- [ ] I have not added any unnecessary try catch blocks in my controller code


Convention for Error Strings
-----------------
-[ ] I have followed the below convention for Error Strings in Exceptions:

- Use "error.DESCRIPTION" format...Capitalize every fist letter of words.
- Simple statements of Notfound,Invalid,Cannotfind. ex:Teacher not found, follow: error.TeacherNotFound 
- Statements include two variables. ex: No mlps found for this MlpId, follow : error.MlpFind.MLPId
- Use {0},{1},{2}.. in message.properties for passing dynamic Variables. Declare an String array in files of throwing API exceptions.
- For Api Exceptions : use Invalid Input,Couldnot Process Request,Unauthorized Access, Resource Not Found for Invalid Request,Server error,Unauthorized,No record found respectively.