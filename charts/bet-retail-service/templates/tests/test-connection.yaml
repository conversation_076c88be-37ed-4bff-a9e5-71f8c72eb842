apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "bet-retail-service.fullname" . }}-test-connection"
  labels:
    {{- include "bet-retail-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "bet-retail-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
