@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;1,100;1,200;1,300;1,500;1,600;1,800;1,900&display=swap');
body,html {background: #ffffff;color: #787878;font-family: 'Poppins', sans-serif;font-size: 14px;font-weight: 400;overflow-x: hidden;margin: 0 auto;padding: 0;-ms-box-sizing: border-box;-o-box-sizing: border-box;-webkit-box-sizing: border-box;box-sizing: border-box;line-height: 1.6em;-webkit-font-smoothing: antialiased;-moz-font-smoothing: antialiased; }
.tableDesign {
    padding: 15px 0;
}
.container-fluid {
    width: 95%;
    margin:0 auto
}
.tableBlk {
    border: 1px solid #ddd;
    border-radius: 4px;
}
.table-responsive {
    width: 100%;
    margin: 0 auto;
}
.tableBlk th, .tableBlk td {
    height: 42px;
    padding: 5px;
    text-align: left;
    width: calc(100% / 9);
    font-size: 13px;
}

.tableBlk th {
    color: #fff;
    font-weight: 500;
}
.tableBlk table tr:first-child {
    background-color: #113e59;
}
.tableBlk table tr:nth-child(even) {
    background-color: #eee;
}
.tableBlk table tr .miniTable tr:first-child, .tableBlk table tr .miniTable tr:nth-child(even),
.tableBlk table tr .miniTableStudent tr:first-child, .tableBlk table tr .miniTableStudent tr:nth-child(even) {background-color: transparent;}
.hd {
    background-color: #f6b519;
}
.hd.sticky {
    position: fixed;
    left: 0;
    right: 0;
}
.hdBlk {
    display: flex;
    align-items: center;
}
.logo {
    width: 120px;
    height: 72px;
    display: inline-flex;
    align-items: center;
}
.logo img {
    width: 100%;
}
.ftText {
    padding: 15px 0;
    background-color: #113e59;
    color: #fff;
}
.miniTableStudent {
    background-color: #fff;
    box-shadow: 0 0 10px rgb(0 0 0 / 20%);
    border-radius: 4px;
}
.tableBlk .miniTableStudent td {border-bottom: 1px solid #ddd;}
.tableBlk .miniTableStudent tr:last-child td {
    border-bottom: 0;
}
.footer {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
}

@media (min-width:2301px) {
    .tableBlk th, .tableBlk td {font-size: 26px;}
}
@media (min-width:1921px) and (max-width:2300px) {
    .tableBlk th, .tableBlk td {font-size: 18px;}
}
@media (min-width:1400px) and (max-width:1920px) {
    .tableBlk th, .tableBlk td {font-size: 15px;}
}
@media (max-width:1280px) {
    .tableBlk {
        width: 100%;
        display: block;
        overflow-x: auto;
    }
    .table-responsive {width:1366px}
    .tableBlk th, .tableBlk td {font-size: 12px;}
}
@media (max-width:992px) {
    .tableBlk th, .tableBlk td {font-size: 13px;}
}