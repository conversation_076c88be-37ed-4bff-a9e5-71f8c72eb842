<?xml version = "1.0" encoding = "UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<!-- $Id$ -->
<schema xmlns = "http://www.w3.org/2001/XMLSchema" targetNamespace = "http://www.w3.org/1999/XSL/Format"  xmlns:fo = "http://www.w3.org/1999/XSL/Format" xmlns:xs = "http://www.w3.org/2001/XMLSchema" elementFormDefault = "qualified">

  <annotation>
    <documentation> 

      I'm not sure where to place this.
      It applies to the page context (NOT implemented)
      <attribute name = "size"  type = "size_Type"/>
      
      I have not coded for the functions described in 5.10 Core Function Library
      They need to be segregated into groups and then inserted in the types
      
      common_functions
      object  inherited-property-value(NCName) 
      object  from-parent( NCName) 
      object  from-nearest-specified-value( NCName) 
      object  merge-property-values( NCName) 
      
      font_functions
      object  system-font( NCName , NCName) 
      
      length_functions
      numeric floor( numeric) 
      numeric ceiling(numeric) 
      numeric round(numeric) 
      numeric min( numeric , numeric) 
      numeric max(numeric , numeric) 
      numeric abs( numeric)
      
      table_cell_or_descendants_functions
      object  from-table-column( NCName) 

      color_functions
      color rgb(numeric , numeric , numeric) 
      color rgb-icc(numeric , numeric , numeric , NCName , numeric , numeric) 
      color system-color( NCName) 

      label_functions
      numeric body-start() 
      numeric label-end() 
      
      (defined)
      table-column_functions
      numeric proportional-column-width( numeric) 
      
      This schema has been developed in order to validate XSL FO documents for FOP
      All of the elements need to be prefixed with fo:
      The namespace prefix is xmlns:fo = "http://www.w3.org/1999/XSL/Format".

      This schema, as delivered, may either validate the full spec, or, just the FOP portion. 
      (What it validates depends upon what I was doing with it when released.)
      
      If you want to restrict it to just those elements and attributes implemented by FOP, 
      you need to edit the <group xmlns=""/> and <attributeGroup xmlns=""/> tags to exclude the groups ending with _Not

      Some schema tools complain about the placement of comments in this schema and will remove or reorder them
      There are fop_result and fop_fail comments on specific features not implemented by FOP

      FOP does not enforce the following schema requirements

      fo:simple-page-master model = "(region-body,region-before?,region-after?,region-start?,region-end?)"
      elements can be in any order

      fo:table-cell model = "(%block;)+"
      Can be empty

      fo:flow model = "(%block;)+"
      Can be empty

      This schema allows the length attribute to be negative for some elements like margins.
      There may be instances where I've entered %integer_Type; and it should be positive-integer or number
      The schema trys to handle the text based rules re: fo:markers, fo:float, footer and fo:initial-property-set
      But, allows you to do illegal things if you want because I couldn't figure out how to constrain against the illegal actions.

      Please e-mail your <NAME_EMAIL>

      Contribution by Oleg Tkachenko 
      (Declarations able to include non-xsl children)
      <xs:any minOccurs = "0" maxOccurs = "unbounded" namespace = "##other" processContents = "skip"/>
      This declaration assumes that all <fo:color-profile/> elements must come before other stuff, 
      which is not required by spec, but I cannot see any way to express such constraints in schema, 
      one could use <xs:choice/> instead of <xs:sequence/>, but this way we lose control over (color-profile)+ constraint.
      
      VCP 21-Oct-2002 
        Updated all (px|pt|mm|cm|in|em) to (px|pt|mm|cm|in|em|%) to allow percentage types.
        Updated the restriction base of those types from NMTOKEN to string

    </documentation>
  </annotation>

  <group name = "initial_property_set_List_Not">
    <choice>
      <element ref = "fo:initial-property-set"/>
    </choice>
  </group>
  
  <annotation>
    <documentation>
      empty group so cannot be defined
      <group name = "initial_property_set_List_FOP">
        <choice/>
      </group>
    </documentation>
  </annotation>
  
  <group name = "initial_property_set_List">
    <annotation>
      <documentation>
        <choice>
          <group ref = "fo:initial_property_set_List_FOP"/>
        </choice>
      </documentation>
    </annotation>
    <choice>
      <group ref = "fo:initial_property_set_List_Not"/>
    </choice>
  </group>
  <group name = "marker_List">
    <choice>
      <element ref = "fo:marker"/>
    </choice>
  </group>
  <group name = "inline_List_FOP">
    <choice>
      <element ref = "fo:character"/>
      <element ref = "fo:external-graphic"/>
      <element ref = "fo:instream-foreign-object"/>
      <element ref = "fo:inline"/>
      <element ref = "fo:leader"/>
      <element ref = "fo:page-number"/>
      <element ref = "fo:page-number-citation"/>
      <element ref = "fo:basic-link"/>
    </choice>
  </group>
  <group name = "inline_List_Not">
    <choice>
      <element ref = "fo:bidi-override"/>
      <element ref = "fo:inline-container"/>
      <element ref = "fo:multi-toggle"/>
    </choice>
  </group>
  <group name = "inline_List">
    <choice>
      <group ref = "fo:inline_List_FOP"/>
      <group ref = "fo:inline_List_Not"/>
    </choice>
  </group>
  <group name = "block_List_FOP">
    <choice>
      <element ref = "fo:block"/>
      <element ref = "fo:block-container"/>
      <element ref = "fo:table"/>
      <element ref = "fo:list-block"/>
    </choice>
  </group>
  <group name = "block_List_Not">
    <choice>
      <element ref = "fo:table-and-caption"/>
    </choice>
  </group>
  <group name = "block_List">
    <choice>
      <group ref = "fo:block_List_FOP"/>
      <group ref = "fo:block_List_Not"/>
    </choice>
  </group>
  <group name = "neutral_List_FOP">
    <choice>
      <element ref = "fo:wrapper"/>
      <element ref = "fo:retrieve-marker"/>
    </choice>
  </group>
  <group name = "neutral_List_Not">
    <choice>
      <element ref = "fo:multi-switch"/>
      <element ref = "fo:multi-properties"/>
    </choice>
  </group>
  <group name = "neutral_List">
    <choice>
      <group ref = "fo:neutral_List_FOP"/>
      <group ref = "fo:neutral_List_Not"/>
    </choice>
  </group>
  <annotation>
    <documentation>
      empty group so cannot be defined
      <group name = "float_List_FOP">
        <choice/>
      </group>
    </documentation>
  </annotation>
  <group name = "float_List_Not">
    <choice>
      <element ref = "fo:float"/>
    </choice>
  </group>
  <group name = "float_List">
    <annotation>
      <documentation>
        empty group
        <choice>
          <group ref = "fo:float_List_FOP"/>
        </choice>
      </documentation>
    </annotation>
    <choice>
      <group ref = "fo:float_List_Not"/>
    </choice>
  </group>
  <group name = "footnote_List">
    <choice>
      <element ref = "fo:footnote"/>
    </choice>
  </group>

  <attributeGroup name = "block_properties_Not">
    <attribute name = "page-break-after"  type = "fo:page_break_after_Type"/>
    <attribute name = "page-break-before" type = "fo:page_break_after_Type"/>
  </attributeGroup>
  <attributeGroup name = "block_properties_FOP">
  </attributeGroup>
  <attributeGroup name = "block_properties">
    <attributeGroup ref = "fo:block_properties_FOP"/>
    <attributeGroup ref = "fo:block_properties_Not"/>
  </attributeGroup>

  <attributeGroup name = "list_properties_Not">
    <attribute name = "page-break-after"  type = "fo:page_break_after_Type"/>
    <attribute name = "page-break-before" type = "fo:page_break_after_Type"/>
    <attribute name = "background-position" type = "fo:background_position_Type"/>
  </attributeGroup>
  <attributeGroup name = "list_properties_FOP">
  </attributeGroup>
  <attributeGroup name = "list_properties">
    <attributeGroup ref = "fo:list_properties_FOP"/>
    <attributeGroup ref = "fo:list_properties_Not"/>
  </attributeGroup>

  <attributeGroup name = "inheritable_properties_List_Not">
    <attribute name = "auto-restore" type = "fo:auto_restore_Type"/>
    <attribute name = "background"    type = "fo:background_Type"   /> 
    <attribute name = "border-spacing" type = "fo:length_Type"/>
    <attribute name = "caption-side" type = "fo:caption_side_Type"/>
    <attribute name = "direction" type = "fo:direction_Type"/>
    <attribute name = "empty-cells" type = "fo:empty_cells_Type"/>
    <attribute name = "font" type = "xs:string"/>
    <attribute name = "font-selection-strategy" type = "fo:font_selection_strategy_Type"/>
    <attribute name = "font-size-adjust" type = "fo:font_size_adjust_Type"/>
    <attribute name = "font-stretch" type = "xs:string"/>
    <attribute name = "font-variant" type = "fo:font_variant_Type"/>
    <attribute name = "glyph-orientation-vertical" type = "fo:orientation_Type"/>
    <attribute name = "glyph-orientation-horizontal" type = "fo:orientation_Type"/>
    <attribute name = "hyphenation-keep" type = "fo:hyphenation_keep_Type"/>
    <attribute name = "hyphenation-ladder-count" type = "fo:hyphenation_ladder_count_Type"/>
    <attribute name = "intrusion-displace" type = "fo:displace_Type"/>
    <attribute name = "last-line-end-indent" type = "fo:last_line_end_indent_Type"/>
    <attribute name = "line-height-shift-adjustment" type = "fo:line_height_shift_adjustment_Type"/>
    <attribute name = "line-stacking-strategy" type = "fo:line_stacking_strategy_Type"/>
    <attribute name = "linefeed-treatment" type = "fo:linefeed_treatment_Type"/>
    <attribute name = "margin"        type = "fo:border_margin_width_Type"       /> 
    <attribute name = "max-width"     type = "fo:max_width_Type"/>
    <attribute name = "min-height"    type = "fo:start_indent_Type"/>
    <attribute name = "min-width"     type = "fo:start_indent_Type"/>
    <attribute name = "page-break-inside" type = "fo:page_break_inside_Type"/>
    <attribute name = "reference-orientation" type = "fo:orientation_Type"/>
    <attribute name = "score-spaces" type = "fo:score_spaces_Type"/>
    <attribute name = "script" type = "xs:string"/>
    <attribute name = "text-transform" type = "fo:text_transform_Type"/>
    <attribute name = "visibility" type = "fo:visibility_Type"/>
    <attribute name = "white-space" type = "fo:white_space_Type"/>
    <attribute name = "white-space-treatment" type = "fo:white_space_treatment_Type"/>
    <attribute name = "word-spacing" type = "fo:letter_spacing_Type"/>
    <attribute name = "writing-mode" type = "fo:writing_mode_Type"/>
  </attributeGroup>


  <attributeGroup name = "inheritable_properties_List_FOP">
    <annotation>
      <documentation>
        Removed because I'm not sure how to handle this<attribute xmlns="" name = "xml:lang" type = "xs:string"/> 
      </documentation>
    </annotation>
    <attribute name = "border-bottom" type = "fo:border_side_Type"/> 
    <attribute name = "border-collapse" type = "fo:border_collapse_Type"/>
    <attribute name = "border-color"  type = "fo:border_color_Type" /> 
    <attribute name = "border-left"   type = "fo:border_side_Type"  /> 
    <attribute name = "border-right"  type = "fo:border_side_Type" /> 
    <attribute name = "border-separation" type = "fo:length_bp_ip_direction_Type"/>
    <attribute name = "border-style"  type = "fo:border_multi_style_Type" /> 
    <attribute name = "border-top"    type = "fo:border_side_Type"   /> 
    <attribute name = "border-width"  type = "fo:border_margin_width_Type" /> 
    <attribute name = "color" type = "fo:color_Type"/>
    <attribute name = "country" type = "fo:country_Type"/>
    <attribute name = "display-align" type = "fo:display_align_Type"/>
    <attribute name = "end-indent" type = "fo:end_indent_Type"/>
    <attribute name = "font-family" type = "fo:family_name_Type"/>
    <attribute name = "font-size" type = "fo:font_size_Type"/>
    <attribute name = "font-style" type = "fo:font_style_Type"/>
    <attribute name = "font-weight" type = "fo:font_weight_Type"/>
    <attribute name = "hyphenate" type = "fo:hyphenate_Type"/>
    <attribute name = "hyphenation-character" type = "fo:hyphenation_character_Type"/>
    <attribute name = "hyphenation-push-character-count" type = "fo:integer_Type"/>
    <attribute name = "hyphenation-remain-character-count" type = "fo:integer_Type"/>
    <attribute name = "keep-together" type = "fo:keep_compound_Type"/>
    <attribute name = "keep-together.within-column" type = "fo:keep_integer_Type"/>
    <attribute name = "keep-together.within-line" type = "fo:keep_integer_Type"/>
    <attribute name = "keep-together.within-page" type = "fo:keep_integer_Type"/>
    <attribute name = "language" type = "fo:language_Type"/>
    <attribute name = "leader-alignment" type = "fo:leader_alignment_Type"/>
    <attribute name = "leader-length" type = "fo:leader_length_Type"/>
    <attribute name = "leader-length.maximum" type = "fo:length_Type"/>
    <attribute name = "leader-length.minimum" type = "fo:length_Type"/>
    <attribute name = "leader-length.optimum" type = "fo:length_Type"/>
    <attribute name = "leader-pattern" type = "fo:leader_pattern_Type"/>
    <attribute name = "leader-pattern-width" type = "fo:leader_pattern_width_Type"/>
    <attribute name = "letter-spacing" type = "fo:letter_spacing_Type"/>
    <attribute name = "line-height" type = "fo:line_height_Type"/>
    <attribute name = "orphans" type = "fo:orphans_Type"/>
    <attribute name = "position" type = "fo:position_Type"/>
    <attribute name = "provisional-distance-between-starts" type = "fo:provisional_distance_between_starts_Type"/>
    <attribute name = "provisional-label-separation" type = "fo:provisional_label_separation_Type"/>
    <attribute name = "relative-align" type = "fo:relative_align_Type"/>
    <attribute name = "rule-style" type = "fo:rule_style_Type"/>
    <attribute name = "rule-thickness" type = "fo:length_Type"/>
    <attribute name = "start-indent" type = "fo:start_indent_Type"/>
    <attribute name = "text-align" type = "fo:text_align_Type"/>
    <attribute name = "text-align-last" type = "fo:text_align_last_Type"/>
    <attribute name = "text-indent" type = "fo:length_percentage_Type"/>
    <attribute name = "white-space-collapse" type = "fo:white_space_collapse_Type"/>
    <attribute name = "widows" type = "fo:widows_Type"/>
    <attribute name = "wrap-option" type = "fo:wrap_option_Type"/>
  </attributeGroup>
  <attributeGroup name = "inheritable_properties_List">
    <attributeGroup ref = "fo:inheritable_properties_List_FOP"/>
    <attributeGroup ref = "fo:inheritable_properties_List_Not"/>
  </attributeGroup>
  <attributeGroup name = "break_Properties_List_Not">
    <annotation>
      <documentation>
        Inheritable
        <attribute name = "page-break-inside" type = "fo:breaks_Type"/>
      </documentation>
    </annotation>
  </attributeGroup>
  <attributeGroup name = "break_Properties_List_FOP">
    <attribute name = "break-after" type = "fo:breaks_Type"/>
    <attribute name = "break-before" type = "fo:breaks_Type"/>
  </attributeGroup>
  <attributeGroup name = "break_Properties_List">
    <attributeGroup ref = "fo:break_Properties_List_FOP"/>
    <attributeGroup ref = "fo:break_Properties_List_Not"/>
  </attributeGroup>
  <attributeGroup name = "text_one_Properties_List_FOP"/>
  <attributeGroup name = "text_one_Properties_List_Not">
    <attribute name = "text-depth" type = "fo:text_depth_Type"/>
    <attribute name = "text-altitude" type = "fo:text_altitude_Type"/>
  </attributeGroup>
  <attributeGroup name = "text_one_Properties_List">
    <attributeGroup ref = "fo:text_one_Properties_List_FOP"/>
    <attributeGroup ref = "fo:text_one_Properties_List_Not"/>
  </attributeGroup>
  <attributeGroup name = "line_height_Properties_List_FOP"/>
  <attributeGroup name = "line_height_Properties_List_Not">
    <annotation>
      <documentation>
        Inheritable
        <attribute name = "line-height-shift-adjustment" type = "fo:line_height_shift_adjustment_Type"/>
        <attribute name = "line-stacking-strategy" type = "fo:line_stacking_strategy_Type"/>
      </documentation>
    </annotation>
  </attributeGroup>
  <attributeGroup name = "line_height_Properties_List">
    <attributeGroup ref = "fo:line_height_Properties_List_FOP"/>
    <attributeGroup ref = "fo:line_height_Properties_List_Not"/>
  </attributeGroup>
  <attributeGroup name = "keep_Properties_List_FOP">
    <attribute name = "keep-with-next" type = "fo:keep_integer_Type"/>
  </attributeGroup>
  <attributeGroup name = "keep_Properties_List_Not">
    <annotation>
      <documentation>
        Inheritable
        <attribute name = "keep-together" type = "fo:keep_compound_Type"/>
        <attribute name = "keep-together.within-line" type = "fo:keep_integer_Type"/>
        <attribute name = "keep-together.within-column" type = "fo:keep_integer_Type"/>
        <attribute name = "keep-together.within-page" type = "fo:keep_integer_Type"/>
      </documentation>
    </annotation>
    <attribute name = "keep-with-previous" type = "fo:keep_integer_Type"/>
  </attributeGroup>
  <attributeGroup name = "keep_Properties_List">
    <attributeGroup ref = "fo:keep_Properties_List_FOP"/>
    <attributeGroup ref = "fo:keep_Properties_List_Not"/>
  </attributeGroup>
  <attributeGroup name = "block_hyphenation_Properties_List_FOP"/>
  <attributeGroup name = "block_hyphenation_Properties_List_Not">
    <annotation>
      <documentation>
        Inheritable
        <attribute name = "hyphenation-keep" type = "fo:hyphenation_keep_Type"/>
        <attribute name = "hyphenation-ladder-count" type = "fo:hyphenation_ladder_count_Type"/>
      </documentation>
    </annotation>
    </attributeGroup>
  <attributeGroup name = "block_hyphenation_Properties_List">
    <attributeGroup ref = "fo:block_hyphenation_Properties_List_FOP"/>
    <attributeGroup ref = "fo:block_hyphenation_Properties_List_Not"/>
  </attributeGroup>
  <attributeGroup name = "intrusion_displace_Properties_List_FOP"/>
  <attributeGroup name = "intrusion_displace_Properties_List_Not">
    <annotation>
      <documentation>
        Inheritable attributes
        <attribute name = "intrusion-displace" type = "fo:displace_Type"/>
      </documentation>
    </annotation>
    </attributeGroup>
  <attributeGroup name = "intrusion_displace_Properties_List">
    <attributeGroup ref = "fo:intrusion_displace_Properties_List_FOP"/>
    <attributeGroup ref = "fo:intrusion_displace_Properties_List_Not"/>
  </attributeGroup>
  <attributeGroup name = "progression_Properties_List_FOP"/>
  <attributeGroup name = "progression_Properties_List_Not">
    <attribute name = "block-progression-dimension" type = "fo:margin_width_Type"/>
    <attribute name = "block-progression-dimension.minimum" type = "fo:length_Type"/>
    <attribute name = "block-progression-dimension.optimum" type = "fo:block_progression_dimension_optimum_Type"/>
    <attribute name = "block-progression-dimension.maximum" type = "fo:block_progression_dimension_maximum_Type"/>
    <attribute name = "inline-progression-dimension" type = "fo:margin_width_Type"/>
    <attribute name = "inline-progression-dimension.minimum" type = "fo:length_Type"/>
    <attribute name = "inline-progression-dimension.optimum" type = "fo:block_progression_dimension_optimum_Type"/>
    <attribute name = "inline-progression-dimension.maximum" type = "fo:block_progression_dimension_maximum_Type"/>
  </attributeGroup>
  <attributeGroup name = "progression_Properties_List">
    <attributeGroup ref = "fo:progression_Properties_List_FOP"/>
    <attributeGroup ref = "fo:progression_Properties_List_Not"/>
  </attributeGroup>
  <attributeGroup name = "clip_Properties_List_FOP"/>
  <attributeGroup name = "clip_Properties_List_Not">
    <attribute name = "clip" type = "fo:clip_Type"/>
  </attributeGroup>
  <attributeGroup name = "clip_Properties_List">
    <attributeGroup ref = "fo:clip_Properties_List_FOP"/>
    <attributeGroup ref = "fo:clip_Properties_List_Not"/>
  </attributeGroup>
  <attributeGroup name = "reference_Properties_List_FOP"/>
  <attributeGroup name = "reference_Properties_List_Not">
    <annotation>
      <documentation>
        Inherited
        <attribute name = "reference-orientation" type = "fo:orientation_Type"/>
        <attribute name = "writing-mode" type = "fo:writing_mode_Type"/>
      </documentation>
    </annotation>
    </attributeGroup>
  <attributeGroup name = "reference_Properties_List">
    <attributeGroup ref = "fo:reference_Properties_List_FOP"/>
    <attributeGroup ref = "fo:reference_Properties_List_Not"/>
  </attributeGroup>
  <attributeGroup name = "Accessibility_Properties_List_FOP"/>
  <attributeGroup name = "Accessibility_Properties_List_Not">
    <attribute name = "source-document" type = "xs:string"/>
    <attribute name = "role" type = "xs:string"/>
  </attributeGroup>
  <attributeGroup name = "Accessibility_Properties_List">
    <attributeGroup ref = "fo:Accessibility_Properties_List_FOP"/>
    <attributeGroup ref = "fo:Accessibility_Properties_List_Not"/>
  </attributeGroup>
  <attributeGroup name = "Relative_Position_Properties_List_FOP">
    <annotation>
      <documentation>
        Inherited
        <attribute name = "position" type = "fo:position_Type"/>
      </documentation>
    </annotation>
    <attribute name = "top" type = "fo:top_Type"/>
    <attribute name = "right" type = "fo:right_Type"/>
    <attribute name = "bottom" type = "fo:bottom_Type"/>
    <attribute name = "left" type = "fo:left_Type"/>
  </attributeGroup>
  <attributeGroup name = "Relative_Position_Properties_List_Not">
    <attribute name = "relative-position" type = "fo:relative_position_Type"/>
  </attributeGroup>
  <attributeGroup name = "Relative_Position_Properties_List">
    <attributeGroup ref = "fo:Relative_Position_Properties_List_FOP"/>
    <attributeGroup ref = "fo:Relative_Position_Properties_List_Not"/>
  </attributeGroup>
  <attributeGroup name = "absolute_or_Relative_Position_Properties_List_FOP">
    <attributeGroup ref = "fo:Relative_Position_Properties_List"/>
  </attributeGroup>
  <attributeGroup name = "absolute_or_Relative_Position_Properties_List_Not">
    <attribute name = "absolute-position" type = "fo:absolute_position_Type"/>
  </attributeGroup>
  <attributeGroup name = "absolute_or_Relative_Position_Properties_List">
    <attributeGroup ref = "fo:absolute_or_Relative_Position_Properties_List_FOP"/>
    <attributeGroup ref = "fo:absolute_or_Relative_Position_Properties_List_Not"/>
  </attributeGroup>
  <attributeGroup name = "Aural_Properties_List_FOP"/>
  <attributeGroup name = "Aural_Properties_List_Not">
    <attribute name = "azimuth" type = "fo:azimuth_Type"/>
    <attribute name = "cue" type = "xs:string"/>
    <attribute name = "cue-after" type = "xs:string"/>
    <attribute name = "cue-before" type = "xs:string"/>
    <attribute name = "elevation" type = "fo:elevation_Type"/>
    <attribute name = "pause" type = "xs:string"/>
    <attribute name = "pause-after" type = "xs:string"/>
    <attribute name = "pause-before" type = "xs:string"/>
    <attribute name = "pitch" type = "xs:string"/>
    <attribute name = "pitch-range" type = "xs:string"/>
    <attribute name = "play-during" type = "xs:string"/>
    <attribute name = "richness" type = "xs:string"/>
    <attribute name = "speak" type = "xs:string"/>
    <attribute name = "speak-header" type = "xs:string"/>
    <attribute name = "speak-numeral" type = "fo:speak_numeral_Type"/>
    <attribute name = "speak-punctuation" type = "xs:string"/>
    <attribute name = "speech-rate" type = "fo:speech_rate_Type"/>
    <attribute name = "stress" type = "xs:string"/>
    <attribute name = "voice-family" type = "xs:string"/>
    <attribute name = "volume" type = "xs:string"/>
  </attributeGroup>
  <attributeGroup name = "Aural_Properties_List">
    <attributeGroup ref = "fo:Aural_Properties_List_FOP"/>
    <attributeGroup ref = "fo:Aural_Properties_List_Not"/>
  </attributeGroup>
  <attributeGroup name = "Border_Padding_and_Background_Properties_List">
    <attributeGroup ref = "fo:Border_Properties_List"/>
    <attributeGroup ref = "fo:Padding_Properties_List"/>
    <attributeGroup ref = "fo:Background_Properties_List"/>
  </attributeGroup>
  <attributeGroup name = "Absolute_Position_Properties_List_FOP">
    <annotation>
      <documentation>
        Inherited
        <attribute name = "position" type = "fo:position_Type"/>
      </documentation>
    </annotation>
    <attribute name = "top" type = "fo:top_Type"/>
    <attribute name = "right" type = "fo:right_Type"/>
    <attribute name = "bottom" type = "fo:bottom_Type"/>
    <attribute name = "left" type = "fo:left_Type"/>
  </attributeGroup>
  <attributeGroup name = "Absolute_Position_Properties_List_Not">
    <attribute name = "absolute-position" type = "fo:absolute_position_Type"/>
  </attributeGroup>
  <attributeGroup name = "Absolute_Position_Properties_List">
    <attributeGroup ref = "fo:Absolute_Position_Properties_List_FOP"/>
    <attributeGroup ref = "fo:Absolute_Position_Properties_List_Not"/>
  </attributeGroup>
  <attributeGroup name = "Background_Properties_List_FOP">
    <attribute name = "background-color" type = "fo:background_color_Type"/>
  </attributeGroup>
  <attributeGroup name = "Background_Properties_List_Not">
    <attribute name = "background-attachment" type = "fo:background_attachment_Type"/>
    <attribute name = "background-image" type = "xs:string"/>
    <attribute name = "background-repeat" type = "fo:background_repeat_Type"/>
    <attribute name = "background-position-horizontal" type = "fo:background_position_horizontal_Type"/>
    <attribute name = "background-position-vertical" type = "fo:background_position_vertical_Type"/>
  </attributeGroup>
  <attributeGroup name = "Background_Properties_List">
    <attributeGroup ref = "fo:Background_Properties_List_FOP"/>
    <attributeGroup ref = "fo:Background_Properties_List_Not"/>
  </attributeGroup>
  <attributeGroup name = "Border_Properties_List">
    <attribute name = "border" type = "fo:border_Type"/>
    <attribute name = "border-before-color" type = "fo:color_Type"/>
    <attribute name = "border-before-style" type = "fo:border_style_Type"/>
    <attribute name = "border-before-width" type = "fo:border_before_width_Type"/>
    <attribute name = "border-before-width.length" type = "fo:length_Type"/>
    <attribute name = "border-before-width.conditionality" type = "fo:conditionality_Type"/>
    <attribute name = "border-after-color" type = "fo:color_Type"/>
    <attribute name = "border-after-style" type = "fo:border_style_Type"/>
    <attribute name = "border-after-width" type = "fo:border_before_width_Type"/>
    <attribute name = "border-after-width.length" type = "fo:length_Type"/>
    <attribute name = "border-after-width.conditionality" type = "fo:conditionality_Type"/>
    <attribute name = "border-start-color" type = "fo:color_Type"/>
    <attribute name = "border-start-style" type = "fo:border_style_Type"/>
    <attribute name = "border-start-width" type = "fo:border_before_width_Type"/>
    <attribute name = "border-start-width.length" type = "fo:length_Type"/>
    <attribute name = "border-start-width.conditionality" type = "fo:conditionality_Type"/>
    <attribute name = "border-end-color" type = "fo:color_Type"/>
    <attribute name = "border-end-style" type = "fo:border_style_Type"/>
    <attribute name = "border-end-width" type = "fo:border_before_width_Type"/>
    <attribute name = "border-end-width.length" type = "fo:length_Type"/>
    <attribute name = "border-end-width.conditionality" type = "fo:conditionality_Type"/>
    <attribute name = "border-top-color" type = "fo:color_Type"/>
    <attribute name = "border-top-style" type = "fo:border_style_Type"/>
    <attribute name = "border-top-width" type = "fo:border_top_width_Type"/>
    <attribute name = "border-top-width.length" type = "fo:length_Type"/>
    <attribute name = "border-top-width.conditionality" type = "fo:conditionality_Type"/>
    <attribute name = "border-bottom-color" type = "fo:color_Type"/>
    <attribute name = "border-bottom-style" type = "fo:border_style_Type"/>
    <attribute name = "border-bottom-width" type = "fo:border_top_width_Type"/>
    <attribute name = "border-bottom-width.length" type = "fo:length_Type"/>
    <attribute name = "border-bottom-width.conditionality" type = "fo:conditionality_Type"/>
    <attribute name = "border-left-color" type = "fo:color_Type"/>
    <attribute name = "border-left-style" type = "fo:border_style_Type"/>
    <attribute name = "border-left-width" type = "fo:border_top_width_Type"/>
    <attribute name = "border-left-width.length" type = "fo:length_Type"/>
    <attribute name = "border-left-width.conditionality" type = "fo:conditionality_Type"/>
    <attribute name = "border-right-color" type = "fo:color_Type"/>
    <attribute name = "border-right-style" type = "fo:border_style_Type"/>
    <attribute name = "border-right-width" type = "fo:border_top_width_Type"/>
    <attribute name = "border-right-width.length" type = "fo:length_Type"/>
    <attribute name = "border-right-width.conditionality" type = "fo:conditionality_Type"/>
  </attributeGroup>
  <attributeGroup name = "Padding_Properties_List">
    <attribute name = "padding" type = "fo:padding_before_Type"/>
    <attribute name = "padding-before" type = "fo:padding_before_Type"/>
    <attribute name = "padding-before.length" type = "fo:length_Type"/>
    <attribute name = "padding-before.conditionality" type = "fo:conditionality_Type"/>
    <attribute name = "padding-after" type = "fo:padding_before_Type"/>
    <attribute name = "padding-after.length" type = "fo:length_Type"/>
    <attribute name = "padding-after.conditionality" type = "fo:conditionality_Type"/>
    <attribute name = "padding-start" type = "fo:padding_before_Type"/>
    <attribute name = "padding-start.length" type = "fo:length_Type"/>
    <attribute name = "padding-start.conditionality" type = "fo:conditionality_Type"/>
    <attribute name = "padding-end" type = "fo:padding_before_Type"/>
    <attribute name = "padding-end.length" type = "fo:length_Type"/>
    <attribute name = "padding-end.conditionality" type = "fo:conditionality_Type"/>
    <attribute name = "padding-top" type = "fo:padding_top_Type"/>
    <attribute name = "padding-top.length" type = "fo:length_Type"/>
    <attribute name = "padding-top.conditionality" type = "fo:conditionality_Type"/>
    <attribute name = "padding-bottom" type = "fo:padding_top_Type"/>
    <attribute name = "padding-bottom.length" type = "fo:length_Type"/>
    <attribute name = "padding-bottom.conditionality" type = "fo:conditionality_Type"/>
    <attribute name = "padding-left" type = "fo:padding_top_Type"/>
    <attribute name = "padding-left.length" type = "fo:length_Type"/>
    <attribute name = "padding-left.conditionality" type = "fo:conditionality_Type"/>
    <attribute name = "padding-right" type = "fo:padding_top_Type"/>
    <attribute name = "padding-right.length" type = "fo:length_Type"/>
    <attribute name = "padding-right.conditionality" type = "fo:conditionality_Type"/>
  </attributeGroup>
  <attributeGroup name = "Font_Properties_List_FOP">
    <annotation>
      <documentation>
        Font properties are all inheritable
        <attribute name = "font-family" type = "fo:family_name_Type"/>
        <attribute name = "font-size" type = "fo:font_size_Type"/>
        <attribute name = "font-style" type = "fo:font_style_Type"/>
        <attribute name = "font-weight" type = "fo:font_weight_Type"/>
      </documentation>
    </annotation>
    </attributeGroup>
  <attributeGroup name = "Font_Properties_List_Not">
    <annotation>
      <documentation>
        Font properties are all inheritable
        <attribute name = "font" type = "xs:string"/>
        <attribute name = "font-selection-strategy" type = "fo:font_selection_strategy_Type"/>
        <attribute name = "font-size-adjust" type = "xs:string"/>
        <attribute name = "font-stretch" type = "xs:string"/>
        <attribute name = "font-variant" type = "fo:font_variant_Type"/>
      </documentation>
    </annotation>
    </attributeGroup>
  <attributeGroup name = "Font_Properties_List">
    <attributeGroup ref = "fo:Font_Properties_List_FOP"/>
    <attributeGroup ref = "fo:Font_Properties_List_Not"/>
  </attributeGroup>
  <attributeGroup name = "Hyphenation_Properties_List_FOP">
    <annotation>
      <documentation>
        The hyphenation properties are all inheritable and so superceeded by that list 
        <attribute name = "country" type = "fo:country_Type"/>
        <attribute name = "language" type = "fo:language_Type"/>
        <attribute name = "hyphenate" type = "fo:hyphenate_Type"/>
        <attribute name = "hyphenation-character" type = "fo:hyphenation_character_Type"/>
        <attribute name = "hyphenation-push-character-count" type = "fo:integer_Type"/>
        <attribute name = "hyphenation-remain-character-count" type = "fo:integer_Type"/>
      </documentation>
    </annotation>
    </attributeGroup>
  <attributeGroup name = "Hyphenation_Properties_List_Not">
    <annotation>
      <documentation>
        The hyphenation properties are all inheritable and so superceeded by that list 
        <attribute name = "script" type = "xs:string"/>
      </documentation>
    </annotation>
    </attributeGroup>
  <attributeGroup name = "Hyphenation_Properties_List">
    <attributeGroup ref = "fo:Hyphenation_Properties_List_FOP"/>
    <attributeGroup ref = "fo:Hyphenation_Properties_List_Not"/>
  </attributeGroup>
  <attributeGroup name = "Margin_Properties_Basic_List">
    <attribute name = "margin-top" type = "fo:margin_width_Type"/>
    <attribute name = "margin-bottom" type = "fo:margin_width_Type"/>
    <attribute name = "margin-left" type = "fo:margin_width_Type"/>
    <attribute name = "margin-right" type = "fo:margin_width_Type"/>
  </attributeGroup>
  <attributeGroup name = "Margin_Properties_vertical_Space_List_FOP">
    <annotation>
      <documentation>
        Indent properties are inheritable
        <attribute name = "start-indent" type = "fo:start_indent_Type"/>
        <attribute name = "end-indent" type = "fo:end_indent_Type"/>
      </documentation>
    </annotation>
    <attribute name = "space-before.optimum" type = "fo:space_before_optimum_Type"/>
    <attribute name = "space-after.optimum" type = "fo:space_after_optimum_Type"/>
  </attributeGroup>
  <attributeGroup name = "Margin_Properties_vertical_Space_List_Not">
    <attribute name = "space-before" type = "fo:space_Type"/>
    <attribute name = "space-before.minimum" type = "fo:length_Type"/>
    <attribute name = "space-before.maximum" type = "fo:length_Type"/>
    <attribute name = "space-before.conditionality" type = "fo:conditionality_Type"/>
    <attribute name = "space-before.precedence" type = "fo:integer_Force_Type"/>
    <attribute name = "space-after" type = "fo:space_Type"/>
    <attribute name = "space-after.minimum" type = "fo:length_Type"/>
    <attribute name = "space-after.maximum" type = "fo:length_Type"/>
    <attribute name = "space-after.conditionality" type = "fo:conditionality_Type"/>
    <attribute name = "space-after.precedence" type = "fo:integer_Force_Type"/>
  </attributeGroup>
  <attributeGroup name = "Margin_Properties_vertical_Space_List">
    <attributeGroup ref = "fo:Margin_Properties_vertical_Space_List_FOP"/>
    <attributeGroup ref = "fo:Margin_Properties_vertical_Space_List_Not"/>
  </attributeGroup>
  <attributeGroup name = "Margin_Properties_Block_List">
    <attributeGroup ref = "fo:Margin_Properties_Basic_List"/>
    <attributeGroup ref = "fo:Margin_Properties_vertical_Space_List"/>
  </attributeGroup>
  <attributeGroup name = "Margin_Properties_Inline_List">
    <attributeGroup ref = "fo:Margin_Properties_Basic_List"/>
    <attributeGroup ref = "fo:Margin_Properties_Horizontal_Space_List"/>
  </attributeGroup>
  <attributeGroup name = "Margin_Properties_Horizontal_Space_List_FOP"/>
  <attributeGroup name = "Margin_Properties_Horizontal_Space_List_Not">
    <attribute name = "space-start" type = "fo:space_start_Type"/>
    <attribute name = "space-start.minimum" type = "fo:length_Type"/>
    <attribute name = "space-start.optimum" type = "fo:length_Type"/>
    <attribute name = "space-start.maximum" type = "fo:length_Type"/>
    <attribute name = "space-start.conditionality" type = "fo:conditionality_Type"/>
    <attribute name = "space-start.precedence" type = "fo:integer_Force_Type"/>
    <attribute name = "space-end" type = "fo:space_start_Type"/>
    <attribute name = "space-end.minimum" type = "fo:length_Type"/>
    <attribute name = "space-end.optimum" type = "fo:length_Type"/>
    <attribute name = "space-end.maximum" type = "fo:length_Type"/>
    <attribute name = "space-end.conditionality" type = "fo:conditionality_Type"/>
    <attribute name = "space-end.precedence" type = "fo:integer_Force_Type"/>
  </attributeGroup>
  <attributeGroup name = "Margin_Properties_Horizontal_Space_List">
    <attributeGroup ref = "fo:Margin_Properties_Horizontal_Space_List_FOP"/>
    <attributeGroup ref = "fo:Margin_Properties_Horizontal_Space_List_Not"/>
  </attributeGroup>

  <annotation>
    <documentation>
      Simple Types definitions
    </documentation>
  </annotation>
  <simpleType name = "integer_Type">
    <annotation>
      <documentation>
        A signed integer value which consists of an optional '+' or '-' character followed by a sequence of digits. A property may define additional constraints on the value.
        <enumeration value = "-3"/>
        <enumeration value = "0"/>
        <enumeration value = "1"/>
        <enumeration value = "+3"/>
        <enumeration value = "5"/>
      </documentation>
    </annotation>
    <restriction base = "NMTOKEN">
      <pattern value = "[\+\-]?\d+"/>
    </restriction>
  </simpleType>
  <simpleType name = "number_Type">
    <annotation>
      <documentation>
        A signed real number which consists of an optional '+' or '-' character followed by a sequence of digits followed by an optional '.' character and sequence of digits. A property may define additional constraints on the value. 
        <enumeration value = "-2.4"/>
        <enumeration value = "2"/>
        <enumeration value = "4"/>
      </documentation>
    </annotation>
    <restriction base = "string">
      <pattern value = "[\+\-]?\d+\.\d*"/>
    </restriction>
  </simpleType>
  <simpleType name = "length_Type">
    <annotation>
      <documentation>
        A signed length value where a 'length' is a real number plus a unit qualification. A property may define additional constraints on the value.
        <enumeration value = "1px"/>
        <enumeration value = "1pt"/>
        <enumeration value = "1mm"/>
        <enumeration value = "1cm"/>
        <enumeration value = "1in"/>
        <enumeration value = "1em"/>
      </documentation>
    </annotation>
    <restriction base = "string">
      <pattern value = "[\+\-]?\d+\.?\d*(px|pt|mm|cm|in|em|%)"/>
    </restriction>
  </simpleType>
  <simpleType name = "length_range_Type">
    <annotation>
      <documentation>
        A compound datatype, with components: minimum, optimum, maximum. Each component is a <length xmlns=""/>. If "minimum" is greater than optimum, it will be treated as if it had been set to "optimum". If "maximum" is less than optimum, it will be treated as if it had been set to "optimum". A property may define additional constraints on the values.
        <enumeration value = "1mm 2mm 3mm"/>
        <enumeration value = "2mm 2mm 1mm"/>
        <enumeration value = "3mm 2mm 1mm"/>
      </documentation>
    </annotation>
    <xs:restriction>
      <xs:simpleType>
        <xs:list>
          <xs:simpleType>
            <xs:restriction base = "fo:length_Type"/>
          </xs:simpleType>
        </xs:list>
      </xs:simpleType>
      <xs:minLength value = "3" />
      <xs:maxLength value = "3" />
    </xs:restriction>
  </simpleType>
  <simpleType name = "length_conditional_Type">
    <annotation>
      <documentation>
        A compound datatype, with components: length, conditionality. The length component is a <length xmlns=""/>. The conditionality component is either "discard" or "retain". A property may define additional constraints on the values.
        <enumeration value = "1mm retain"/>
        <enumeration value = "1mm discard"/>
        <enumeration value = "1px retain"/>
        <enumeration value = "1px discard"/>
        <enumeration value = "1pt retain"/>
        <enumeration value = "1pt discard"/>
        <enumeration value = "1mm retain"/>
        <enumeration value = "1mm discard"/>
        <enumeration value = "1cm retain"/>
        <enumeration value = "1cm discard"/>
        <enumeration value = "1in retain"/>
        <enumeration value = "1in discard"/>
        <enumeration value = "1em retain"/>
        <enumeration value = "1em discard"/>
      </documentation>
    </annotation>
    <restriction base = "string">
      <pattern value = "\+?\d+\.?\d*(px|pt|mm|cm|in|em|%) (retain|discard)"/>
    </restriction>
  </simpleType>
  <xs:simpleType name = "length_bp_ip_direction_Type">
    <annotation>
      <documentation>
        A compound datatype, with components: block-progression-direction, and inline-progression-direction. Each component is a <length xmlns=""/>. A property may define additional constraints on the values.
        <enumeration value = "1mm 2mm"/>
        <enumeration value = "2mm"/>
        <enumeration value = "3mm 2mm"/>
      </documentation>
    </annotation>
    <xs:restriction>
      <xs:simpleType>
        <xs:list>
          <xs:simpleType>
            <xs:restriction base = "fo:length_Type"/>
          </xs:simpleType>
        </xs:list>
      </xs:simpleType>
      <xs:minLength value = "1" />
      <xs:maxLength value = "2" />
    </xs:restriction>
  </xs:simpleType>
  <simpleType name = "space_Type">
    <annotation>
      <documentation>
        A compound datatype, with components: minimum, optimum, maximum, precedence, and conditionality. The minimum, optimum, and maximum components are <length xmlns=""/>s. The precedence component is either "force" or an <integer/>. The conditionality component is either "discard" or "retain". If "minimum" is greater than optimum, it will be treated as if it had been set to "optimum". If "maximum" is less than optimum, it will be treated as if it had been set to "optimum".
        <enumeration value = "1mm 2mm 3mm force retain"/>
        <enumeration value = "1mm 2mm 3mm force discard"/>
        <enumeration value = "1mm 2mm 3mm 3 retain"/>
      </documentation>
    </annotation>
    <restriction base = "string">
      <pattern value = "([ ]?\+?\d+\.?\d*(px|pt|mm|cm|in|em|%)){1,3}( \+?\d+| force)?( retain| discard)?"/>
    </restriction>
  </simpleType>
  <simpleType name = "angle_Type">
    <annotation>
      <documentation>
        A representation of an angle consisting of an optional '+' or  '-' character immediately followed by a <number/> immediately followed by an angle unit identifier. Angle unit identifiers are: 'deg' (for degrees), 'grad' (for grads), and 'rad' (for radians). The specified values are normalized to the range 0deg to 360deg. A property may define additional constraints on the value.
        <enumeration value = "3deg"/>
        <enumeration value = "45deg"/>
        <enumeration value = "-12deg"/>
      </documentation>
    </annotation>
    <restriction base = "string">
      <pattern value = "[\+\-]?[1-3]?\d?\d?\.?\d*(deg|grad|rad)"/>
    </restriction>
  </simpleType>
  <simpleType name = "percentage_Type">
    <annotation>
      <documentation>
        A signed real percentage which consists of an optional '+' or '-' character followed by a sequence of digits followed by an optional '.' character and sequence of digits followed by '%'. A property may define additional constraints on the value.
        <enumeration value = "10%"/>
        <enumeration value = "30%"/>
        <enumeration value = "100%"/>
      </documentation>
    </annotation>
    <restriction base = "string">
      <pattern value = "[\+\-]?\d+\.?\d*%"/>
    </restriction>
  </simpleType>
  <simpleType name = "character_Type">
    <annotation>
      <documentation>
        <enumeration value = "-"/>
        <enumeration value = "h"/>
        <enumeration value = "e"/>
        <enumeration value = "l"/>
        <enumeration value = "p"/>
        <enumeration value = "o"/>
      </documentation>
    </annotation>
    <restriction base = "string">
      <pattern value = "."/>
    </restriction>
  </simpleType>
  <simpleType name = "name_Type">
    <annotation>
      <documentation>
        A string of characters representing a name. It must conform to the definition of an NCName in
      </documentation>
    </annotation>
    <restriction base = "string">
      <pattern value = ".*"/>
    </restriction>
  </simpleType>
  <simpleType name = "family_name_Type">
    <annotation>
      <documentation>
        A string of characters identifying a font.
        <enumeration value = "serif"/>
        <enumeration value = "sans-serif"/>
        <enumeration value = "Arial"/>
        <enumeration value = "Times"/>
        <enumeration value = "Courier"/>
        <enumeration value = "Zapf Dingbats"/>
        <enumeration value = "code39"/>
        <enumeration value = "cyberbit"/>
        <enumeration value = "Edwardian Script ITC"/>
        <enumeration value = "Viner Hand ITC"/>
        <enumeration value = "Georgia"/>
        <enumeration value = "Bookman Old Style"/>
        <enumeration value = "Book Antiqua"/>
      </documentation>
    </annotation>
    <restriction base = "string">
      <pattern value = ".*"/>
    </restriction>
  </simpleType>
  <simpleType name = "color_Type">
    <annotation>
      <documentation>
        Either a string of characters representing a keyword or a color function defined in . The list of keyword color names is: aqua, black, blue, fuchsia, gray, green, lime, maroon, navy, olive, purple, red, silver, teal, white, and yellow.
        <enumeration value = "red"/>
        <enumeration value = "blue"/>
        <enumeration value = "yellow"/>
        <enumeration value = "green"/>
      </documentation>
    </annotation>
    <union memberTypes = "fo:color_Hex_Type fo:color_Name_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "proportional_column_width_Type">
    <annotation>
      <documentation>
        The function proportional-column-width(N[0])
        This returns a width as a fraction of the available width as ( N[0] / sum1 ) * available space
        The parent table must have width="x" and table-layout="fixed"
        <enumeration value = "proportional-column-width(Number)"/>
      </documentation>
    </annotation>
    <restriction base = "string">
      <pattern value = "proportional-column-width\(\d+\)"/>
    </restriction>
  </simpleType>
  <simpleType name = "column_width_Type">
    <annotation>
      <documentation>
        <length xmlns=""/>, <percentage/>, <function>proportional-column-width</function>, or <function>common-functions</function>
        <enumeration value = "20mm"/>
        <enumeration value = "15%"/>
      </documentation>
    </annotation>
    <union memberTypes = "fo:length_percentage_Type fo:proportional_column_width_Type"/>
  </simpleType>
  <annotation>
    <documentation>
      <script/> A string of characters conforming to an ISO 15924 script code.
    </documentation>
  </annotation>
  <annotation>
    <documentation>
      <id/>     A string of characters conforming to the definition of an NCName in and is unique within the stylesheet.
    </documentation>
  </annotation>
  <annotation>
    <documentation>
      <idRef/>  A string of characters conforming to the definition of an NCName in and that matches an ID property value used within the stylesheet.
    </documentation>
  </annotation>
  <annotation>
    <documentation>
      <uri-specification/> A sequence of characters that is "url(", followed by optional white space, followed by an optional single quote (') or double quote (") character, followed by a URI reference as defined in , followed by an optional single quote (') or double quote (") character, followed by optional white space, followed by ")". The two quote characters must be the same and must both be present or absent. If the URI reference contains a single quote, the two quote characters must be present and be double quotes.
    </documentation>
  </annotation>
  <annotation>
    <documentation>
      <time/>   A <number/> immediately followed by a time unit identifier. Time unit identifiers are: 'ms' (for milliseconds) and 's' (for seconds).
    </documentation>
  </annotation>
  <annotation>
    <documentation>
      <frequency/> A <number/> immediately followed by a frequency unit identifier. Frequency unit identifiers are: 'Hz' (for Hertz) and 'kHz' (for kilo Hertz).
    </documentation>
  </annotation>
  <annotation>
    <documentation>
      <generic-family/> The following generic families are defined: "serif", "sans-serif", "cursive", "fantasy", and "monospace". Please see the section on generic font families for descriptions of these families. Generic font family names are keywords, and therefore must not be quoted.
    </documentation>
  </annotation>
  <simpleType name = "absolute_size_Type">
    <annotation>
      <documentation>
        An <absolute-size/> keyword refers to an entry in a table of font sizes computed and kept by the user agent. Possible values are: [ xx-small | x-small | small | medium | large | x-large | xx-large ] On a computer screen a scaling factor of 1.2 is suggested between adjacent indexes; if the "medium" font is 12pt, the "large" font could be 14.4pt. Different media may need different scaling factors. Also, the user agent should take the quality and availability of fonts into account when computing the table. The table may be different from one font family to another. Note. In CSS1, the suggested scaling factor between adjacent indexes was 1.5 which user experience proved to be too large.
      </documentation>
    </annotation>
    <restriction base = "NMTOKEN">
      <enumeration value = "xx-small"/>
      <enumeration value = "x-small"/>
      <enumeration value = "small"/>
      <enumeration value = "medium"/>
      <enumeration value = "large"/>
      <enumeration value = "x-large"/>
      <enumeration value = "xx-large"/>
    </restriction>
  </simpleType>
  <simpleType name = "relative_size_Type">
    <annotation>
      <documentation>
        A <relative-size/> keyword is interpreted relative to the table of font sizes and the font size of the parent element. Possible values are: [ larger | smaller ] For example, if the parent element has a font size of "medium", a value of "larger" will make the font size of the current element be "large". If the parent element's size is not close to a table entry, the user agent is free to interpolate between table entries or round off to the closest one. The user agent may have to extrapolate table values if the numerical value goes beyond the keywords.
      </documentation>
    </annotation>
    <restriction base = "NMTOKEN">
      <enumeration value = "larger"/>
      <enumeration value = "smaller"/>
    </restriction>
  </simpleType>
  <simpleType name = "clip_Type">
    <annotation>
      <documentation>
        shape_Type In CSS2, the only valid <shape/> value is: rect (<top/>, <right/>, <bottom/>, <left/>) where <top/>, <bottom/> <right/>, and <left/> specify offsets from the respective sides of the box. <top/>, <right/>, <bottom/>, and <left/> may either have a <length xmlns=""/> value or "auto". Negative lengths are permitted. The value "auto" means that a given edge of the clipping region will be the same as the edge of the element's generated box (i.e., "auto" means the same as "0".) When coordinates are rounded to pixel coordinates, care should be taken that no pixels remain visible when <left/> + <right/> is equal to the element's width (or <top/> + <bottom/> equals the element's height), and conversely that no pixels remain hidden when these values are 0.
      </documentation>
    </annotation>
    <restriction base = "string">
      <annotation>
        <documentation>
          length_Type{1,2}
        </documentation>
      </annotation>
      <pattern value = "(rect\((\+?\d+\.?\d*(px|pt|mm|cm|in|em|%)|auto),(\+?\d+\.?\d*(px|pt|mm|cm|in|em|%)|auto),(\+?\d+\.?\d*(px|pt|mm|cm|in|em|%)|auto),(\+?\d+\.?\d*(px|pt|mm|cm|in|em|%)|auto)\)|auto|inherit)"/>
    </restriction>
  </simpleType>
  
  <simpleType name = "border_side_Type">
    <annotation>
      <documentation>
        "fo:width_Type fo:border_style_Type fo:color_Type"
        <enumeration value="thin solid red"/>
        <enumeration value="medium solid black"/>
        <enumeration value="thick solid blue"/>
      </documentation>
    </annotation>
    <restriction base = "string">
      <pattern value = "(thin|medium|thick) (none|hidden|dotted|dashed|solid|double|groove|ridge|inset|outset) ([a-z]{3,16}|#[0-9A-F]{6})"/>
    </restriction>
  </simpleType>
  <simpleType name = "border_margin_width_Type">
    <annotation>
      <documentation>
        "fo:width_Type{1,4} fo:inherit_Type"
        <enumeration value = "none"/>
        <enumeration value = "thin"/>
        <enumeration value = "medium"/>
        <enumeration value = "thick"/>
        <enumeration value = "thick thin medium none"/>
      </documentation>
    </annotation>
    <xs:restriction>
      <xs:simpleType>
        <xs:list>
          <xs:simpleType>
            <union memberTypes = "fo:width_Type fo:positive_length_Type"/>
          </xs:simpleType>
        </xs:list>
      </xs:simpleType>
      <xs:minLength value = "1" />
      <xs:maxLength value = "4" />
    </xs:restriction>
  </simpleType>
  <simpleType name = "border_multi_style_Type" >
    <annotation>
      <documentation>
        "fo:border_style_Type{1,4} fo:inherit_Type"
        <enumeration value = "none"/>
        <enumeration value = "hidden"/>
        <enumeration value = "dotted"/>
        <enumeration value = "dashed"/>
        <enumeration value = "solid"/>
        <enumeration value = "double"/>
        <enumeration value = "groove"/>
        <enumeration value = "ridge"/>
        <enumeration value = "inset"/>
        <enumeration value = "outset"/>
        <enumeration value = "solid dashed ridge groove"/>
      </documentation>
    </annotation>
    <xs:restriction>
      <xs:simpleType>
        <xs:list>
          <xs:simpleType>
            <xs:restriction base = "fo:border_style_Type"/>
          </xs:simpleType>
        </xs:list>
      </xs:simpleType>
      <xs:minLength value = "1" />
      <xs:maxLength value = "4" />
    </xs:restriction>
  </simpleType>
  <simpleType name = "border_color_Type" >
    <annotation>
      <documentation>
        "fo:background_color_Type{1,4}"
        <enumeration value="red"/>
        <enumeration value="green"/>
        <enumeration value="blue red yellow"/>
        <enumeration value="black"/>
        <enumeration value="black red yellow green"/>
        <enumeration value="silver black"/>
      </documentation>
    </annotation>
    <xs:restriction>
      <xs:simpleType>
        <xs:list>
          <xs:simpleType>
            <xs:restriction base = "fo:color_Type"/>
          </xs:simpleType>
        </xs:list>
      </xs:simpleType>
      <xs:minLength value = "1" />
      <xs:maxLength value = "4" />
    </xs:restriction>
  </simpleType>
  <simpleType name = "max_width_Type">
    <annotation>
      <documentation>
        <enumeration value="100%"/>
        <enumeration value="160mm"/>
      </documentation>
    </annotation>
    <union memberTypes = "fo:length_Type fo:percentage_Type fo:none_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "background_Type">
    <union memberTypes = "fo:background_color_Type fo:background_image_Type fo:background_repeat_Type fo:background_attachment_Type fo:background_position_Type"/>
  </simpleType>
  <simpleType name = "background_position_Type">
    <union memberTypes = "fo:background_position_percentage_Type fo:background_position_length_Type fo:background_position_base_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "background_position_percentage_Type">
    <xs:restriction>
      <xs:simpleType>
        <xs:list>
          <xs:simpleType>
            <xs:restriction base = "fo:percentage_Type"/>
          </xs:simpleType>
        </xs:list>
      </xs:simpleType>
      <xs:minLength value = "1" />
      <xs:maxLength value = "2" />
    </xs:restriction>
  </simpleType>
  <simpleType name = "background_position_length_Type">
    <xs:restriction>
      <xs:simpleType>
        <xs:list>
          <xs:simpleType>
            <xs:restriction base = "fo:length_Type"/>
          </xs:simpleType>
        </xs:list>
      </xs:simpleType>
      <xs:minLength value = "1" />
      <xs:maxLength value = "2" />
    </xs:restriction>
  </simpleType>
  <simpleType name = "background_position_base_Type">
    <restriction base = "string">
      <enumeration value = "top left"/>
      <enumeration value = "top center"/>
      <enumeration value = "top right"/>
      <enumeration value = "center left"/>
      <enumeration value = "center center"/>
      <enumeration value = "center right"/>
      <enumeration value = "bottom left"/>
      <enumeration value = "bottom center"/>
      <enumeration value = "bottom right"/>
    </restriction>
  </simpleType>
  <simpleType name = "size_Type">
    <union memberTypes = "fo:background_position_length_Type fo:size_base_Type"/>
  </simpleType>
  <simpleType name = "size_base_Type">
    <restriction base = "string">
      <enumeration value = "auto"/>
      <enumeration value = "landscape"/>
      <enumeration value = "portrait"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "vertical_align_base_Type">
    <restriction base = "string">
      <enumeration value = "baseline"/>
      <enumeration value = "middle"/>
      <enumeration value = "sub"/>
      <enumeration value = "super"/>
      <enumeration value = "text-top"/>
      <enumeration value = "text-bottom"/>
      <enumeration value = "top"/>
      <enumeration value = "bottom"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "vertical_align_Type">
    <union memberTypes = "fo:vertical_align_base_Type fo:percentage_Type fo:length_Type"/>
  </simpleType>
  <simpleType name = "leader_length_Type">
    <annotation>
      <documentation>
        <enumeration value = "50% 100% 100%"/>
      </documentation>
    </annotation>
    <union memberTypes = "fo:length_range_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "letter_spacing_Type">
    <union memberTypes = "fo:normal_Type fo:length_Type fo:space_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "font_size_adjust_Type">
    <union memberTypes = "fo:number_Type fo:none_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "space_start_Type">
    <union memberTypes = "fo:space_Type fo:percentage_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "normal_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "normal"/>
    </restriction>
  </simpleType>
  <simpleType name = "background_attachment_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "scroll"/>
      <enumeration value = "fixed"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "background_repeat_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "repeat"/>
      <enumeration value = "repeat-x"/>
      <enumeration value = "repeat-y"/>
      <enumeration value = "no-repeat"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "speech_rate_base_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "x-slow"/>
      <enumeration value = "slow"/>
      <enumeration value = "medium"/>
      <enumeration value = "fast"/>
      <enumeration value = "x-fast"/>
      <enumeration value = "faster"/>
      <enumeration value = "slower"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "speech_rate_Type">
    <union memberTypes = "fo:speech_rate_base_Type fo:integer_Type"/>
  </simpleType>
  <simpleType name = "speak_numeral_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "digits"/>
      <enumeration value = "continuous"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "font_variant_Type">
    <restriction base = "string">
      <enumeration value = "normal"/>
      <enumeration value = "small-caps"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "line_height_Type">
    <union memberTypes = "fo:normal_Type fo:length_Type fo:integer_Type fo:percentage_Type fo:space_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "text_shadow_Type">
    <union memberTypes = "fo:none_Type fo:color_Type fo:length_range_Type"/>
  </simpleType>
  <simpleType name = "height_Type">
    <union memberTypes = "fo:length_Type fo:percentage_Type fo:auto_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "hyphenation_character_Type">
    <union memberTypes = "fo:character_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "content_height_Type">
    <union memberTypes = "fo:auto_Type fo:scale_to_fit_Type fo:length_Type fo:percentage_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "scale_to_fit_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "scale-to-fit"/>
    </restriction>
  </simpleType>

  <simpleType name = "font_weight_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "normal"/>
      <enumeration value = "bold">
        <annotation>
          <documentation>
            <fop_result>heavier than no value</fop_result>
          </documentation>
        </annotation>
      </enumeration>
      <enumeration value = "bolder">
        <annotation>
          <documentation>
            <fop_result>lighter than normal</fop_result> <fop_fail>unknown font</fop_fail>
          </documentation>
        </annotation>
      </enumeration>
      <enumeration value = "lighter">
        <annotation>
          <documentation>
            <fop_result>lighter than normal</fop_result> <fop_fail>unknown font</fop_fail>
          </documentation>
        </annotation>
      </enumeration>
      <enumeration value = "100">
        <annotation>
          <documentation>
            <fop_result>same as normal</fop_result>
          </documentation>
        </annotation>
      </enumeration>
      <enumeration value = "200">
        <annotation>
          <documentation>
            <fop_result>same as normal</fop_result>
          </documentation>
        </annotation>
      </enumeration>
      <enumeration value = "300">
        <annotation>
          <documentation>
            <fop_result>same as normal</fop_result>
          </documentation>
        </annotation>
      </enumeration>
      <enumeration value = "400">
        <annotation>
          <documentation>
            <fop_result>same as normal</fop_result>
          </documentation>
        </annotation>
      </enumeration>
      <enumeration value = "500">
        <annotation>
          <documentation>
            <fop_result>same as normal</fop_result>
          </documentation>
        </annotation>
      </enumeration>
      <enumeration value = "600">
        <annotation>
          <documentation>
            <fop_result>same as normal</fop_result>
          </documentation>
        </annotation>
      </enumeration>
      <enumeration value = "700">
        <annotation>
          <documentation>
            <fop_result>same as bold</fop_result>
          </documentation>
        </annotation>
      </enumeration>
      <enumeration value = "800">
        <annotation>
          <documentation>
            <fop_result>same as bold</fop_result>
          </documentation>
        </annotation>
      </enumeration>
      <enumeration value = "900">
        <annotation>
          <documentation>
            <fop_result>same as bold</fop_result>
          </documentation>
        </annotation>
      </enumeration>
      <enumeration value = "inherit">
        <annotation>
          <documentation>
            <fop_result>lighter than normal</fop_result> <fop_fail>unknown font</fop_fail>
          </documentation>
        </annotation>
      </enumeration>
    </restriction>
  </simpleType>
  <simpleType name = "font_style_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "normal"/>
      <enumeration value = "italic"/>
      <enumeration value = "oblique"/>
      <enumeration value = "backslant">
        <annotation>
          <documentation>
            <fop_fail>unknown font</fop_fail>
          </documentation>
        </annotation>
      </enumeration>
      <enumeration value = "inherit">
        <annotation>
          <documentation>
            <fop_fail>unknown font</fop_fail>
          </documentation>
        </annotation>
      </enumeration>
    </restriction>
  </simpleType>
  <simpleType name = "page_break_inside_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "auto"/>
      <enumeration value = "avoid"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "page_break_after_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "auto"/>
      <enumeration value = "always"/>
      <enumeration value = "avoid"/>
      <enumeration value = "left"/>
      <enumeration value = "right"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "white_space_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "normal"/>
      <enumeration value = "pre"/>
      <enumeration value = "nowrap"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "show_destination_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "replace"/>
      <enumeration value = "new"/>
    </restriction>
  </simpleType>
  <simpleType name = "indicate_destination_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "true"/>
      <enumeration value = "false"/>
    </restriction>
  </simpleType>
  <simpleType name = "speak_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "normal"/>
      <enumeration value = "none"/>
      <enumeration value = "spell-out"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "relative_align_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "before"/>
      <enumeration value = "baseline"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "empty_cells_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "show"/>
      <enumeration value = "hide"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "ends_row_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "true"/>
      <enumeration value = "false"/>
    </restriction>
  </simpleType>
  <simpleType name = "starts_row_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "true"/>
      <enumeration value = "false"/>
    </restriction>
  </simpleType>
  <simpleType name = "position_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "static"/>
      <enumeration value = "relative"/>
      <enumeration value = "absolute"/>
      <enumeration value = "fixed"/>
      <enumeration value = "inherit">
        <annotation>
          <documentation>
            <fop_fail>Unknown enumerated value</fop_fail>
          </documentation>
        </annotation>
      </enumeration>
    </restriction>
  </simpleType>
  <simpleType name = "conditionality_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "retain"/>
      <enumeration value = "discard"/>
    </restriction>
  </simpleType>
  <simpleType name = "keep_compound_Type">
    <annotation>
      <documentation>
        A compound datatype, with components: within-line, within-column, within-page. Each component is a <keep/>. 
        <enumeration value = "2 auto always"/>
        <enumeration value = "auto"/>
        <enumeration value = "always"/>
      </documentation>
    </annotation>
    <xs:restriction>
      <xs:simpleType>
        <xs:list>
          <xs:simpleType>
            <xs:restriction base = "fo:keep_integer_Type"/>
          </xs:simpleType>
        </xs:list>
      </xs:simpleType>
      <xs:minLength value = "1" />
      <xs:maxLength value = "3" />
    </xs:restriction>
  </simpleType>
  <simpleType name = "keep_integer_Type">
    <annotation>
      <documentation>
        <enumeration value = "2"/>
        <enumeration value = "5"/>
        <enumeration value = "10"/>
        <enumeration value = "auto"/>
        <enumeration value = "always"/>
      </documentation>
    </annotation>
    <restriction base = "NMTOKEN">
      <pattern value = "(\+?\d+|auto|always)"/>
    </restriction>
  </simpleType>
  <simpleType name = "letter_value_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "auto"/>
      <enumeration value = "alphabetic"/>
      <enumeration value = "traditional"/>
    </restriction>
  </simpleType>
  <simpleType name = "alignment_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "baseline"/>
      <enumeration value = "before-edge"/>
      <enumeration value = "text-before-edge"/>
      <enumeration value = "middle"/>
      <enumeration value = "central"/>
      <enumeration value = "after-edge"/>
      <enumeration value = "text-after-edge"/>
      <enumeration value = "ideographic"/>
      <enumeration value = "alphabetic"/>
      <enumeration value = "hanging"/>
      <enumeration value = "mathematical"/>
    </restriction>
  </simpleType>
  <simpleType name = "box_alignment_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "top"/>
      <enumeration value = "text-top"/>
      <enumeration value = "bottom"/>
      <enumeration value = "text-bottom"/>
    </restriction>
  </simpleType>
  <simpleType name = "breaks_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "inherit">
        <annotation>
          <documentation>
            <fop_fail>Unknown enumerated value</fop_fail>
          </documentation>
        </annotation>
      </enumeration>
      <enumeration value = "auto"/>
      <enumeration value = "column"/>
      <enumeration value = "page"/>
      <enumeration value = "even-page"/>
      <enumeration value = "odd-page"/>
    </restriction>
  </simpleType>
  <simpleType name = "force_page_count_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "auto"/>
      <enumeration value = "even"/>
      <enumeration value = "odd"/>
      <enumeration value = "end-on-even"/>
      <enumeration value = "end-on-odd"/>
      <enumeration value = "no-force"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "table_layout_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "auto"/>
      <enumeration value = "fixed"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "scaling_method_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "auto"/>
      <enumeration value = "integer-pixels"/>
      <enumeration value = "resample-any-method"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "displace_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "inherit"/>
      <enumeration value = "auto"/>
      <enumeration value = "none"/>
      <enumeration value = "line"/>
      <enumeration value = "indent"/>
      <enumeration value = "block"/>
    </restriction>
  </simpleType>
  <simpleType name = "media_usage_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "auto"/>
      <enumeration value = "paginate"/>
      <enumeration value = "bounded-in-one-dimension"/>
      <enumeration value = "unbounded"/>
    </restriction>
  </simpleType>
  <simpleType name = "rendering_intent_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "auto"/>
      <enumeration value = "perceptual"/>
      <enumeration value = "relative-colorimetric"/>
      <enumeration value = "saturation"/>
      <enumeration value = "absolute-colorimetric"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "hyphenate_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "false"/>
      <enumeration value = "true"/>
      <enumeration value = "inherit">
        <annotation>
          <documentation>
            <fop_fail>Unknown enumerated value</fop_fail>
          </documentation>
        </annotation>
      </enumeration>
    </restriction>
  </simpleType>
  <simpleType name = "suppress_at_line_break_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "auto"/>
      <enumeration value = "suppress"/>
      <enumeration value = "retain"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "treat_as_word_space_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "auto"/>
      <enumeration value = "true"/>
      <enumeration value = "false"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "dominant_baseline_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "auto"/>
      <enumeration value = "use-script"/>
      <enumeration value = "no-change"/>
      <enumeration value = "reset-size"/>
      <enumeration value = "ideographic"/>
      <enumeration value = "alphabetic"/>
      <enumeration value = "hanging"/>
      <enumeration value = "mathematical"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "orientation_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "inherit"/>
      <enumeration value = "0"/>
      <enumeration value = "90"/>
      <enumeration value = "180"/>
      <enumeration value = "270"/>
      <enumeration value = "-90"/>
      <enumeration value = "-180"/>
      <enumeration value = "-270"/>
      <enumeration value = "0deg"/>
      <enumeration value = "90deg"/>
      <enumeration value = "180deg"/>
      <enumeration value = "270deg"/>
      <enumeration value = "-90deg"/>
      <enumeration value = "-180deg"/>
      <enumeration value = "-270deg"/>
    </restriction>
  </simpleType>
  <simpleType name = "measurement_Type">
    <annotation>
      <documentation>
        Here to document the acceptable measurements 
      </documentation>
    </annotation>
    <restriction base = "string">
      <enumeration value = "px"/>
      <enumeration value = "pt"/>
      <enumeration value = "mm"/>
      <enumeration value = "in"/>
      <enumeration value = "cm"/>
      <enumeration value = "em"/>
    </restriction>
  </simpleType>
  <simpleType name = "positive_length_Type">
    <restriction base = "string">
      <pattern value = "\+?\d+\.?\d*(px|pt|mm|cm|in|em|%)"/>
    </restriction>
  </simpleType>
  <simpleType name = "negative_length_Type">
    <restriction base = "string">
      <pattern value = "-\d+\.?\d*(px|pt|mm|cm|in|em|%)"/>
    </restriction>
  </simpleType>
  <simpleType name = "integer_Force_Type">
    <restriction base = "string">
      <pattern value = "([0-9]+|force)"/>
    </restriction>
  </simpleType>
  <simpleType name = "padding_top_Type">
    <union memberTypes = "fo:width_Type fo:length_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "padding_before_Type">
    <union memberTypes = "fo:width_Type fo:length_Type fo:length_conditional_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "country_Type">
    <annotation>
      <documentation>
        A string of characters conforming to an ISO 3166 country code. 
      </documentation>
    </annotation>
    <restriction base = "NMTOKEN">
      <enumeration value = "none"/>
      <enumeration value = "inherit"/>
      <enumeration value = "AF"><annotation><documentation> country-name = "AFGHANISTAN"/ </documentation></annotation>               </enumeration>
      <enumeration value = "AL"><annotation><documentation> country-name = "ALBANIA"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "DZ"><annotation><documentation> country-name = "ALGERIA"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "AS"><annotation><documentation> country-name = "AMERICANSAMOA"/ </documentation></annotation>               </enumeration>
      <enumeration value = "AD"><annotation><documentation> country-name = "ANDORRA"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "AO"><annotation><documentation> country-name = "ANGOLA"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "AI"><annotation><documentation> country-name = "ANGUILLA"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "AQ"><annotation><documentation> country-name = "ANTARCTICA"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "AG"><annotation><documentation> country-name = "ANTIGUAANDBARBUDA"/ </documentation></annotation>             </enumeration>
      <enumeration value = "AR"><annotation><documentation> country-name = "ARGENTINA"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "AM"><annotation><documentation> country-name = "ARMENIA"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "AW"><annotation><documentation> country-name = "ARUBA"/ </documentation></annotation>                   </enumeration>
      <enumeration value = "AU"><annotation><documentation> country-name = "AUSTRALIA"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "AT"><annotation><documentation> country-name = "AUSTRIA"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "AZ"><annotation><documentation> country-name = "AZERBAIJAN"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "BS"><annotation><documentation> country-name = "BAHAMAS"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "BH"><annotation><documentation> country-name = "BAHRAIN"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "BD"><annotation><documentation> country-name = "BANGLADESH"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "BB"><annotation><documentation> country-name = "BARBADOS"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "BY"><annotation><documentation> country-name = "BELARUS"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "BE"><annotation><documentation> country-name = "BELGIUM"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "BZ"><annotation><documentation> country-name = "BELIZE"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "BJ"><annotation><documentation> country-name = "BENIN"/ </documentation></annotation>                   </enumeration>
      <enumeration value = "BM"><annotation><documentation> country-name = "BERMUDA"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "BT"><annotation><documentation> country-name = "BHUTAN"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "BO"><annotation><documentation> country-name = "BOLIVIA"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "BA"><annotation><documentation> country-name = "BOSNIAANDHERZEGOVINA"/ </documentation></annotation>            </enumeration>
      <enumeration value = "BW"><annotation><documentation> country-name = "BOTSWANA"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "BV"><annotation><documentation> country-name = "BOUVETISLAND"/ </documentation></annotation>                </enumeration>
      <enumeration value = "BR"><annotation><documentation> country-name = "BRAZIL"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "IO"><annotation><documentation> country-name = "BRITISHINDIANOCEANTERRITORY"/ </documentation></annotation>       </enumeration>
      <enumeration value = "BN"><annotation><documentation> country-name = "BRUNEIDARUSSALAM"/ </documentation></annotation>              </enumeration>
      <enumeration value = "BG"><annotation><documentation> country-name = "BULGARIA"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "BF"><annotation><documentation> country-name = "BURKINAFASO"/ </documentation></annotation>               </enumeration>
      <enumeration value = "BI"><annotation><documentation> country-name = "BURUNDI"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "KH"><annotation><documentation> country-name = "CAMBODIA"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "CM"><annotation><documentation> country-name = "CAMEROON"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "CA"><annotation><documentation> country-name = "CANADA"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "CV"><annotation><documentation> country-name = "CAPEVERDE"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "KY"><annotation><documentation> country-name = "CAYMANISLANDS"/ </documentation></annotation>               </enumeration>
      <enumeration value = "CF"><annotation><documentation> country-name = "CENTRALAFRICANREPUBLIC"/ </documentation></annotation>            </enumeration>
      <enumeration value = "TD"><annotation><documentation> country-name = "CHAD"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "CL"><annotation><documentation> country-name = "CHILE"/ </documentation></annotation>                   </enumeration>
      <enumeration value = "CN"><annotation><documentation> country-name = "CHINA"/ </documentation></annotation>                   </enumeration>
      <enumeration value = "CX"><annotation><documentation> country-name = "CHRISTMASISLAND"/ </documentation></annotation>             </enumeration>
      <enumeration value = "CC"><annotation><documentation> country-name = "COCOS(KEELING)ISLANDS"/ </documentation></annotation>           </enumeration>
      <enumeration value = "CO"><annotation><documentation> country-name = "COLOMBIA"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "KM"><annotation><documentation> country-name = "COMOROS"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "CG"><annotation><documentation> country-name = "CONGO"/ </documentation></annotation>                   </enumeration>
      <enumeration value = "CD"><annotation><documentation> country-name = "CONGO,THEDEMOCRATICREPUBLICOFTHE"/ </documentation></annotation>      </enumeration>
      <enumeration value = "CK"><annotation><documentation> country-name = "COOKISLANDS"/ </documentation></annotation>               </enumeration>
      <enumeration value = "CR"><annotation><documentation> country-name = "COSTARICA"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "CI"><annotation><documentation> country-name = "COTED'IVOIRE"/ </documentation></annotation>                </enumeration>
      <enumeration value = "HR"><annotation><documentation> country-name = "CROATIA"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "CU"><annotation><documentation> country-name = "CUBA"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "CY"><annotation><documentation> country-name = "CYPRUS"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "CZ"><annotation><documentation> country-name = "CZECHREPUBLIC"/ </documentation></annotation>               </enumeration>
      <enumeration value = "DK"><annotation><documentation> country-name = "DENMARK"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "DJ"><annotation><documentation> country-name = "DJIBOUTI"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "DM"><annotation><documentation> country-name = "DOMINICA"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "DO"><annotation><documentation> country-name = "DOMINICANREPUBLIC"/ </documentation></annotation>             </enumeration>
      <enumeration value = "TP"><annotation><documentation> country-name = "EASTTIMOR"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "EC"><annotation><documentation> country-name = "ECUADOR"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "EG"><annotation><documentation> country-name = "EGYPT"/ </documentation></annotation>                   </enumeration>
      <enumeration value = "SV"><annotation><documentation> country-name = "ELSALVADOR"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "GQ"><annotation><documentation> country-name = "EQUATORIALGUINEA"/ </documentation></annotation>              </enumeration>
      <enumeration value = "ER"><annotation><documentation> country-name = "ERITREA"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "EE"><annotation><documentation> country-name = "ESTONIA"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "ET"><annotation><documentation> country-name = "ETHIOPIA"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "FK"><annotation><documentation> country-name = "FALKLANDISLANDS(MALVINAS)"/ </documentation></annotation>         </enumeration>
      <enumeration value = "FO"><annotation><documentation> country-name = "FAROEISLANDS"/ </documentation></annotation>                </enumeration>
      <enumeration value = "FJ"><annotation><documentation> country-name = "FIJI"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "FI"><annotation><documentation> country-name = "FINLAND"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "FR"><annotation><documentation> country-name = "FRANCE"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "GF"><annotation><documentation> country-name = "FRENCHGUIANA"/ </documentation></annotation>                </enumeration>
      <enumeration value = "PF"><annotation><documentation> country-name = "FRENCHPOLYNESIA"/ </documentation></annotation>             </enumeration>
      <enumeration value = "TF"><annotation><documentation> country-name = "FRENCHSOUTHERNTERRITORIES"/ </documentation></annotation>         </enumeration>
      <enumeration value = "GA"><annotation><documentation> country-name = "GABON"/ </documentation></annotation>                   </enumeration>
      <enumeration value = "GM"><annotation><documentation> country-name = "GAMBIA"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "GE"><annotation><documentation> country-name = "GEORGIA"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "DE"><annotation><documentation> country-name = "GERMANY"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "GH"><annotation><documentation> country-name = "GHANA"/ </documentation></annotation>                   </enumeration>
      <enumeration value = "GI"><annotation><documentation> country-name = "GIBRALTAR"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "GR"><annotation><documentation> country-name = "GREECE"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "GL"><annotation><documentation> country-name = "GREENLAND"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "GD"><annotation><documentation> country-name = "GRENADA"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "GP"><annotation><documentation> country-name = "GUADELOUPE"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "GU"><annotation><documentation> country-name = "GUAM"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "GT"><annotation><documentation> country-name = "GUATEMALA"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "GN"><annotation><documentation> country-name = "GUINEA"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "GW"><annotation><documentation> country-name = "GUINEA-BISSAU"/ </documentation></annotation>               </enumeration>
      <enumeration value = "GY"><annotation><documentation> country-name = "GUYANA"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "HT"><annotation><documentation> country-name = "HAITI"/ </documentation></annotation>                   </enumeration>
      <enumeration value = "HM"><annotation><documentation> country-name = "HEARDISLANDANDMCDONALDISLANDS"/ </documentation></annotation>       </enumeration>
      <enumeration value = "VA"><annotation><documentation> country-name = "HOLYSEE(VATICANCITYSTATE)"/ </documentation></annotation>         </enumeration>
      <enumeration value = "HN"><annotation><documentation> country-name = "HONDURAS"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "HK"><annotation><documentation> country-name = "HONGKONG"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "HU"><annotation><documentation> country-name = "HUNGARY"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "IS"><annotation><documentation> country-name = "ICELAND"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "IN"><annotation><documentation> country-name = "INDIA"/ </documentation></annotation>                   </enumeration>
      <enumeration value = "ID"><annotation><documentation> country-name = "INDONESIA"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "IR"><annotation><documentation> country-name = "IRAN,ISLAMICREPUBLICOF"/ </documentation></annotation>            </enumeration>
      <enumeration value = "IQ"><annotation><documentation> country-name = "IRAQ"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "IE"><annotation><documentation> country-name = "IRELAND"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "IL"><annotation><documentation> country-name = "ISRAEL"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "IT"><annotation><documentation> country-name = "ITALY"/ </documentation></annotation>                   </enumeration>
      <enumeration value = "JM"><annotation><documentation> country-name = "JAMAICA"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "JP"><annotation><documentation> country-name = "JAPAN"/ </documentation></annotation>                   </enumeration>
      <enumeration value = "JO"><annotation><documentation> country-name = "JORDAN"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "KZ"><annotation><documentation> country-name = "KAZAKSTAN"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "KE"><annotation><documentation> country-name = "KENYA"/ </documentation></annotation>                   </enumeration>
      <enumeration value = "KI"><annotation><documentation> country-name = "KIRIBATI"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "KP"><annotation><documentation> country-name = "KOREA,DEMOCRATICPEOPLE'SREPUBLICOF"/ </documentation></annotation>      </enumeration>
      <enumeration value = "KR"><annotation><documentation> country-name = "KOREA,REPUBLICOF"/ </documentation></annotation>              </enumeration>
      <enumeration value = "KW"><annotation><documentation> country-name = "KUWAIT"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "KG"><annotation><documentation> country-name = "KYRGYZSTAN"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "LA"><annotation><documentation> country-name = "LAOPEOPLE'SDEMOCRATICREPUBLIC"/ </documentation></annotation>       </enumeration>
      <enumeration value = "LV"><annotation><documentation> country-name = "LATVIA"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "LB"><annotation><documentation> country-name = "LEBANON"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "LS"><annotation><documentation> country-name = "LESOTHO"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "LR"><annotation><documentation> country-name = "LIBERIA"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "LY"><annotation><documentation> country-name = "LIBYANARABJAMAHIRIYA"/ </documentation></annotation>            </enumeration>
      <enumeration value = "LI"><annotation><documentation> country-name = "LIECHTENSTEIN"/ </documentation></annotation>               </enumeration>
      <enumeration value = "LT"><annotation><documentation> country-name = "LITHUANIA"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "LU"><annotation><documentation> country-name = "LUXEMBOURG"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "MO"><annotation><documentation> country-name = "MACAU"/ </documentation></annotation>                   </enumeration>
      <enumeration value = "MK"><annotation><documentation> country-name = "MACEDONIA,THEFORMERYUGOSLAVREPUBLICOF"/ </documentation></annotation>   </enumeration>
      <enumeration value = "MG"><annotation><documentation> country-name = "MADAGASCAR"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "MW"><annotation><documentation> country-name = "MALAWI"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "MY"><annotation><documentation> country-name = "MALAYSIA"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "MV"><annotation><documentation> country-name = "MALDIVES"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "ML"><annotation><documentation> country-name = "MALI"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "MT"><annotation><documentation> country-name = "MALTA"/ </documentation></annotation>                   </enumeration>
      <enumeration value = "MH"><annotation><documentation> country-name = "MARSHALLISLANDS"/ </documentation></annotation>             </enumeration>
      <enumeration value = "MQ"><annotation><documentation> country-name = "MARTINIQUE"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "MR"><annotation><documentation> country-name = "MAURITANIA"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "MU"><annotation><documentation> country-name = "MAURITIUS"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "YT"><annotation><documentation> country-name = "MAYOTTE"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "MX"><annotation><documentation> country-name = "MEXICO"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "FM"><annotation><documentation> country-name = "MICRONESIA,FEDERATEDSTATESOF"/ </documentation></annotation>        </enumeration>
      <enumeration value = "MD"><annotation><documentation> country-name = "MOLDOVA,REPUBLICOF"/ </documentation></annotation>              </enumeration>
      <enumeration value = "MC"><annotation><documentation> country-name = "MONACO"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "MN"><annotation><documentation> country-name = "MONGOLIA"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "MS"><annotation><documentation> country-name = "MONTSERRAT"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "MA"><annotation><documentation> country-name = "MOROCCO"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "MZ"><annotation><documentation> country-name = "MOZAMBIQUE"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "MM"><annotation><documentation> country-name = "MYANMAR"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "NA"><annotation><documentation> country-name = "NAMIBIA"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "NR"><annotation><documentation> country-name = "NAURU"/ </documentation></annotation>                   </enumeration>
      <enumeration value = "NP"><annotation><documentation> country-name = "NEPAL"/ </documentation></annotation>                   </enumeration>
      <enumeration value = "NL"><annotation><documentation> country-name = "NETHERLANDS"/ </documentation></annotation>               </enumeration>
      <enumeration value = "AN"><annotation><documentation> country-name = "NETHERLANDSANTILLES"/ </documentation></annotation>           </enumeration>
      <enumeration value = "NC"><annotation><documentation> country-name = "NEWCALEDONIA"/ </documentation></annotation>                </enumeration>
      <enumeration value = "NZ"><annotation><documentation> country-name = "NEWZEALAND"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "NI"><annotation><documentation> country-name = "NICARAGUA"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "NE"><annotation><documentation> country-name = "NIGER"/ </documentation></annotation>                   </enumeration>
      <enumeration value = "NG"><annotation><documentation> country-name = "NIGERIA"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "NU"><annotation><documentation> country-name = "NIUE"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "NF"><annotation><documentation> country-name = "NORFOLKISLAND"/ </documentation></annotation>               </enumeration>
      <enumeration value = "MP"><annotation><documentation> country-name = "NORTHERNMARIANAISLANDS"/ </documentation></annotation>            </enumeration>
      <enumeration value = "NO"><annotation><documentation> country-name = "NORWAY"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "OM"><annotation><documentation> country-name = "OMAN"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "PK"><annotation><documentation> country-name = "PAKISTAN"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "PW"><annotation><documentation> country-name = "PALAU"/ </documentation></annotation>                   </enumeration>
      <enumeration value = "PS"><annotation><documentation> country-name = "PALESTINIANTERRITORY,OCCUPIED"/ </documentation></annotation>       </enumeration>
      <enumeration value = "PA"><annotation><documentation> country-name = "PANAMA"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "PG"><annotation><documentation> country-name = "PAPUANEWGUINEA"/ </documentation></annotation>                </enumeration>
      <enumeration value = "PY"><annotation><documentation> country-name = "PARAGUAY"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "PE"><annotation><documentation> country-name = "PERU"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "PH"><annotation><documentation> country-name = "PHILIPPINES"/ </documentation></annotation>               </enumeration>
      <enumeration value = "PN"><annotation><documentation> country-name = "PITCAIRN"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "PL"><annotation><documentation> country-name = "POLAND"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "PT"><annotation><documentation> country-name = "PORTUGAL"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "PR"><annotation><documentation> country-name = "PUERTORICO"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "QA"><annotation><documentation> country-name = "QATAR"/ </documentation></annotation>                   </enumeration>
      <enumeration value = "RE"><annotation><documentation> country-name = "R+UNION"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "RO"><annotation><documentation> country-name = "ROMANIA"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "RU"><annotation><documentation> country-name = "RUSSIANFEDERATION"/ </documentation></annotation>             </enumeration>
      <enumeration value = "RW"><annotation><documentation> country-name = "RWANDA"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "SH"><annotation><documentation> country-name = "SAINTHELENA"/ </documentation></annotation>               </enumeration>
      <enumeration value = "KN"><annotation><documentation> country-name = "SAINTKITTSANDNEVIS"/ </documentation></annotation>              </enumeration>
      <enumeration value = "LC"><annotation><documentation> country-name = "SAINTLUCIA"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "PM"><annotation><documentation> country-name = "SAINTPIERREANDMIQUELON"/ </documentation></annotation>            </enumeration>
      <enumeration value = "VC"><annotation><documentation> country-name = "SAINTVINCENTANDTHEGRENADINES"/ </documentation></annotation>        </enumeration>
      <enumeration value = "WS"><annotation><documentation> country-name = "SAMOA"/ </documentation></annotation>                   </enumeration>
      <enumeration value = "SM"><annotation><documentation> country-name = "SANMARINO"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "ST"><annotation><documentation> country-name = "SAOTOMEANDPRINCIPE"/ </documentation></annotation>              </enumeration>
      <enumeration value = "SA"><annotation><documentation> country-name = "SAUDIARABIA"/ </documentation></annotation>               </enumeration>
      <enumeration value = "SN"><annotation><documentation> country-name = "SENEGAL"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "SC"><annotation><documentation> country-name = "SEYCHELLES"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "SL"><annotation><documentation> country-name = "SIERRALEONE"/ </documentation></annotation>               </enumeration>
      <enumeration value = "SG"><annotation><documentation> country-name = "SINGAPORE"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "SK"><annotation><documentation> country-name = "SLOVAKIA"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "SI"><annotation><documentation> country-name = "SLOVENIA"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "SB"><annotation><documentation> country-name = "SOLOMONISLANDS"/ </documentation></annotation>                </enumeration>
      <enumeration value = "SO"><annotation><documentation> country-name = "SOMALIA"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "ZA"><annotation><documentation> country-name = "SOUTHAFRICA"/ </documentation></annotation>               </enumeration>
      <enumeration value = "GS"><annotation><documentation> country-name = "SOUTHGEORGIAANDTHESOUTHSANDWICHISLANDS"/ </documentation></annotation>    </enumeration>
      <enumeration value = "ES"><annotation><documentation> country-name = "SPAIN"/ </documentation></annotation>                   </enumeration>
      <enumeration value = "LK"><annotation><documentation> country-name = "SRILANKA"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "SD"><annotation><documentation> country-name = "SUDAN"/ </documentation></annotation>                   </enumeration>
      <enumeration value = "SR"><annotation><documentation> country-name = "SURINAME"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "SJ"><annotation><documentation> country-name = "SVALBARDANDJANMAYEN"/ </documentation></annotation>           </enumeration>
      <enumeration value = "SZ"><annotation><documentation> country-name = "SWAZILAND"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "SE"><annotation><documentation> country-name = "SWEDEN"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "CH"><annotation><documentation> country-name = "SWITZERLAND"/ </documentation></annotation>               </enumeration>
      <enumeration value = "SY"><annotation><documentation> country-name = "SYRIANARABREPUBLIC"/ </documentation></annotation>              </enumeration>
      <enumeration value = "TW"><annotation><documentation> country-name = "TAIWAN,PROVINCEOFCHINA"/ </documentation></annotation>            </enumeration>
      <enumeration value = "TJ"><annotation><documentation> country-name = "TAJIKISTAN"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "TZ"><annotation><documentation> country-name = "TANZANIA,UNITEDREPUBLICOF"/ </documentation></annotation>         </enumeration>
      <enumeration value = "TH"><annotation><documentation> country-name = "THAILAND"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "TG"><annotation><documentation> country-name = "TOGO"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "TK"><annotation><documentation> country-name = "TOKELAU"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "TO"><annotation><documentation> country-name = "TONGA"/ </documentation></annotation>                   </enumeration>
      <enumeration value = "TT"><annotation><documentation> country-name = "TRINIDADANDTOBAGO"/ </documentation></annotation>             </enumeration>
      <enumeration value = "TN"><annotation><documentation> country-name = "TUNISIA"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "TR"><annotation><documentation> country-name = "TURKEY"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "TM"><annotation><documentation> country-name = "TURKMENISTAN"/ </documentation></annotation>                </enumeration>
      <enumeration value = "TC"><annotation><documentation> country-name = "TURKSANDCAICOSISLANDS"/ </documentation></annotation>           </enumeration>
      <enumeration value = "TV"><annotation><documentation> country-name = "TUVALU"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "UG"><annotation><documentation> country-name = "UGANDA"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "UA"><annotation><documentation> country-name = "UKRAINE"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "AE"><annotation><documentation> country-name = "UNITEDARABEMIRATES"/ </documentation></annotation>              </enumeration>
      <enumeration value = "GB"><annotation><documentation> country-name = "UNITEDKINGDOM"/ </documentation></annotation>               </enumeration>
      <enumeration value = "US"><annotation><documentation> country-name = "UNITEDSTATES"/ </documentation></annotation>                </enumeration>
      <enumeration value = "UM"><annotation><documentation> country-name = "UNITEDSTATESMINOROUTLYINGISLANDS"/ </documentation></annotation>      </enumeration>
      <enumeration value = "UY"><annotation><documentation> country-name = "URUGUAY"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "UZ"><annotation><documentation> country-name = "UZBEKISTAN"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "VU"><annotation><documentation> country-name = "VANUATU"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "VE"><annotation><documentation> country-name = "VENEZUELA"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "VN"><annotation><documentation> country-name = "VIETNAM"/ </documentation></annotation>                 </enumeration>
      <enumeration value = "VG"><annotation><documentation> country-name = "VIRGINISLANDS,BRITISH"/ </documentation></annotation>           </enumeration>
      <enumeration value = "VI"><annotation><documentation> country-name = "VIRGINISLANDS,U.S."/ </documentation></annotation>              </enumeration>
      <enumeration value = "WF"><annotation><documentation> country-name = "WALLISANDFUTUNA"/ </documentation></annotation>             </enumeration>
      <enumeration value = "EH"><annotation><documentation> country-name = "WESTERNSAHARA"/ </documentation></annotation>               </enumeration>
      <enumeration value = "YE"><annotation><documentation> country-name = "YEMEN"/ </documentation></annotation>                   </enumeration>
      <enumeration value = "YU"><annotation><documentation> country-name = "YUGOSLAVIA"/ </documentation></annotation>                  </enumeration>
      <enumeration value = "ZM"><annotation><documentation> country-name = "ZAMBIA"/ </documentation></annotation>                    </enumeration>
      <enumeration value = "ZW"><annotation><documentation> country-name = "ZIMBABWE"/ </documentation></annotation>                  </enumeration>
    </restriction>
  </simpleType>
  <simpleType name = "language_Type">
  <annotation><documentation> A string of characters conforming to the ISO 639 3-letter code. (Rather odd since all of them have 2 letters)</documentation></annotation>
    <restriction base = "NMTOKEN">
      <enumeration value = "none"/>
      <enumeration value = "inherit"/>
      <enumeration value = "AY"><annotation><documentation> language-name = "AYMARA" language-family = "AMERINDIAN"/ </documentation></annotation>        </enumeration>
      <enumeration value = "GN"><annotation><documentation> language-name = "GUARANI" language-family = "AMERINDIAN"/ </documentation></annotation>     </enumeration>
      <enumeration value = "QU"><annotation><documentation> language-name = "QUECHUA" language-family = "AMERINDIAN"/ </documentation></annotation>     </enumeration>
      <enumeration value = "DZ"><annotation><documentation> language-name = "BHUTANI" language-family = "ASIAN"/ </documentation></annotation>          </enumeration>
      <enumeration value = "MY"><annotation><documentation> language-name = "BURMESE" language-family = "ASIAN"/ </documentation></annotation>          </enumeration>
      <enumeration value = "KM"><annotation><documentation> language-name = "CAMBODIAN" language-family = "ASIAN"/ </documentation></annotation>        </enumeration>
      <enumeration value = "ZH"><annotation><documentation> language-name = "CHINESE" language-family = "ASIAN"/ </documentation></annotation>          </enumeration>
      <enumeration value = "JA"><annotation><documentation> language-name = "JAPANESE" language-family = "ASIAN"/ </documentation></annotation>       </enumeration>
      <enumeration value = "KO"><annotation><documentation> language-name = "KOREAN" language-family = "ASIAN"/ </documentation></annotation>         </enumeration>
      <enumeration value = "LO"><annotation><documentation> language-name = "LAOTHIAN" language-family = "ASIAN"/ </documentation></annotation>       </enumeration>
      <enumeration value = "TH"><annotation><documentation> language-name = "THAI" language-family = "ASIAN"/ </documentation></annotation>         </enumeration>
      <enumeration value = "BO"><annotation><documentation> language-name = "TIBETAN" language-family = "ASIAN"/ </documentation></annotation>          </enumeration>
      <enumeration value = "VI"><annotation><documentation> language-name = "VIETNAMESE" language-family = "ASIAN"/ </documentation></annotation>       </enumeration>
      <enumeration value = "LV"><annotation><documentation> language-name = "LATVIAN;LETTISH" language-family = "BALTIC"/ </documentation></annotation>   </enumeration>
      <enumeration value = "LT"><annotation><documentation> language-name = "LITHUANIAN" language-family = "BALTIC"/ </documentation></annotation>        </enumeration>
      <enumeration value = "EU"><annotation><documentation> language-name = "BASQUE" language-family = "BASQUE"/ </documentation></annotation>          </enumeration>
      <enumeration value = "BR"><annotation><documentation> language-name = "BRETON" language-family = "CELTIC"/ </documentation></annotation>          </enumeration>
      <enumeration value = "GA"><annotation><documentation> language-name = "IRISH" language-family = "CELTIC"/ </documentation></annotation>         </enumeration>
      <enumeration value = "GD"><annotation><documentation> language-name = "SCOTS-GAELIC" language-family = "CELTIC"/ </documentation></annotation>      </enumeration>
      <enumeration value = "CY"><annotation><documentation> language-name = "WELSH" language-family = "CELTIC"/ </documentation></annotation>         </enumeration>
      <enumeration value = "KN"><annotation><documentation> language-name = "KANNADA" language-family = "DRAVIDIAN"/ </documentation></annotation>        </enumeration>
      <enumeration value = "ML"><annotation><documentation> language-name = "MALAYALAM" language-family = "DRAVIDIAN"/ </documentation></annotation>      </enumeration>
      <enumeration value = "TA"><annotation><documentation> language-name = "TAMIL" language-family = "DRAVIDIAN"/ </documentation></annotation>        </enumeration>
      <enumeration value = "TE"><annotation><documentation> language-name = "TELUGU" language-family = "DRAVIDIAN"/ </documentation></annotation>       </enumeration>
      <enumeration value = "KL"><annotation><documentation> language-name = "GREENLANDIC" language-family = "ESKIMO"/ </documentation></annotation>     </enumeration>
      <enumeration value = "IK"><annotation><documentation> language-name = "INUPIAK" language-family = "ESKIMO"/ </documentation></annotation>       </enumeration>
      <enumeration value = "ET"><annotation><documentation> language-name = "ESTONIAN" language-family = "FINNO-UGRIC"/ </documentation></annotation>     </enumeration>
      <enumeration value = "FI"><annotation><documentation> language-name = "FINNISH" language-family = "FINNO-UGRIC"/ </documentation></annotation>      </enumeration>
      <enumeration value = "HU"><annotation><documentation> language-name = "HUNGARIAN" language-family = "FINNO-UGRIC"/ </documentation></annotation>      </enumeration>
      <enumeration value = "AF"><annotation><documentation> language-name = "AFRIKAANS" language-family = "GERMANIC"/ </documentation></annotation>     </enumeration>
      <enumeration value = "DA"><annotation><documentation> language-name = "DANISH" language-family = "GERMANIC"/ </documentation></annotation>        </enumeration>
      <enumeration value = "NL"><annotation><documentation> language-name = "DUTCH" language-family = "GERMANIC"/ </documentation></annotation>       </enumeration>
      <enumeration value = "EN"><annotation><documentation> language-name = "ENGLISH" language-family = "GERMANIC"/ </documentation></annotation>       </enumeration>
      <enumeration value = "FO"><annotation><documentation> language-name = "FAROESE" language-family = "GERMANIC"/ </documentation></annotation>       </enumeration>
      <enumeration value = "FY"><annotation><documentation> language-name = "FRISIAN" language-family = "GERMANIC"/ </documentation></annotation>       </enumeration>
      <enumeration value = "DE"><annotation><documentation> language-name = "GERMAN" language-family = "GERMANIC"/ </documentation></annotation>        </enumeration>
      <enumeration value = "IS"><annotation><documentation> language-name = "ICELANDIC" language-family = "GERMANIC"/ </documentation></annotation>     </enumeration>
      <enumeration value = "NO"><annotation><documentation> language-name = "NORWEGIAN" language-family = "GERMANIC"/ </documentation></annotation>     </enumeration>
      <enumeration value = "SV"><annotation><documentation> language-name = "SWEDISH" language-family = "GERMANIC"/ </documentation></annotation>       </enumeration>
      <enumeration value = "YI"><annotation><documentation> language-name = "YIDDISH" language-family = "GERMANIC"/ </documentation></annotation>       </enumeration>
      <enumeration value = "OM"><annotation><documentation> language-name = "AFAN-(OROMO)" language-family = "HAMITIC"/ </documentation></annotation>     </enumeration>
      <enumeration value = "AA"><annotation><documentation> language-name = "AFAR" language-family = "HAMITIC"/ </documentation></annotation>         </enumeration>
      <enumeration value = "SO"><annotation><documentation> language-name = "SOMALI" language-family = "HAMITIC"/ </documentation></annotation>       </enumeration>
      <enumeration value = "AB"><annotation><documentation> language-name = "ABKHAZIAN" language-family = "IBERO-CAUCASIAN"/ </documentation></annotation>    </enumeration>
      <enumeration value = "KA"><annotation><documentation> language-name = "GEORGIAN" language-family = "IBERO-CAUCASIAN"/ </documentation></annotation>   </enumeration>
      <enumeration value = "AS"><annotation><documentation> language-name = "ASSAMESE" language-family = "INDIAN"/ </documentation></annotation>        </enumeration>
      <enumeration value = "BN"><annotation><documentation> language-name = "BENGALI;BANGLA" language-family = "INDIAN"/ </documentation></annotation>      </enumeration>
      <enumeration value = "BH"><annotation><documentation> language-name = "BIHARI" language-family = "INDIAN"/ </documentation></annotation>          </enumeration>
      <enumeration value = "GU"><annotation><documentation> language-name = "GUJARATI" language-family = "INDIAN"/ </documentation></annotation>        </enumeration>
      <enumeration value = "HI"><annotation><documentation> language-name = "HINDI" language-family = "INDIAN"/ </documentation></annotation>         </enumeration>
      <enumeration value = "KS"><annotation><documentation> language-name = "KASHMIRI" language-family = "INDIAN"/ </documentation></annotation>        </enumeration>
      <enumeration value = "MR"><annotation><documentation> language-name = "MARATHI" language-family = "INDIAN"/ </documentation></annotation>       </enumeration>
      <enumeration value = "NE"><annotation><documentation> language-name = "NEPALI" language-family = "INDIAN"/ </documentation></annotation>          </enumeration>
      <enumeration value = "OR"><annotation><documentation> language-name = "ORIYA" language-family = "INDIAN"/ </documentation></annotation>         </enumeration>
      <enumeration value = "PA"><annotation><documentation> language-name = "PUNJABI" language-family = "INDIAN"/ </documentation></annotation>       </enumeration>
      <enumeration value = "SA"><annotation><documentation> language-name = "SANSKRIT" language-family = "INDIAN"/ </documentation></annotation>        </enumeration>
      <enumeration value = "SD"><annotation><documentation> language-name = "SINDHI" language-family = "INDIAN"/ </documentation></annotation>          </enumeration>
      <enumeration value = "SI"><annotation><documentation> language-name = "SINGHALESE" language-family = "INDIAN"/ </documentation></annotation>        </enumeration>
      <enumeration value = "UR"><annotation><documentation> language-name = "URDU" language-family = "INDIAN"/ </documentation></annotation>          </enumeration>
      <enumeration value = "SQ"><annotation><documentation> language-name = "ALBANIAN" language-family = "INDO-EUROPEAN(OTHER)"/ </documentation></annotation>  </enumeration>
      <enumeration value = "HY"><annotation><documentation> language-name = "ARMENIAN" language-family = "INDO-EUROPEAN(OTHER)"/ </documentation></annotation>  </enumeration>
      <enumeration value = "EO"><annotation><documentation> language-name = "ESPERANTO" language-family = "INTERNATIONAL-AUX."/ </documentation></annotation> </enumeration>
      <enumeration value = "IA"><annotation><documentation> language-name = "INTERLINGUA" language-family = "INTERNATIONAL-AUX."/ </documentation></annotation> </enumeration>
      <enumeration value = "IE"><annotation><documentation> language-name = "INTERLINGUE" language-family = "INTERNATIONAL-AUX."/ </documentation></annotation> </enumeration>
      <enumeration value = "VO"><annotation><documentation> language-name = "VOLAPUK" language-family = "INTERNATIONAL-AUX."/ </documentation></annotation> </enumeration>
      <enumeration value = "KU"><annotation><documentation> language-name = "KURDISH" language-family = "IRANIAN"/ </documentation></annotation>        </enumeration>
      <enumeration value = "PS"><annotation><documentation> language-name = "PASHTO;PUSHTO" language-family = "IRANIAN"/ </documentation></annotation>      </enumeration>
      <enumeration value = "FA"><annotation><documentation> language-name = "PERSIAN-(farsi)" language-family = "IRANIAN"/ </documentation></annotation>    </enumeration>
      <enumeration value = "TG"><annotation><documentation> language-name = "TAJIK" language-family = "IRANIAN"/ </documentation></annotation>          </enumeration>
      <enumeration value = "EL"><annotation><documentation> language-name = "GREEK" language-family = "LATIN/GREEK"/ </documentation></annotation>        </enumeration>
      <enumeration value = "LA"><annotation><documentation> language-name = "LATIN" language-family = "LATIN/GREEK"/ </documentation></annotation>        </enumeration>
      <enumeration value = "HA"><annotation><documentation> language-name = "HAUSA" language-family = "NEGRO-AFRICAN"/ </documentation></annotation>      </enumeration>
      <enumeration value = "RW"><annotation><documentation> language-name = "KINYARWANDA" language-family = "NEGRO-AFRICAN"/ </documentation></annotation>    </enumeration>
      <enumeration value = "RN"><annotation><documentation> language-name = "KURUNDI" language-family = "NEGRO-AFRICAN"/ </documentation></annotation>      </enumeration>
      <enumeration value = "LN"><annotation><documentation> language-name = "LINGALA" language-family = "NEGRO-AFRICAN"/ </documentation></annotation>      </enumeration>
      <enumeration value = "SG"><annotation><documentation> language-name = "SANGHO" language-family = "NEGRO-AFRICAN"/ </documentation></annotation>     </enumeration>
      <enumeration value = "ST"><annotation><documentation> language-name = "SESOTHO" language-family = "NEGRO-AFRICAN"/ </documentation></annotation>      </enumeration>
      <enumeration value = "TN"><annotation><documentation> language-name = "SETSWANA" language-family = "NEGRO-AFRICAN"/ </documentation></annotation>   </enumeration>
      <enumeration value = "SN"><annotation><documentation> language-name = "SHONA" language-family = "NEGRO-AFRICAN"/ </documentation></annotation>      </enumeration>
      <enumeration value = "SS"><annotation><documentation> language-name = "SISWATI" language-family = "NEGRO-AFRICAN"/ </documentation></annotation>      </enumeration>
      <enumeration value = "SW"><annotation><documentation> language-name = "SWAHILI" language-family = "NEGRO-AFRICAN"/ </documentation></annotation>      </enumeration>
      <enumeration value = "TS"><annotation><documentation> language-name = "TSONGA" language-family = "NEGRO-AFRICAN"/ </documentation></annotation>     </enumeration>
      <enumeration value = "TW"><annotation><documentation> language-name = "TWI" language-family = "NEGRO-AFRICAN"/ </documentation></annotation>        </enumeration>
      <enumeration value = "WO"><annotation><documentation> language-name = "WOLOF" language-family = "NEGRO-AFRICAN"/ </documentation></annotation>      </enumeration>
      <enumeration value = "XH"><annotation><documentation> language-name = "XHOSA" language-family = "NEGRO-AFRICAN"/ </documentation></annotation>      </enumeration>
      <enumeration value = "YO"><annotation><documentation> language-name = "YORUBA" language-family = "NEGRO-AFRICAN"/ </documentation></annotation>     </enumeration>
      <enumeration value = "ZU"><annotation><documentation> language-name = "ZULU" language-family = "NEGRO-AFRICAN"/ </documentation></annotation>     </enumeration>
      <enumeration value = "FJ"><annotation><documentation> language-name = "FIJI" language-family = "OCEANIC/INDONESIAN"/ </documentation></annotation>    </enumeration>
      <enumeration value = "ID"><annotation><documentation> language-name = "INDONESIAN" language-family = "OCEANIC/INDONESIAN"/ </documentation></annotation>  </enumeration>
      <enumeration value = "JV"><annotation><documentation> language-name = "JAVANESE" language-family = "OCEANIC/INDONESIAN"/ </documentation></annotation>  </enumeration>
      <enumeration value = "MG"><annotation><documentation> language-name = "MALAGASY" language-family = "OCEANIC/INDONESIAN"/ </documentation></annotation>  </enumeration>
      <enumeration value = "MS"><annotation><documentation> language-name = "MALAY" language-family = "OCEANIC/INDONESIAN"/ </documentation></annotation>   </enumeration>
      <enumeration value = "MI"><annotation><documentation> language-name = "MAORI" language-family = "OCEANIC/INDONESIAN"/ </documentation></annotation>   </enumeration>
      <enumeration value = "SM"><annotation><documentation> language-name = "SAMOAN" language-family = "OCEANIC/INDONESIAN"/ </documentation></annotation>    </enumeration>
      <enumeration value = "SU"><annotation><documentation> language-name = "SUNDANESE" language-family = "OCEANIC/INDONESIAN"/ </documentation></annotation> </enumeration>
      <enumeration value = "TL"><annotation><documentation> language-name = "TAGALOG" language-family = "OCEANIC/INDONESIAN"/ </documentation></annotation> </enumeration>
      <enumeration value = "TO"><annotation><documentation> language-name = "TONGA" language-family = "OCEANIC/INDONESIAN"/ </documentation></annotation>   </enumeration>
      <enumeration value = "CA"><annotation><documentation> language-name = "CATALAN" language-family = "ROMANCE"/ </documentation></annotation>        </enumeration>
      <enumeration value = "CO"><annotation><documentation> language-name = "CORSICAN" language-family = "ROMANCE"/ </documentation></annotation>       </enumeration>
      <enumeration value = "FR"><annotation><documentation> language-name = "FRENCH" language-family = "ROMANCE"/ </documentation></annotation>       </enumeration>
      <enumeration value = "GL"><annotation><documentation> language-name = "GALICIAN" language-family = "ROMANCE"/ </documentation></annotation>       </enumeration>
      <enumeration value = "IT"><annotation><documentation> language-name = "ITALIAN" language-family = "ROMANCE"/ </documentation></annotation>        </enumeration>
      <enumeration value = "MO"><annotation><documentation> language-name = "MOLDAVIAN" language-family = "ROMANCE"/ </documentation></annotation>        </enumeration>
      <enumeration value = "OC"><annotation><documentation> language-name = "OCCITAN" language-family = "ROMANCE"/ </documentation></annotation>        </enumeration>
      <enumeration value = "PT"><annotation><documentation> language-name = "PORTUGUESE" language-family = "ROMANCE"/ </documentation></annotation>     </enumeration>
      <enumeration value = "RM"><annotation><documentation> language-name = "RHAETO-ROMANCE" language-family = "ROMANCE"/ </documentation></annotation>   </enumeration>
      <enumeration value = "RO"><annotation><documentation> language-name = "ROMANIAN" language-family = "ROMANCE"/ </documentation></annotation>       </enumeration>
      <enumeration value = "ES"><annotation><documentation> language-name = "SPANISH" language-family = "ROMANCE"/ </documentation></annotation>        </enumeration>
      <enumeration value = "AM"><annotation><documentation> language-name = "AMHARIC" language-family = "SEMITIC"/ </documentation></annotation>        </enumeration>
      <enumeration value = "AR"><annotation><documentation> language-name = "ARABIC" language-family = "SEMITIC"/ </documentation></annotation>       </enumeration>
      <enumeration value = "HE"><annotation><documentation> language-name = "HEBREW" language-family = "SEMITIC"/ </documentation></annotation>       </enumeration>
      <enumeration value = "MT"><annotation><documentation> language-name = "MALTESE" language-family = "SEMITIC"/ </documentation></annotation>        </enumeration>
      <enumeration value = "TI"><annotation><documentation> language-name = "TIGRINYA" language-family = "SEMITIC"/ </documentation></annotation>       </enumeration>
      <enumeration value = "BG"><annotation><documentation> language-name = "BULGARIAN" language-family = "SLAVIC"/ </documentation></annotation>       </enumeration>
      <enumeration value = "BE"><annotation><documentation> language-name = "BYELORUSSIAN" language-family = "SLAVIC"/ </documentation></annotation>      </enumeration>
      <enumeration value = "HR"><annotation><documentation> language-name = "CROATIAN" language-family = "SLAVIC"/ </documentation></annotation>        </enumeration>
      <enumeration value = "CS"><annotation><documentation> language-name = "CZECH" language-family = "SLAVIC"/ </documentation></annotation>         </enumeration>
      <enumeration value = "MK"><annotation><documentation> language-name = "MACEDONIAN" language-family = "SLAVIC"/ </documentation></annotation>        </enumeration>
      <enumeration value = "PL"><annotation><documentation> language-name = "POLISH" language-family = "SLAVIC"/ </documentation></annotation>          </enumeration>
      <enumeration value = "RU"><annotation><documentation> language-name = "RUSSIAN" language-family = "SLAVIC"/ </documentation></annotation>       </enumeration>
      <enumeration value = "SR"><annotation><documentation> language-name = "SERBIAN" language-family = "SLAVIC"/ </documentation></annotation>       </enumeration>
      <enumeration value = "SH"><annotation><documentation> language-name = "SERBO-CROATIAN" language-family = "SLAVIC"/ </documentation></annotation>      </enumeration>
      <enumeration value = "SK"><annotation><documentation> language-name = "SLOVAK" language-family = "SLAVIC"/ </documentation></annotation>          </enumeration>
      <enumeration value = "SL"><annotation><documentation> language-name = "SLOVENIAN" language-family = "SLAVIC"/ </documentation></annotation>       </enumeration>
      <enumeration value = "UK"><annotation><documentation> language-name = "UKRAINIAN" language-family = "SLAVIC"/ </documentation></annotation>       </enumeration>
      <enumeration value = "AZ"><annotation><documentation> language-name = "AZERBAIJANI" language-family = "TURKIC/ALTAIC"/ </documentation></annotation>    </enumeration>
      <enumeration value = "BA"><annotation><documentation> language-name = "BASHKIR" language-family = "TURKIC/ALTAIC"/ </documentation></annotation>      </enumeration>
      <enumeration value = "KK"><annotation><documentation> language-name = "KAZAKH" language-family = "TURKIC/ALTAIC"/ </documentation></annotation>     </enumeration>
      <enumeration value = "KY"><annotation><documentation> language-name = "KIRGHIZ" language-family = "TURKIC/ALTAIC"/ </documentation></annotation>      </enumeration>
      <enumeration value = "TT"><annotation><documentation> language-name = "TATAR" language-family = "TURKIC/ALTAIC"/ </documentation></annotation>      </enumeration>
      <enumeration value = "TR"><annotation><documentation> language-name = "TURKISH" language-family = "TURKIC/ALTAIC"/ </documentation></annotation>      </enumeration>
      <enumeration value = "TK"><annotation><documentation> language-name = "TURKMEN" language-family = "TURKIC/ALTAIC"/ </documentation></annotation>      </enumeration>
      <enumeration value = "UZ"><annotation><documentation> language-name = "UZBEK" language-family = "TURKIC/ALTAIC"/ </documentation></annotation>      </enumeration>
      <enumeration value = "BI"><annotation><documentation> language-name = "BISLAMA" language-family = "[not-given]"/ </documentation></annotation>      </enumeration>
      <enumeration value = "MN"><annotation><documentation> language-name = "MONGOLIAN" language-family = "[not-given]"/ </documentation></annotation>      </enumeration>
      <enumeration value = "NA"><annotation><documentation> language-name = "NAURU" language-family = "[not-given]"/ </documentation></annotation>        </enumeration>
    </restriction>
  </simpleType>
  <simpleType name = "always_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "always"/>
    </restriction>
  </simpleType>
  <simpleType name = "auto_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "auto"/>
    </restriction>
  </simpleType>
  <simpleType name = "baseline_base_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "baseline"/>
      <enumeration value = "sub"/>
      <enumeration value = "super"/>
    </restriction>
  </simpleType>
  <simpleType name = "caption_side_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "before"/>
      <enumeration value = "after"/>
      <enumeration value = "start"/>
      <enumeration value = "end"/>
      <enumeration value = "top"/>
      <enumeration value = "bottom"/>
      <enumeration value = "left"/>
      <enumeration value = "right"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "display_align_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "auto"/>
      <enumeration value = "before"/>
      <enumeration value = "center"/>
      <enumeration value = "after"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "float_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "inherit"/>
      <enumeration value = "before"/>
      <enumeration value = "start"/>
      <enumeration value = "end"/>
      <enumeration value = "left"/>
      <enumeration value = "right"/>
      <enumeration value = "none"/>
    </restriction>
  </simpleType>
  <simpleType name = "blank_or_not_blank_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "blank"/>
      <enumeration value = "not-blank"/>
      <enumeration value = "any"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "text_transform_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "capitalize"/>
      <enumeration value = "uppercase"/>
      <enumeration value = "lowercase"/>
      <enumeration value = "none"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "border_collapse_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "collapse"/>
      <enumeration value = "separate"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "line_height_shift_adjustment_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "consider-shifts"/>
      <enumeration value = "disregard-shifts"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "white_space_collapse_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "false"/>
      <enumeration value = "true"/>
      <enumeration value = "inherit">
        <annotation>
          <documentation>
            <fop_fail>Unknown enumerated value</fop_fail>
          </documentation>
        </annotation>
      </enumeration>
    </restriction>
  </simpleType>
  <simpleType name = "retrieve_position_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "first-starting-within-page"/>
      <enumeration value = "first-including-carryover"/>
      <enumeration value = "last-starting-within-page"/>
      <enumeration value = "last-ending-within-page"/>
    </restriction>
  </simpleType>
  <simpleType name = "page_position_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "first"/>
      <enumeration value = "last"/>
      <enumeration value = "rest"/>
      <enumeration value = "any"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "force_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "force"/>
    </restriction>
  </simpleType>
  <simpleType name = "white_space_treatment_Type">
    <annotation>
      <documentation>
        <fop_fail>property ignored</fop_fail>
      </documentation>
    </annotation>
    <restriction base = "NMTOKEN">
      <enumeration value = "ignore"/>
      <enumeration value = "preserve"/>
      <enumeration value = "ignore-if-before-linefeed"/>
      <enumeration value = "ignore-if-after-linefeed"/>
      <enumeration value = "ignore-if-surrounding-linefeed"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "linefeed_treatment_Type">
    <annotation>
      <documentation>
        <fop_fail>property is not implemented yet</fop_fail>
      </documentation>
    </annotation>
    <restriction base = "NMTOKEN">
      <enumeration value = "ignore"/>
      <enumeration value = "preserve"/>
      <enumeration value = "treat-as-space"/>
      <enumeration value = "treat-as-zero-width-space"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "inherit_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "horizontal_position_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "left"/>
      <enumeration value = "center"/>
      <enumeration value = "right"/>
    </restriction>
  </simpleType>
  <simpleType name = "line_stacking_strategy_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "line-height"/>
      <enumeration value = "font-height"/>
      <enumeration value = "max-height"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "active_state_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "link"/>
      <enumeration value = "visited"/>
      <enumeration value = "active"/>
      <enumeration value = "hover"/>
      <enumeration value = "focus"/>
    </restriction>
  </simpleType>
  <simpleType name = "writing_mode_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "lr-tb"/>
      <enumeration value = "rl-tb"/>
      <enumeration value = "tb-rl"/>
      <enumeration value = "lr"/>
      <enumeration value = "rl"/>
      <enumeration value = "tb"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "direction_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "ltr"/>
      <enumeration value = "rtl"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "wrap_option_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "no-wrap"/>
      <enumeration value = "wrap"/>
      <enumeration value = "inherit">
        <annotation>
          <documentation>
            <fop_fail>Unknown enumerated value</fop_fail>
          </documentation>
        </annotation>
      </enumeration>
    </restriction>
  </simpleType>
  <simpleType name = "span_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "none"/>
      <enumeration value = "all"/>
      <enumeration value = "inherit">
        <annotation>
          <documentation>
            <fop_fail>Unknown enumerated value</fop_fail>
          </documentation>
        </annotation>
      </enumeration>
    </restriction>
  </simpleType>
  <simpleType name = "rule_style_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "none"/>
      <enumeration value = "dotted"/>
      <enumeration value = "dashed"/>
      <enumeration value = "solid"/>
      <enumeration value = "double"/>
      <enumeration value = "groove"/>
      <enumeration value = "ridge"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "border_style_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "inherit">
        <annotation>
          <documentation>
            <fop_fail>Unknown enumerated value</fop_fail>
          </documentation>
        </annotation>
      </enumeration>
      <enumeration value = "none"/>
      <enumeration value = "hidden"/>
      <enumeration value = "dotted"/>
      <enumeration value = "dashed"/>
      <enumeration value = "solid"/>
      <enumeration value = "double"/>
      <enumeration value = "groove"/>
      <enumeration value = "ridge"/>
      <enumeration value = "inset"/>
      <enumeration value = "outset"/>
    </restriction>
  </simpleType>
  <simpleType name = "leader_alignment_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "none"/>
      <enumeration value = "reference-area"/>
      <enumeration value = "page"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "text_decoration_Type">
    <annotation>
      <documentation>
        <fop_result>not implemented</fop_result>
      </documentation>
    </annotation>
    <restriction base = "NMTOKEN">
      <enumeration value = "none"/>
      <enumeration value = "underline"/>
      <enumeration value = "no-underline"/>
      <enumeration value = "overline"/>
      <enumeration value = "no-overline"/>
      <enumeration value = "line-through"/>
      <enumeration value = "no-line-through"/>
      <enumeration value = "blink"/>
      <enumeration value = "no-blink"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "unicode_bidi_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "normal"/>
      <enumeration value = "embed"/>
      <enumeration value = "bidi-override"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "odd_or_even_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "odd"/>
      <enumeration value = "even"/>
      <enumeration value = "any"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "once_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "once"/>
    </restriction>
  </simpleType>
  <simpleType name = "retrieve_boundary_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "page"/>
      <enumeration value = "page-sequence"/>
      <enumeration value = "document"/>
    </restriction>
  </simpleType>
  <simpleType name = "starting_state_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "show"/>
      <enumeration value = "hide"/>
    </restriction>
  </simpleType>
  <simpleType name = "leader_pattern_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "space"/>
      <enumeration value = "rule"/>
      <enumeration value = "dots"/>
      <enumeration value = "use-content"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "text_align_last_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "start"/>
      <enumeration value = "center"/>
      <enumeration value = "end"/>
      <enumeration value = "justify"/>
      <enumeration value = "inside">
        <annotation>
          <documentation>
            <fop_fail>Unknown enumerated value</fop_fail>
          </documentation>
        </annotation>
      </enumeration>
      <enumeration value = "outside">
        <annotation>
          <documentation>
            <fop_fail>Unknown enumerated value</fop_fail>
          </documentation>
        </annotation>
      </enumeration>
      <enumeration value = "left">
        <annotation>
          <documentation>
            <fop_fail>Unknown enumerated value</fop_fail>
          </documentation>
        </annotation>
      </enumeration>
      <enumeration value = "right">
        <annotation>
          <documentation>
            <fop_fail>Unknown enumerated value</fop_fail>
          </documentation>
        </annotation>
      </enumeration>
      <enumeration value = "inherit">
        <annotation>
          <documentation>
            <fop_fail>Unknown enumerated value</fop_fail>
          </documentation>
        </annotation>
      </enumeration>
    </restriction>
  </simpleType>
  <simpleType name = "text_align_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "start"/>
      <enumeration value = "center"/>
      <enumeration value = "end"/>
      <enumeration value = "justify"/>
      <enumeration value = "inside">
        <annotation>
          <documentation>
            <fop_fail>Unknown enumerated value</fop_fail>
          </documentation>
        </annotation>
      </enumeration>
      <enumeration value = "outside">
        <annotation>
          <documentation>
            <fop_fail>Unknown enumerated value</fop_fail>
          </documentation>
        </annotation>
      </enumeration>
      <enumeration value = "left"/>
      <enumeration value = "right"/>
      <enumeration value = "inherit">
        <annotation>
          <documentation>
            <fop_fail>Unknown enumerated value</fop_fail>
          </documentation>
        </annotation>
      </enumeration>
    </restriction>
  </simpleType>
  <simpleType name = "width_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "thin"/>
      <enumeration value = "medium"/>
      <enumeration value = "thick"/>
    </restriction>
  </simpleType>
  <simpleType name = "vertical_position_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "top"/>
      <enumeration value = "center"/>
      <enumeration value = "bottom"/>
    </restriction>
  </simpleType>
  <simpleType name = "auto_restore_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "true"/>
      <enumeration value = "false"/>
    </restriction>
  </simpleType>
  <simpleType name = "table_omit_footer_at_break_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "true"/>
      <enumeration value = "false"/>
    </restriction>
  </simpleType>
  <simpleType name = "table_omit_header_at_break_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "true"/>
      <enumeration value = "false"/>
    </restriction>
  </simpleType>
  <simpleType name = "precedence_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "true"/>
      <enumeration value = "false"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "score_spaces_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "true"/>
      <enumeration value = "false"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "scaling_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "uniform"/>
      <enumeration value = "non-uniform"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "use_font_metrics_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "use-font-metrics"/>
    </restriction>
  </simpleType>
  <simpleType name = "background_image_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "uri-specification"/>
      <enumeration value = "none"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "hyphenation_keep_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "auto"/>
      <enumeration value = "column"/>
      <enumeration value = "page"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "no_limit_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "no-limit"/>
    </restriction>
  </simpleType>
  <simpleType name = "visibility_Type">
    <annotation>
      <documentation>
        <fop_fail>property is not implemented yet</fop_fail>
      </documentation>
    </annotation>
    <restriction base = "NMTOKEN">
      <enumeration value = "visible"/>
      <enumeration value = "hidden"/>
      <enumeration value = "collapse"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "overflow_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "auto"/>
      <enumeration value = "visible"/>
      <enumeration value = "hidden"/>
      <enumeration value = "scroll"/>
      <enumeration value = "error-if-overflow"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "transparent_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "transparent"/>
    </restriction>
  </simpleType>
  <simpleType name = "none_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "none"/>
    </restriction>
  </simpleType>
  <simpleType name = "absolute_position_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "auto"/>
      <enumeration value = "absolute"/>
      <enumeration value = "fixed"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "azimuth_base_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "left-side"/>
      <enumeration value = "far-left"/>
      <enumeration value = "left"/>
      <enumeration value = "center-left"/>
      <enumeration value = "center"/>
      <enumeration value = "center-right"/>
      <enumeration value = "right"/>
      <enumeration value = "far-right"/>
      <enumeration value = "right-side"/>
      <enumeration value = "behind"/>
      <enumeration value = "leftwards"/>
      <enumeration value = "rightwards"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "azimuth_Type">
    <union memberTypes = "fo:angle_Type fo:azimuth_base_Type"/>
  </simpleType>
  <simpleType name = "elevation_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "below"/>
      <enumeration value = "level"/>
      <enumeration value = "above"/>
      <enumeration value = "higher"/>
      <enumeration value = "lower"/>
      <enumeration value = "0"/>
      <enumeration value = "90"/>
      <enumeration value = "180"/>
      <enumeration value = "270"/>
      <enumeration value = "0deg"/>
      <enumeration value = "90deg"/>
      <enumeration value = "180deg"/>
      <enumeration value = "270deg"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "font_selection_strategy_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "auto"/>
      <enumeration value = "character-by-character"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "font_stretch_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "normal"/>
      <enumeration value = "wider"/>
      <enumeration value = "narrower"/>
      <enumeration value = "ultra-condensed"/>
      <enumeration value = "extra-condensed"/>
      <enumeration value = "condensed"/>
      <enumeration value = "semi-condensed"/>
      <enumeration value = "semi-expanded"/>
      <enumeration value = "expanded"/>
      <enumeration value = "extra-expanded"/>
      <enumeration value = "ultra-expanded"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "clear_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "start"/>
      <enumeration value = "end"/>
      <enumeration value = "left"/>
      <enumeration value = "right"/>
      <enumeration value = "both"/>
      <enumeration value = "none"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "color_Name_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "aliceblue"/>
      <enumeration value = "antiquewhite"/>
      <enumeration value = "aqua"/>
      <enumeration value = "aquamarine"/>
      <enumeration value = "azure"/>
      <enumeration value = "beige"/>
      <enumeration value = "bisque"/>
      <enumeration value = "black"/>
      <enumeration value = "blanchedalmond"/>
      <enumeration value = "blue"/>
      <enumeration value = "blueviolet"/>
      <enumeration value = "brown"/>
      <enumeration value = "burlywood"/>
      <enumeration value = "cadetblue"/>
      <enumeration value = "chartreuse"/>
      <enumeration value = "chocolate"/>
      <enumeration value = "coral"/>
      <enumeration value = "cornflowerblue"/>
      <enumeration value = "cornsilk"/>
      <enumeration value = "crimson"/>
      <enumeration value = "cyan"/>
      <enumeration value = "darkblue"/>
      <enumeration value = "darkcyan"/>
      <enumeration value = "darkgoldenrod"/>
      <enumeration value = "darkgray"/>
      <enumeration value = "darkgreen"/>
      <enumeration value = "darkgrey"/>
      <enumeration value = "darkkhaki"/>
      <enumeration value = "darkmagenta"/>
      <enumeration value = "darkolivegreen"/>
      <enumeration value = "darkorange"/>
      <enumeration value = "darkorchid"/>
      <enumeration value = "darkred"/>
      <enumeration value = "darksalmon"/>
      <enumeration value = "darkseagreen"/>
      <enumeration value = "darkslateblue"/>
      <enumeration value = "darkslategray"/>
      <enumeration value = "darkslategrey"/>
      <enumeration value = "darkturquoise"/>
      <enumeration value = "darkviolet"/>
      <enumeration value = "deeppink"/>
      <enumeration value = "deepskyblue"/>
      <enumeration value = "dimgray"/>
      <enumeration value = "dimgrey"/>
      <enumeration value = "dodgerblue"/>
      <enumeration value = "firebrick"/>
      <enumeration value = "floralwhite"/>
      <enumeration value = "forestgreen"/>
      <enumeration value = "fuchsia"/>
      <enumeration value = "gainsboro"/>
      <enumeration value = "lightpink"/>
      <enumeration value = "lightsalmon"/>
      <enumeration value = "lightseagreen"/>
      <enumeration value = "lightskyblue"/>
      <enumeration value = "lightslategray"/>
      <enumeration value = "lightslategrey"/>
      <enumeration value = "lightsteelblue"/>
      <enumeration value = "lightyellow"/>
      <enumeration value = "lime"/>
      <enumeration value = "limegreen"/>
      <enumeration value = "linen"/>
      <enumeration value = "magenta"/>
      <enumeration value = "maroon"/>
      <enumeration value = "mediumaquamarine"/>
      <enumeration value = "mediumblue"/>
      <enumeration value = "mediumorchid"/>
      <enumeration value = "mediumpurple"/>
      <enumeration value = "mediumseagreen"/>
      <enumeration value = "mediumslateblue"/>
      <enumeration value = "mediumspringgreen"/>
      <enumeration value = "mediumturquoise"/>
      <enumeration value = "mediumvioletred"/>
      <enumeration value = "midnightblue"/>
      <enumeration value = "mintcream"/>
      <enumeration value = "mistyrose"/>
      <enumeration value = "moccasin"/>
      <enumeration value = "navajowhite"/>
      <enumeration value = "navy"/>
      <enumeration value = "oldlace"/>
      <enumeration value = "olive"/>
      <enumeration value = "olivedrab"/>
      <enumeration value = "orange"/>
      <enumeration value = "orangered"/>
      <enumeration value = "orchid"/>
      <enumeration value = "palegoldenrod"/>
      <enumeration value = "palegreen"/>
      <enumeration value = "paleturquoise"/>
      <enumeration value = "palevioletred"/>
      <enumeration value = "papayawhip"/>
      <enumeration value = "peachpuff"/>
      <enumeration value = "peru"/>
      <enumeration value = "pink"/>
      <enumeration value = "plum"/>
      <enumeration value = "powderblue"/>
      <enumeration value = "purple"/>
      <enumeration value = "red"/>
      <enumeration value = "rosybrown"/>
      <enumeration value = "royalblue"/>
      <enumeration value = "saddlebrown"/>
      <enumeration value = "salmon"/>
      <enumeration value = "ghostwhite"/>
      <enumeration value = "gold"/>
      <enumeration value = "goldenrod"/>
      <enumeration value = "gray"/>
      <enumeration value = "grey"/>
      <enumeration value = "green"/>
      <enumeration value = "greenyellow"/>
      <enumeration value = "honeydew"/>
      <enumeration value = "hotpink"/>
      <enumeration value = "indianred"/>
      <enumeration value = "indigo"/>
      <enumeration value = "ivory"/>
      <enumeration value = "khaki"/>
      <enumeration value = "lavender"/>
      <enumeration value = "lavenderblush"/>
      <enumeration value = "lawngreen"/>
      <enumeration value = "lemonchiffon"/>
      <enumeration value = "lightblue"/>
      <enumeration value = "lightcoral"/>
      <enumeration value = "lightcyan"/>
      <enumeration value = "lightgoldenrodyellow"/>
      <enumeration value = "lightgray"/>
      <enumeration value = "lightgreen"/>
      <enumeration value = "lightgrey"/>
      <enumeration value = "sandybrown"/>
      <enumeration value = "seagreen"/>
      <enumeration value = "seashell"/>
      <enumeration value = "sienna"/>
      <enumeration value = "silver"/>
      <enumeration value = "skyblue"/>
      <enumeration value = "slateblue"/>
      <enumeration value = "slategray"/>
      <enumeration value = "slategrey"/>
      <enumeration value = "snow"/>
      <enumeration value = "springgreen"/>
      <enumeration value = "steelblue"/>
      <enumeration value = "tan"/>
      <enumeration value = "teal"/>
      <enumeration value = "thistle"/>
      <enumeration value = "tomato"/>
      <enumeration value = "turquoise"/>
      <enumeration value = "violet"/>
      <enumeration value = "wheat"/>
      <enumeration value = "white"/>
      <enumeration value = "whitesmoke"/>
      <enumeration value = "yellow"/>
      <enumeration value = "yellowgreen"/>
    </restriction>
  </simpleType>
  <simpleType name = "color_Hex_Type">
    <restriction base = "string">
      <pattern value = "#[0-9A-F]{6}"/>
    </restriction>
  </simpleType>
  <simpleType name = "background_color_Type">
    <union memberTypes = "fo:color_Hex_Type fo:color_Name_Type fo:inherit_Type fo:transparent_Type"/>
  </simpleType>
  <simpleType name = "relative_position_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "static"/>
      <enumeration value = "relative"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "speak_header_Type">
    <restriction base = "NMTOKEN">
      <enumeration value = "once"/>
      <enumeration value = "always"/>
      <enumeration value = "inherit"/>
    </restriction>
  </simpleType>
  <simpleType name = "border_Type">
    <annotation>
      <documentation>
        "fo:width_Type fo:border_style_Type fo:color_Type"
        <enumeration value="solid 4px yellow"/>
        <enumeration value="solid 2px green"/>
        <enumeration value="solid 1px blue"/>
        <enumeration value="solid 1px silver"/>
      </documentation>
    </annotation>
    <restriction base = "string">
      <pattern value = "(none|hidden|dotted|dashed|solid|double|groove|ridge|inset|outset) \+?\d+\.?\d*(px|pt|mm|cm|in|em|%) (#[0-9A-F]{6}|[a-z]{3,16})"/>
    </restriction>
  </simpleType>
  <simpleType name = "font_size_Type">
    <union memberTypes = "fo:absolute_size_Type fo:relative_size_Type fo:length_Type fo:percentage_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "alignment_baseline_Type">
    <union memberTypes = "fo:auto_Type fo:alignment_Type fo:box_alignment_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "alignment_adjust_Type">
    <union memberTypes = "fo:auto_Type fo:alignment_Type fo:percentage_Type fo:length_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "z_index_Type">
    <union memberTypes = "fo:auto_Type fo:integer_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "margin_width_Type">
    <union memberTypes = "fo:auto_Type fo:length_Type fo:percentage_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "block_progression_dimension_optimum_Type">
    <union memberTypes = "fo:auto_Type fo:length_Type"/>
  </simpleType>
  <simpleType name = "block_progression_dimension_maximum_Type">
    <union memberTypes = "fo:auto_Type fo:length_Type"/>
  </simpleType>
  <simpleType name = "inline_progression_dimension_maximum_Type">
    <union memberTypes = "fo:auto_Type fo:length_Type"/>
  </simpleType>
  <simpleType name = "inline_progression_dimension_optimum_Type">
    <union memberTypes = "fo:auto_Type fo:length_Type"/>
  </simpleType>
  <simpleType name = "baseline_shift_Type">
    <union memberTypes = "fo:baseline_base_Type fo:percentage_Type fo:length_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "space_start_precedence_Type">
    <union memberTypes = "fo:integer_Type fo:force_Type"/>
  </simpleType>
  <simpleType name = "space_end_precedence_Type">
    <union memberTypes = "fo:integer_Type fo:force_Type"/>
  </simpleType>
  <simpleType name = "space_after_precedence_Type">
    <union memberTypes = "fo:integer_Type fo:force_Type"/>
  </simpleType>
  <simpleType name = "space_before_precedence_Type">
    <union memberTypes = "fo:integer_Type fo:force_Type"/>
  </simpleType>
  <simpleType name = "widows_Type">
    <union memberTypes = "fo:integer_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "orphans_Type">
    <union memberTypes = "fo:integer_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "space_start_maximum_Type">
    <union memberTypes = "fo:length_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "space_start_minimum_Type">
    <union memberTypes = "fo:length_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "space_end_minimum_Type">
    <union memberTypes = "fo:length_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "space_end_optimum_Type">
    <union memberTypes = "fo:length_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "space_before_minimum_Type">
    <union memberTypes = "fo:length_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "space_after_maximum_Type">
    <union memberTypes = "fo:length_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "space_start_optimum_Type">
    <union memberTypes = "fo:length_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "space_end_maximum_Type">
    <union memberTypes = "fo:length_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "space_before_optimum_Type">
    <union memberTypes = "fo:length_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "space_before_maximum_Type">
    <union memberTypes = "fo:length_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "space_after_optimum_Type">
    <union memberTypes = "fo:length_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "space_after_minimum_Type">
    <union memberTypes = "fo:length_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "left_Type">
    <union memberTypes = "fo:length_Type fo:percentage_Type fo:auto_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "bottom_Type">
    <union memberTypes = "fo:length_Type fo:percentage_Type fo:auto_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "right_Type">
    <union memberTypes = "fo:length_Type fo:percentage_Type fo:auto_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "top_Type">
    <union memberTypes = "fo:length_Type fo:percentage_Type fo:auto_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "start_indent_Type">
    <union memberTypes = "fo:length_Type fo:percentage_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "last_line_end_indent_Type">
    <union memberTypes = "fo:length_Type fo:percentage_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "provisional_distance_between_starts_Type">
    <union memberTypes = "fo:length_Type fo:percentage_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "end_indent_Type">
    <union memberTypes = "fo:length_Type fo:percentage_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "extent_Type">
    <union memberTypes = "fo:length_Type fo:percentage_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "provisional_label_separation_Type">
    <union memberTypes = "fo:length_Type fo:percentage_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "length_percentage_Type">
    <union memberTypes = "fo:length_Type fo:percentage_Type"/>
  </simpleType>
  <simpleType name = "hyphenation_ladder_count_Type">
    <union memberTypes = "fo:no_limit_Type fo:integer_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "background_position_horizontal_Type">
    <union memberTypes = "fo:percentage_Type fo:length_Type fo:horizontal_position_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "background_position_vertical_Type">
    <union memberTypes = "fo:percentage_Type fo:length_Type fo:vertical_position_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "leader_pattern_width_Type">
    <union memberTypes = "fo:use_font_metrics_Type fo:length_Type fo:percentage_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "text_altitude_Type">
    <union memberTypes = "fo:use_font_metrics_Type fo:length_Type fo:percentage_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "text_depth_Type">
    <union memberTypes = "fo:use_font_metrics_Type fo:length_Type fo:percentage_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "border_before_width_Type">
    <union memberTypes = "fo:width_Type fo:length_Type fo:length_conditional_Type fo:inherit_Type"/>
  </simpleType>
  <simpleType name = "border_top_width_Type">
    <union memberTypes = "fo:width_Type fo:length_Type fo:inherit_Type"/>
  </simpleType>
  
  <annotation>
    <documentation>
      Element definitions 
    </documentation>
  </annotation>
  
  <element name = "root">
    <complexType>
      <sequence>
        <element ref = "fo:layout-master-set"/>
        <element ref = "fo:declarations" minOccurs = "0"/>
        <element ref = "fo:page-sequence" maxOccurs = "unbounded"/>
      </sequence>
      <attribute name = "media-usage" type = "fo:media_usage_Type"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "declarations">
    <complexType>
      <sequence>
        <element ref = "fo:color-profile" maxOccurs = "unbounded"/>
        <any minOccurs = "0" maxOccurs = "unbounded" namespace = "##other" processContents = "skip"/>
      </sequence>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "color-profile">
    <complexType>
      <attribute name = "src" type = "xs:string" use = "required"/>
      <attribute name = "color-profile-name" type = "xs:string" use = "required"/>
      <attribute name = "rendering-intent" type = "fo:rendering_intent_Type"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "layout-master-set">
    <complexType>
      <choice maxOccurs = "unbounded">
        <element ref = "fo:simple-page-master"/>
        <element ref = "fo:page-sequence-master"/>
      </choice>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "page-sequence-master">
    <complexType>
      <choice maxOccurs = "unbounded">
        <element ref = "fo:single-page-master-reference"/>
        <element ref = "fo:repeatable-page-master-reference"/>
        <element ref = "fo:repeatable-page-master-alternatives"/>
      </choice>
      <attribute name = "master-name" type = "xs:string" use = "required"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "single-page-master-reference">
    <complexType>
      <attribute name = "master-reference" type = "xs:string" use = "required"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "repeatable-page-master-reference">
    <complexType>
      <attribute name = "master-reference" type = "xs:string" use = "required"/>
      <attribute name = "maximum-repeats" type = "fo:integer_Type"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "repeatable-page-master-alternatives">
    <complexType>
      <sequence>
        <element ref = "fo:conditional-page-master-reference" maxOccurs = "unbounded"/>
      </sequence>
      <attribute name = "maximum-repeats" type = "fo:integer_Type"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "conditional-page-master-reference">
    <complexType>
      <attribute name = "master-reference" type = "xs:string" use = "required"/>
      <attribute name = "page-position" type = "fo:page_position_Type"/>
      <attribute name = "odd-or-even" type = "fo:odd_or_even_Type"/>
      <attribute name = "blank-or-not-blank" type = "fo:blank_or_not_blank_Type"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "simple-page-master">
    <complexType>
      <sequence>
        <element ref = "fo:region-body"/>
        <element ref = "fo:region-before" minOccurs = "0"/>
        <element ref = "fo:region-after" minOccurs = "0"/>
        <element ref = "fo:region-start" minOccurs = "0"/>
        <element ref = "fo:region-end" minOccurs = "0"/>
      </sequence>
      <attributeGroup ref = "fo:Margin_Properties_Block_List"/>
      <attribute name = "master-name" type = "xs:string" use = "required"/>
      <attribute name = "page-height" type = "fo:length_Type"/>
      <attribute name = "page-width" type = "fo:length_Type"/>
      <attributeGroup ref = "fo:reference_Properties_List"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "region-body">
    <complexType>
      <annotation>
        <documentation>
          Inheritable <attribute name = "display-align" type = "fo:display_align_Type"/> 
        </documentation>
      </annotation>
      <attributeGroup ref = "fo:Margin_Properties_Block_List"/>
      <attributeGroup ref = "fo:Border_Padding_and_Background_Properties_List"/>
      <attributeGroup ref = "fo:clip_Properties_List"/>
      <attribute name = "column-count" type = "fo:integer_Type"/>
      <attribute name = "column-gap" type = "fo:length_Type"/>
      <attribute name = "overflow" type = "fo:overflow_Type"/>
      <attribute name = "region-name" type = "xs:string"/>
      <attributeGroup ref = "fo:reference_Properties_List"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "region-before">
    <complexType>
      <annotation>
        <documentation>
          Inheritable <attribute name = "display-align" type = "fo:display_align_Type"/> 
        </documentation>
      </annotation>
      <attributeGroup ref = "fo:Border_Padding_and_Background_Properties_List"/>
      <attributeGroup ref = "fo:clip_Properties_List"/>
      <attribute name = "extent" type = "fo:extent_Type"/>
      <attribute name = "overflow" type = "fo:overflow_Type"/>
      <attribute name = "precedence" type = "fo:precedence_Type"/>
      <attribute name = "region-name" type = "xs:string"/>
      <attributeGroup ref = "fo:reference_Properties_List"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "region-after">
    <complexType>
      <annotation>
        <documentation>
          Inheritable <attribute name = "display-align" type = "fo:display_align_Type"/> 
        </documentation>
      </annotation>
      <attributeGroup ref = "fo:Border_Padding_and_Background_Properties_List"/>
      <attributeGroup ref = "fo:clip_Properties_List"/>
      <attribute name = "extent" type = "fo:extent_Type"/>
      <attribute name = "overflow" type = "fo:overflow_Type"/>
      <attribute name = "precedence" type = "fo:precedence_Type"/>
      <attribute name = "region-name" type = "xs:string"/>
      <attributeGroup ref = "fo:reference_Properties_List"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "region-start">
    <complexType>
      <annotation>
        <documentation>
          Inheritable <attribute name = "display-align" type = "fo:display_align_Type"/> 
        </documentation>
      </annotation>
      <attributeGroup ref = "fo:Border_Padding_and_Background_Properties_List"/>
      <attributeGroup ref = "fo:clip_Properties_List"/>
      <attribute name = "extent" type = "fo:extent_Type"/>
      <attribute name = "overflow" type = "fo:overflow_Type"/>
      <attribute name = "precedence" type = "fo:precedence_Type"/>
      <attribute name = "region-name" type = "xs:string"/>
      <attributeGroup ref = "fo:reference_Properties_List"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "region-end">
    <complexType>
      <annotation>
        <documentation>
          Inheritable <attribute name = "display-align" type = "fo:display_align_Type"/> 
        </documentation>
      </annotation>
      <attributeGroup ref = "fo:Border_Padding_and_Background_Properties_List"/>
      <attributeGroup ref = "fo:clip_Properties_List"/>
      <attribute name = "extent" type = "fo:extent_Type"/>
      <attribute name = "overflow" type = "fo:overflow_Type"/>
      <attribute name = "precedence" type = "fo:precedence_Type"/>
      <attribute name = "region-name" type = "xs:string"/>
      <attributeGroup ref = "fo:reference_Properties_List"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "page-sequence">
    <complexType>
      <annotation>
        <documentation>
          Inheritable 
          <attribute name = "country" type = "fo:country_Type"/> 
          <attribute name = "language" type = "fo:language_Type"/>
        </documentation>
      </annotation>
      <sequence>
        <element ref = "fo:title" minOccurs = "0"/>
        <element ref = "fo:static-content" minOccurs = "0" maxOccurs = "unbounded"/>
        <element ref = "fo:flow"/>
      </sequence>
      <attribute name = "id" type = "xs:string"/>
      <attribute name = "master-reference" type = "xs:string" use = "required"/>
      <attribute name = "initial-page-number" type = "fo:integer_Type"/>
      <attribute name = "force-page-count" type = "fo:force_page_count_Type"/>
      <attribute name = "format" type = "xs:string"/>
      <attribute name = "letter-value" type = "fo:letter_value_Type"/>
      <attribute name = "grouping-separator" type = "fo:character_Type"/>
      <attribute name = "grouping-size" type = "fo:integer_Type"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "title">
    <complexType mixed = "true">
      <annotation>
        <documentation>
          Inheritable 
          <attribute name = "color" type = "fo:color_Type"/>
          <attribute name = "line-height" type = "fo:line_height_Type"/>
          <attribute name = "visibility" type = "fo:visibility_Type"/>
        </documentation>
      </annotation>
      <choice minOccurs = "0" maxOccurs = "unbounded">
        <group ref = "fo:inline_List"/>
        <group ref = "fo:neutral_List"/>
      </choice>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attributeGroup ref = "fo:Aural_Properties_List"/>
      <attributeGroup ref = "fo:Border_Padding_and_Background_Properties_List"/>
      <attributeGroup ref = "fo:Font_Properties_List"/>
      <attributeGroup ref = "fo:Margin_Properties_Inline_List"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "static-content">
    <complexType>
      <choice maxOccurs = "unbounded">
        <group ref = "fo:block_List"/>
        <group ref = "fo:neutral_List"/>
        <group ref = "fo:float_List"/>
        <group ref = "fo:footnote_List"/>
      </choice>
      <attribute name = "flow-name" type = "xs:string" use = "required"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "flow">
    <complexType>
      <choice maxOccurs = "unbounded">
        <group ref = "fo:marker_List"/>
        <group ref = "fo:block_List"/>
        <group ref = "fo:neutral_List"/>
        <group ref = "fo:float_List"/>
        <group ref = "fo:footnote_List"/>
      </choice>
      <attribute name = "flow-name" type = "xs:string" use = "required"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "block">
    <complexType mixed = "true">
      <annotation>
        <documentation>
          Inheritable
          <attribute name = "color" type = "fo:color_Type"/>
          <attribute name = "last-line-end-indent" type = "fo:last_line_end_indent_Type"/>
          <attribute name = "linefeed-treatment" type = "fo:linefeed_treatment_Type"/>
          <attribute name = "line-height" type = "fo:line_height_Type"/>
          <attribute name = "orphans" type = "fo:orphans_Type"/>
          <attribute name = "white-space-treatment" type = "fo:white_space_treatment_Type"/>
          <attribute name = "text-align" type = "fo:text_align_Type"/>
          <attribute name = "text-align-last" type = "fo:text_align_last_Type"/>
          <attribute name = "text-indent" type = "fo:length_percentage_Type"/>
          <attribute name = "visibility" type = "fo:visibility_Type"/>
          <attribute name = "white-space-collapse" type = "fo:white_space_collapse_Type"/>
          <attribute name = "widows" type = "fo:widows_Type"/>
          <attribute name = "wrap-option" type = "fo:wrap_option_Type"/>
        </documentation>
      </annotation>
      <choice minOccurs = "0" maxOccurs = "unbounded">
        <group ref = "fo:marker_List"/>
        <group ref = "fo:initial_property_set_List"/>
        <group ref = "fo:inline_List"/>
        <group ref = "fo:block_List"/>
        <group ref = "fo:neutral_List"/>
        <group ref = "fo:float_List"/>
        <group ref = "fo:footnote_List"/>
      </choice>
      <attributeGroup ref = "fo:block_properties"/>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attributeGroup ref = "fo:Aural_Properties_List"/>
      <attributeGroup ref = "fo:Border_Padding_and_Background_Properties_List"/>
      <attributeGroup ref = "fo:Font_Properties_List"/>
      <attributeGroup ref = "fo:Hyphenation_Properties_List"/>
      <attributeGroup ref = "fo:Margin_Properties_Block_List"/>
      <attributeGroup ref = "fo:Relative_Position_Properties_List"/>
      <attributeGroup ref = "fo:break_Properties_List"/>
      <attributeGroup ref = "fo:text_one_Properties_List"/>
      <attributeGroup ref = "fo:block_hyphenation_Properties_List"/>
      <attribute name = "id" type = "xs:string"/>
      <attributeGroup ref = "fo:intrusion_displace_Properties_List"/>
      <attributeGroup ref = "fo:keep_Properties_List"/>
      <attributeGroup ref = "fo:line_height_Properties_List"/>
      <attribute name = "span" type = "fo:span_Type"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "block-container">
    <complexType>
      <annotation>
        <documentation>
          Inheritable <attribute name = "display-align" type = "fo:display_align_Type"/> 
        </documentation>
      </annotation>
      <choice maxOccurs = "unbounded">
        <group ref = "fo:marker_List"/>
        <group ref = "fo:block_List"/>
        <group ref = "fo:neutral_List"/>
        <group ref = "fo:float_List"/>
        <group ref = "fo:footnote_List"/>
      </choice>
      <attributeGroup ref = "fo:block_properties"/>
      <attributeGroup ref = "fo:Absolute_Position_Properties_List"/>
      <attributeGroup ref = "fo:Border_Padding_and_Background_Properties_List"/>
      <attributeGroup ref = "fo:Margin_Properties_Block_List"/>
      <attributeGroup ref = "fo:progression_Properties_List"/>
      <attributeGroup ref = "fo:break_Properties_List"/>
      <attributeGroup ref = "fo:clip_Properties_List"/>
      <attribute name = "height" type = "fo:height_Type"/>
      <attribute name = "id" type = "xs:string"/>
      <attributeGroup ref = "fo:intrusion_displace_Properties_List"/>
      <attributeGroup ref = "fo:keep_Properties_List"/>
      <attribute name = "overflow" type = "fo:overflow_Type"/>
      <attributeGroup ref = "fo:reference_Properties_List"/>
      <attribute name = "span" type = "fo:span_Type"/>
      <attribute name = "width" type = "fo:height_Type"/>
      <attribute name = "z-index" type = "fo:z_index_Type"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "bidi-override">
    <complexType mixed = "true">
      <annotation>
        <documentation>
          Inheritable 
          <attribute name = "color" type = "fo:color_Type"/> 
          <attribute name = "direction" type = "fo:direction_Type"/> 
          <attribute name = "letter-spacing" type = "fo:letter_spacing_Type"/>
          <attribute name = "line-height" type = "fo:line_height_Type"/> 
          <attribute name = "score-spaces" type = "fo:score_spaces_Type"/> 
          <attribute name = "word-spacing" type = "fo:letter_spacing_Type"/> 
        </documentation>
      </annotation>
      <choice minOccurs = "0" maxOccurs = "unbounded">
        <group ref = "fo:marker_List"/>
        <group ref = "fo:inline_List"/>
        <group ref = "fo:block_List"/>
        <group ref = "fo:neutral_List"/>
        <group ref = "fo:float_List"/>
        <group ref = "fo:footnote_List"/>
      </choice>
      <attributeGroup ref = "fo:Aural_Properties_List"/>
      <attributeGroup ref = "fo:Font_Properties_List"/>
      <attributeGroup ref = "fo:Relative_Position_Properties_List"/>
      <attribute name = "id" type = "xs:string"/>
      <attribute name = "unicode-bidi" type = "fo:unicode_bidi_Type"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
      <attribute name = "vertical-align" type = "fo:vertical_align_Type"/>
    </complexType>
  </element>
  <element name = "character">
    <complexType>
      <annotation>
        <documentation>
          Inheritable
          <attribute name = "color" type = "fo:color_Type"/> 
          <attribute name = "glyph-orientation-horizontal" type = "fo:orientation_Type"/> 
          <attribute name = "glyph-orientation-vertical" type = "fo:orientation_Type"/> 
          <attribute name = "letter-spacing" type = "fo:letter_spacing_Type"/> 
          <attribute name = "line-height" type = "fo:line_height_Type"/> 
          <attribute name = "score-spaces" type = "fo:score_spaces_Type"/> 
          <attribute name = "text-transform" type = "fo:text_transform_Type"/> 
          <attribute name = "visibility" type = "fo:visibility_Type"/> 
          <attribute name = "word-spacing" type = "fo:letter_spacing_Type"/>
        </documentation>
      </annotation>
      <attributeGroup ref = "fo:Aural_Properties_List"/>
      <attributeGroup ref = "fo:Border_Padding_and_Background_Properties_List"/>
      <attributeGroup ref = "fo:Font_Properties_List"/>
      <attributeGroup ref = "fo:Hyphenation_Properties_List"/>
      <attributeGroup ref = "fo:Margin_Properties_Inline_List"/>
      <attributeGroup ref = "fo:Relative_Position_Properties_List"/>
      <attribute name = "alignment-adjust" type = "fo:alignment_adjust_Type"/>
      <attribute name = "treat-as-word-space" type = "fo:treat_as_word_space_Type"/>
      <attribute name = "alignment-baseline" type = "fo:alignment_baseline_Type"/>
      <attribute name = "baseline-shift" type = "fo:baseline_shift_Type"/>
      <attribute name = "character" type = "fo:character_Type"/>
      <attribute name = "dominant-baseline" type = "fo:dominant_baseline_Type"/>
      <attributeGroup ref = "fo:text_one_Properties_List"/>
      <attribute name = "id" type = "xs:string"/>
      <attribute name = "keep-with-next" type = "fo:keep_integer_Type"/>
      <attribute name = "keep-with-previous" type = "fo:keep_integer_Type"/>
      <attribute name = "suppress-at-line-break" type = "fo:suppress_at_line_break_Type"/>
      <attribute name = "text-decoration" type = "fo:text_decoration_Type"/>
      <attribute name = "text-shadow" type = "fo:text_shadow_Type"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
      <attribute name = "vertical-align" type = "fo:vertical_align_Type"/>
    </complexType>
  </element>
  <element name = "initial-property-set">
    <complexType>
      <annotation>
        <documentation>
          Inheritable
          <attribute name = "color" type = "fo:color_Type"/> 
          <attribute name = "letter-spacing" type = "fo:letter_spacing_Type"/> 
          <attribute name = "line-height" type = "fo:line_height_Type"/> 
          <attribute name = "score-spaces" type = "fo:score_spaces_Type"/> 
          <attribute name = "text-transform" type = "fo:text_transform_Type"/> 
          <attribute name = "word-spacing" type = "fo:letter_spacing_Type"/>
        </documentation>
      </annotation>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attributeGroup ref = "fo:Aural_Properties_List"/>
      <attributeGroup ref = "fo:Border_Padding_and_Background_Properties_List"/>
      <attributeGroup ref = "fo:Font_Properties_List"/>
      <attributeGroup ref = "fo:Relative_Position_Properties_List"/>
      <attribute name = "id" type = "xs:string"/>
      <attribute name = "text-decoration" type = "fo:text_decoration_Type"/>
      <attribute name = "text-shadow" type = "fo:text_shadow_Type"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "external-graphic">
    <complexType>
      <annotation>
        <documentation>
          Inheritable
          <attribute name = "display-align" type = "fo:display_align_Type"/> 
          <attribute name = "line-height" type = "fo:line_height_Type"/> 
          <attribute name = "text-align" type = "fo:text_align_Type"/>
        </documentation>
      </annotation>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attributeGroup ref = "fo:Aural_Properties_List"/>
      <attributeGroup ref = "fo:Border_Padding_and_Background_Properties_List"/>
      <attributeGroup ref = "fo:Margin_Properties_Inline_List"/>
      <attributeGroup ref = "fo:Relative_Position_Properties_List"/>
      <attribute name = "alignment-adjust" type = "fo:alignment_adjust_Type"/>
      <attribute name = "alignment-baseline" type = "fo:alignment_baseline_Type"/>
      <attribute name = "baseline-shift" type = "fo:baseline_shift_Type"/>
      <attributeGroup ref = "fo:progression_Properties_List"/>
      <attributeGroup ref = "fo:clip_Properties_List"/>
      <attribute name = "content-height" type = "fo:content_height_Type"/>
      <attribute name = "content-type" type = "xs:string"/>
      <attribute name = "content-width" type = "fo:content_height_Type"/>
      <attribute name = "dominant-baseline" type = "fo:dominant_baseline_Type"/>
      <attribute name = "height" type = "fo:height_Type"/>
      <attribute name = "id" type = "xs:string"/>
      <attribute name = "keep-with-next" type = "fo:keep_integer_Type"/>
      <attribute name = "keep-with-previous" type = "fo:keep_integer_Type"/>
      <attribute name = "overflow" type = "fo:overflow_Type"/>
      <attribute name = "scaling" type = "fo:scaling_Type"/>
      <attribute name = "scaling-method" type = "fo:scaling_method_Type"/>
      <attribute name = "src" type = "xs:string" use = "required"/>
      <attribute name = "width" type = "fo:height_Type"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
      <attribute name = "vertical-align" type = "fo:vertical_align_Type"/>
    </complexType>
  </element>
  <element name = "instream-foreign-object">
    <complexType>
      <annotation>
        <documentation>
          Inheritable
          <attribute name = "display-align" type = "fo:display_align_Type"/> 
          <attribute name = "line-height" type = "fo:line_height_Type"/> 
          <attribute name = "text-align" type = "fo:text_align_Type"/>
        </documentation>
      </annotation>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attributeGroup ref = "fo:Aural_Properties_List"/>
      <attributeGroup ref = "fo:Border_Padding_and_Background_Properties_List"/>
      <attributeGroup ref = "fo:Margin_Properties_Inline_List"/>
      <attributeGroup ref = "fo:Relative_Position_Properties_List"/>
      <attribute name = "alignment-adjust" type = "fo:alignment_adjust_Type"/>
      <attribute name = "alignment-baseline" type = "fo:alignment_baseline_Type"/>
      <attribute name = "baseline-shift" type = "fo:baseline_shift_Type"/>
      <attributeGroup ref = "fo:progression_Properties_List"/>
      <attributeGroup ref = "fo:clip_Properties_List"/>
      <attribute name = "content-height" type = "fo:content_height_Type"/>
      <attribute name = "content-type" type = "xs:string"/>
      <attribute name = "content-width" type = "fo:content_height_Type"/>
      <attribute name = "dominant-baseline" type = "fo:dominant_baseline_Type"/>
      <attribute name = "height" type = "fo:height_Type"/>
      <attribute name = "id" type = "xs:string"/>
      <attribute name = "keep-with-next" type = "fo:keep_integer_Type"/>
      <attribute name = "keep-with-previous" type = "fo:keep_integer_Type"/>
      <attribute name = "overflow" type = "fo:overflow_Type"/>
      <attribute name = "scaling" type = "fo:scaling_Type"/>
      <attribute name = "scaling-method" type = "fo:scaling_method_Type"/>
      <attribute name = "width" type = "fo:height_Type"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
      <attribute name = "vertical-align" type = "fo:vertical_align_Type"/>
    </complexType>
  </element>
  <element name = "inline">
    <complexType mixed = "true">
      <annotation>
        <documentation>
          Inheritable
          <attribute name = "color" type = "fo:color_Type"/> 
          <attribute name = "line-height" type = "fo:line_height_Type"/> 
          <attribute name = "visibility" type = "fo:visibility_Type"/> 
          <attribute name = "wrap-option" type = "fo:wrap_option_Type"/>
        </documentation>
      </annotation>
      <choice minOccurs = "0" maxOccurs = "unbounded">
        <group ref = "fo:marker_List"/>
        <group ref = "fo:inline_List"/>
        <group ref = "fo:block_List"/>
        <group ref = "fo:neutral_List"/>
        <group ref = "fo:float_List"/>
        <group ref = "fo:footnote_List"/>
      </choice>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attributeGroup ref = "fo:Aural_Properties_List"/>
      <attributeGroup ref = "fo:Border_Padding_and_Background_Properties_List">
        <annotation>
          <documentation>
            <fop_result>Border and background properties not implemented</fop_result>
          </documentation>
        </annotation>
      </attributeGroup>
      <attributeGroup ref = "fo:Font_Properties_List"/>
      <attributeGroup ref = "fo:Margin_Properties_Inline_List"/>
      <attributeGroup ref = "fo:Relative_Position_Properties_List"/>
      <attribute name = "alignment-adjust" type = "fo:alignment_adjust_Type"/>
      <attribute name = "alignment-baseline" type = "fo:alignment_baseline_Type"/>
      <attribute name = "baseline-shift" type = "fo:baseline_shift_Type"/>
      <attributeGroup ref = "fo:progression_Properties_List"/>
      <attribute name = "dominant-baseline" type = "fo:dominant_baseline_Type"/>
      <attribute name = "height" type = "fo:height_Type"/>
      <attribute name = "id" type = "xs:string"/>
      <attributeGroup ref = "fo:keep_Properties_List"/>
      <attribute name = "text-decoration" type = "fo:text_decoration_Type"/>
      <attribute name = "width" type = "fo:height_Type"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
      <attribute name = "vertical-align" type = "fo:vertical_align_Type"/>
    </complexType>
  </element>
  <element name = "inline-container">
    <complexType>
      <annotation>
        <documentation>
          Inheritable 
          <attribute name = "display-align" type = "fo:display_align_Type"/>
          <attribute name = "line-height" type = "fo:line_height_Type"/>
        </documentation>
      </annotation>
      <choice maxOccurs = "unbounded">
        <group ref = "fo:marker_List"/>
        <group ref = "fo:block_List"/>
        <group ref = "fo:neutral_List"/>
        <group ref = "fo:float_List"/>
        <group ref = "fo:footnote_List"/>
      </choice>
      <attributeGroup ref = "fo:Border_Padding_and_Background_Properties_List"/>
      <attributeGroup ref = "fo:Margin_Properties_Inline_List"/>
      <attributeGroup ref = "fo:Relative_Position_Properties_List"/>
      <attribute name = "alignment-adjust" type = "fo:alignment_adjust_Type"/>
      <attribute name = "alignment-baseline" type = "fo:alignment_baseline_Type"/>
      <attribute name = "baseline-shift" type = "fo:baseline_shift_Type"/>
      <attributeGroup ref = "fo:progression_Properties_List"/>
      <attributeGroup ref = "fo:clip_Properties_List"/>
      <attribute name = "dominant-baseline" type = "fo:dominant_baseline_Type"/>
      <attribute name = "height" type = "fo:height_Type"/>
      <attribute name = "id" type = "xs:string"/>
      <attributeGroup ref = "fo:keep_Properties_List"/>
      <attribute name = "overflow" type = "fo:overflow_Type"/>
      <attributeGroup ref = "fo:reference_Properties_List"/>
      <attribute name = "width" type = "fo:height_Type"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
      <attribute name = "vertical-align" type = "fo:vertical_align_Type"/>
    </complexType>
  </element>
  <element name = "leader">
    <complexType mixed = "true">
      <annotation>
        <documentation>
          Inheritable 
          <attribute name = "color" type = "fo:color_Type"/> 
          <attribute name = "leader-alignment" type = "fo:leader_alignment_Type"/> 
          <attribute name = "leader-length" type = "fo:leader_length_Type"/> 
          <attribute name = "leader-length.minimum" type = "fo:length_Type"/>
          <attribute name = "leader-length.optimum" type = "fo:length_Type"/>
          <attribute name = "leader-length.maximum" type = "fo:length_Type"/>
          <attribute name = "leader-pattern" type = "fo:leader_pattern_Type"/> 
          <attribute name = "leader-pattern-width" type = "fo:leader_pattern_width_Type"/> 
          <attribute name = "rule-style" type = "fo:rule_style_Type"/> 
          <attribute name = "rule-thickness" type = "fo:length_Type"/> 
          <attribute name = "letter-spacing" type = "fo:letter_spacing_Type"/> 
          <attribute name = "line-height" type = "fo:line_height_Type"/> 
          <attribute name = "visibility" type = "fo:visibility_Type"/> 
          <attribute name = "word-spacing" type = "fo:letter_spacing_Type"/>
        </documentation>
      </annotation>
      <choice minOccurs = "0" maxOccurs = "unbounded">
        <group ref = "fo:inline_List"/>
        <group ref = "fo:neutral_List"/>
      </choice>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attributeGroup ref = "fo:Aural_Properties_List"/>
      <attributeGroup ref = "fo:Border_Padding_and_Background_Properties_List"/>
      <attributeGroup ref = "fo:Font_Properties_List"/>
      <attributeGroup ref = "fo:Margin_Properties_Inline_List"/>
      <attributeGroup ref = "fo:Relative_Position_Properties_List"/>
      <attribute name = "alignment-adjust" type = "fo:alignment_adjust_Type"/>
      <attribute name = "alignment-baseline" type = "fo:alignment_baseline_Type"/>
      <attribute name = "baseline-shift" type = "fo:baseline_shift_Type"/>
      <attribute name = "dominant-baseline" type = "fo:dominant_baseline_Type"/>
      <attributeGroup ref = "fo:text_one_Properties_List"/>
      <attribute name = "id" type = "xs:string"/>
      <attribute name = "keep-with-next" type = "fo:keep_integer_Type"/>
      <attribute name = "keep-with-previous" type = "fo:keep_integer_Type"/>
      <attribute name = "text-shadow" type = "fo:text_shadow_Type"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
      <attribute name = "vertical-align" type = "fo:vertical_align_Type"/>
    </complexType>
  </element>
  <element name = "page-number">
    <complexType>
      <annotation>
        <documentation>
          Inheritable 
          <attribute name = "letter-spacing" type = "fo:letter_spacing_Type"/>
          <attribute name = "line-height" type = "fo:line_height_Type"/>
          <attribute name = "score-spaces" type = "fo:score_spaces_Type"/>
          <attribute name = "text-transform" type = "fo:text_transform_Type"/>
          <attribute name = "visibility" type = "fo:visibility_Type"/>
          <attribute name = "word-spacing" type = "fo:letter_spacing_Type"/>
          <attribute name = "wrap-option" type = "fo:wrap_option_Type"/>
        </documentation>
      </annotation>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attributeGroup ref = "fo:Aural_Properties_List"/>
      <attributeGroup ref = "fo:Border_Padding_and_Background_Properties_List"/>
      <attributeGroup ref = "fo:Font_Properties_List"/>
      <attributeGroup ref = "fo:Margin_Properties_Inline_List"/>
      <attributeGroup ref = "fo:Relative_Position_Properties_List"/>
      <attribute name = "alignment-adjust" type = "fo:alignment_adjust_Type"/>
      <attribute name = "alignment-baseline" type = "fo:alignment_baseline_Type"/>
      <attribute name = "baseline-shift" type = "fo:baseline_shift_Type"/>
      <attribute name = "dominant-baseline" type = "fo:dominant_baseline_Type"/>
      <attribute name = "id" type = "xs:string"/>
      <attribute name = "keep-with-next" type = "fo:keep_integer_Type"/>
      <attribute name = "keep-with-previous" type = "fo:keep_integer_Type"/>
      <attributeGroup ref = "fo:text_one_Properties_List"/>
      <attribute name = "text-decoration" type = "fo:text_decoration_Type"/>
      <attribute name = "text-shadow" type = "fo:text_shadow_Type"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
      <attribute name = "vertical-align" type = "fo:vertical_align_Type"/>
    </complexType>
  </element>
  <element name = "page-number-citation">
    <complexType>
      <annotation>
        <documentation>
          Inheritable 
          <attribute name = "letter-spacing" type = "fo:letter_spacing_Type"/>
          <attribute name = "line-height" type = "fo:line_height_Type"/>
          <attribute name = "score-spaces" type = "fo:score_spaces_Type"/>
          <attribute name = "text-transform" type = "fo:text_transform_Type"/>
          <attribute name = "visibility" type = "fo:visibility_Type"/>
          <attribute name = "word-spacing" type = "fo:letter_spacing_Type"/>
          <attribute name = "wrap-option" type = "fo:wrap_option_Type"/>
        </documentation>
      </annotation>
      <attribute name = "ref-id" type = "xs:string" use = "required"/>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attributeGroup ref = "fo:Aural_Properties_List"/>
      <attributeGroup ref = "fo:Border_Padding_and_Background_Properties_List"/>
      <attributeGroup ref = "fo:Font_Properties_List"/>
      <attributeGroup ref = "fo:Margin_Properties_Inline_List"/>
      <attributeGroup ref = "fo:Relative_Position_Properties_List"/>
      <attribute name = "alignment-adjust" type = "fo:alignment_adjust_Type"/>
      <attribute name = "alignment-baseline" type = "fo:alignment_baseline_Type"/>
      <attribute name = "baseline-shift" type = "fo:baseline_shift_Type"/>
      <attribute name = "dominant-baseline" type = "fo:dominant_baseline_Type"/>
      <attribute name = "id" type = "xs:string"/>
      <attribute name = "keep-with-next" type = "fo:keep_integer_Type"/>
      <attribute name = "keep-with-previous" type = "fo:keep_integer_Type"/>
      <attributeGroup ref = "fo:text_one_Properties_List"/>
      <attribute name = "text-decoration" type = "fo:text_decoration_Type"/>
      <attribute name = "text-shadow" type = "fo:text_shadow_Type"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
      <attribute name = "vertical-align" type = "fo:vertical_align_Type"/>
    </complexType>
  </element>
  <element name = "table-and-caption">
    <complexType>
      <annotation>
        <documentation>
          Inheritable
          <attribute name = "caption-side" type = "fo:caption_side_Type"/>
          <attribute name = "text-align" type = "fo:text_align_Type"/>
        </documentation>
      </annotation>
      <sequence>
        <element ref = "fo:table-caption" minOccurs = "0"/>
        <element ref = "fo:table"/>
      </sequence>
      <attributeGroup ref = "fo:block_properties"/>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attributeGroup ref = "fo:Aural_Properties_List"/>
      <attributeGroup ref = "fo:Border_Padding_and_Background_Properties_List"/>
      <attributeGroup ref = "fo:Margin_Properties_Block_List"/>
      <attributeGroup ref = "fo:Relative_Position_Properties_List"/>
      <attributeGroup ref = "fo:break_Properties_List"/>
      <attribute name = "id" type = "xs:string"/>
      <attributeGroup ref = "fo:intrusion_displace_Properties_List"/>
      <attributeGroup ref = "fo:keep_Properties_List"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "table-caption">
    <complexType>
      <annotation>
        <documentation>
          Inheritable 
          <attribute name = "keep-together" type = "fo:keep_compound_Type"/>
          <attribute name = "keep-together.within-line" type = "fo:keep_integer_Type"/>
          <attribute name = "keep-together.within-column" type = "fo:keep_integer_Type"/>
          <attribute name = "keep-together.within-page" type = "fo:keep_integer_Type"/>
        </documentation>
      </annotation>
      <choice maxOccurs = "unbounded">
        <group ref = "fo:marker_List"/>
        <group ref = "fo:block_List"/>
        <group ref = "fo:neutral_List"/>
        <group ref = "fo:float_List"/>
        <group ref = "fo:footnote_List"/>
      </choice>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attributeGroup ref = "fo:Aural_Properties_List"/>
      <attributeGroup ref = "fo:Border_Padding_and_Background_Properties_List"/>
      <attributeGroup ref = "fo:Relative_Position_Properties_List"/>
      <attributeGroup ref = "fo:progression_Properties_List"/>
      <attribute name = "height" type = "fo:height_Type"/>
      <attribute name = "id" type = "xs:string"/>
      <attributeGroup ref = "fo:intrusion_displace_Properties_List"/>
      <attribute name = "width" type = "fo:height_Type"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "table">
    <complexType>
      <annotation>
        <documentation>
          Inheritable
          <attribute name = "border-collapse" type = "fo:border_collapse_Type"/>
          <attribute name = "border-separation" type = "fo:length_bp_ip_direction_Type"/>
          <attribute name = "border-spacing" type = "fo:length_Type"/>
          <attribute name = "writing-mode" type = "fo:writing_mode_Type"/>
        </documentation>
      </annotation>
      <sequence>
        <element ref = "fo:table-column" minOccurs = "0" maxOccurs = "unbounded"/>
        <element ref = "fo:table-header" minOccurs = "0"/>
        <element ref = "fo:table-footer" minOccurs = "0"/>
        <element ref = "fo:table-body" maxOccurs = "unbounded"/>
      </sequence>
      <attributeGroup ref = "fo:block_properties"/>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attributeGroup ref = "fo:Aural_Properties_List"/>
      <attributeGroup ref = "fo:Border_Padding_and_Background_Properties_List"/>
      <attributeGroup ref = "fo:Margin_Properties_Block_List"/>
      <attributeGroup ref = "fo:Relative_Position_Properties_List"/>
      <attributeGroup ref = "fo:progression_Properties_List"/>
      <attribute name = "border-after-precedence" type = "fo:precedence_Type"/>
      <attribute name = "border-before-precedence" type = "fo:precedence_Type"/>
      <attribute name = "border-end-precedence" type = "fo:precedence_Type"/>
      <attribute name = "border-start-precedence" type = "fo:precedence_Type"/>
      <attributeGroup ref = "fo:break_Properties_List"/>
      <attribute name = "id" type = "xs:string"/>
      <attributeGroup ref = "fo:intrusion_displace_Properties_List"/>
      <attribute name = "height" type = "fo:height_Type"/>
      <attributeGroup ref = "fo:keep_Properties_List"/>
      <attribute name = "table-layout" type = "fo:table_layout_Type"/>
      <attribute name = "table-omit-footer-at-break" type = "fo:table_omit_footer_at_break_Type"/>
      <attribute name = "table-omit-header-at-break" type = "fo:table_omit_header_at_break_Type"/>
      <attribute name = "width" type = "fo:height_Type"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "table-column">
    <complexType>
      <annotation>
        <documentation>
          Inheritable 
          <attribute name = "visibility" type = "fo:visibility_Type"/> 
        </documentation>
      </annotation>
      <attributeGroup ref = "fo:Border_Properties_List"/>
      <attributeGroup ref = "fo:Background_Properties_List"/>
      <attribute name = "border-after-precedence" type = "fo:precedence_Type"/>
      <attribute name = "border-before-precedence" type = "fo:precedence_Type"/>
      <attribute name = "border-end-precedence" type = "fo:precedence_Type"/>
      <attribute name = "border-start-precedence" type = "fo:precedence_Type"/>
      <attribute name = "column-number" type = "fo:integer_Type"/>
      <attribute name = "column-width" type = "fo:column_width_Type"/>
      <attribute name = "number-columns-repeated" type = "fo:integer_Type"/>
      <attribute name = "number-columns-spanned" type = "fo:integer_Type"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "table-header">
    <complexType>
      <annotation>
        <documentation>
          Inheritable <attribute name = "visibility" type = "fo:visibility_Type"/> 
        </documentation>
      </annotation>
      <choice>
        <element ref = "fo:table-row" maxOccurs = "unbounded"/>
        <element ref = "fo:table-cell" maxOccurs = "unbounded"/>
      </choice>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attributeGroup ref = "fo:Aural_Properties_List"/>
      <attributeGroup ref = "fo:Border_Properties_List"/>
      <attributeGroup ref = "fo:Background_Properties_List"/>
      <attributeGroup ref = "fo:Relative_Position_Properties_List"/>
      <attribute name = "border-after-precedence" type = "fo:precedence_Type"/>
      <attribute name = "border-before-precedence" type = "fo:precedence_Type"/>
      <attribute name = "border-end-precedence" type = "fo:precedence_Type"/>
      <attribute name = "border-start-precedence" type = "fo:precedence_Type"/>
      <attribute name = "id" type = "xs:string"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "table-footer">
    <complexType>
      <annotation>
        <documentation>
          Inheritable <attribute name = "visibility" type = "fo:visibility_Type"/> 
        </documentation>
      </annotation>
      <choice>
        <element ref = "fo:table-row" maxOccurs = "unbounded"/>
        <element ref = "fo:table-cell" maxOccurs = "unbounded"/>
      </choice>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attributeGroup ref = "fo:Aural_Properties_List"/>
      <attributeGroup ref = "fo:Border_Properties_List"/>
      <attributeGroup ref = "fo:Background_Properties_List"/>
      <attributeGroup ref = "fo:Relative_Position_Properties_List"/>
      <attribute name = "border-after-precedence" type = "fo:precedence_Type"/>
      <attribute name = "border-before-precedence" type = "fo:precedence_Type"/>
      <attribute name = "border-end-precedence" type = "fo:precedence_Type"/>
      <attribute name = "border-start-precedence" type = "fo:precedence_Type"/>
      <attribute name = "id" type = "xs:string"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "table-body">
    <complexType>
      <annotation>
        <documentation>
          Inheritable <attribute name = "visibility" type = "fo:visibility_Type"/> 
        </documentation>
      </annotation>
      <choice>
        <element ref = "fo:table-row" maxOccurs = "unbounded"/>
        <element ref = "fo:table-cell" maxOccurs = "unbounded"/>
      </choice>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attributeGroup ref = "fo:Aural_Properties_List"/>
      <attributeGroup ref = "fo:Border_Properties_List"/>
      <attributeGroup ref = "fo:Background_Properties_List"/>
      <attributeGroup ref = "fo:Relative_Position_Properties_List"/>
      <attribute name = "border-after-precedence" type = "fo:precedence_Type"/>
      <attribute name = "border-before-precedence" type = "fo:precedence_Type"/>
      <attribute name = "border-end-precedence" type = "fo:precedence_Type"/>
      <attribute name = "border-start-precedence" type = "fo:precedence_Type"/>
      <attribute name = "id" type = "xs:string"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "table-row">
    <complexType>
      <annotation>
        <documentation>
          Inheritable <attribute name = "visibility" type = "fo:visibility_Type"/> 
        </documentation>
      </annotation>
      <sequence>
        <element ref = "fo:table-cell" maxOccurs = "unbounded"/>
      </sequence>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attributeGroup ref = "fo:Aural_Properties_List"/>
      <attributeGroup ref = "fo:Border_Properties_List"/>
      <attributeGroup ref = "fo:Background_Properties_List"/>
      <attributeGroup ref = "fo:Relative_Position_Properties_List"/>
      <attribute name = "block-progression-dimension" type = "fo:margin_width_Type"/>
      <attribute name = "block-progression-dimension.minimum" type = "fo:length_Type"/>
      <attribute name = "block-progression-dimension.optimum" type = "fo:block_progression_dimension_optimum_Type"/>
      <attribute name = "block-progression-dimension.maximum" type = "fo:block_progression_dimension_maximum_Type"/>
      <attribute name = "border-after-precedence" type = "fo:precedence_Type"/>
      <attribute name = "border-before-precedence" type = "fo:precedence_Type"/>
      <attribute name = "border-end-precedence" type = "fo:precedence_Type"/>
      <attribute name = "border-start-precedence" type = "fo:precedence_Type"/>
      <attributeGroup ref = "fo:break_Properties_List"/>
      <attribute name = "id" type = "xs:string"/>
      <attribute name = "height" type = "fo:height_Type"/>
      <attributeGroup ref = "fo:keep_Properties_List"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
      <attribute name = "page-break-after"  type = "fo:page_break_after_Type"/>
      <attribute name = "page-break-before" type = "fo:page_break_after_Type"/>
    </complexType>
  </element>
  <element name = "table-cell">
    <complexType>
      <annotation>
        <documentation>
          Inheritable
          <attribute name = "display-align" type = "fo:display_align_Type"/>
          <attribute name = "relative-align" type = "fo:relative_align_Type"/>
          <attribute name = "empty-cells" type = "fo:empty_cells_Type"/>
        </documentation>
      </annotation>
      <choice maxOccurs = "unbounded">
        <group ref = "fo:marker_List"/>
        <group ref = "fo:block_List"/>
        <group ref = "fo:neutral_List"/>
        <group ref = "fo:float_List"/>
        <group ref = "fo:footnote_List"/>
      </choice>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attributeGroup ref = "fo:Aural_Properties_List"/>
      <attributeGroup ref = "fo:Border_Padding_and_Background_Properties_List"/>
      <attributeGroup ref = "fo:Relative_Position_Properties_List"/>
      <attribute name = "border-after-precedence" type = "fo:precedence_Type"/>
      <attribute name = "border-before-precedence" type = "fo:precedence_Type"/>
      <attribute name = "border-end-precedence" type = "fo:precedence_Type"/>
      <attribute name = "border-start-precedence" type = "fo:precedence_Type"/>
      <attributeGroup ref = "fo:progression_Properties_List"/>
      <attribute name = "column-number" type = "fo:integer_Type"/>
      <attribute name = "ends-row" type = "fo:ends_row_Type"/>
      <attribute name = "height" type = "fo:height_Type"/>
      <attribute name = "id" type = "xs:string"/>
      <attribute name = "number-columns-spanned" type = "fo:integer_Type"/>
      <attribute name = "number-rows-spanned" type = "fo:integer_Type"/>
      <attribute name = "starts-row" type = "fo:starts_row_Type"/>
      <attribute name = "width" type = "fo:height_Type"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
      <attribute name = "vertical-align" type = "fo:vertical_align_Type"/>
    </complexType>
  </element>
  <element name = "list-block">
    <complexType>
      <annotation>
        <documentation>
          Inheritable
          <attribute name = "provisional-distance-between-starts" type = "fo:provisional_distance_between_starts_Type"/>
          <attribute name = "provisional-label-separation" type = "fo:provisional_label_separation_Type"/>
        </documentation>
      </annotation>
      <sequence>
        <element ref = "fo:list-item" maxOccurs = "unbounded"/>
      </sequence>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attributeGroup ref = "fo:Aural_Properties_List"/>
      <attributeGroup ref = "fo:Border_Padding_and_Background_Properties_List"/>
      <attributeGroup ref = "fo:Margin_Properties_Block_List"/>
      <attributeGroup ref = "fo:Relative_Position_Properties_List"/>
      <attributeGroup ref = "fo:break_Properties_List"/>
      <attribute name = "id" type = "xs:string"/>
      <attributeGroup ref = "fo:intrusion_displace_Properties_List"/>
      <attributeGroup ref = "fo:keep_Properties_List"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
      <attributeGroup ref = "fo:list_properties"/>
    </complexType>
  </element>
  <element name = "list-item">
    <complexType>
      <annotation>
        <documentation>
          Inheritable <attribute name = "relative-align" type = "fo:relative_align_Type"/> 
        </documentation>
      </annotation>
      <sequence>
        <element ref = "fo:list-item-label"/>
        <element ref = "fo:list-item-body"/>
      </sequence>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attributeGroup ref = "fo:Aural_Properties_List"/>
      <attributeGroup ref = "fo:Border_Padding_and_Background_Properties_List"/>
      <attributeGroup ref = "fo:Margin_Properties_Block_List"/>
      <attributeGroup ref = "fo:Relative_Position_Properties_List"/>
      <attributeGroup ref = "fo:break_Properties_List"/>
      <attribute name = "id" type = "xs:string"/>
      <attributeGroup ref = "fo:intrusion_displace_Properties_List"/>
      <attributeGroup ref = "fo:keep_Properties_List"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
      <attributeGroup ref = "fo:list_properties"/>
    </complexType>
  </element>
  <element name = "list-item-body">
    <complexType>
      <annotation>
        <documentation>
          Inheritable 
          <attribute name = "keep-together" type = "fo:keep_compound_Type"/>
          <attribute name = "keep-together.within-line" type = "fo:keep_integer_Type"/>
          <attribute name = "keep-together.within-column" type = "fo:keep_integer_Type"/>
          <attribute name = "keep-together.within-page" type = "fo:keep_integer_Type"/>
        </documentation>
      </annotation>
      <choice maxOccurs = "unbounded">
        <group ref = "fo:marker_List"/>
        <group ref = "fo:block_List"/>
        <group ref = "fo:neutral_List"/>
        <group ref = "fo:float_List"/>
        <group ref = "fo:footnote_List"/>
      </choice>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attribute name = "id" type = "xs:string"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
      <attributeGroup ref = "fo:list_properties"/>
    </complexType>
  </element>
  <element name = "list-item-label">
    <complexType>
      <annotation>
        <documentation>
          Inheritable 
          <attribute name = "keep-together" type = "fo:keep_compound_Type"/>
          <attribute name = "keep-together.within-line" type = "fo:keep_integer_Type"/>
          <attribute name = "keep-together.within-column" type = "fo:keep_integer_Type"/>
          <attribute name = "keep-together.within-page" type = "fo:keep_integer_Type"/>
        </documentation>
      </annotation>
      <choice maxOccurs = "unbounded">
        <group ref = "fo:marker_List"/>
        <group ref = "fo:block_List"/>
        <group ref = "fo:neutral_List"/>
        <group ref = "fo:float_List"/>
        <group ref = "fo:footnote_List"/>
      </choice>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attribute name = "id" type = "xs:string"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
      <attributeGroup ref = "fo:list_properties"/>
    </complexType>
  </element>
  <element name = "float">
    <complexType>
      <choice maxOccurs = "unbounded">
        <group ref = "fo:block_List"/>
        <group ref = "fo:neutral_List"/>
        <group ref = "fo:float_List"/>
        <group ref = "fo:footnote_List"/>
      </choice>
      <attribute name = "float" type = "fo:float_Type"/>
      <attribute name = "clear" type = "fo:clear_Type"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "footnote">
    <complexType>
      <sequence>
        <element ref = "fo:inline"/>
        <element ref = "fo:footnote-body"/>
      </sequence>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "footnote-body">
    <complexType>
      <choice maxOccurs = "unbounded">
        <group ref = "fo:block_List"/>
        <group ref = "fo:neutral_List"/>
        <group ref = "fo:float_List"/>
        <group ref = "fo:footnote_List"/>
      </choice>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "basic-link">
    <complexType mixed = "true">
      <annotation>
        <documentation>
          Inheritable <attribute name = "line-height" type = "fo:line_height_Type"/> 
        </documentation>
      </annotation>
      <choice minOccurs = "0" maxOccurs = "unbounded">
        <group ref = "fo:marker_List"/>
        <group ref = "fo:inline_List"/>
        <group ref = "fo:block_List"/>
        <group ref = "fo:neutral_List"/>
        <group ref = "fo:float_List"/>
        <group ref = "fo:footnote_List"/>
      </choice>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attributeGroup ref = "fo:Aural_Properties_List"/>
      <attributeGroup ref = "fo:Border_Padding_and_Background_Properties_List"/>
      <attributeGroup ref = "fo:Margin_Properties_Inline_List"/>
      <attributeGroup ref = "fo:Relative_Position_Properties_List"/>
      <attribute name = "alignment-adjust" type = "fo:alignment_adjust_Type"/>
      <attribute name = "alignment-baseline" type = "fo:alignment_baseline_Type"/>
      <attribute name = "baseline-shift" type = "fo:baseline_shift_Type"/>
      <attribute name = "destination-placement-offset" type = "fo:length_Type"/>
      <attribute name = "dominant-baseline" type = "fo:dominant_baseline_Type"/>
      <attribute name = "external-destination" type = "xs:string"/>
      <attribute name = "id" type = "xs:string"/>
      <attribute name = "indicate-destination" type = "fo:indicate_destination_Type"/>
      <attribute name = "internal-destination" type = "xs:string"/>
      <attributeGroup ref = "fo:keep_Properties_List"/>
      <attribute name = "show-destination" type = "fo:show_destination_Type"/>
      <attribute name = "target-processing-context" type = "xs:string"/>
      <attribute name = "target-presentation-context" type = "xs:string"/>
      <attribute name = "target-stylesheet" type = "xs:string"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
      <attribute name = "vertical-align" type = "fo:vertical_align_Type"/>
    </complexType>
  </element>
  <element name = "wrapper">
    <complexType mixed = "true">
      <choice minOccurs = "0" maxOccurs = "unbounded">
        <group ref = "fo:marker_List"/>
        <group ref = "fo:inline_List"/>
        <group ref = "fo:block_List"/>
        <group ref = "fo:neutral_List"/>
        <group ref = "fo:float_List"/>
        <group ref = "fo:footnote_List"/>
      </choice>
      <attribute name = "id" type = "xs:string"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "marker">
    <complexType mixed = "true">
      <choice minOccurs = "0" maxOccurs = "unbounded">
        <group ref = "fo:inline_List"/>
        <group ref = "fo:block_List"/>
      </choice>
      <attribute name = "marker-class-name" type = "xs:string"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "retrieve-marker">
    <complexType>
      <attribute name = "retrieve-class-name" type = "xs:string"/>
      <attribute name = "retrieve-position" type = "fo:retrieve_position_Type"/>
      <attribute name = "retrieve-boundary" type = "fo:retrieve_boundary_Type"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "multi-switch">
    <complexType>
      <annotation>
        <documentation>
          Inheritable <attribute name = "auto-restore" type = "fo:auto_restore_Type"/> 
        </documentation>
      </annotation>
      <sequence>
        <element ref = "fo:multi-case"/>
      </sequence>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attribute name = "id" type = "xs:string"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "multi-case">
    <complexType mixed = "true">
      <choice minOccurs = "0" maxOccurs = "unbounded">
        <group ref = "fo:inline_List"/>
        <group ref = "fo:block_List"/>
        <group ref = "fo:neutral_List"/>
        <group ref = "fo:float_List"/>
        <group ref = "fo:footnote_List"/>
      </choice>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attribute name = "id" type = "xs:string"/>
      <attribute name = "starting-state" type = "fo:starting_state_Type"/>
      <attribute name = "case-name" type = "xs:string"/>
      <attribute name = "case-title" type = "xs:string"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "multi-toggle">
    <complexType mixed = "true">
      <choice minOccurs = "0" maxOccurs = "unbounded">
        <group ref = "fo:inline_List"/>
        <group ref = "fo:block_List"/>
        <group ref = "fo:neutral_List"/>
        <group ref = "fo:float_List"/>
        <group ref = "fo:footnote_List"/>
      </choice>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attribute name = "id" type = "xs:string"/>
      <attribute name = "switch-to" type = "xs:string"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
      <attribute name = "vertical-align" type = "fo:vertical_align_Type"/>
    </complexType>
  </element>
  <element name = "multi-properties">
    <complexType>
      <sequence>
        <element ref = "fo:multi-property-set" maxOccurs = "unbounded"/>
        <element ref = "fo:wrapper"/>
      </sequence>
      <attributeGroup ref = "fo:Accessibility_Properties_List"/>
      <attribute name = "id" type = "xs:string"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
  <element name = "multi-property-set">
    <complexType>
      <attribute name = "id" type = "xs:string"/>
      <attribute name = "active-state" type = "fo:active_state_Type" use = "required"/>
      <attributeGroup ref = "fo:inheritable_properties_List"/>
    </complexType>
  </element>
</schema>
