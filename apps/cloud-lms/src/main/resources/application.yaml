spring:
  mail:
    default-encoding: UTF-8
    protocol: smtp
    host: email-smtp.ap-south-1.amazonaws.com
    port: 587
    username: ********************
    password: BHC+HlnuRCE28wD7j2UiJFLpybntOY/QWKvy02Cp/g3f
    from-email: <EMAIL>
  jpa:
    hibernate:
      ddl-auto: update
  liquibase:
    change-log: classpath:db/changelog/db.changelog-master.xml
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  servlet:
    multipart:
      max-file-size: 25MB
      max-request-size: 25MB
  application:
    name: retail-service
cloud:
  type: aws
app:
  google:
    client-id: 445344249545-4t3np0g1mkbctib94qto5cip2p2s52nv.apps.googleusercontent.com
    client-secret: 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
  storageKey:
    accessKey: 6696MH3R56KJT9DB4RC4
    secretKey: yDNqZoTXGzKBORAFgjGT5pbK2PbFul1nQc288QRF
    endpoint: https://s3.ap-southeast-1.wasabisys.com
    region: ap-southeast-1
  razorPay:
    keyId: "rzp_test_9dZpBsYQ5MUJRe"
    secretKey: "PJc6G9vL1ZXRYp5etBuHJA5U"
  omrPath: wexl-omr
  parseable:
    enabled: true
    #streamName: prodretail -> This is env specific
    baseUrl: http://parseable.omr.svc.cluster.local:8000
    username: admin
    password: password@123
  contentToken: "Bearer eyJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YPzmd87jrmPThQko_Ua-zzEsCOIantN961cPgrPVGgc"
  localeFilesLocation: file:///i18n/
  omr-url: http://omr-service.omr.svc.cluster.local:9081/
  wordFinderUrl: https://api.dictionaryapi.dev/api/v2/entries/en/
  publicDomainUrl: dev.wexl.in
  qrCode:
    url: https://console.wexledu.com/#/qr-signup?uuid=%s
  i18n:
    enJson: en.json
  switchOffExamRevision: true
  validImageExtension: png,jpg,jpeg
  switchOffGroupTest: true
  switchOffGroupMlp: true
  excludeOrgsForMlpAttendance: ach989608,act555,after-my-school,aks040,akshaya,amr977,anh802812,apple,ava818164,bad794,bas050, bas714,bha160192, bha263653,bha288990,bha730324,bha928576,bha999313,blu125,bse089,chanakya,chi141056,cpr268798,dro047408,dwsc, dwss,eksj,excellencia,exp829,fut400337,gac011886,gec,git864,gol117759,gow395,gpu387,ind252,ind547,info,inn146,ins208139,ist358,kak013165,kak116005,kak118073,kak209864,kak304488,kak305058,kak371958,kak499721,kak525229,kak532,kak602192,kak647655,kak690086,kak755936,kak775684,kak840064,kak866498,kak949307,kakatiya-nzmbd,kids,kir311,kit156449,kri053,kri082, kri109,kri168,kri197,kri363,kri370,kri445,kri506,kri557,kri652,kri663,kri786,kri796,kri841,kri856,kri870,kri925,krs114151,krs460202,krs754806,krs849588,kxt224,lea828363,lit761063,lot716,lotus,lotuslap,masterminds,mems,mlp564,nal068848,nal102147,nal238039,nal389146,nal548894,nal622890,nal785964,nal884925,nal919523,nal942970,new133,nil959630,oak355,oak499,obu242,orb319,oswaal,oxford,pcsc,pep054403,pep197034,pla948848,ppk546158,pptp,pro922891,pud263898,pudami,pul715753,radiant-tapovan,rai395,rai503,rav324353,rot797,rvm,sad250788,sai670,saie,sbs253077,sgic,siddhartha,sindhu,sri205,sri927,sshs,stf104,stm809476,tap256750,tes460,the749,tho193213,tho937044,tra610,uned,unicent,ush641943,van650316,ver613831,vid877736,vivekananda,wex658,wex908,wexl-academy,wexl-gitanjali,wexl-internal,wip697586,you882175
  msg91:
    templateId: 63ae9112d6fc0537dd1c01b3
    authkey:  386992AIQn3Fech63a01d0aP1
    sendOtp:   https://api.msg91.com/api/v5/otp?template_id=%s&mobile=%s&authkey=%s
    verifyOtp:  https://api.msg91.com/api/v5/otp/verify?otp=%s&authkey=%s&mobile=%s
    sendBulkSms: https://control.msg91.com/api/v5/flow/
    emailUrl: https://control.msg91.com/api/v5/email/send
    resultUrl: api/public/test-results/
  teacher:
    defaultSubjects: mathematics,english,evs,social,science,physics,Chemistry,biology
  batch:
    url: http://batch-service:8080/job
  #storageBucket: wexl-student-info - this is an important environment specific param
  tempPath: /tmp/
  device:
    single:
      organizations: rvm
    switch:
      organizations: vasavi
  latestAcademicYear: 24-25
  mobile:
    minimumVersion: 1.0.76
    notifications:
      wexlIconImage: https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/wexl_logo_5df21fe504.png
      newMlpNotification:
        notificationText: A New MLP has been created by the teacher!
        notificationTitle: New MLP Assigned
      newNotification:
        notificationText: A New %s has been created by the teacher!
        notificationTitle: New %s Assigned
  sql:
    getTeacherCurriculum: |
      select distinct ts.board_slug, s.grade_id, ts.subject_slug ,s.uuid as sectionUuid  from teacher_subjects ts
      inner join sections s on ts.section_id=s.id
      where ts.teacher_id= :teacherId  and s.deleted_At is null
    getSectionMlpsDetails: |
      select m.id as mlp_id, concat(m.title, '(' ,to_char(m.created_at,'Mon DD'),')') as title,
      count(inst.exam_id) as section_attendance from mlp m
      inner join mlp_inst inst on inst.mlp_id = m.id
      where m.section_id = :sectionId and cast(to_char(m.created_at,'MM') as int) = :month
      and m.subject_slug = :subjectSlug and m.subtopic_slug is not null
      group by m.id
    getMlpReportsByStudentId: |
      select mi.student_id as studentId, m.id as mlpId, concat(m.title, '(' ,to_char(m.created_at,'Mon DD'),')') as title ,
      case when mi.exam_id is not null then true else false end as status, mi.knowledge_percentage as percentageScored
      from mlp m
      left join mlp_inst mi on mi.mlp_id = m.id
      where m.id in (:mlpIds) and mi.student_id in (:studentIds)
      order by m.id asc
  mobileNumberLogin:
    users:
      limit: 4

management:
  endpoints:
    web:
      exposure:
        include: prometheus,health
  health:
    mail:
      enabled: false
jwt:
  tokenSecret: HHGDsdfdsfjk344fjs73jfh47984sdsdf4GSjskG273owyrbnsjrt46Ghdshdj29837HDjsdjhfdjh736567
  erpTokenSecret: HHGDsdfdsfjk344fjs73jayfh47984sdsdf4GSjsamkG273owyrbnsjrt46Ghdshdj29837HDjsdjhfdjh736567
  oauthTokenSecret: HHGDsdfdsfjk344fjs73jayfh47984sdsdf4GSjsamkG273owyrbnsjrt46Ghdshdj29837HDjsdjhfdjh736568
  validityInDays: 60
  disabledEmails: <EMAIL>,<EMAIL>

reCaptcha:
  key: 6LdlFfYZAAAAADMsGeEzAgx0nOx6-pA69QqsceF7
  secret: 6LdlFfYZAAAAAIJltTcfH_H-J23wDDqaymJcz9Vv
  api: https://www.google.com/recaptcha/api/siteverify

urls:
  content: http://bet-content-service:8080/content/

mobileApp:
  updatableApps:  com.wexledu.mobile.school.ngs,com.wexledu.mobile.school.neo_g,com.wexledu.mobile.school.mahajana,com.wexledu.mobile.school.ideal_jc,com.wexledu.mobile.school.kakatiya,com.wexledu.mobile.school.ista,com.wexledu.mobile.school.gis_learning,com.wexledu.mobile.school.francis_xaviers,com.wexledu.mobile.school.e_nestor,com.wexledu.mobile.school.vikram,com.wexledu.mobile.school.vaidavi,com.wexledu.mobile.school.srbs,com.wexledu.mobile.school.saint_conrads,com.wexledu.mobile.school.sai_school,com.wexledu.mobile.school.bpmg_public_school,com.wexledu.mobile.school.chanakya_highschool,com.wexledu.mobile.school.vs_publishers,com.wexledu.mobile.school.mgcv,com.wexledu.mobile.school