<configuration>
	<appender name="console"
		class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>%d{HH:mm:ss.SSS} | %-5level | %thread | %logger{1} | %m%n%rEx{full,
				java.lang.reflect.Method,
				org.apache.catalina,
				org.springframework.aop,
				org.springframework.security,
				org.springframework.transaction,
				org.springframework.web,
				sun.reflect,
				net.sf.cglib,
				ByCGLIB
				}</pattern>
			<charset>utf8</charset>
		</encoder>
	</appender>
	<!-- LOG everything at INFO level -->
	<root level="info">
		<appender-ref ref="console" />
	</root>

	<!-- LOG "com.wexl*" at TRACE level -->
	<logger name="com.wexl" level="info" additivity="false">
		<appender-ref ref="console" />
	</logger>

</configuration>


