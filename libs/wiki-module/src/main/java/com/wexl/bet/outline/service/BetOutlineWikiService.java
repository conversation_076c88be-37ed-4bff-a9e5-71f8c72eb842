package com.wexl.bet.outline.service;

import com.wexl.bet.outline.dto.BetOutlineWiki;
import com.wexl.bet.outline.dto.CollectionDto;
import com.wexl.bet.outline.dto.DocumentDto;
import com.wexl.bet.outline.dto.OutlineResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class BetOutlineWikiService {
  private final OutlineWikiService outlineWikiService;
  private final OutlineAttachmentUtil outlineAttachmentUtil;
  private final MarkdownProcessor markdownProcessor;

  @Value("${server.servlet.context-path:}")
  private String contextPath;

  /**
   * Creates a collection if it doesn't already exist.
   *
   * @param title The title of the collection
   * @return The collection, either existing or newly created
   */
  public OutlineResponse<CollectionDto.Collection> createCollectionIfNotExists(String title) {
    log.info("Creating collection if not exists: {}", title);

    if (title == null || title.trim().isEmpty()) {
      log.error("Cannot create collection with null or empty title");
      return OutlineResponse.error("Collection name is required");
    }

    // First, check if a collection with this name already exists
    OutlineResponse<CollectionDto.Collection> existingCollection =
        outlineWikiService.getCollectionByName(title);

    if (existingCollection.ok() && existingCollection.data() != null) {
      log.info("Collection already exists with ID: {}", existingCollection.data().id());
      return existingCollection;
    }

    // If not found, create a new collection
    log.info("Collection not found, creating new collection: {}", title);
    CollectionDto.CreateRequest createRequest =
        new CollectionDto.CreateRequest(
            title,
            "", // description
            "#123123", // color
            "bookmark", // icon
            "read_write", // permission
            false // sharing
            );

    return outlineWikiService.createCollection(createRequest);
  }

  /**
   * Creates a document in a collection if it doesn't already exist.
   *
   * @param title The title of the document
   * @param collectionUuid The UUID of the collection
   * @return The document, either existing or newly created
   */
  public OutlineResponse<DocumentDto.Response> createDocumentIfNotExists(
      String title, String collectionUuid) {
    log.info("Creating document if not exists: {} in collection: {}", title, collectionUuid);

    // First, search for a document with the provided title in collection
    OutlineResponse<List<DocumentDto.NavigationNode>> documentsResponse =
        outlineWikiService.getDocumentsInCollection(collectionUuid);

    if (documentsResponse.ok() && documentsResponse.data() != null) {
      // Search for a document with the matching title
      Optional<DocumentDto.NavigationNode> existingDocument =
          findDocumentByTitle(documentsResponse.data(), title);

      if (existingDocument.isPresent()) {
        log.info("Document already exists with ID: {}", existingDocument.get().id());
        // Get the details of the document
        return outlineWikiService.getDocumentInfoByUuid(existingDocument.get().id());
      }
    }

    // If not found, create a new document
    log.info(
        "Document not found, creating new document: {} in collection: {}", title, collectionUuid);
    DocumentDto.CreateRequest createRequest = new DocumentDto.CreateRequest(title, collectionUuid);

    return outlineWikiService.createDocument(createRequest);
  }

  /**
   * Creates a child document under a parent document.
   *
   * @param title The title of the child document
   * @param parentDocumentUuid The UUID of the parent document
   * @param collectionUuid The UUID of the collection
   * @return The newly created child document
   */
  public OutlineResponse<DocumentDto.Response> createChildDocumentUnderParent(
      String title, String parentDocumentUuid, String collectionUuid) {
    log.info(
        "Creating child document: {} under parent: {} in collection: {}",
        title,
        parentDocumentUuid,
        collectionUuid);

    // First, verify that the parent document exists
    OutlineResponse<DocumentDto.Response> parentDocumentResponse =
        outlineWikiService.getDocumentInfoByUuid(parentDocumentUuid);

    if (!parentDocumentResponse.ok() || parentDocumentResponse.data() == null) {
      log.error("Parent document not found with UUID: {}", parentDocumentUuid);
      return OutlineResponse.error("Parent document not found with UUID: " + parentDocumentUuid);
    }

    // If parent document exists, create a child document
    log.info("Parent document found, creating child document: {}", title);
    DocumentDto.CreateRequest createRequest =
        new DocumentDto.CreateRequest(title, collectionUuid, parentDocumentUuid);

    return outlineWikiService.createDocument(createRequest);
  }

  /**
   * Helper method to find a document by title in a list of navigation nodes.
   *
   * @param nodes The list of navigation nodes
   * @param title The title to search for
   * @return The document if found, otherwise empty
   */
  private Optional<DocumentDto.NavigationNode> findDocumentByTitle(
      List<DocumentDto.NavigationNode> nodes, String title) {
    for (DocumentDto.NavigationNode node : nodes) {
      // Check if this node matches
      if (title.equals(node.title())) {
        return Optional.of(node);
      }

      // Check children recursively if this node has children
      if (node.children() != null && !node.children().isEmpty()) {
        Optional<DocumentDto.NavigationNode> childMatch =
            findDocumentByTitle(node.children(), title);
        if (childMatch.isPresent()) {
          return childMatch;
        }
      }
    }

    return Optional.empty();
  }

  public DocumentDto.WikiDocumentResponse exploreMarkDown(
      BetOutlineWiki.GetDocumentRequest request) {
    OutlineResponse<DocumentDto.DocumentExportResponse> documentExportResponseOutlineResponse =
        outlineWikiService.exportDocumentInfoByUuid(request.documentId());
    if (documentExportResponseOutlineResponse.ok()) {
      DocumentDto.DocumentExportResponse documentExportResponse =
          documentExportResponseOutlineResponse.data();
      if (documentExportResponse != null) {
        String data = documentExportResponse.data();
        String processedMarkdown = markdownProcessor.processMarkdown(data);
        Map<String, Object> response =
            markdownProcessor.createAngularFriendlyResponse(processedMarkdown);
        return DocumentDto.WikiDocumentResponse.builder()
            .documentId(request.documentId())
            .data(response.get("content"))
            .build();

      } else {
        log.info("Document export response is null");
      }
    } else {
      log.info("Error exporting document: " + documentExportResponseOutlineResponse.error());
    }
    return null;
  }

  /**
   * Process markdown from a file and return it in a format friendly for Angular to display. This
   * method extracts attachment links and replaces them with actual URLs.
   *
   * @return A map containing the processed markdown, ready for Angular to display
   */
  public Map<String, Object> exploreMarkDownFromFile() {
    String markDown =
        """
            # 01. First Day at Work

            ![Person meeting the team on day](/api/attachments.redirect?id=f0b79b5b-ecf5-474e-aa06-8d5f1c80c285 "left-50")

            # This is a big heading

            Reusable components in Figma allow you to create consistent, scalable designs while improving efficiency. This tutorial will guide you through creating and using components effectively.


            ## **Step 1: Understanding Components in Figma**

            * A **component** is a reusable design element that maintains consistency.
            * **Instances** are copies of a component that inherit properties from the main component.
            * Changes made to the main component update all its instances.

            ## **Step 2: Creating a Component**

            By following these steps, you can create effective, reusable components that improve your design workflow and ensure consistency across projects!""";

    log.info("Processing markdown from file");

    String processedMarkdown = markdownProcessor.processMarkdown(markDown);

    // Create an Angular-friendly response
    return markdownProcessor.createAngularFriendlyResponse(processedMarkdown);
  }

  /**
   * Retrieves an attachment from Outline API and returns it as a response entity. This method acts
   * as a proxy for the Outline API's attachment endpoint. It includes cache-control headers for
   * better performance.
   *
   * <p>If the attachment is found in the cache, it will return a redirect to the public URL.
   * Otherwise, it will retrieve the attachment from Outline API, upload it to a public S3 bucket,
   * cache the URL, and return a redirect to the public URL.
   *
   * @param attachmentId The ID of the attachment to retrieve
   * @return A response entity containing the attachment content or a redirect to the public URL
   */
  public ResponseEntity<String> retrieveAttachment(String attachmentId) throws IOException {
    log.info("Retrieving attachment with ID: {}", attachmentId);

    if (attachmentId == null || attachmentId.trim().isEmpty()) {
      throw new IllegalArgumentException("Attachment ID cannot be null or empty");
    }

    try {
      // Call the OutlineWikiService to retrieve the attachment
      String outlineResponse = outlineAttachmentUtil.retrieveAndSaveAttachment(attachmentId);

      return ResponseEntity.ok().body(outlineResponse);
    } catch (Exception e) {
      log.error("Error retrieving attachment: {}", e.getMessage(), e);
      throw e;
    }
  }
}
