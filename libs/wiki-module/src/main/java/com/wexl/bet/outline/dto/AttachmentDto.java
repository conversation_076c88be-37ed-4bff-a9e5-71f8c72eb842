package com.wexl.bet.outline.dto;

/** Data transfer objects for Outline API attachment operations. */
public class AttachmentDto {

  /** Request for the attachments.redirect endpoint. */
  public record RedirectRequest(String id) {
    /**
     * Creates a new redirect request.
     *
     * @param id The ID of the attachment
     */
    public RedirectRequest {
      if (id == null || id.trim().isEmpty()) {
        throw new IllegalArgumentException("Attachment ID cannot be null or empty");
      }
    }
  }

  /**
   * Response from the attachments.redirect endpoint. Since this endpoint returns a 302 redirect,
   * we'll store the URL.
   */
  public record RedirectResponse(String url) {
    /**
     * Creates a new redirect response.
     *
     * @param url The URL of the attachment
     */
    public RedirectResponse {
      if (url == null || url.trim().isEmpty()) {
        throw new IllegalArgumentException("Attachment URL cannot be null or empty");
      }
    }
  }
}
