package com.wexl.bet.outline.service;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/** Service for processing markdown content. */
@Slf4j
@Service
@RequiredArgsConstructor
public class MarkdownProcessor {
  private final OutlineAttachmentUtil outlineAttachmentUtil;

  // Pattern to match links with format /api/attachments.redirect?id=<id>
  private static final Pattern ATTACHMENT_PATTERN =
      Pattern.compile("/api/attachments?\\.redirect\\?id=([a-zA-Z0-9-]+)");

  /**
   * Process markdown content to make it Angular-friendly. This includes replacing attachment links
   * with actual URLs.
   *
   * @param markdown The markdown content to process
   * @return The processed markdown content
   */
  public String processMarkdown(String markdown) {
    if (markdown == null || markdown.isEmpty()) {
      return "";
    }

    // Extract all attachment IDs and get their URLs
    Map<String, String> attachmentUrls = extractAndFetchAttachmentUrls(markdown);

    // Replace all attachment links with their actual URLs
    return replaceAttachmentLinks(markdown, attachmentUrls);
  }

  /**
   * Extract attachment IDs from markdown and construct URLs to our proxy endpoint.
   *
   * @param markdown The markdown content
   * @return A map of attachment IDs to their URLs
   */
  private Map<String, String> extractAndFetchAttachmentUrls(String markdown) {
    Map<String, String> attachmentUrls = new HashMap<>();
    Matcher matcher = ATTACHMENT_PATTERN.matcher(markdown);

    while (matcher.find()) {
      String attachmentId = matcher.group(1);
      if (!attachmentUrls.containsKey(attachmentId)) {
        try {
          // Construct URL to our proxy endpoint
          String proxyUrl = outlineAttachmentUtil.retrieveAndSaveAttachment(attachmentId);
          attachmentUrls.put(attachmentId, proxyUrl);
          log.debug("Created proxy URL for attachment {}: {}", attachmentId, proxyUrl);
        } catch (Exception e) {
          log.error(
              "Failed to create proxy URL for attachment {}: {}", attachmentId, e.getMessage());
          // Use a placeholder URL for failed attachments
          attachmentUrls.put(attachmentId, "#broken-attachment-" + attachmentId);
        }
      }
    }

    return attachmentUrls;
  }

  /**
   * Replace attachment links in markdown with their actual URLs.
   *
   * @param markdown The markdown content
   * @param attachmentUrls A map of attachment IDs to their URLs
   * @return The markdown with attachment links replaced
   */
  private String replaceAttachmentLinks(String markdown, Map<String, String> attachmentUrls) {
    if (attachmentUrls.isEmpty()) {
      return markdown;
    }

    StringBuffer result = new StringBuffer();
    Matcher matcher = ATTACHMENT_PATTERN.matcher(markdown);

    while (matcher.find()) {
      String attachmentId = matcher.group(1);
      String replacementUrl =
          attachmentUrls.getOrDefault(attachmentId, "#missing-attachment-" + attachmentId);

      // Replace the attachment link with the actual URL
      // Need to escape any special regex characters in the replacement string
      String escapedReplacement = Matcher.quoteReplacement(replacementUrl);
      matcher.appendReplacement(result, escapedReplacement);
    }

    matcher.appendTail(result);
    return result.toString();
  }

  /**
   * Create a response object that is friendly for Angular to display.
   *
   * @param markdown The processed markdown content
   * @return A map containing the processed markdown
   */
  public Map<String, Object> createAngularFriendlyResponse(String markdown) {
    Map<String, Object> response = new HashMap<>();
    response.put("content", markdown);
    response.put("success", true);
    return response;
  }
}
