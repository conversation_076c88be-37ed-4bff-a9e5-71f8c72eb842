package com.wexl.bet.outline.config;

import java.time.Duration;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/** Configuration for the Outline API client. */
@Configuration
public class OutlineApiConfig {

  /**
   * Creates a RestTemplate bean for making HTTP requests to the Outline API.
   *
   * @param builder The RestTemplateBuilder
   * @return The configured RestTemplate
   */
  @Bean
  public RestTemplate outlineRestTemplate(RestTemplateBuilder builder) {
    return builder
        .setConnectTimeout(Duration.ofSeconds(10))
        .setReadTimeout(Duration.ofSeconds(30))
        .build();
  }
}
