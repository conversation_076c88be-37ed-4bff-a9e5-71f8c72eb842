package com.wexl.bet.outline.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.time.ZonedDateTime;
import java.util.List;
import lombok.Builder;

/** DTO for Outline Document objects. */
public class DocumentDto {

  /** Request object for getting a document by ID. */
  public record InfoRequest(String id) {}

  /** Request object for creating a document. */
  public record CreateRequest(
      String title,
      String text,
      String collectionId,
      String parentDocumentId,
      Boolean template,
      Boolean publish) {

    /** Constructor with required fields only. */
    public CreateRequest(String title, String collectionId) {
      this(title, null, collectionId, null, null, true);
    }

    /** Constructor with parent document ID. */
    public CreateRequest(String title, String collectionId, String parentDocumentId) {
      this(title, null, collectionId, parentDocumentId, null, true);
    }
  }

  /** Navigation node for document structure. */
  @JsonIgnoreProperties(ignoreUnknown = true)
  public record NavigationNode(
      String id, String title, String url, List<NavigationNode> children) {}

  /** Response object for document operations. */
  @JsonIgnoreProperties(ignoreUnknown = true)
  public record Response(
      String id,
      String urlId,
      String title,
      String text,
      String emoji,
      String collectionId,
      String parentDocumentId,
      Boolean fullWidth,
      Boolean pinned,
      Boolean template,
      Integer revision,
      ZonedDateTime createdAt,
      ZonedDateTime updatedAt,
      ZonedDateTime publishedAt) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record DocumentExportResponse(String data, String status, String ok) {
    // Constructor to handle direct string value (markdown content)
    public DocumentExportResponse(String content) {
      this(content, null, null);
    }
  }

  @Builder
  public record WikiDocumentResponse(String documentId, Object data) {}
}
