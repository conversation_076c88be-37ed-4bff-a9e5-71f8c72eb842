package com.wexl.bet.outline.dto;

/**
 * Base response class for Outline API responses. All Outline API responses include an "ok" field
 * indicating success or failure.
 */
public record OutlineResponse<T>(boolean ok, T data, String error) {
  /**
   * Creates a successful response with data.
   *
   * @param data The response data
   * @param <T> The type of the response data
   * @return A successful response
   */
  public static <T> OutlineResponse<T> success(T data) {
    return new OutlineResponse<>(true, data, null);
  }

  /**
   * Creates an error response.
   *
   * @param error The error message
   * @param <T> The type of the response data
   * @return An error response
   */
  public static <T> OutlineResponse<T> error(String error) {
    return new OutlineResponse<>(false, null, error);
  }
}
