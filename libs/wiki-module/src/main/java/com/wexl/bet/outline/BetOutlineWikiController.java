package com.wexl.bet.outline;

import com.wexl.bet.outline.dto.BetOutlineWiki;
import com.wexl.bet.outline.dto.CollectionDto;
import com.wexl.bet.outline.service.BetOutlineWikiService;
import java.io.IOException;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class BetOutlineWikiController {
  private final BetOutlineWikiService betOutlineWikiService;

  @PostMapping("/public/outline/wiki/collection")
  public CollectionDto.Collection createCollection(
      @RequestBody BetOutlineWiki.CreateCollectionRequest request) {
    return betOutlineWikiService.createCollectionIfNotExists(request.name()).data();
  }

  @PostMapping("/public/outline/wiki/document")
  public void exploreMarkDown(@RequestBody BetOutlineWiki.GetDocumentRequest request) {
    betOutlineWikiService.exploreMarkDown(request);
  }

  /**
   * Process markdown from a file and return it in a format friendly for Angular to display. This
   * endpoint extracts attachment links and replaces them with actual URLs.
   *
   * @return A map containing the processed markdown, ready for Angular to display
   */
  @PostMapping("/public/outline/wiki/markdown")
  public Map<String, Object> exploreMarkDownFromFile() {
    return betOutlineWikiService.exploreMarkDownFromFile();
  }

  /**
   * Retrieves an attachment from Outline API and returns it as a response entity. This endpoint
   * acts as a proxy for the Outline API's attachment endpoint. It includes cache-control headers
   * for better performance.
   *
   * @param attachmentId The ID of the attachment to retrieve
   * @return A response entity containing the attachment content
   */
  @GetMapping("/public/outline/wiki/attachment/{attachmentId}")
  public ResponseEntity<String> retrieveAttachment(@PathVariable String attachmentId)
      throws IOException {
    return betOutlineWikiService.retrieveAttachment(attachmentId);
  }
}
