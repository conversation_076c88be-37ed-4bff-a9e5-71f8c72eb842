package com.wexl.bet.outline.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.time.ZonedDateTime;
import lombok.Builder;
import org.springframework.lang.Nullable;

/** DTO for Outline Collection objects. */
public class CollectionDto {

  /** Sort configuration for collections */
  public record Sort(String field, String direction) {}

  /** User information for archived collections */
  public record ArchivedByUser(
      String id,
      String name,
      String avatarUrl,
      String email,
      String role,
      Boolean isSuspended,
      ZonedDateTime lastActiveAt,
      ZonedDateTime createdAt) {}

  @Builder
  public record CreateRequest(
      String name,
      String description,
      String color,
      String icon,
      String permission,
      Boolean sharing) {}

  /** Request object for getting a collection by ID. */
  public record InfoRequest(String id) {}

  /** Request object for getting a collection by name. */
  public record ByNameRequest(String name) {}

  /** Response object for collection operations. */
  @JsonIgnoreProperties(ignoreUnknown = true)
  public record Collection(
      String id,
      String urlId,
      String name,
      String description,
      Sort sort,
      String index,
      String color,
      String icon,
      String permission,
      Boolean sharing,
      ZonedDateTime createdAt,
      ZonedDateTime updatedAt,
      @Nullable ZonedDateTime deletedAt,
      @Nullable ZonedDateTime archivedAt,
      @Nullable ArchivedByUser archivedBy) {}
}
