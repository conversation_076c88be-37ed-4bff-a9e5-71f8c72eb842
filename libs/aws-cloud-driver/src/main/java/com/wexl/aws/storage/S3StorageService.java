package com.wexl.aws.storage;

import com.amazonaws.HttpMethod;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.Headers;
import com.amazonaws.services.s3.model.*;
import com.amazonaws.services.s3.transfer.MultipleFileUpload;
import com.amazonaws.services.s3.transfer.TransferManager;
import com.amazonaws.services.s3.transfer.TransferManagerBuilder;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.storage.StorageService;
import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.concurrent.Executors;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.filefilter.TrueFileFilter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamSource;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
public class S3StorageService implements StorageService {

  private static final long EXPIRY_TIME = (long) 1000 * 60 * 60;

  @Value("${app.storageBucket}")
  private String s3BucketName;

  @Value("${app.storage}")
  private String storageBuket;

  @Value("${app.tempPath}")
  private String tempPath;

  @Value("${app.storageKey.accessKey}")
  private String accessKey;

  @Value("${app.storageKey.secretKey}")
  private String secretKey;

  @Value("${app.storageKey.endpoint}")
  private String endpoint;

  @Value("${app.storageKey.region}")
  private String region;

  public String generatePreSignedUrlForFetch(String objKey) {
    var preSignedUrlRequest =
        new GeneratePresignedUrlRequest(s3BucketName, objKey)
            .withMethod(HttpMethod.GET)
            .withExpiration(getExpiration(EXPIRY_TIME));
    return generatePreSignedUrl(preSignedUrlRequest);
  }

  public String generatePreSignedUrlForFetchWithMaxExpiry(String objKey, long days) {
    var preSignedUrlRequest =
        new GeneratePresignedUrlRequest(storageBuket, objKey)
            .withMethod(HttpMethod.GET)
            .withExpiration(getExpiration(1000 * 60 * 60 * 24 * days));
    return generatePreSignedUrl(preSignedUrlRequest);
  }

  @Override
  public String generatePreSignedUrlForUpload(
      String bucketName, String objKey, Map<String, String> metadata) {
    final GeneratePresignedUrlRequest preSignedUrlRequest =
        getGeneratePresignedUrlRequest(bucketName, objKey);
    metadata.forEach(
        (key, value) ->
            preSignedUrlRequest.addRequestParameter(Headers.S3_USER_METADATA_PREFIX + key, value));

    return generatePreSignedUrl(preSignedUrlRequest);
  }

  @Override
  public String generatePreSignedUrlForUpload(String objKey, Map<String, String> metadata) {
    return generatePreSignedUrlForUpload(s3BucketName, objKey, metadata);
  }

  @Override
  public String generatePreSignedUrlForUpload(String objKey) {
    final GeneratePresignedUrlRequest preSignedUrlRequest =
        getGeneratePresignedUrlRequest(s3BucketName, objKey);
    return generatePreSignedUrl(preSignedUrlRequest);
  }

  private GeneratePresignedUrlRequest getGeneratePresignedUrlRequest(
      String s3BucketName, String objKey) {
    return new GeneratePresignedUrlRequest(s3BucketName, objKey)
        .withMethod(HttpMethod.PUT)
        .withExpiration(getExpiration(EXPIRY_TIME));
  }

  private static String guessContentType(String objKey) {
    try {
      return Files.probeContentType(Path.of(objKey));
    } catch (Exception e) {
      return MediaType.TEXT_PLAIN_VALUE;
    }
  }

  public boolean isFileAvailable(String objKey) {
    return getS3Client().doesObjectExist(s3BucketName, objKey);
  }

  public boolean isFileAvailableInBucket(String objKey, String bucketName) {
    return getS3Client().doesObjectExist(bucketName, objKey);
  }

  private Date getExpiration(long expiryTime) {
    final var expiration = new java.util.Date();
    long expTimeMillis = expiration.getTime();
    expTimeMillis += expiryTime;
    expiration.setTime(expTimeMillis);
    return expiration;
  }

  private String generatePreSignedUrl(GeneratePresignedUrlRequest preSignedUrlRequest) {
    final URL url = getS3Client().generatePresignedUrl(preSignedUrlRequest);
    return url.toString();
  }

  @Deprecated(forRemoval = true)
  public void uploadFile(InputStreamSource inputStreamSource, String filePath) {
    try {
      if (!(inputStreamSource instanceof MultipartFile)) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.File.Uploaded");
      }
      MultipartFile multipartFile = (MultipartFile) inputStreamSource;
      if (multipartFile.isEmpty()) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.File.Uploaded");
      }

      var metadata = new ObjectMetadata();
      metadata.setContentLength(multipartFile.getSize());
      metadata.setContentType(multipartFile.getContentType());

      getS3Client().putObject(s3BucketName, filePath, multipartFile.getInputStream(), metadata);
    } catch (ApiException e) {
      throw e;
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.CouldntProcessRequest");
    }
  }

  /**
   * @param inputStream The input stream containing the data to be uploaded to Amazon S3.
   * @param filePath FilePath under which to store the specified file in S3 bucket.
   * @param size Size of the file that is being uploaded to S3 bucket.
   * @deprecated By {@link #generatePreSignedUrlForUpload(String)}
   *     <p>will upload file directly from client machine to S3 bucket instead of routing it through
   *     retail-service by providing S3PresignedUrl of <code>PUT</code> operation.
   */
  @SneakyThrows
  @Deprecated(forRemoval = true)
  public void uploadFile(InputStream inputStream, String filePath, int size) {
    var metadata = new ObjectMetadata();
    metadata.setContentLength(size);
    metadata.setContentType(MediaType.APPLICATION_PDF_VALUE);
    getS3Client().putObject(s3BucketName, filePath, inputStream, metadata);
  }

  public InputStream getInputStream(String filePath) {
    try {
      S3Object s3Object = getS3Client().getObject(s3BucketName, filePath);
      return s3Object.getObjectContent();
    } catch (Exception ex) {
      log.error(
          "The file [" + filePath + "] is not available in bucket [" + s3BucketName + "].", ex);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.File.Available", ex);
    }
  }

  private AmazonS3 getS3Client() {
    BasicAWSCredentials wasabiCreds = new BasicAWSCredentials(accessKey, secretKey);

    return AmazonS3ClientBuilder.standard()
        // Specify the Wasabi endpoint and region
        .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(endpoint, region))
        // Provide credentials
        .withCredentials(new AWSStaticCredentialsProvider(wasabiCreds))
        // Enable path-style access (important for Wasabi)
        .withPathStyleAccessEnabled(true)
        .build();
  }

  public File downloadFile(String key, String bucketName) throws IOException {
    var s3object = getS3Client().getObject(bucketName, key);
    S3ObjectInputStream inputStream = s3object.getObjectContent();
    File downloadedS3Object = new File(tempPath + key);
    FileUtils.deleteQuietly(downloadedS3Object);
    FileUtils.copyInputStreamToFile(inputStream, downloadedS3Object);
    return downloadedS3Object;
  }

  public <T> T downloadFile(String key, Class<T> cls) throws IOException {
    final AmazonS3 s3Client = getS3Client();
    var isFileExist = s3Client.doesObjectExist(s3BucketName, key);
    String bucketName = isFileExist ? s3BucketName : getProdBucketName(s3BucketName);

    S3Object s3object = s3Client.getObject(bucketName, key);
    S3ObjectInputStream inputStream = s3object.getObjectContent();
    ObjectMapper mapper = new ObjectMapper();
    return mapper.readValue(inputStream, cls);
  }

  private String getProdBucketName(String s3BucketName) {
    // little hack for making it work in non-prod
    if ("wexl-student-info-wasabi-nonprod".equals(s3BucketName)) {
      return "wexl-student-info";
    }
    if ("wexledu-wasabi-nonprod".equals(s3BucketName)) {
      return "wexledu";
    }
    return s3BucketName;
  }

  private String generateS3Key(String srcDir, String fileCanonicalPath) {
    if (srcDir != null
        && fileCanonicalPath != null
        && fileCanonicalPath.startsWith(srcDir)
        && fileCanonicalPath.length() >= srcDir.length()) {
      return fileCanonicalPath.substring(srcDir.length());
    }

    return "";
  }

  private String guessContentType(File srcFile) {
    FileNameMap fileNameMap = URLConnection.getFileNameMap();
    return fileNameMap.getContentTypeFor(srcFile.getName());
  }

  private void writeFileToS3(String bucketName, String contentType, String key, byte[] array) {
    var metadata = new ObjectMetadata();
    metadata.setContentLength(array.length);
    metadata.setContentType(contentType);
    PutObjectRequest putObjectRequest =
        new PutObjectRequest(
            bucketName, key.replace("\\", "/"), new ByteArrayInputStream(array), metadata);

    getS3Client().putObject(putObjectRequest);
  }

  public void uploadFile(byte[] content, String filePath, String contentType) {
    if (content.length == 0) {
      return;
    }
    var metadata = new ObjectMetadata();
    metadata.setContentType(contentType);
    metadata.setContentLength(content.length);
    getS3Client()
        .putObject(
            s3BucketName,
            filePath,
            new BufferedInputStream(new ByteArrayInputStream(content)),
            metadata);
  }

  public void writeFile(
      String bucketName, String contentType, String key, byte[] array, boolean makePublic) {
    var metadata = new ObjectMetadata();
    metadata.setContentLength(array.length);
    metadata.setContentType(contentType);
    PutObjectRequest putObjectRequest =
        new PutObjectRequest(
            bucketName, key.replace("\\", "/"), new ByteArrayInputStream(array), metadata);
    if (makePublic) {
      putObjectRequest = putObjectRequest.withCannedAcl(CannedAccessControlList.PublicRead);
    }

    getS3Client().putObject(putObjectRequest);
  }

  @Override
  public String generatePreSignedUrlForFetch(String objKey, String s3BucketName) {
    var preSignedUrlRequest =
        new GeneratePresignedUrlRequest(s3BucketName, objKey)
            .withMethod(HttpMethod.GET)
            .withExpiration(getExpiration(EXPIRY_TIME));
    return generatePreSignedUrl(preSignedUrlRequest);
  }

  public void uploadDirectory(String bucketName, File srcDir, String keyPrefix) {
    if (!srcDir.isDirectory()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.directory.file");
    }
    Collection<File> srcFiles =
        FileUtils.listFiles(srcDir, TrueFileFilter.INSTANCE, TrueFileFilter.INSTANCE);
    srcFiles.forEach(
        srcFile -> {
          try {
            writeFileToS3(
                bucketName,
                guessContentType(srcFile),
                keyPrefix.formatted(
                    generateS3Key(srcDir.getCanonicalPath(), srcFile.getCanonicalPath())),
                Files.readAllBytes(srcFile.toPath()));

          } catch (Exception ex) {
            log.error("Unable to copy a specific file", ex);
          }
        });
  }

  @Override
  public void copyFile(
      String sourceBucket, String sourceKey, String destinationBucket, String destinationKey) {
    try {
      CopyObjectRequest copyRequest =
          new CopyObjectRequest(sourceBucket, sourceKey, destinationBucket, destinationKey);
      getS3Client().copyObject(copyRequest);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.CouldntProcessRequest");
    }
  }

  @Override
  public void uploadDirectoryInBulk(File srcDir, String keyPrefix) {

    TransferManager trMgr =
        TransferManagerBuilder.standard()
            .withS3Client(getS3Client())
            .withExecutorFactory(() -> Executors.newFixedThreadPool(10))
            .build();
    try {
      MultipleFileUpload xfer = trMgr.uploadDirectory(s3BucketName, keyPrefix, srcDir, true);
      xfer.waitForCompletion();
      if (xfer.isDone()) {
        log.info("S3 batch upload is Done!");
      }
    } catch (Exception exp) {
      log.error(exp.getMessage(), exp);
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "Failed to  upload files into s3");
    } finally {
      trMgr.shutdownNow();
    }
  }

  @Override
  public void uploadFile(
      String bucketName, String contentType, String key, byte[] array, boolean makePublic) {
    var metadata = new ObjectMetadata();
    metadata.setContentLength(array.length);
    metadata.setContentType(contentType);
    PutObjectRequest putObjectRequest =
        new PutObjectRequest(
            bucketName, key.replace("\\", "/"), new ByteArrayInputStream(array), metadata);
    if (makePublic) {
      putObjectRequest = putObjectRequest.withCannedAcl(CannedAccessControlList.PublicRead);
    }

    getS3Client().putObject(putObjectRequest);
  }
}
