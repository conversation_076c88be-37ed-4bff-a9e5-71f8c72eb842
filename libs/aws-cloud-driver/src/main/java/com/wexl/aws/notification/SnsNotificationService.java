package com.wexl.aws.notification;

import com.wexl.retail.notification.NotificationService;
import com.wexl.retail.notification.dto.TestRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class SnsNotificationService implements NotificationService {

  private final ApplicationEventPublisher eventPublisher;

  public void notifyTestCompletion(TestRequest testRequest) {
    eventPublisher.publishEvent(new TestRequestEvent(testRequest));
  }
}
