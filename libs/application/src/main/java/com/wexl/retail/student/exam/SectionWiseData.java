package com.wexl.retail.student.exam;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

@Builder
public record SectionWiseData() {

  @Builder
  public record ExamAttributes(
      @JsonProperty("exam_attributes") List<SectionWiseTestData> sectionWiseTestData) {}

  @Builder
  public record SectionWiseTestData(
      @JsonProperty("section_id") Long sectionId,
      @JsonProperty("section_name") String sectionName,
      @JsonProperty("no_of_questions") Long noOfQuestions,
      @JsonProperty("total_marks") Float totalMarks,
      @JsonProperty("marks_scored") Float marksScored) {}
}
