package com.wexl.retail.student.attributes.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.model.Student;
import com.wexl.retail.relationship.StudentRelationshipModel;
import com.wexl.retail.relationship.repository.StudentRelationshipRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.student.attributes.dto.StudentAttributeDto;
import com.wexl.retail.student.attributes.model.StudentAttributeDefinitionModel;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.student.attributes.repository.StudentAttributeDefinitionRepository;
import com.wexl.retail.student.attributes.repository.StudentAttributeValueRepository;
import com.wexl.retail.util.ValidationUtils;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class StudentAttributeService {
  private final StudentAttributeDefinitionRepository studentAttributeDefinitionRepository;
  private final StudentAttributeValueRepository studentAttributeValueRepository;
  private final ValidationUtils validationUtils;
  private final StudentRepository studentRepository;
  private final StudentRelationshipRepository studentRelationshipRepository;

  public Map<String, String> getStudentAttributes(String studentAuthId, String academicYear) {
    var user = validationUtils.isValidUser(studentAuthId);
    var student = user.getStudentInfo();
    List<StudentAttributeValueModel> studentAttributeValue;
    if (academicYear != null) {
      if (student.getAcademicYearSlug().equals(academicYear)) {
        studentAttributeValue = studentAttributeValueRepository.findAllByStudentId(student.getId());
      } else {
        if (student.getPrevStudentId() != null) {
          studentAttributeValue =
              studentAttributeValueRepository.findAllByStudentId(student.getPrevStudentId());
        } else {
          throw new ApiException(
              InternalErrorCodes.INVALID_REQUEST, "error.StudentFind.AcademicYear");
        }
      }
    } else {
      studentAttributeValue = studentAttributeValueRepository.findAllByStudentId(student.getId());
    }

    Map<String, String> attributes = new HashMap<>();
    studentAttributeValue.forEach(
        value -> attributes.put(value.getAttributeDefinition().getName(), value.getValue()));

    return attributes;
  }

  public void saveStudentDefinitionAttributes(
      String studentAuthId, String orgSlug, StudentAttributeDto.Request request) {

    var user = validationUtils.isValidUser(studentAuthId);
    var student = studentRepository.findByUserId(user.getId());
    var attributes = request.attributes();
    var studentAttributeDefinition = studentAttributeDefinitionRepository.findAllByOrgSlug(orgSlug);
    if (studentAttributeDefinition.isEmpty()) {
      attributes.forEach(
          (key, value) -> saveNewStudentAttributeDefinition(orgSlug, key, student, value));
      return;
    }
    updateStudentAttributeDefinition(attributes, studentAttributeDefinition, orgSlug, student);
  }

  public void saveNewStudentAttributeDefinition(
      String orgSlug, String key, Student student, String instName) {
    var newStudentAttributeDefinition =
        studentAttributeDefinitionRepository.save(buildStudentAttributeDefinition(orgSlug, key));
    saveNewStudentAttributeValue(newStudentAttributeDefinition, student, instName);
  }

  private void updateStudentAttributeDefinition(
      Map<String, String> attributes,
      List<StudentAttributeDefinitionModel> studentAttributeDefinitionModel,
      String orgSlug,
      Student student) {
    attributes.forEach(
        (key, value) -> {
          var optionalStudentAttributeDefinition =
              studentAttributeDefinitionModel.stream()
                  .filter(x -> x.getName().equals(key))
                  .findFirst();
          if (optionalStudentAttributeDefinition.isEmpty()) {
            saveNewStudentAttributeDefinition(orgSlug, key, student, value);
            return;
          }
          updateStudentAttributeValue(student, optionalStudentAttributeDefinition.get(), value);
        });
  }

  public void updateStudentAttributeValue(
      Student student, StudentAttributeDefinitionModel definition, String instName) {
    var studentAttributeValue =
        studentAttributeValueRepository.findByStudentAndAttributeDefinitionId(
            student, definition.getId());

    if (studentAttributeValue.isEmpty()) {
      saveNewStudentAttributeValue(definition, student, instName);
      return;
    }
    studentAttributeValue.get().setValue(instName);
    studentAttributeValueRepository.save(studentAttributeValue.get());
  }

  public void saveNewStudentAttributeValue(
      StudentAttributeDefinitionModel attributeDefinitionId, Student student, String value) {
    studentAttributeValueRepository.save(
        buildStudentAttributeValue(attributeDefinitionId, student, value));
  }

  private StudentAttributeValueModel buildStudentAttributeValue(
      StudentAttributeDefinitionModel attributeDefinition, Student student, String value) {
    return StudentAttributeValueModel.builder()
        .student(student)
        .value(value)
        .attributeDefinition(attributeDefinition)
        .build();
  }

  public String getDateOfBirthFormat(
      Optional<StudentAttributeValueModel> studentAttributeValueModel) {
    DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");

    var dateOfBirth =
        studentAttributeValueModel.map(StudentAttributeValueModel::getValue).orElse(null);
    try {
      if (Objects.nonNull(dateOfBirth)) {
        return LocalDate.parse(dateOfBirth, inputFormatter).format(outputFormatter);
      }
      return dateOfBirth;
    } catch (Exception e) {
      log.error("Failed to parse date of birth {}", dateOfBirth, e);
      return dateOfBirth;
    }
  }

  private StudentAttributeDefinitionModel buildStudentAttributeDefinition(
      String orgSlug, String key) {
    return StudentAttributeDefinitionModel.builder().name(key).orgSlug(orgSlug).build();
  }

  public List<GenericMetricResponse> getStudentProfile(String orgSlug) {
    var students = studentRepository.findByOrgSlug(orgSlug);
    var filterStudents =
        students.stream().filter(student -> student.getUserInfo() != null).toList();
    var studentIds = filterStudents.stream().map(Student::getId).toList();
    var allStudentAttributes = studentAttributeValueRepository.findAllByStudentIds(studentIds);
    return buildResponse(filterStudents, allStudentAttributes);
  }

  private List<GenericMetricResponse> buildResponse(
      List<Student> studentList, List<StudentAttributeValueModel> allStudentAttributes) {
    List<GenericMetricResponse> genericMetricResponse = new ArrayList<>();
    Map<Long, List<StudentAttributeValueModel>> attributesByStudentId =
        allStudentAttributes.stream()
            .collect(Collectors.groupingBy(attr -> attr.getStudent().getId()));

    for (Student student : studentList) {
      Map<String, Object> attributes = new HashMap<>();
      var studentDetails = getStudentDetails(student);
      var studentRelationship = getStudentRelationship(student);

      attributes.putAll(studentDetails);
      attributes.putAll(studentRelationship);
      List<StudentAttributeValueModel> studentAttributes =
          attributesByStudentId.getOrDefault(student.getId(), Collections.emptyList());

      studentAttributes.forEach(
          value -> attributes.put(value.getAttributeDefinition().getName(), value.getValue()));

      genericMetricResponse.add(GenericMetricResponse.builder().data(attributes).build());
    }
    return genericMetricResponse;
  }

  private Map<String, Object> getStudentRelationship(Student student) {
    Map<String, Object> attributes = new HashMap<>();
    var relationShip = studentRelationshipRepository.findBystudentA(student);
    if (relationShip.isEmpty()) {
      return attributes;
    }
    for (int i = 0; i < 3 && i < relationShip.size(); i++) {
      var buildRel = buildRelationShipDetails(relationShip.get(i));
      int number = i + 1;
      attributes.put("relationship_" + number, buildRel);
    }
    return attributes;
  }

  private Object buildRelationShipDetails(StudentRelationshipModel studentRelationshipModel) {
    var student = studentRelationshipModel.getStudentB();
    var relation = studentRelationshipModel.getRelationshipModel().getAIsToB();
    if (student.getUserInfo() == null) {
      return null;
    }
    var user = student.getUserInfo();
    var section = student.getSection();

    return String.join(
        ", ",
        user.getFirstName() + user.getLastName(),
        section.getName(),
        section.getGradeName(),
        relation);
  }

  private Map<String, Object> getStudentDetails(Student student) {
    Map<String, Object> attributes = new HashMap<>();
    var user = student.getUserInfo();
    var section = student.getSection();
    attributes.put("student_name", user.getFirstName() + " " + user.getLastName());
    attributes.put("section_name", section.getName());
    attributes.put("grade_slug", section.getGradeSlug());
    attributes.put("grade_name", section.getGradeName());

    return attributes;
  }
}
