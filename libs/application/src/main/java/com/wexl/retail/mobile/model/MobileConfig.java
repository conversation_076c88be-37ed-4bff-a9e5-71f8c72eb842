package com.wexl.retail.mobile.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.Data;

@Data
@Entity
@Table(name = "mobile_config")
public class MobileConfig extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  @Column(name = "description")
  private String packageDescription;

  private Boolean status;

  @Column(name = "package_name")
  private String packageName;
}
