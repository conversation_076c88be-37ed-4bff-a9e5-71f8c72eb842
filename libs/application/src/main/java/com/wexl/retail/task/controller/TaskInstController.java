package com.wexl.retail.task.controller;

import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.task.dto.StudentTasksResponse;
import com.wexl.retail.task.service.TaskInstService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("orgs/{orgSlug}/tasks-inst")
public class TaskInstController {

  private final TaskInstService taskInstService;

  @IsOrgAdminOrTeacher
  @DeleteMapping("/{taskInstId}")
  @ResponseStatus(HttpStatus.ACCEPTED)
  public void deleteScheduleId(@PathVariable String orgSlug, @PathVariable long taskInstId) {

    taskInstService.deleteTaskInst(orgSlug, taskInstId);
  }

  @GetMapping("/students/{StudentAuthId}/activity")
  public List<StudentTasksResponse> getStudentTasksByDates(
      @PathVariable("StudentAuthId") String studentId,
      @RequestParam Long date,
      @RequestParam(required = false, defaultValue = "100") int limit) {

    return taskInstService.getStudentActivitiesByDates(studentId, date, limit);
  }

  @IsStudent
  @PostMapping("/{taskInstId}:unsubmit")
  @ResponseStatus(HttpStatus.ACCEPTED)
  public void unSubmitAssignment(@PathVariable String orgSlug, @PathVariable long taskInstId) {

    taskInstService.unSubmitAssignment(orgSlug, taskInstId);
  }
}
