package com.wexl.retail.test.schedule.domain;

import com.wexl.retail.model.Model;
import com.wexl.retail.model.User;
import com.wexl.retail.test.school.domain.TestDefinition;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Type;

@Data
@Entity
@Table(name = "test_schedule")
@EqualsAndHashCode(callSuper = true)
public class ScheduleTest extends Model {
  boolean allStudents;

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "test_schedule_id_seq")
  @SequenceGenerator(name = "test_schedule_id_seq")
  private long id;

  @ManyToOne private TestDefinition testDefinition;
  private LocalDateTime startDate;
  private String testName;
  private LocalDateTime endDate;
  private String status;
  private int duration;
  private String message;

  @OneToMany(fetch = FetchType.LAZY, mappedBy = "scheduleTest", cascade = CascadeType.ALL)
  private List<ScheduleTestStudent> scheduleTestStudent;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb")
  private ScheduleTestMetadata metadata;

  @ManyToOne
  @JoinColumn(name = "teacher_id", nullable = false)
  private User teacher;

  private String published;
  private String type;

  @ManyToOne
  @JoinColumn(name = "parent_test_schedule_id")
  private ScheduleTest parent;

  private String orgSlug;
  private Boolean notificationStatus;

  @Column(name = "is_dac")
  private Boolean isDac = false;

  private String dacMigrationStatus;
  private String studentsResultPath;
}
