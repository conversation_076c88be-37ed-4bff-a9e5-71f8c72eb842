package com.wexl.retail.student.studentpublisher;

import com.wexl.retail.calenderevent.dto.CalenderEventVisibility;
import com.wexl.retail.calenderevent.model.CalendarEvent;
import com.wexl.retail.calenderevent.model.CalendarEventUser;
import com.wexl.retail.calenderevent.repository.CalenderEventRepository;
import com.wexl.retail.model.Student;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.student.attributes.dto.StudentAttributeDto;
import com.wexl.retail.student.attributes.repository.StudentAttributeValueRepository;
import com.wexl.retail.student.attributes.service.StudentAttributeService;
import com.wexl.retail.test.schedule.domain.ScheduleTestMetadata;
import com.wexl.retail.test.schedule.dto.SimpleScheduleTestRequest;
import com.wexl.retail.test.schedule.repository.ScheduleTestRepository;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import com.wexl.retail.test.schedule.repository.StudentScheduleTestAnswerRepository;
import com.wexl.retail.test.schedule.service.ScheduleTestService;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Slf4j
@AllArgsConstructor
@Component
public class StudentCreationEventListener implements ApplicationListener<StudentCreationEvent> {
  private final CalenderEventRepository calenderEventRepository;
  private final ScheduleTestStudentRepository scheduleTestStudentRepository;
  private final OrganizationRepository organizationRepository;
  private final ScheduleTestService scheduleTestService;
  private final ScheduleTestRepository scheduleTestRepository;
  private final StudentAttributeService studentAttributeService;
  private final StudentAttributeValueRepository studentAttributeValueRepository;
  private final StudentScheduleTestAnswerRepository studentScheduleTestAnswerRepository;
  private TestDefinitionRepository testDefinitionRepository;

  @Override
  public void onApplicationEvent(StudentCreationEvent studentCreationEvent) {
    Object source = studentCreationEvent.getSource();
    if (source instanceof Student student) {
      addCalenderEventForNewStudent(student);
    }
  }

  public void addCalenderEventForNewStudent(Student student) {
    var studentSection = student.getSection().getUuid().toString();
    List<CalendarEvent> calendarEventList =
        calenderEventRepository.findBySectionUuidInAndOrgSlug(
            Collections.singletonList(studentSection), student.getUserInfo().getOrganization());
    for (CalendarEvent calendarEvent : calendarEventList) {
      if (calendarEvent.getVisibility() == CalenderEventVisibility.ALL) {
        List<CalendarEventUser> studentEventUser = new ArrayList<>();
        studentEventUser.add(buildCalendarEvent(student, calendarEvent));
        calendarEvent.setCalendarEventUsers(studentEventUser);
        calenderEventRepository.save(calendarEvent);
      }
    }
  }

  public CalendarEventUser buildCalendarEvent(Student student, CalendarEvent calendarEvent) {
    return CalendarEventUser.builder()
        .userId(student.getUserInfo().getId())
        .isStudent(true)
        .calendarEvent(calendarEvent)
        .build();
  }

  public void addNewStudentToPreSchedule(Student student) {
    var userInfo = student.getUserInfo();
    var userTestDefinition =
        testDefinitionRepository.getUserTestDefForBet(
            "BET-CE-%", "wexl-internal", student.getUserInfo().getId());

    var notTakenTestDef =
        testDefinitionRepository.getTestDefinitionByTestNameAndOrgSlug(
            "BET-CE-%",
            "wexl-internal",
            userTestDefinition.isEmpty()
                ? null
                : userTestDefinition.stream().map(TestDefinition::getId).toList());

    if (notTakenTestDef.isEmpty()) {
      log.info("No exams Found");
      return;
    }
    Map<String, String> attributes = new HashMap<>();
    int attributeIndex = 1;
    for (int cycle = 0; cycle < 5; cycle++) {
      for (TestDefinition testDefinition : notTakenTestDef) {
        var scheduleRequest = getSimpleScheduleTestRequest(student, testDefinition);
        var response =
            scheduleTestService.scheduleBetTest(
                testDefinition, scheduleRequest, student.getUserInfo());

        attributes.put("bet_corp_schedule_" + attributeIndex++, String.valueOf(response.getId()));
      }
    }
    var buildAttributes = StudentAttributeDto.Request.builder().attributes(attributes).build();
    studentAttributeService.saveStudentDefinitionAttributes(
        userInfo.getAuthUserId(), student.getUserInfo().getOrganization(), buildAttributes);
  }

  private SimpleScheduleTestRequest getSimpleScheduleTestRequest(
      Student student, TestDefinition testDefinition) {
    return SimpleScheduleTestRequest.builder()
        .testDefinitionId(testDefinition.getId())
        .allStudents(false)
        .message("All the Best")
        .startDate(Instant.now().toEpochMilli())
        .endDate(
            LocalDateTime.now()
                .plusMonths(6)
                .atZone(ZoneId.systemDefault())
                .toInstant()
                .toEpochMilli())
        .duration(120)
        .metadata(
            buildScheduleTestMetaData(Collections.singletonList(student.getSection().getName())))
        .studentIds(Collections.singleton(student.getId()))
        .orgSlug(student.getUserInfo().getOrganization())
        .build();
  }

  public ScheduleTestMetadata buildScheduleTestMetaData(List<String> sections) {
    return ScheduleTestMetadata.builder().board("bet").grade("stdg").sections(sections).build();
  }
}
