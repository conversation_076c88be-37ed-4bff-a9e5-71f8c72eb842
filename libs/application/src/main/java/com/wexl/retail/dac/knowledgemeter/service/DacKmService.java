package com.wexl.retail.dac.knowledgemeter.service;

import static com.wexl.retail.task.domain.TaskStatus.NOT_STARTED;
import static com.wexl.retail.util.Constants.*;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.classroom.core.service.ScheduleInstAttendanceService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.*;
import com.wexl.retail.curriculum.service.CurriculumService;
import com.wexl.retail.dac.knowledgemeter.dto.*;
import com.wexl.retail.dac.knowledgemeter.model.DacKMLog;
import com.wexl.retail.dac.knowledgemeter.model.TestScheduleKnowledge;
import com.wexl.retail.dac.knowledgemeter.model.TestScheduleKnowledgeDetail;
import com.wexl.retail.dac.knowledgemeter.repository.DacKMLogRepository;
import com.wexl.retail.dac.knowledgemeter.repository.DacKmRepository;
import com.wexl.retail.dac.knowledgemeter.repository.TestScheduleKnowledgeRepository;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.mlp.dto.KMGradesRequest;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Subject;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.student.answer.ExamAnswer;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.ExamRepository;
import com.wexl.retail.teacher.orgs.TeacherOrgsService;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.schedule.repository.ScheduleTestRepository;
import com.wexl.retail.test.schedule.service.ScheduleTestService;
import com.wexl.retail.test.schedule.service.ScheduleTestStatus;
import com.wexl.retail.test.school.domain.TestDefinitionSection;
import com.wexl.retail.test.school.domain.TestQuestion;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.StrapiService;
import com.wexl.retail.util.UploadService;
import com.wexl.retail.util.ValidationUtils;
import com.wexl.retail.util.dto.SubjectDetailResponse;
import jakarta.transaction.Transactional;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@EnableAsync
@RequiredArgsConstructor
public class DacKmService {
  private final DacKmRepository dacKmRepository;
  private final OrganizationRepository organizationRepository;
  private final SectionRepository sectionRepository;
  private final ContentService contentService;
  private final CurriculumService curriculumService;
  private final StrapiService strapiService;
  private final StudentRepository studentRepository;
  private final ExamRepository examRepository;
  private final TestScheduleKnowledgeRepository knowledgeRepository;
  private final ScheduleInstAttendanceService attendanceService;
  private final ScheduleTestRepository scheduleTestRepository;
  private final ScheduleTestService scheduleTestService;
  private final SectionService sectionService;
  private final AuthService authService;
  private final TeacherOrgsService teacherOrgsService;
  private final UserRepository userRepository;
  private final TestDefinitionRepository testDefinitionRepository;
  private final DateTimeUtil dateTimeUtil;
  private final ValidationUtils validationUtils;
  private final UploadService uploadService;
  private final StorageService storageService;
  private final DacKMLogRepository dacKMLogRepository;

  @Value("${app.contentToken}")
  private String contentBearerToken;

  public DacKmDto.DacKmResponse getSummaryByBoard(
      String orgSlug,
      String boardSlug,
      Long fDate,
      Long tDate,
      List<KMGradesRequest> gradeRequest) {
    var org = organizationRepository.findBySlug(orgSlug);
    if (org == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.OrganizationFind.Slug");
    }
    Double averagePercentage;
    LocalDateTime fromDate = null;
    LocalDateTime toDate = null;
    if (Objects.nonNull(fDate) && Objects.nonNull(tDate)) {
      fromDate = dateTimeUtil.convertEpochToIso8601Legacy(fDate);
      toDate = dateTimeUtil.convertEpochToIso8601Legacy(tDate);

      averagePercentage =
          dacKmRepository.getOverallOrgAvg(
              orgSlug,
              boardSlug,
              fromDate.toLocalDate().toString(),
              toDate.toLocalDate().toString());
    } else {
      averagePercentage = dacKmRepository.getOverallOrgAvg(orgSlug, boardSlug, null, null);
    }
    return DacKmDto.DacKmResponse.builder()
        .organization(org.getName())
        .grades(
            Objects.nonNull(fromDate)
                ? buildGradeResponse(
                    orgSlug,
                    boardSlug,
                    gradeRequest,
                    fromDate.toLocalDate().toString(),
                    toDate.toLocalDate().toString())
                : buildGradeResponse(orgSlug, boardSlug, gradeRequest, null, null))
        .averagePercentage(averagePercentage)
        .build();
  }

  private Double calculateKnowledgePercentage(List<TestScheduleKnowledge> testScheduleKnowledges) {
    var studentKnowldegeDetails =
        testScheduleKnowledges.stream()
            .map(
                testScheduleKnowledge ->
                    testScheduleKnowledge.getKnowledgeDetails().stream()
                        .max(Comparator.comparing(TestScheduleKnowledgeDetail::getCreatedAt))
                        .orElse(null))
            .filter(Objects::nonNull)
            .mapToDouble(TestScheduleKnowledgeDetail::getKnowledgePercentage)
            .average()
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.SomethingWrong"));
    return Double.parseDouble(Constants.DECIMAL_FORMAT.format(studentKnowldegeDetails));
  }

  public List<DacKmDto.GeneralResponse> buildGradeResponse(
      String orgSlug,
      String boardSlug,
      List<KMGradesRequest> gradeRequest,
      String fromDate,
      String toDate) {
    List<DacKmDto.GeneralResponse> gradeResponse = new ArrayList<>();
    for (KMGradesRequest gradeSlug : gradeRequest) {
      List<AvgPercentageResponse> averagePercentages =
          dacKmRepository.getOverallOrgAvgByGrade(
              orgSlug, boardSlug, gradeSlug.getGradeSlug(), fromDate, toDate);
      for (AvgPercentageResponse avgPercentage : averagePercentages) {
        var grade = contentService.getGradeBySlug(avgPercentage.getGradeSlug());
        DacKmDto.GeneralResponse generalResponse =
            DacKmDto.GeneralResponse.builder()
                .slug(grade.getSlug())
                .name(grade.getName())
                .order(grade.getOrder())
                .averagePercentage(avgPercentage.getAvgKnowledgePercentage())
                .examRecords(new ArrayList<>())
                .build();
        gradeResponse.add(generalResponse);
      }
    }
    return gradeResponse.stream()
        .sorted(Comparator.comparing(DacKmDto.GeneralResponse::order))
        .toList();
  }

  public List<DacKmDto.SectionResponse> getSummaryByBoardAndGrade(
      String orgSlug,
      String boardSlug,
      String gradeSlug,
      DacKmDto.Data subjectList,
      Long fDate,
      Long tDate) {
    Organization org = organizationRepository.findBySlug(orgSlug);
    Entity board = strapiService.getEduBoardBySlug(boardSlug);
    Grade grade = contentService.getGradeBySlug(gradeSlug);

    if (org == null || board == null || grade == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidRequest");
    }

    List<SubTopicResponse> subtopicList =
        fetchSubtopics(
            orgSlug, boardSlug, Collections.singletonList(gradeSlug), subjectList.subjects());
    List<Subject> subjects =
        curriculumService.getSubjectsByBoardIdAndGradeId(orgSlug, board.getId(), grade.getId());
    List<Section> sections =
        sectionRepository.getSectionsUsingGradeSlugsAndBoardSlugs(
            Collections.singletonList(gradeSlug), orgSlug, board.getSlug());

    List<DacKmDto.SectionResponse> response = new ArrayList<>();
    for (Section section : sections) {
      List<Student> sectionStudents =
          studentRepository.getStudentsBySectionAndDeletedAtIsNull(section);
      List<Long> studentIds = sectionStudents.stream().map(Student::getId).toList();

      Double averagePercentage;
      LocalDateTime fromDate = null;
      LocalDateTime toDate = null;
      if (Objects.nonNull(fDate) && Objects.nonNull(tDate)) {
        fromDate = dateTimeUtil.convertEpochToIso8601Legacy(fDate);
        toDate = dateTimeUtil.convertEpochToIso8601Legacy(tDate);

        averagePercentage =
            dacKmRepository.getOverallOrgAndGradeByAvg(
                orgSlug,
                studentIds,
                fromDate.toLocalDate().toString(),
                toDate.toLocalDate().toString());
      } else {
        averagePercentage =
            dacKmRepository.getOverallOrgAndGradeByAvg(orgSlug, studentIds, null, null);
      }
      response.add(
          DacKmDto.SectionResponse.builder()
              .name(section.getName())
              .averagePercentage(averagePercentage)
              .uuid(section.getUuid())
              .data(
                  Objects.nonNull(fDate) && Objects.nonNull(averagePercentage)
                      ? buildSectionResponse(
                          orgSlug,
                          subjects,
                          subtopicList,
                          studentIds,
                          fromDate.toLocalDate().toString(),
                          toDate.toLocalDate().toString())
                      : (Objects.nonNull(averagePercentage)
                          ? buildSectionResponse(
                              orgSlug, subjects, subtopicList, studentIds, null, null)
                          : null))
              .build());
      response.sort(Comparator.comparing(DacKmDto.SectionResponse::name));
    }
    return response;
  }

  private List<DacKmDto.GeneralResponse> buildSectionResponse(
      String orgSlug,
      List<Subject> subjects,
      List<SubTopicResponse> subtopicList,
      List<Long> studentIds,
      String fromDate,
      String toDate) {
    List<DacKmDto.GeneralResponse> response = new ArrayList<>();

    for (Subject subject : subjects) {
      List<SubTopicResponse> subTopics =
          subtopicList.stream()
              .filter(subtopic -> subject.getSlug().equals(subtopic.getSubjectSlug()))
              .toList();
      List<String> subtopicSlugs = subTopics.stream().map(SubTopicResponse::getSlug).toList();
      Double averagePercentage =
          dacKmRepository.getOverallOrgAndGradeByAvgSec(
              orgSlug, subtopicSlugs, studentIds, fromDate, toDate);
      response.add(
          DacKmDto.GeneralResponse.builder()
              .name(subject.getName())
              .slug(subject.getSlug())
              .averagePercentage(averagePercentage)
              .build());
    }
    return response;
  }

  public List<DacKmDto.SubjectResponse> getSummaryBySection(
      String orgSlug,
      String boardSlug,
      String sectionUuid,
      DacKmDto.Data subjects,
      Long fDate,
      Long tDate) {
    Organization org = organizationRepository.findBySlug(orgSlug);
    Entity board = strapiService.getEduBoardBySlug(boardSlug);
    if (org == null || board == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidRequest");
    }

    Section section =
        sectionRepository
            .findByUuid(UUID.fromString(sectionUuid))
            .orElseThrow(
                () ->
                    new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.SectionNotFound"));
    List<Student> sectionStudents =
        studentRepository.getStudentsBySectionAndDeletedAtIsNull(section);
    List<Long> studentIds = sectionStudents.stream().map(Student::getId).toList();
    List<SubTopicResponse> subtopicList =
        fetchSubtopics(
            orgSlug,
            board.getSlug(),
            Collections.singletonList(section.getGradeSlug()),
            subjects.subjects());

    LocalDateTime fromDate = null;
    LocalDateTime toDate = null;
    if (Objects.nonNull(fDate) && Objects.nonNull(tDate)) {
      fromDate = dateTimeUtil.convertEpochToIso8601Legacy(fDate);
      toDate = dateTimeUtil.convertEpochToIso8601Legacy(tDate);
      return calculateSubjectResponses(
          orgSlug,
          subjects.subjects(),
          subtopicList,
          studentIds,
          true,
          fromDate.toLocalDate().toString(),
          toDate.toLocalDate().toString());
    }
    return calculateSubjectResponses(
        orgSlug, subjects.subjects(), subtopicList, studentIds, true, null, null);
  }

  private List<DacKmDto.ChapterResponse> buildChapterResponse(
      String orgSlug,
      List<SubTopicResponse> subtopicList,
      List<Long> studentIds,
      Boolean showSubTopicResponse,
      String fromDate,
      String toDate) {

    Map<Long, List<SubTopicResponse>> subtopicsByChapterId =
        subtopicList.stream()
            .collect(Collectors.groupingBy(subtopic -> Long.valueOf(subtopic.getChapterId())));

    List<String> allSubtopicSlugs =
        subtopicList.stream().map(SubTopicResponse::getSlug).distinct().toList();

    List<TestScheduleKnowledge> allTestScheduleKmList =
        dacKmRepository.findAllByOrgSlugAndSubTopicSlugInAndStudentIdIn(
            orgSlug, allSubtopicSlugs, studentIds);

    Map<String, List<TestScheduleKnowledge>> testScheduleKmBySubtopicSlug =
        allTestScheduleKmList.stream()
            .collect(Collectors.groupingBy(TestScheduleKnowledge::getSubTopicSlug));

    List<DacKmDto.ChapterResponse> response = new ArrayList<>();
    for (Long chapterId : subtopicsByChapterId.keySet()) {
      ChapterResponse chapter = contentService.getChapterById(chapterId);
      List<SubTopicResponse> chapterSubtopics = subtopicsByChapterId.get(chapterId);
      List<String> chapterSubtopicSlugs =
          chapterSubtopics.stream().map(SubTopicResponse::getChapterSlug).toList();
      Double averagePercentage =
          dacKmRepository.getOverallOrgAvgByChapterSlug(
              orgSlug, chapterSubtopicSlugs, fromDate, toDate);

      response.add(
          DacKmDto.ChapterResponse.builder()
              .chapterName(chapter.getName())
              .chapterSlug(chapter.getChapterSlug())
              .average(averagePercentage != null ? averagePercentage : 0)
              .subtopics(
                  Boolean.TRUE.equals(showSubTopicResponse) && averagePercentage != null
                      ? buildSubtopicResponse(
                          orgSlug, chapterSubtopics, testScheduleKmBySubtopicSlug, fromDate, toDate)
                      : null)
              .build());
    }
    var filterSubjects =
        response.stream().filter(x -> x.average() != null && x.average() != 0.0).toList();
    List<DacKmDto.ChapterResponse> sortedResponse =
        filterSubjects.stream()
            .sorted(
                Comparator.comparing(DacKmDto.ChapterResponse::average)
                    .reversed()
                    .thenComparing(s -> s.average() == 0 ? 1 : 0))
            .toList();

    return sortedResponse;
  }

  private List<DacKmDto.GeneralResponse> buildSubtopicResponse(
      String orgSlug,
      List<SubTopicResponse> subtopics,
      Map<String, List<TestScheduleKnowledge>> testScheduleKmBySubtopicSlug,
      String fromDate,
      String toDate) {

    List<DacKmDto.GeneralResponse> responses =
        subtopics.stream()
            .map(
                subtopic -> {
                  List<TestScheduleKnowledge> testScheduleKmList =
                      testScheduleKmBySubtopicSlug.getOrDefault(
                          subtopic.getSlug(), Collections.emptyList());
                  List<String> chapterSlugs =
                      testScheduleKmList.stream()
                          .map(TestScheduleKnowledge::getChapterSlug)
                          .toList();
                  Double averagePercentage =
                      dacKmRepository.getOverallOrgAvgByChapterSlug(
                          orgSlug, chapterSlugs, fromDate, toDate);

                  return DacKmDto.GeneralResponse.builder()
                      .name(subtopic.getName())
                      .slug(subtopic.getSlug())
                      .averagePercentage(averagePercentage)
                      .build();
                })
            .toList();
    var filterNull = responses.stream().filter(x -> x.averagePercentage() != null).toList();
    return filterNull.stream()
        .sorted(
            Comparator.comparing(
                DacKmDto.GeneralResponse::averagePercentage,
                Comparator.nullsLast(Comparator.naturalOrder())))
        .toList();
  }

  private Exam validateExamIfTestDefinitionPresent(long examId) {
    return examRepository.findByIdAndTestDefinitionIsNotNull(examId).orElse(null);
  }

  @Async
  @Transactional
  public void migrateKnowledge(DacKmDto.KmRequest request) {
    var exam = validateExamIfTestDefinitionPresent(request.examId());
    try {
      if (Objects.isNull(exam)) {
        return;
      }
      Map<String, List<ExamAnswer>> topicExamAnswersMap =
          exam.getExamAnswers().stream()
              .collect(Collectors.groupingBy(ExamAnswer::getSubtopicSlug));

      List<TestScheduleKnowledge> testScheduleKnowledges = new ArrayList<>();

      topicExamAnswersMap.forEach(
          (topic, topicAnswers) ->
              addTestScheduleKnowledges(exam, topic, topicAnswers, testScheduleKnowledges));
      knowledgeRepository.saveAll(new ArrayList<>(testScheduleKnowledges));
      log.info("Saved test schedule knowledges");
    } catch (Exception e) {
      log.error(
          "There was an error while populating test knowledge meter exam  id : [%s] "
              .formatted(request.examId()),
          e);
    }
  }

  private void addTestScheduleKnowledges(
      Exam exam,
      String topic,
      List<ExamAnswer> topicAnswers,
      List<TestScheduleKnowledge> testScheduleKnowledges) {

    var subTopicResponse =
        contentService.getSubTopicBySlug(
            exam.getStudent().getUserInfo().getOrganization(), topic, contentBearerToken);

    var optionalKnowledge =
        knowledgeRepository.findByStudentIdAndSubTopicSlug(exam.getStudentId(), topic);
    TestScheduleKnowledge scheduleKnowledge = new TestScheduleKnowledge();
    if (optionalKnowledge.isPresent()) {
      scheduleKnowledge = optionalKnowledge.get();
    } else {
      buildTestScheduleKnowledge(scheduleKnowledge, exam.getStudent(), topic, subTopicResponse);
    }
    if (Objects.nonNull(topicAnswers) && !topicAnswers.isEmpty()) {
      scheduleKnowledge.setKnowledgeDetails(
          new ArrayList<>(
              List.of(buildTestScheduleKnowledgeDetails(topicAnswers, scheduleKnowledge))));
    }
    testScheduleKnowledges.add(scheduleKnowledge);
  }

  private TestScheduleKnowledge buildTestScheduleKnowledge(
      TestScheduleKnowledge scheduleKnowledge,
      Student student,
      String topic,
      SubTopicResponse topicResponse) {
    scheduleKnowledge.setOrgSlug(student.getUserInfo().getOrganization());
    scheduleKnowledge.setStudentId(student.getId());
    scheduleKnowledge.setSubTopicSlug(topic);
    if (Objects.nonNull(topicResponse)) {
      scheduleKnowledge.setBoard(topicResponse.getBoardSlug());
      scheduleKnowledge.setGradeSlug(topicResponse.getGradeSlug());
      scheduleKnowledge.setGradeName(topicResponse.getGradeName());
      scheduleKnowledge.setChapterSlug(topicResponse.getChapterSlug());
      scheduleKnowledge.setSubjectSlug(topicResponse.getSubjectSlug());
    }
    return scheduleKnowledge;
  }

  private TestScheduleKnowledgeDetail buildTestScheduleKnowledgeDetails(
      List<ExamAnswer> examAnswers, TestScheduleKnowledge scheduleKnowledge) {

    var scheduleTest = examAnswers.get(0).getExam().getScheduleTest();

    List<ExamAnswer> scheduleWiseAnswers =
        examAnswers.stream()
            .filter(ea -> ea.getExam().getScheduleTest().getId() == scheduleTest.getId())
            .toList();
    return TestScheduleKnowledgeDetail.builder()
        .testScheduleId(scheduleTest.getId())
        .calenderDetails(attendanceService.constructCalendarDetails())
        .testScheduleKnowledge(scheduleKnowledge)
        .examId(examAnswers.getFirst().getExam().getId())
        .totalMarks(calculateTotalMarks(scheduleWiseAnswers))
        .marks(calculateMarksSecured(scheduleWiseAnswers))
        .knowledgePercentage(calculateKnowlgePercentage(scheduleWiseAnswers))
        .build();
  }

  private double calculateKnowlgePercentage(List<ExamAnswer> scheduleWiseAnswers) {
    var totalMarks =
        scheduleWiseAnswers.stream()
            .mapToDouble(ExamAnswer::getMarksPerQuestion)
            .filter(Objects::nonNull)
            .sum();
    var marksScored =
        scheduleWiseAnswers.stream()
            .mapToDouble(ExamAnswer::getMarksScoredPerQuestion)
            .filter(Objects::nonNull)
            .sum();
    return marksScored <= 0 ? 0 : (marksScored / totalMarks) * 100;
  }

  private double calculateMarksSecured(List<ExamAnswer> scheduleWiseAnswers) {
    return scheduleWiseAnswers.stream().mapToDouble(ExamAnswer::getMarksScoredPerQuestion).sum();
  }

  private double calculateTotalMarks(List<ExamAnswer> scheduleWiseAnswers) {
    return scheduleWiseAnswers.stream().mapToDouble(ExamAnswer::getMarksPerQuestion).sum();
  }

  public void migrateTestScheduleKnowledgeBySchedule(
      DacKmDto.TestKmMigrationRequest request, String bearerToken) {

    var scheduleIds = scheduleTestRepository.findChildTestScheduleIds(request.testScheduleId());
    scheduleIds.add(request.testScheduleId());
    var scheduleTests =
        scheduleTestRepository.findAllByIdInAndDacMigrationStatus(scheduleIds, NOT_STARTED.name());
    if (scheduleTests.isEmpty()) {
      return;
    }
    var exams = examRepository.findAllByScheduleTestIn(scheduleTests);
    if (exams.isEmpty()) {
      log.error("No exams found for test schedule knowledge migration");
      return;
    }

    Map<ScheduleTest, List<Exam>> scheduleTestExams =
        exams.stream().collect(Collectors.groupingBy(Exam::getScheduleTest));

    scheduleTestExams.forEach(
        (ts, examList) -> {
          if (isCompletedTestCompleted(ts)) {
            try {
              ts.setDacMigrationStatus(STARTED);
              examList.forEach(
                  exam -> {
                    migrateExamAnswersTopicsByExam(exam, ts, bearerToken);
                    migrateKnowledge(DacKmDto.KmRequest.builder().examId(exam.getId()).build());
                  });
              ts.setDacMigrationStatus(COMPLETED);
            } catch (Exception e) {
              log.error("Failed to migrate test schedule :" + e.getMessage(), e);
              ts.setDacMigrationStatus(FAILED);
            } finally {
              scheduleTestRepository.save(ts);
            }
          }
        });
  }

  private boolean isCompletedTestCompleted(ScheduleTest scheduleTest) {
    ScheduleTestStatus scheduleTestStatus =
        scheduleTestService.getScheduleTestResponseStatus(
            scheduleTest,
            DateTimeUtil.convertIso8601ToEpoch(scheduleTest.getStartDate()),
            DateTimeUtil.convertIso8601ToEpoch(scheduleTest.getEndDate()));
    return ScheduleTestStatus.EVALUATED.equals(scheduleTestStatus);
  }

  private void migrateExamAnswersTopicsByExam(
      Exam exam, ScheduleTest scheduleTest, String bearerToken) {
    Map<String, String> questionTopicsMap;
    if (!scheduleTest.getTestDefinition().getTestDefinitionSections().isEmpty()) {
      List<TestQuestion> testQuestions =
          exam.getTestDefinition().getTestDefinitionSections().stream()
              .map(TestDefinitionSection::getTestQuestions)
              .flatMap(Collection::stream)
              .toList();
      questionTopicsMap =
          testQuestions.stream()
              .collect(
                  Collectors.toMap(TestQuestion::getQuestionUuid, TestQuestion::getSubtopicSlug));
    } else {
      questionTopicsMap = new HashMap<>();
    }
    exam.getExamAnswers()
        .forEach(
            examAnswer -> {
              if (Objects.nonNull(examAnswer.getSubtopicSlug())) {
                if (!questionTopicsMap.isEmpty()
                    && questionTopicsMap.containsKey(examAnswer.getQuestionUuid())) {
                  examAnswer.setSubtopicSlug(questionTopicsMap.get(examAnswer.getQuestionUuid()));
                } else {
                  Question question =
                      contentService.getQuestionByUuid(
                          bearerToken,
                          QuestionType.valueOf(examAnswer.getType()),
                          examAnswer.getQuestionUuid());
                  examAnswer.setSubtopicSlug(question.getSubtopicSlug());
                }
              }
            });
    examRepository.save(exam);
  }

  public List<GenericMetricResponse> getOrgWiseKnowledgeReport(
      String orgSlug, String board, String grade, String subject) {
    var parentOrg = organizationRepository.findBySlug(orgSlug);
    User user = authService.getUserDetails();
    List<Organization> teacherOrgs = teacherOrgsService.getChildOrgs(user.getAuthUserId());
    var childOrganization = organizationRepository.findByParentAndDeletedAtIsNull(parentOrg);
    List<Organization> childOrgs =
        teacherOrgs.stream().filter(childOrganization::contains).toList();
    if (childOrgs.isEmpty()) {
      return Collections.emptyList();
    }
    List<String> childOrgSlugs = childOrgs.stream().map(Organization::getSlug).toList();
    List<TestScheduleKnowledge> testScheduleKnowledges =
        getTestScheduleKnowledgesByFilteringBoardGradeAndSubject(
            childOrgSlugs, board, grade, subject);

    Map<String, List<TestScheduleKnowledge>> knowledgeMap =
        testScheduleKnowledges.stream()
            .collect(Collectors.groupingBy(TestScheduleKnowledge::getOrgSlug));

    return childOrgs.stream()
        .map(
            childOrg ->
                GenericMetricResponse.builder()
                    .data(buidTestScheduleKnowledgeDataMap(childOrg, knowledgeMap))
                    .build())
        .toList();
  }

  private Map<String, Object> buidTestScheduleKnowledgeDataMap(
      Organization childOrg, Map<String, List<TestScheduleKnowledge>> knowledgeMap) {
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put("org_slug", childOrg.getSlug());
    dataMap.put("org_name", childOrg.getName());
    dataMap.put(
        "knowledge_percentage",
        calculateTestKnowledgeAverage(knowledgeMap.get(childOrg.getSlug())));
    return dataMap;
  }

  private List<TestScheduleKnowledge> getTestScheduleKnowledgesByFilteringBoardGradeAndSubject(
      List<String> childOrgSlugs, String board, String grade, String subject) {
    if (Objects.nonNull(board) && Objects.nonNull(grade) && Objects.nonNull(subject)) {
      return knowledgeRepository.findAllByOrgSlugInAndBoardAndGradeSlugAndSubjectSlug(
          childOrgSlugs, board, grade, subject);
    } else if (Objects.nonNull(board) && Objects.nonNull(grade)) {
      return knowledgeRepository.findAllByOrgSlugInAndBoardAndGradeSlug(
          childOrgSlugs, board, grade);
    } else if (Objects.nonNull(board)) {
      return knowledgeRepository.findAllByOrgSlugInAndBoard(childOrgSlugs, board);
    }
    return knowledgeRepository.findAllByOrgSlugIn(childOrgSlugs);
  }

  private Double calculateTestKnowledgeAverage(List<TestScheduleKnowledge> testScheduleKnowledges) {
    if (Objects.isNull(testScheduleKnowledges) || testScheduleKnowledges.isEmpty()) {
      return null;
    }
    var knowledegePercentage =
        testScheduleKnowledges.stream()
            .map(TestScheduleKnowledge::getKnowledgeDetails)
            .flatMap(Collection::stream)
            .mapToDouble(TestScheduleKnowledgeDetail::getMarks)
            .average()
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.SomethingWrong"));
    return Double.parseDouble(Constants.DECIMAL_FORMAT.format(knowledegePercentage));
  }

  public List<GenericMetricResponse> getAllOrganizationKnowledgeMeter(
      String orgSlug, String board, List<String> gradeList, List<String> subjectList) {
    var parentOrg = organizationRepository.findBySlug(orgSlug);
    User user = authService.getUserDetails();
    List<Organization> teacherOrgs = teacherOrgsService.getChildOrgs(user.getAuthUserId());
    var childOrganization = organizationRepository.findByParentAndDeletedAtIsNull(parentOrg);
    List<Organization> childOrgs =
        teacherOrgs.stream().filter(childOrganization::contains).toList();
    if (childOrgs.isEmpty()) {
      return Collections.emptyList();
    }
    List<GenericMetricResponse> response = new ArrayList<>();
    List<OrgsTestKnowledgeResult> testKnowledgeResult =
        knowledgeRepository.getSubjectWiseOrganizationKnowledgeMeter(
            childOrgs.stream().map(Organization::getSlug).toList(), board, gradeList, subjectList);

    Map<String, List<OrgsTestKnowledgeResult>> testKnowledgeMap;
    if (!testKnowledgeResult.isEmpty()) {
      testKnowledgeMap =
          testKnowledgeResult.stream()
              .filter(result -> result.getSubjectSlug() != null)
              .collect(Collectors.groupingBy(OrgsTestKnowledgeResult::getSubjectSlug));
    } else {
      testKnowledgeMap = new HashMap<>();
    }

    List<Grade> grades =
        strapiService.getAllGrades().stream()
            .filter(grade -> gradeList.contains(grade.getSlug()))
            .toList();
    List<SubjectDetailResponse> subjects =
        contentService.getAllSubjects().stream()
            .filter(sub -> subjectList.contains(sub.getSubjectSlug()))
            .toList();

    for (Grade grade : grades) {
      for (SubjectDetailResponse subject : subjects) {
        response.add(
            buildGenericResponse(
                childOrgs, grade, subject, testKnowledgeMap.get(subject.getSubjectSlug())));
      }
    }
    return response;
  }

  private GenericMetricResponse buildGenericResponse(
      List<Organization> organizations,
      Grade grade,
      SubjectDetailResponse subject,
      List<OrgsTestKnowledgeResult> testKnowledgeResult) {
    return GenericMetricResponse.builder()
        .summary(buildSummary(grade, subject))
        .data(buildData(testKnowledgeResult, organizations, grade, subject))
        .build();
  }

  private Map<String, Object> buildData(
      List<OrgsTestKnowledgeResult> testKnowledgeResult,
      List<Organization> organizations,
      Grade grade,
      SubjectDetailResponse subject) {
    Map<String, Object> dataMap = new HashMap<>();

    var responses =
        organizations.stream()
            .map(
                org ->
                    DacKmDto.OrgTestKnowledgeResponse.builder()
                        .orgSlug(org.getSlug())
                        .orgName(org.getName())
                        .knowledgePercentage(
                            getKnowledgePercentage(
                                testKnowledgeResult, org.getSlug(), grade, subject))
                        .build())
            .toList();
    dataMap.put("org_response", responses);
    return dataMap;
  }

  private Long getKnowledgePercentage(
      List<OrgsTestKnowledgeResult> testKnowledgeResult,
      String orgSlug,
      Grade grade,
      SubjectDetailResponse subject) {
    if (Objects.isNull(testKnowledgeResult) || testKnowledgeResult.isEmpty()) {
      return null;
    }
    var result =
        testKnowledgeResult.stream()
            .filter(t -> t.getOrgSlug().equals(orgSlug))
            .filter(t -> t.getGradeSlug().equals(grade.getSlug()))
            .filter(t -> t.getSubjectSlug().equals(subject.getSubjectSlug()))
            .findFirst();
    return result.map(OrgsTestKnowledgeResult::getKmPercentage).orElse(null);
  }

  private Map<String, Object> buildSummary(Grade grade, SubjectDetailResponse subject) {
    Map<String, Object> summaryMap = new HashMap<>();
    summaryMap.put("subject_slug", subject.getSubjectSlug());
    summaryMap.put("subject_name", subject.getSubjectName());
    summaryMap.put("grade_slug", grade.getSlug());
    summaryMap.put("grade_name", grade.getName());
    return summaryMap;
  }

  public List<DacKmDto.SectionResponse> getSubjectWiseSummary(String orgSlug, String authUserId) {
    User user = userRepository.getUserByAuthUserId(authUserId);
    Student student = studentRepository.findByUserId(user.getId());
    Entity board = strapiService.getEduBoardById(student.getBoardId());
    Grade grade = contentService.getGradeById(student.getClassId());
    List<DacKmDto.SectionResponse> response = new ArrayList<>();

    List<TestScheduleKnowledge> testScheduleKnowledges =
        dacKmRepository.findAllByOrgSlugAndStudentIdIn(
            orgSlug, Collections.singletonList(student.getId()));

    Double averagePercentage = null;

    if (!testScheduleKnowledges.isEmpty()) {
      averagePercentage = calculateKnowledgePercentage(testScheduleKnowledges);
    }
    List<Subject> allSubjectsList =
        curriculumService.getSubjectsByBoardIdAndGradeId(orgSlug, board.getId(), grade.getId());

    List<String> studentSubjectSlugs = allSubjectsList.stream().map(Subject::getSlug).toList();
    List<SubTopicResponse> subtopicList =
        fetchSubtopics(
            orgSlug,
            board.getSlug(),
            Collections.singletonList(grade.getSlug()),
            studentSubjectSlugs);
    response.add(
        DacKmDto.SectionResponse.builder()
            .averagePercentage(averagePercentage)
            .data(
                buildSectionResponse(
                    orgSlug,
                    allSubjectsList,
                    subtopicList,
                    Collections.singletonList(student.getId()),
                    null,
                    null))
            .build());

    return response;
  }

  public List<DacKmDto.SubjectResponse> getSubjectWiseKms(
      String orgSlug, String authUserId, DacKmDto.Data subjects) {
    User user = userRepository.getUserByAuthUserId(authUserId);
    Student student = studentRepository.findByUserId(user.getId());
    Entity board = strapiService.getEduBoardById(student.getBoardId());
    Grade grade = contentService.getGradeById(student.getClassId());

    List<SubTopicResponse> subtopicList =
        fetchSubtopics(
            orgSlug,
            board.getSlug(),
            Collections.singletonList(grade.getSlug()),
            subjects.subjects());
    return calculateSubjectResponses(
        orgSlug,
        subjects.subjects(),
        subtopicList,
        Collections.singletonList(student.getId()),
        false,
        null,
        null);
  }

  public List<SubTopicResponse> fetchSubtopics(
      String orgSlug, String boardSlug, List<String> gradeSlug, List<String> subjects) {
    List<SubTopicResponse> subtopicList =
        contentService.getSubtopicsByBoardAndGradeAndSubject(
            orgSlug, boardSlug, gradeSlug, subjects);

    if (subtopicList == null || subtopicList.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.SubTopicNotFound");
    }
    return subtopicList;
  }

  public List<DacKmDto.SubjectResponse> calculateSubjectResponses(
      String orgSlug,
      List<String> subjects,
      List<SubTopicResponse> subtopicList,
      List<Long> studentIds,
      Boolean showSubTopicResponse,
      String fromDate,
      String toDate) {

    List<DacKmDto.SubjectResponse> response = new ArrayList<>();

    for (String slug : subjects) {
      var subject = strapiService.getSubjectBySlug(slug);
      List<SubTopicResponse> subtopics =
          subtopicList.stream()
              .filter(subtopic -> subject.getSlug().equals(subtopic.getSubjectSlug()))
              .toList();
      List<String> subtopicSlugs = subtopics.stream().map(SubTopicResponse::getSlug).toList();

      Double averagePercentage;
      if (Objects.nonNull(fromDate) && Objects.nonNull(toDate)) {
        averagePercentage =
            dacKmRepository.getOverallOrgAndGradeByAvgSec(
                orgSlug, subtopicSlugs, studentIds, fromDate, toDate);
      } else {
        averagePercentage =
            dacKmRepository.getOverallOrgAndGradeByAvgSec(
                orgSlug, subtopicSlugs, studentIds, null, null);
      }
      response.add(
          DacKmDto.SubjectResponse.builder()
              .subjectName(subject.getName())
              .subjectSlug(subject.getSlug())
              .subjectAverage(Objects.isNull(averagePercentage) ? 0 : averagePercentage)
              .chapters(
                  averagePercentage == null
                      ? null
                      : buildChapterResponse(
                          orgSlug, subtopics, studentIds, showSubTopicResponse, fromDate, toDate))
              .build());
    }
    var filterSubjects = response.stream().filter(x -> x.chapters() != null).toList();
    List<DacKmDto.SubjectResponse> sortedResponse =
        filterSubjects.stream()
            .sorted(
                Comparator.comparing(DacKmDto.SubjectResponse::subjectAverage)
                    .reversed()
                    .thenComparing(s -> s.subjectAverage() == 0 ? 1 : 0))
            .toList();

    return sortedResponse;
  }

  public List<DacKmDto.ChapterResponse> getChapterWiseKms(
      String orgSlug, String authUserId, List<String> chapters) {
    User user = userRepository.getUserByAuthUserId(authUserId);
    Student student = studentRepository.findByUserId(user.getId());
    List<DacKmDto.ChapterResponse> response = new ArrayList<>();

    List<TestScheduleKnowledge> testScheduleKnowledges =
        dacKmRepository.findAllByOrgSlugAndChapterSlugInAndStudentIdIn(
            orgSlug, chapters, Collections.singletonList(student.getId()));
    Double averagePercentage = null;

    if (!testScheduleKnowledges.isEmpty()) {
      averagePercentage = calculateKnowledgePercentage(testScheduleKnowledges);
    }
    var chapter = contentService.getChapterBySlug(chapters.get(0));
    List<SubTopicResponse> subtopicList =
        contentService.getSubtopicsByBoardAndGradeAndSubject(
            orgSlug,
            chapter.getEduboardSlug(),
            Collections.singletonList(chapter.getGradeSlug()),
            Collections.singletonList(chapter.getSubjectSlug()));
    List<SubTopicResponse> chapterSubtopics =
        subtopicList.stream()
            .filter(subTopic -> chapter.getChapterSlug().equals(subTopic.getChapterSlug()))
            .toList();
    response.add(
        DacKmDto.ChapterResponse.builder()
            .chapterName(chapter.getName())
            .subjectName(strapiService.getSubjectNameBySlug(chapter.getSubjectSlug()))
            .chapterSlug(chapter.getChapterSlug())
            .average(Objects.isNull(averagePercentage) ? 0 : averagePercentage)
            .subtopics(buildSubtopicResponse(orgSlug, student.getId(), chapterSubtopics))
            .build());

    return response;
  }

  private List<DacKmDto.GeneralResponse> buildSubtopicResponse(
      String orgSlug, Long studentId, List<SubTopicResponse> chapterSubtopics) {
    return chapterSubtopics.stream()
        .map(
            subtopic -> {
              List<TestScheduleKnowledge> testScheduleKm =
                  dacKmRepository.findByOrgSlugAndStudentIdAndSubTopicSlug(
                      orgSlug, studentId, subtopic.getSlug());
              return testScheduleKm.isEmpty()
                  ? DacKmDto.GeneralResponse.builder()
                      .name(subtopic.getName())
                      .slug(subtopic.getSlug())
                      .averagePercentage(0.0)
                      .examRecords(Collections.emptyList())
                      .build()
                  : processTestScheduleKnowledge(subtopic, testScheduleKm.get(0));
            })
        .toList();
  }

  private DacKmDto.GeneralResponse processTestScheduleKnowledge(
      SubTopicResponse subTopicResponse, TestScheduleKnowledge testScheduleKm) {
    ScheduleTest testSchedule =
        scheduleTestRepository
            .findById(testScheduleKm.getKnowledgeDetails().getFirst().getTestScheduleId())
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, "error.InvalidTestSchedule"));
    var examRecords =
        DacKmDto.ExamRecords.builder()
            .examId(testScheduleKm.getKnowledgeDetails().getFirst().getExamId())
            .title(testSchedule.getTestDefinition().getTestName())
            .build();
    var averagePercentage = calculateKnowledgePercentage(Collections.singletonList(testScheduleKm));
    return DacKmDto.GeneralResponse.builder()
        .name(subTopicResponse.getName())
        .slug(testScheduleKm.getSubTopicSlug())
        .averagePercentage(Objects.isNull(averagePercentage) ? 0 : averagePercentage)
        .examRecords(Collections.singletonList(examRecords))
        .build();
  }

  public DacKmDto.gradeResponse getKmByGrade(
      String orgSlug,
      String boardSlug,
      String gradeSlug,
      DacKmDto.Data subjects,
      Long fromDate,
      Long toDate) {
    var subjectData =
        buildSubjectResponse(orgSlug, boardSlug, gradeSlug, subjects, fromDate, toDate);
    var average = buildSubjectAverage(subjectData);

    return DacKmDto.gradeResponse
        .builder()
        .name(gradeSlug)
        .averagePercentage(average)
        .subjectResponses(subjectData)
        .build();
  }

  private double buildSubjectAverage(List<DacKmDto.SubjectResponse> subjectData) {
    double total = 0;
    double count = 0;

    for (DacKmDto.SubjectResponse subject : subjectData) {
      Double subjectAverage = subject.subjectAverage();
      if (subjectAverage != null) {
        total += subjectAverage;
        count++;
      }
    }

    return count > 0 ? validationUtils.formatMarks(total / count) : 0;
  }

  private List<DacKmDto.SubjectResponse> buildSubjectResponse(
      String orgSlug,
      String boardSlug,
      String gradeSlug,
      DacKmDto.Data subjects,
      Long fromDate,
      Long toDate) {
    LocalDateTime fDate = null;
    LocalDateTime tDate = null;
    fDate = dateTimeUtil.convertEpochToIso8601Legacy(fromDate);
    tDate = dateTimeUtil.convertEpochToIso8601Legacy(toDate);
    List<String> subjectSlugs = subjects.subjects();
    return buildSubjectResponse(
        dacKmRepository.getKmByGrade(
            orgSlug,
            boardSlug,
            gradeSlug,
            subjectSlugs,
            fDate.toLocalDate().toString(),
            tDate.toLocalDate().toString()),
        subjects);
  }

  private List<DacKmDto.SubjectResponse> buildSubjectResponse(
      List<DacKmByGrade> kmByGrade, DacKmDto.Data subjects) {
    List<DacKmDto.SubjectResponse> responses = new ArrayList<>();

    for (String subject : subjects.subjects()) {
      var subjectd = kmByGrade.stream().filter(x -> x.getSubjectSlug().equals(subject)).findAny();
      responses.add(
          DacKmDto.SubjectResponse.builder()
              .subjectSlug(subject)
              .subjectAverage(subjectd.map(DacKmByGrade::getAvgKnowledgePercentage).orElse(null))
              .build());
    }
    return responses;
  }

  public List<DacKmDto.BoardSummary> dacKmSummary(String orgSlug, Long fromDate, Long toDate) {
    var request = buildKMOverAllRequest(orgSlug);
    List<DacKmDto.BoardSummary> response = new ArrayList<>();
    request
        .boards()
        .forEach(
            board -> {
              DacKmDto.DacKmResponse overAllSummary =
                  getSummaryByBoard(orgSlug, board.slug(), fromDate, toDate, board.grades());
              response.add(buildBoardSummary(board, orgSlug, overAllSummary, fromDate, toDate));
            });
    return response;
  }

  private DacKmDto.BoardSummary buildBoardSummary(
      DacKmDto.BoardsRequest board,
      String orgSlug,
      DacKmDto.DacKmResponse overAllSummary,
      Long fromDate,
      Long toDate) {
    return DacKmDto.BoardSummary.builder()
        .boardName(board.name())
        .boardSlug(board.slug())
        .summary(buildSummary(board, orgSlug, overAllSummary, fromDate, toDate))
        .build();
  }

  private DacKmDto.DacKmResponse buildSummary(
      DacKmDto.BoardsRequest board,
      String orgSlug,
      DacKmDto.DacKmResponse overAllSummary,
      Long fromDate,
      Long toDate) {
    return DacKmDto.DacKmResponse.builder()
        .averagePercentage(overAllSummary.averagePercentage())
        .organization(overAllSummary.organization())
        .grades(buildGradesSummary(board, overAllSummary.grades(), orgSlug, fromDate, toDate))
        .build();
  }

  private List<DacKmDto.GeneralResponse> buildGradesSummary(
      DacKmDto.BoardsRequest board,
      List<DacKmDto.GeneralResponse> grades,
      String orgSlug,
      Long fromDate,
      Long toDate) {
    return grades.stream()
        .map(grade -> buildGradeResponse(board, grade, orgSlug, fromDate, toDate))
        .toList();
  }

  private DacKmDto.GeneralResponse buildGradeResponse(
      DacKmDto.BoardsRequest board,
      DacKmDto.GeneralResponse grade,
      String orgSlug,
      Long fromDate,
      Long toDate) {
    return DacKmDto.GeneralResponse.builder()
        .averagePercentage(grade.averagePercentage())
        .name(grade.name())
        .slug(grade.slug())
        .sectionResponses(buildSectionResponses(board, grade, orgSlug, fromDate, toDate))
        .build();
  }

  private List<DacKmDto.SectionResponse> buildSectionResponses(
      DacKmDto.BoardsRequest board,
      DacKmDto.GeneralResponse grade,
      String orgSlug,
      Long fromDate,
      Long toDate) {
    List<String> subjects = extractSubjects(board, grade);
    DacKmDto.Data subjectData = DacKmDto.Data.builder().subjects(subjects).build();

    List<DacKmDto.SectionResponse> sections =
        getSummaryByBoardAndGrade(
            orgSlug, board.slug(), grade.slug(), subjectData, fromDate, toDate);

    return sections.stream()
        .map(
            section ->
                buildSectionResponse(section, orgSlug, board, grade, fromDate, toDate, subjectData))
        .toList();
  }

  private List<String> extractSubjects(
      DacKmDto.BoardsRequest board, DacKmDto.GeneralResponse grade) {
    return board.grades().stream()
        .filter(x -> x.getGradeSlug().equals(grade.slug()))
        .flatMap(x -> x.getSubjects().stream())
        .toList();
  }

  private DacKmDto.SectionResponse buildSectionResponse(
      DacKmDto.SectionResponse section,
      String orgSlug,
      DacKmDto.BoardsRequest board,
      DacKmDto.GeneralResponse grade,
      Long fromDate,
      Long toDate,
      DacKmDto.Data subjectData) {
    return DacKmDto.SectionResponse.builder()
        .name(section.name())
        .uuid(section.uuid())
        .data(buildSectionData(orgSlug, board, grade, fromDate, toDate, subjectData))
        .averagePercentage(section.averagePercentage())
        .build();
  }

  private List<DacKmDto.GeneralResponse> buildSectionData(
      String orgSlug,
      DacKmDto.BoardsRequest board,
      DacKmDto.GeneralResponse grade,
      Long fromDate,
      Long toDate,
      DacKmDto.Data subjectData) {
    return sectionRepository
        .getSectionsUsingGradeSlugsAndBoardSlugs(
            Collections.singletonList(grade.slug()), orgSlug, board.slug())
        .stream()
        .map(
            section -> buildGeneralResponse(orgSlug, board, section, fromDate, toDate, subjectData))
        .toList();
  }

  private DacKmDto.GeneralResponse buildGeneralResponse(
      String orgSlug,
      DacKmDto.BoardsRequest board,
      Section section,
      Long fromDate,
      Long toDate,
      DacKmDto.Data subjectData) {
    List<DacKmDto.SubjectResponse> subjectResponses =
        getSummaryBySection(
            orgSlug,
            board.slug(),
            String.valueOf(section.getUuid()),
            subjectData,
            fromDate,
            toDate);

    return DacKmDto.GeneralResponse.builder().subjectResponses(subjectResponses).build();
  }

  public void uploadKmSummary(String orgSlug, Long fromDate, Long toDate) {
    var destPath = constructPath(orgSlug);
    var dacKmLog = saveOrUpdateDacKmLog(orgSlug, DacKMLogDto.JobStatus.IN_PROGRESS, null, null);
    try {
      var getKmSummaryJson = dacKmSummary(orgSlug, fromDate, toDate);
      ObjectMapper objectMapper = new ObjectMapper();
      String jsonString = objectMapper.writeValueAsString(getKmSummaryJson);
      byte[] jsonBytes = jsonString.getBytes();
      InputStream inputStream = new ByteArrayInputStream(jsonBytes);
      storageService.uploadFile(inputStream, destPath, jsonBytes.length);
      saveOrUpdateDacKmLog(orgSlug, DacKMLogDto.JobStatus.COMPLETED, null, dacKmLog);
    } catch (Exception exp) {
      saveOrUpdateDacKmLog(orgSlug, DacKMLogDto.JobStatus.FAILED, exp.getMessage(), dacKmLog);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.uploadKmSummary", exp);
    }
  }

  private DacKMLog saveOrUpdateDacKmLog(
      String orgSlug, DacKMLogDto.JobStatus status, String failureReason, DacKMLog dacKmLogData) {
    if (dacKmLogData == null) {
      dacKmLogData = DacKMLog.builder().orgSlug(orgSlug).build();
    }
    dacKmLogData.setStatus(status);
    dacKmLogData.setFailureReason(buildFailureReason(failureReason));
    dacKMLogRepository.save(dacKmLogData);
    return dacKmLogData;
  }

  private String buildFailureReason(String failureReason) {
    if (failureReason == null) {
      return null;
    }
    return failureReason.length() > 5000 ? failureReason.substring(0, 5000) : failureReason;
  }

  private DacKmDto.KMOverAllRequest buildKMOverAllRequest(String orgSlug) {
    return DacKmDto.KMOverAllRequest.builder().boards(buildBoards(orgSlug)).build();
  }

  private List<DacKmDto.BoardsRequest> buildBoards(String orgSlug) {
    List<DacKmDto.BoardsRequest> response = new ArrayList<>();
    var orgCurriculum = curriculumService.getBoardsHierarchy(orgSlug);
    orgCurriculum.forEach(
        board ->
            response.add(
                DacKmDto.BoardsRequest.builder()
                    .name(board.getName())
                    .slug(board.getSlug())
                    .grades(buildGrades(board.getGrades()))
                    .build()));
    return response;
  }

  private List<KMGradesRequest> buildGrades(List<com.wexl.retail.model.Grade> grades) {
    List<KMGradesRequest> response = new ArrayList<>();
    grades.forEach(
        grade -> {
          var subjects = grade.getSubjects().stream().map(Subject::getSlug).distinct().toList();
          response.add(
              KMGradesRequest.builder().gradeSlug(grade.getSlug()).subjects(subjects).build());
        });
    return response;
  }

  private String constructPath(String orgSlug) {
    return String.format("%s/dac-km-summary.json", orgSlug);
  }

  public List<DacKmDto.BoardSummary> getDacKmSummary(String orgSlug) {
    List<DacKmDto.BoardSummary> res = new ArrayList<>();
    String filePath = constructPath(orgSlug);
    var fileInputStream = storageService.getInputStream(filePath);
    try {
      ObjectMapper objectMapper = new ObjectMapper();
      res =
          objectMapper.readValue(
              fileInputStream, new TypeReference<List<DacKmDto.BoardSummary>>() {});
    } catch (IOException e) {
      e.printStackTrace();
    }

    return res;
  }
}
