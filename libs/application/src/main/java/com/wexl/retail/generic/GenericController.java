package com.wexl.retail.generic;

import com.wexl.retail.commons.security.annotation.IsNonAdmin;
import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.commons.security.annotation.IsTeacher;
import com.wexl.retail.generic.LineItems.LineItem;
import com.wexl.retail.section.dto.response.SectionResponse;
import com.wexl.retail.section.service.SectionService;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/")
@IsNonAdmin
@RequiredArgsConstructor
public class GenericController {

  private final GenericService genericService;
  private final SectionService sectionService;

  @PostMapping("generic")
  public GenericResponse getData(@Valid @RequestBody GenericRequest genericRequest) {
    return genericService.fetchData(genericRequest);
  }

  @PostMapping("generic/{key}")
  public GenericResponse getDataByUserId(@Valid @PathVariable String key) {
    return genericService.fetchDataById(key);
  }

  @IsOrgAdminOrTeacher
  @GetMapping("orgs/{orgId}/grades/{gradeId}/sections")
  public List<SectionResponse> getSectionsInGrade(
      @PathVariable String orgId,
      @PathVariable int gradeId,
      @RequestParam(required = false) String board) {
    return sectionService.getSectionsOfGrade(orgId, gradeId, board);
  }

  @GetMapping("orgs/{orgId}/items")
  @IsTeacher
  public LineItems genericItemsForTeacher(@PathVariable String orgId) {
    return genericService.getResources(orgId);
  }

  @GetMapping("orgs/{orgId}/items/{gradeSlug}")
  @IsStudent
  public LineItems genericItemsForTeacher(
      @PathVariable String orgId, @PathVariable String gradeSlug) {
    List<LineItem> itemList =
        genericService.getResources(orgId).getConfig().stream()
            .filter(i -> i.getGradeSlug().equals(gradeSlug))
            .toList();

    return new LineItems(itemList);
  }
}
