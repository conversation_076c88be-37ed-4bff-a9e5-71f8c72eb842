package com.wexl.retail.qpgen.repository;

import com.wexl.retail.qpgen.model.BluePrint;
import com.wexl.retail.qpgen.model.BluePrintSections;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface BluePrintSectionRepository extends JpaRepository<BluePrintSections, Long> {
  List<BluePrintSections> findAllByBluePrintOrderBySectionName(BluePrint bluePrint);

  @Query(
      value =
          """
              select qgbs.* from qp_gen_blueprint_sections qgbs
              join qp_gen_blueprints qgb on qgb.id = qgbs.blue_print_id
              join qp_gen_pro qgp on qgp.blueprint_id = qgb.id
              where qgp.test_definition_id = :testDefinitionId
              """,
      nativeQuery = true)
  List<BluePrintSections> getBpSectionsByTestDefinitionId(Long testDefinitionId);
}
