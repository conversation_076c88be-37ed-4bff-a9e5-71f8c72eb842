package com.wexl.retail.pdf.viewer.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.sql.Timestamp;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AnnotationResponse {
  private long id;
  private String icon;
  private String text;
  private String font;
  private String color;
  private double opacity;
  private String coordinate;
  private List<CommentResponse> comments;

  @JsonProperty("doc_id")
  private String docId;

  @JsonProperty("read_only")
  private int readOnly;

  @JsonProperty("read_only_comment")
  private boolean readOnlyComment;

  @JsonProperty("background_color")
  private String backgroundColor;

  @JsonProperty("font_size")
  private int fontSize;

  @JsonProperty("page_index")
  private long pageIndex;

  @JsonProperty("page_width")
  private long pageWidth;

  @JsonProperty("page_height")
  private long pageHeight;

  @JsonProperty("form_field_name")
  private String formFieldName;

  @JsonProperty("form_field_value")
  private String formFieldValue;

  @JsonProperty("line_width")
  private long lineWidth;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonProperty("date_created")
  private Timestamp createdAt;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonProperty("date_modified")
  private Timestamp updatedAt;

  @JsonProperty("annotation_type_id")
  private long annotationTypeId;

  @JsonProperty("calibration_value")
  private String calibrationValue;

  @JsonProperty("calibration_label")
  private String calibrationLabel;

  @JsonProperty("calibration_measurement_type_id")
  private long calibrationMeasurementTypeId;

  @JsonProperty("measurement_type_id")
  private long measurementTypeId;

  @JsonProperty("line_style_id")
  private long lineStyleId;

  @JsonProperty("drawing_positions")
  private List<DrawingPositionResponse> drawingPositions;

  @JsonProperty("highlight_text_rects")
  private List<HighlightTextRectResponse> highlightTextRects;
}
