package com.wexl.retail.mlp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.mlp.model.QuestionsAssigneeMode;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
public class MlpRequest {

  private String title;

  @JsonProperty("teacher_id")
  @NotNull
  private Long teacherId;

  @JsonProperty("section_uuid")
  private String sectionUuid;

  @JsonProperty("subject_slug")
  @NotNull
  private String subjectSlug;

  @JsonProperty("chapter_slug")
  @NotNull
  private String chapterSlug;

  @JsonProperty("subtopic_slug")
  private String subtopicSlug;

  @JsonProperty("video_slug")
  private String videoSlug;

  @JsonProperty("alt_video_slug")
  private String altVideoSlug;

  @JsonProperty("synopsis_slug")
  private String synopsisSlug;

  @JsonProperty("grade_slug")
  private String gradeSlug;

  @JsonProperty("description")
  private String description;

  @JsonProperty("question_count")
  private Integer questionCount;

  @JsonProperty("day_of_week")
  private Integer dayOfWeek;

  @JsonProperty("video_source")
  private String videoSource;

  private QuestionsAssigneeMode questionsAssigneeMode = QuestionsAssigneeMode.AUTO;

  @JsonProperty(value = "board_slug", required = false)
  private String boardSlug;

  @JsonProperty("sha_link")
  private String shaLink;
}
