package com.wexl.retail.attendance.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_DEFAULT)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AttendanceResponse {
  @JsonProperty("attendance_id")
  private long attendanceId;

  @JsonProperty("meeting_start_time")
  private long meetingStartTime;

  private String grade;

  @JsonProperty("meeting_id")
  private long meetingId;

  @JsonProperty("section_name")
  private String sectionName;

  @JsonProperty("section_uuid")
  private String sectionUuid;

  @JsonProperty("count")
  private long studentCount;

  @JsonProperty("meeting_name")
  private String meetingName;

  @JsonProperty("zoom_meeting_number")
  private String zoomMeetingNumber;
}
