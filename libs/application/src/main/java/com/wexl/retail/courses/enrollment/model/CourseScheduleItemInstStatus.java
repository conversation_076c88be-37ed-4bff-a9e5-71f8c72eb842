package com.wexl.retail.courses.enrollment.model;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum CourseScheduleItemInstStatus {
  NOT_STARTED("NOT_STARTED"),
  IN_PROGRESS("IN_PROGRESS"),
  COMPLETED("COMPLETED"),
  ABANDONED("ABANDONED");

  private final String value;

  public static CourseScheduleItemInstStatus fromValue(String value) {
    if (value == null || "".equals(value)) {
      throw new IllegalArgumentException("Value cannot be null or empty!");
    }

    for (var enumEntry : CourseScheduleItemInstStatus.values()) {
      if (enumEntry.toString().equalsIgnoreCase(value)) {
        return enumEntry;
      }
    }

    throw new IllegalArgumentException("Do not understand the Type " + value);
  }

  @Override
  public String toString() {
    return this.value;
  }
}
