package com.wexl.retail.zoom.domain;

import jakarta.persistence.*;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "event_lc")
@EqualsAndHashCode(callSuper = true)
public class LcEvent extends Event {

  @Id
  @GeneratedValue
  @GenericGenerator(
      name = "lc-event-sequence-generator",
      strategy = "org.hibernate.id.enhanced.SequenceStyleGenerator",
      parameters = {
        @org.hibernate.annotations.Parameter(name = "sequence_name", value = "lc_event_id_seq"),
        @org.hibernate.annotations.Parameter(name = "initial_value", value = "2200000"),
        @org.hibernate.annotations.Parameter(name = "increment_size", value = "1")
      })
  private long id;

  @Column(name = "host_id")
  private String hostId;

  @Column(name = "event_duration")
  private Integer eventDuration;

  @Column(name = "event_topic")
  private String eventTopic;

  @Column(name = "end_time")
  private Timestamp endTime;

  @Column(name = "event_tz")
  private String eventTz;
}
