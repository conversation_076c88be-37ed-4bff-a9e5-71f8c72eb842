package com.wexl.retail.client.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

@Service
public class LocaleSelectorService {

  @Value("${app.localeFilesLocation}")
  private Resource localeFilesLocation;

  @Value("${app.i18n.enJson:en.json}")
  private String enJsonFile;

  public String getSelectedLocaleFile(String inputLang) {
    try {
      var lang = hackyPmpLanguageFix(inputLang);
      final File file = localeFilesLocation.getFile();
      for (File listFile : Objects.requireNonNull(file.listFiles())) {
        if (listFile.getName().contains(lang)) {
          return FileUtils.readFileToString(listFile, StandardCharsets.UTF_8);
        }
      }
    } catch (Exception e) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.LocaleFileNotFound",
          new String[] {inputLang},
          e);
    }
    throw new ApiException(
        InternalErrorCodes.INVALID_REQUEST, "error.LocaleFileNotFound", new String[] {inputLang});
  }

  private String hackyPmpLanguageFix(String lang) {
    if ("en.json".equals(lang)) {
      return enJsonFile;
    }
    return lang;
  }
}
