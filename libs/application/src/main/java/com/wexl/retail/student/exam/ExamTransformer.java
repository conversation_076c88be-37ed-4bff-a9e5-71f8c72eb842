package com.wexl.retail.student.exam;

import org.springframework.stereotype.Component;

@Component
public class ExamTransformer {

  public ExamResponse mapExamToExamResponse(Exam exam) {
    return ExamResponse.builder()
        .allowedDuration(exam.getAllowedDuration())
        .examDifficultyLevelId(exam.getExamDifficultyLevelId())
        .examType(exam.getExamType())
        .noOfQuestions(exam.getNoOfQuestions())
        .startTime(exam.getStartTime())
        .id(exam.getId())
        .build();
  }
}
