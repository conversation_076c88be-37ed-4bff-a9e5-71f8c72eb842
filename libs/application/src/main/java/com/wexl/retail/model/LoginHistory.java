package com.wexl.retail.model;

import jakarta.persistence.*;
import java.util.Date;
import lombok.*;

@Data
@Entity
@Table(name = "login_history")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoginHistory extends Model {

  @Id @GeneratedValue private Long id;

  @NonNull @Column private Long userId;

  private Date loginTime;

  @Enumerated(EnumType.STRING)
  @Column(name = "login_device")
  private LoginDevice loginDevice;

  @Enumerated(EnumType.STRING)
  @Column(name = "login_method")
  private LoginMethod loginMethod;
}
