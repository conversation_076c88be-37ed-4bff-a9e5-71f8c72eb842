package com.wexl.retail.teacher.student;

import static com.wexl.retail.util.Constants.AUTHORIZATION_HEADER;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.security.annotation.IsTeacher;
import com.wexl.retail.student.answer.ExamAnswerDto;
import com.wexl.retail.student.answer.ExamQuestionsMarks;
import com.wexl.retail.student.answer.StudentAnswerResponse;
import com.wexl.retail.student.exam.ExamService;
import com.wexl.retail.util.UploadService;
import java.io.InputStream;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.CacheControl;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/teacher/student")
public class TeacherStudentController {

  private final ExamService examService;
  private final AuthService authService;
  private final UploadService uploadService;

  @IsTeacher
  @GetMapping("/result/{id}")
  public StudentAnswerResponse examResult(
      @PathVariable("id") long examId, @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {
    return examService.examResultByExamId(examId, bearerToken);
  }

  @IsTeacher
  @PutMapping("/correct/{id}")
  public StudentAnswerResponse correctExam(
      @PathVariable("id") long examId,
      @RequestBody List<ExamQuestionsMarks> examQuestionsMarks,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {

    return examService.correctExam(examId, examQuestionsMarks, bearerToken);
  }

  @IsTeacher
  @PutMapping("/exam-answers")
  public void editStudentAnswer(@RequestBody ExamAnswerDto.ExamAnswerUpdateRequest updateRequests) {
    examService.updateExamAnswersById(updateRequests);
  }

  @IsTeacher
  @PutMapping("/exam-answers-marks/{examId}")
  public void editExamAnswerMarks(
      @PathVariable("examId") long examId,
      @RequestBody List<ExamQuestionsMarks> examQuestionsMarks) {
    examService.updateExamAnswersMarks(examId, examQuestionsMarks);
  }

  @GetMapping(value = "/answer-sheet/{examId}", produces = "application/pdf")
  public ResponseEntity<InputStreamResource> getDocument(@PathVariable long examId) {

    InputStream s3is = uploadService.getS3ObjectInputStream(examId);
    return ResponseEntity.ok()
        .contentType(org.springframework.http.MediaType.APPLICATION_PDF)
        .cacheControl(CacheControl.noCache())
        .header("Content-Disposition", "attachment; filename=" + examId + ".pdf")
        .body(new InputStreamResource(s3is));
  }
}
