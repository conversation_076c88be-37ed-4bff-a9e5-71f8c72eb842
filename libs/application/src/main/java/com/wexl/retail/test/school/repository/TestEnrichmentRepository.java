package com.wexl.retail.test.school.repository;

import com.wexl.retail.test.school.domain.TestEnrichment;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface TestEnrichmentRepository extends JpaRepository<TestEnrichment, Long> {

  List<TestEnrichment> findByQuestionUuidIn(List<String> questionUuids);
}
