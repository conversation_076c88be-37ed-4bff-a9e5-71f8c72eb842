package com.wexl.retail.student.answer;

import com.wexl.retail.model.Model;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Entity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "exam_answer_attachments")
public class ExamAnswerAttachment extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "exam_answer_attachment_id_seq")
  @SequenceGenerator(name = "exam_answer_attachment_id_seq", allocationSize = 1)
  private long id;

  @ManyToOne
  @Getter(AccessLevel.PRIVATE)
  @JoinColumn(name = "answer_id")
  private ExamAnswer answer;

  private String path;
}
