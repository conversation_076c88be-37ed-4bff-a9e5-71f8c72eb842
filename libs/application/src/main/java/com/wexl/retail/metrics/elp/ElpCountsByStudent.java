package com.wexl.retail.metrics.elp;

import com.wexl.retail.elp.service.ElpService;
import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.handler.AbstractMetricHandler;
import com.wexl.retail.metrics.handler.MetricHandler;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ElpCountsByStudent extends AbstractMetricHandler implements MetricHandler {

  private final ElpService elpService;

  @Override
  public String name() {
    return "elp-counts-byStudent";
  }

  @Override
  public List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    List<String> section =
        Optional.ofNullable(genericMetricRequest.getInput().get(SECTION))
            .map(List.class::cast)
            .orElse(Collections.emptyList());
    return elpService.getElpCountsByStudent(org, genericMetricRequest.getFromDate(), section);
  }
}
