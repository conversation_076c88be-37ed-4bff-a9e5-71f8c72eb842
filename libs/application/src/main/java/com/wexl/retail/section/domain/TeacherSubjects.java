package com.wexl.retail.section.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wexl.retail.model.Teacher;
import jakarta.persistence.*;
import java.sql.Timestamp;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Entity
@Table(name = "teacher_subjects")
@Data
@Builder
@EntityListeners(AuditingEntityListener.class)
@AllArgsConstructor
@NoArgsConstructor
public class TeacherSubjects {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  @CreatedDate private Timestamp createdAt;

  @LastModifiedDate private Timestamp updatedAt;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
  private Date deletedAt;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "section_id")
  private Section section;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "teacher_id")
  private Teacher teacher;

  @Column(name = "subject_slug")
  private String subject;

  @Column(name = "board_slug")
  private String board;
}
