package com.wexl.retail.metrics.handler;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class GradeWiseCountOfNotification extends AbstractMetricHandler {

  @Override
  public String name() {
    return "grade-wise-notifications-count";
  }

  @Override
  protected List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    return notificationsService.getNotificationCountByGrade(org);
  }
}
