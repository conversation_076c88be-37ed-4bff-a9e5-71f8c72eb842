package com.wexl.retail.thread.model;

import com.wexl.retail.model.Model;
import com.wexl.retail.model.Teacher;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "thread_reply")
public class ThreadReply extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String reply;

  @ManyToOne
  @JoinColumn(name = "thread_id")
  private Thread thread;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "teacher_id")
  private Teacher teacher;

  @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
  @JoinColumn(name = "thread_reply_id")
  private List<ThreadReplyComments> replyComments;
}
