package com.wexl.retail.products.model;

import com.wexl.retail.model.Model;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "product_courses")
public class ProductCourses extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Type(JsonType.class)
  @Column(name = "course_def_ids", columnDefinition = "jsonb")
  private List<Long> courseDefinitionId;

  private String productRef;

  @JoinColumn(name = "org_slug")
  private String orgSlug;

  @Type(JsonType.class)
  @Column(name = "course_bundle_ids", columnDefinition = "jsonb")
  private List<Long> courseBundleIds;
}
