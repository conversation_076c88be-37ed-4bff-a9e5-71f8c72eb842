package com.wexl.retail.proctoring.repository;

import com.wexl.retail.proctoring.model.ProctoringSession;
import com.wexl.retail.proctoring.model.ProctoringStatus;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ProctoringRepository extends JpaRepository<ProctoringSession, Long> {
  Optional<ProctoringSession> findByExamId(Long examId);

  Optional<ProctoringSession> findByTssUuidAndStatus(String tssUuid, ProctoringStatus status);

  List<ProctoringSession> findByOrgSlugAndStudentId(String orgSlug, long studentId);

  Optional<ProctoringSession> findByIdAndOrgSlug(Long proctoringId, String orgSlug);

  Optional<ProctoringSession> findByStudentIdAndTestScheduleId(Long studentId, Long testScheduleId);

  List<ProctoringSession> findByExamIdOrderByEndTimeDesc(Long examId);
}
