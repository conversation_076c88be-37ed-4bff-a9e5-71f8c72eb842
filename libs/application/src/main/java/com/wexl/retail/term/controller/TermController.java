package com.wexl.retail.term.controller;

import com.wexl.retail.term.dto.TermDto;
import com.wexl.retail.term.service.TermService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}")
@RequiredArgsConstructor
public class TermController {
  private final TermService termService;

  @GetMapping("/terms")
  public List<TermDto.TermDetails> getAllTermDetails(
      @RequestParam("grade") String gradeSlug, @RequestParam("board") String boardSlug) {
    return termService.getAllTerms(gradeSlug, boardSlug);
  }

  @PostMapping("/term-assessments/{termAssessmentId}")
  public void createAssessmentCategory(
      @PathVariable String orgSlug,
      @PathVariable Long termAssessmentId,
      @RequestBody TermDto.TermAssessmentCategoryRequest request) {
    termService.createAssessmentCategory(orgSlug, termAssessmentId, request);
  }

  @GetMapping("/term-assessments/{termAssessmentId}")
  public List<TermDto.TermAssessmentCategoryDetails> getAssessmentCategories(
      @PathVariable String orgSlug, @PathVariable Long termAssessmentId) {
    return termService.getAssessmentCategories(orgSlug, termAssessmentId);
  }

  @PutMapping("/term-assessment-categories/{assessmentCategoryId}")
  public void updateAssessmentCategory(
      @PathVariable Long assessmentCategoryId,
      @RequestBody TermDto.TermAssessmentCategoryRequest request) {
    termService.updateAssessmentCategory(assessmentCategoryId, request);
  }

  @DeleteMapping("/term-assessment-categories/{assessmentCategoryId}")
  public void deleteAssessmentCategory(@PathVariable Long assessmentCategoryId) {
    termService.deleteAssessmentCategory(assessmentCategoryId);
  }
}
