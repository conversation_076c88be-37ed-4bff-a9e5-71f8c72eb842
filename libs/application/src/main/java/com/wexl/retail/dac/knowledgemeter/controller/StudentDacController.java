package com.wexl.retail.dac.knowledgemeter.controller;

import com.wexl.retail.dac.knowledgemeter.dto.DacKmDto;
import com.wexl.retail.dac.knowledgemeter.service.DacKmService;
import com.wexl.retail.mlp.dto.KMBoardRequest;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}/students/{authUserId}")
@RequiredArgsConstructor
public class StudentDacController {
  private final DacKmService dacKmService;

  @PostMapping("/dac-kms")
  public List<DacKmDto.SectionResponse> getSubjectWiseSummary(
      @PathVariable String orgSlug, @PathVariable String authUserId) {
    return dacKmService.getSubjectWiseSummary(orgSlug, authUserId);
  }

  @PostMapping("/dac-kms-subject")
  public List<DacKmDto.SubjectResponse> getSubjectWiseKms(
      @PathVariable String orgSlug,
      @PathVariable String authUserId,
      @RequestBody DacKmDto.Data subjects) {
    return dacKmService.getSubjectWiseKms(orgSlug, authUserId, subjects);
  }

  @PostMapping("/dac-kms-chapter")
  public List<DacKmDto.ChapterResponse> getChapterWiseKms(
      @PathVariable String orgSlug,
      @PathVariable String authUserId,
      @RequestBody KMBoardRequest request) {
    return dacKmService.getChapterWiseKms(orgSlug, authUserId, request.getData());
  }
}
