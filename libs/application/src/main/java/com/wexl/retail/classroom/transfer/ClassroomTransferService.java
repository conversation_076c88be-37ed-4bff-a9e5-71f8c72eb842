package com.wexl.retail.classroom.transfer;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.classroom.core.model.Classroom;
import com.wexl.retail.classroom.core.repository.ClassroomRepository;
import com.wexl.retail.classroom.history.ClassroomHistory;
import com.wexl.retail.classroom.history.ClassroomHistoryRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.model.Student;
import com.wexl.retail.util.ValidationUtils;
import java.time.LocalDateTime;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class ClassroomTransferService {
  private final ValidationUtils validationUtils;
  private final ClassroomRepository classroomRepository;
  private final ClassroomHistoryRepository classroomHistoryRepository;
  private final AuthService authService;

  @Transactional
  public void classroomTransfer(
      String orgSlug, ClassRoomTransferDto.Request request, Long oldClassroomId) {
    var student = validationUtils.isStudentValid(request.studentId());
    removeStudentFromOldClassroom(student.getId(), oldClassroomId, orgSlug);
    updateHistoryTable(student, oldClassroomId, orgSlug);
    insertStudentIntoNewClassroom(student, request.newClassRoomId(), orgSlug);
    insertIntoHistoryTable(student, request.newClassRoomId(), orgSlug);
  }

  private void insertStudentIntoNewClassroom(Student student, Long newClassroomId, String orgSlug) {
    var newClassroom = getClassroomByIdAndOrgSlug(newClassroomId, orgSlug);
    newClassroom.getStudents().add(student);
    classroomRepository.save(newClassroom);
  }

  private Classroom getClassroomByIdAndOrgSlug(Long newClassroomId, String orgSlug) {
    var newClassroom = classroomRepository.findByIdAndOrgSlug(newClassroomId, orgSlug);
    if (newClassroom.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.ClassRoomValidity.ClassIdAndOrg",
          new String[] {Long.toString(newClassroomId), orgSlug});
    }
    return newClassroom.get();
  }

  private void insertIntoHistoryTable(Student student, Long newClassroomId, String orgSlug) {
    var newClassroom = getClassroomByIdAndOrgSlug(newClassroomId, orgSlug);
    var data =
        ClassroomHistory.builder()
            .classroomName(newClassroom.getName())
            .startDate(LocalDateTime.now())
            .status(Boolean.TRUE)
            .student(student)
            .createdBy(authService.getUserDetails().getId())
            .build();
    classroomHistoryRepository.save(data);
  }

  private void updateHistoryTable(Student student, Long oldClassroomId, String orgSlug) {
    var oldClassroom = getClassroomByIdAndOrgSlug(oldClassroomId, orgSlug);
    var getHistory =
        classroomHistoryRepository.findByStudentAndClassroomNameAndEndDateIsNull(
            student, oldClassroom.getName());
    if (getHistory != null) {
      getHistory.setEndDate(LocalDateTime.now());
      getHistory.setStatus(Boolean.FALSE);
      classroomHistoryRepository.save(getHistory);
    }
  }

  private void removeStudentFromOldClassroom(Long studentId, Long oldClassroomId, String orgSlug) {
    var oldClassroom = getClassroomByIdAndOrgSlug(oldClassroomId, orgSlug);
    var findStudent =
        oldClassroom.getStudents().stream().filter(x -> x.getId() == studentId).findFirst();
    if (findStudent.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.ClassRoomValidity.ClassIdAndOrg",
          new String[] {Long.toString(oldClassroomId), orgSlug});
    }
    oldClassroom.getStudents().remove(findStudent.get());
    classroomRepository.save(oldClassroom);
  }
}
