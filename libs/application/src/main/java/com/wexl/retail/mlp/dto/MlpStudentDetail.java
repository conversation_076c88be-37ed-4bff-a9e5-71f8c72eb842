package com.wexl.retail.mlp.dto;

import com.wexl.retail.mlp.model.MlpItemStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MlpStudentDetail {
  private Long studentId;
  private Long examId;
  private Float marksScored;
  private Float totalMarks;
  private Long startTime;
  private Long endTime;

  private String userName;
  private String fullName;
  private String practiceStatus;
  private MlpItemStatus videoStatus;
  private MlpItemStatus synopsisStatus;
  private String subject;
  private String chapter;
  private String mlpName;
  private Double attendancePercentage;
  private Float percentage;
  private String subtopicSlug;
  private String videoSlug;
  private String synopsisSlug;
  private String orgSlug;
  private Long mlpId;
  private String section;
  private String orgName;
}
