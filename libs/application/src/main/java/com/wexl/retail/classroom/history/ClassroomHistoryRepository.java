package com.wexl.retail.classroom.history;

import com.wexl.retail.model.Student;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ClassroomHistoryRepository extends JpaRepository<ClassroomHistory, Long> {
  ClassroomHistory findByStudentAndClassroomNameAndEndDateIsNull(Student student, String name);

  List<ClassroomHistory> findAllByStudent(Student student);
}
