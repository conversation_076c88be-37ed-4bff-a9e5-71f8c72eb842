package com.wexl.retail.schedules.repository;

import com.wexl.retail.schedules.domain.SectionSchedule;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface SectionScheduleRepository extends JpaRepository<SectionSchedule, Long> {

  @Query(
      """
      SELECT ss from SectionSchedule ss \
      inner join fetch ss.section section \
      where ss.deletedAt is null and section.organization=:organization \
      and section.uuid=:sectionUuid\
      """)
  List<SectionSchedule> getSectionSchedules(String organization, UUID sectionUuid);

  @Query(
      value =
          """
                select ss.* from teacher_sections ts
                inner join section_schedule ss on ts.section_id = ss.section_id
                inner join sections s on s.id = ss.section_id
                where ss.deleted_at is null and ts.teacher_id = :teacherId and s.deleted_at is null""",
      nativeQuery = true)
  List<SectionSchedule> getAllTeacherSchedules(long teacherId);
}
