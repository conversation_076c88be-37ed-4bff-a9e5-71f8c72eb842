package com.wexl.retail.courses.teams.controller;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.courses.teams.model.ScheduleCourseDto;
import com.wexl.retail.courses.teams.service.TeamCoursesService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class TeamCoursesController {

  private final TeamCoursesService teamCoursesService;

  @PostMapping("orgs/{orgSlug}/teams/{teamId}/courses/{courseDefId}/schedule")
  public void scheduleCourseToTeam(
      @PathVariable String orgSlug,
      @PathVariable Long courseDefId,
      @RequestBody ScheduleCourseDto.ScheduleCourseRequest scheduleCourseRequest) {
    try {
      teamCoursesService.scheduleCourseToTeam(scheduleCourseRequest, orgSlug, courseDefId);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }
}
