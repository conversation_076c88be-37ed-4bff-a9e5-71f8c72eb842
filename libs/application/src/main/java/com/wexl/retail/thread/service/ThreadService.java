package com.wexl.retail.thread.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.organization.admin.teacher.TeacherService;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.services.StudentService;
import com.wexl.retail.thread.dto.ThreadDto;
import com.wexl.retail.thread.model.Thread;
import com.wexl.retail.thread.model.ThreadReply;
import com.wexl.retail.thread.model.ThreadReplyComments;
import com.wexl.retail.thread.repository.ThreadRepository;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ThreadService {

  private final StudentService studentService;
  private final TeacherService teacherService;
  private final UserRepository userRepository;
  private final TeacherRepository teacherRepository;
  private final ThreadRepository threadRepository;
  private final StudentRepository studentRepository;

  public void createThread(
      ThreadDto.CreateThreadRequest request, String orgSlug, String studentAuthId) {
    var student = studentService.getStudentByAuthId(studentAuthId);
    var teacher = teacherService.getTeacherByAuthId(request.assignedTo());
    threadRepository.save(buildThreadRequest(student, teacher, orgSlug, request));
  }

  private Thread buildThreadRequest(
      Student student, Teacher teacher, String orgSlug, ThreadDto.CreateThreadRequest request) {
    return Thread.builder()
        .title(request.title())
        .question(request.question())
        .createdBy(student)
        .tags(request.tags())
        .assignedTo(teacher)
        .orgSlug(orgSlug)
        .build();
  }

  public List<ThreadDto.ThreadResponse> getStudentThreads(String studentAuthId) {
    var student = studentService.getStudentByAuthId(studentAuthId);
    var studentThreads = threadRepository.findAllByCreatedBy(student);

    return buildStudentThreadsResponse(studentThreads);
  }

  private List<ThreadDto.ThreadResponse> buildStudentThreadsResponse(List<Thread> studentThreads) {
    List<ThreadDto.ThreadResponse> responseList = new ArrayList<>();
    studentThreads.forEach(
        thread -> {
          var teacherUser = thread.getAssignedTo().getUserInfo();
          var studentUser = thread.getCreatedBy().getUserInfo();
          responseList.add(
              ThreadDto.ThreadResponse.builder()
                  .Id(thread.getId())
                  .title(thread.getTitle())
                  .question(thread.getQuestion())
                  .tags(thread.getTags())
                  .teacherName(teacherUser.getFirstName() + " " + teacherUser.getLastName())
                  .studentName(studentUser.getFirstName() + " " + studentUser.getLastName())
                  .replyCount(threadRepository.replyCountByThreadId(thread.getId()))
                  .createdAt(
                      DateTimeUtil.convertIso8601ToEpoch(thread.getCreatedAt().toLocalDateTime()))
                  .build());
        });
    responseList.sort(Comparator.comparing(ThreadDto.ThreadResponse::Id).reversed());
    return responseList;
  }

  public ThreadDto.ThreadByIdResponse getThreadById(Long threadId) {
    var studentThreads = threadRepository.findById(threadId);
    if (studentThreads.isEmpty()) {
      return ThreadDto.ThreadByIdResponse.builder().build();
    }
    var thread = studentThreads.get();
    return ThreadDto.ThreadByIdResponse.builder()
        .threadResponse(buildThreadResponse(thread))
        .threadReplies(buildThreadReplies(thread.getThreadReplies()))
        .build();
  }

  private List<ThreadDto.ThreadReply> buildThreadReplies(List<ThreadReply> threadReplies) {
    List<ThreadDto.ThreadReply> replies = new ArrayList<>();
    threadReplies.forEach(
        reply -> {
          var teacherUser = reply.getTeacher().getUserInfo();
          replies.add(
              ThreadDto.ThreadReply.builder()
                  .id(reply.getId())
                  .replied_by(teacherUser.getFirstName() + " " + teacherUser.getLastName())
                  .reply(reply.getReply())
                  .replyComments(buildReplyComments(reply.getReplyComments()))
                  .build());
        });
    return replies;
  }

  private List<ThreadDto.ThreadReplyComments> buildReplyComments(
      List<ThreadReplyComments> threadReplyComments) {
    List<ThreadDto.ThreadReplyComments> replyComments = new ArrayList<>();
    threadReplyComments.forEach(
        comment -> {
          var user = comment.getUserInfo();
          replyComments.add(
              ThreadDto.ThreadReplyComments.builder()
                  .id(comment.getId())
                  .reply(comment.getReply())
                  .replied_by(user.getFirstName() + " " + user.getLastName())
                  .isTeacher(comment.getIsTeacher())
                  .isStudent(comment.getIsStudent())
                  .build());
        });
    return replyComments;
  }

  private ThreadDto.ThreadResponse buildThreadResponse(Thread thread) {
    var teacherUser = thread.getAssignedTo().getUserInfo();
    return ThreadDto.ThreadResponse.builder()
        .Id(thread.getId())
        .question(thread.getQuestion())
        .tags(thread.getTags())
        .title(thread.getTitle())
        .teacherName(teacherUser.getFirstName() + " " + teacherUser.getLastName())
        .replyCount(threadRepository.replyCountByThreadId(thread.getId()))
        .createdAt(DateTimeUtil.convertIso8601ToEpoch(thread.getCreatedAt().toLocalDateTime()))
        .build();
  }

  public Thread findThreadById(Long threadId) {
    var thread = threadRepository.findById(threadId);
    if (thread.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.ThreadId", new String[] {threadId.toString()});
    }
    return thread.get();
  }

  public List<ThreadDto.ThreadResponse> getThreadsByOrg(String teacherAuthId) {
    try {
      var user = userRepository.findByAuthUserId(teacherAuthId);
      if (user.isPresent()) {
        var teacher = teacherRepository.findByUserInfo(user.get());
        if (teacher.isPresent()) {
          var studentThreads = threadRepository.findAllByAssignedTo(teacher.get());
          return buildStudentThreadsResponse(studentThreads);
        }
      }
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Teacher.AuthUserID", e);
    }
    return Collections.emptyList();
  }

  public void deletingThreadById(String studentAuthId, Long threadId, String orgSlug) {

    Student student = studentRepository.getStudentByAuthUserIdAndOrgSlug(studentAuthId, orgSlug);
    if (student == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.StudentNotFound");
    }

    Optional<Thread> thread = threadRepository.findByIdAndCreatedBy(threadId, student);

    if (thread.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.ThreadNotFound");
    } else {
      threadRepository.deleteById(threadId);
    }
  }

  public void updateThread(
      String orgSlug, ThreadDto.CreateThreadRequest request, String studentAuthId, Long threadId) {
    Student studentDetails =
        studentRepository.getStudentByAuthUserIdAndOrgSlug(studentAuthId, orgSlug);
    if (studentDetails == null) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "error.StudentNotFound");
    } else {
      var studentThread =
          threadRepository
              .findByIdAndCreatedBy(threadId, studentDetails)
              .orElseThrow(
                  () ->
                      new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "error.ThreadNotFound"));
      studentThread.setQuestion(request.question());
      threadRepository.save(studentThread);
    }
  }

  public List<ThreadDto.ThreadResponse> getAllStudentThreads(String orgSlug, String gradeSlug) {
    var allStudentThreads = threadRepository.getAllByOrgSlugAndGradeSlug(orgSlug, gradeSlug);
    return buildStudentThreadsResponse(allStudentThreads);
  }
}
