package com.wexl.retail.zoom.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LeftEventRequest extends ZoomMeetingEventRequest {

  @JsonProperty("payload")
  private LeftEventPayload payload;

  @Data
  public static class LeftEventPayload {

    @JsonProperty("object")
    private LeftEventObject object;

    @JsonProperty("account_id")
    private String accountId;
  }

  @Data
  @EqualsAndHashCode(callSuper = true)
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class LeftEventObject extends ZoomEventObject {

    @JsonProperty("participant")
    private LeftEvent participant;
  }

  @Data
  @EqualsAndHashCode(callSuper = true)
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class LeftEvent extends ZoomEventParticipantInfo {

    @JsonProperty("leave_time")
    private String leaveTime;

    @JsonProperty("leave_reason")
    private String leaveReason;
  }
}
