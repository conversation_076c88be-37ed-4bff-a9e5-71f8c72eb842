package com.wexl.retail.student.exam.competitive.processor;

import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.student.exam.competitive.dto.CompetitiveExamsDto;
import com.wexl.retail.test.school.domain.TestCategory;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.domain.TestDefinitionSection;
import com.wexl.retail.test.school.domain.TestQuestion;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CustomProcessor extends AbstractCompetitiveExamProcessor {

  private static final int TOTAL_MARKS = 80;
  private static final String SECTION_MATHS = "Subject: Mathematics";
  private static final String SECTION_PHYSICS = "Subject: Physics";
  private static final String SECTION_CHEMISTRY = "Subject: Chemistry";
  private final String[] sectionNames =
      new String[] {SECTION_MATHS, SECTION_PHYSICS, SECTION_CHEMISTRY};

  @Override
  public boolean supports(TestCategory testCategory) {
    return TestCategory.CUSTOM.equals(testCategory);
  }

  @Override
  public int getTotalMarks() {
    return TOTAL_MARKS;
  }

  protected List<TestDefinitionSection> buildTestDefinitionSections(
      TestDefinition testDefinition, List<CompetitiveExamsDto.AnswerKeys> answerKeys) {
    List<TestDefinitionSection> sections = new ArrayList<>();
    testDefinition.setTotalMarks(answerKeys.size());
    testDefinition.setNoOfQuestions(answerKeys.size());
    for (int i = 0; i <= 2; i++) {
      if (i == 0) {
        sections.add(
            buildSections(
                testDefinition, sectionNames[i], 40L, QuestionType.MCQ, answerKeys, i + 1L));
      } else {
        sections.add(
            buildSections(
                testDefinition, sectionNames[i], 20L, QuestionType.MCQ, answerKeys, i + 1L));
      }
    }
    return sections;
  }

  @Override
  protected List<TestQuestion> buildTestQuestions(
      TestDefinitionSection testDefinitionSection,
      String sectionName,
      Long questionCount,
      QuestionType type,
      List<CompetitiveExamsDto.AnswerKeys> answerKeys) {
    List<TestQuestion> questions = new ArrayList<>();
    var answerKeyStartIndex = getAnswerKeyCount(sectionName);
    for (int i = 0; i < questionCount; i++) {
      questions.add(
          TestQuestion.builder()
              .testDefinitionSection(testDefinitionSection)
              .mcqAnswer(Long.valueOf(answerKeys.get(answerKeyStartIndex).answer()))
              .negativeMarks(0)
              .questionUuid(
                  generateMcqQuestionUuid(
                      Long.valueOf(answerKeys.get(answerKeyStartIndex).answer()),
                      answerKeys.get(answerKeyStartIndex).questionNumber()))
              .type(QuestionType.MCQ.name())
              .marks(1)
              .build());
      answerKeyStartIndex = answerKeyStartIndex + 1;
    }

    return questions;
  }

  private int getAnswerKeyCount(String sectionName) {
    return switch (sectionName) {
      case SECTION_MATHS -> 0;
      case SECTION_PHYSICS -> 40;
      case SECTION_CHEMISTRY -> 60;
      default -> 100;
    };
  }
}
