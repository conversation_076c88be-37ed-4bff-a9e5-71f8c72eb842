package com.wexl.retail.courses.enrollment.repository;

import com.wexl.retail.courses.enrollment.model.CourseScheduleInst;
import com.wexl.retail.courses.enrollment.model.CourseScheduleItemInst;
import com.wexl.retail.courses.step.model.CourseItem;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CourseScheduleItemInstRepository
    extends JpaRepository<CourseScheduleItemInst, Long> {
  CourseScheduleItemInst findByCourseItemAndCourseScheduleInst(
      CourseItem courseItem, CourseScheduleInst courseScheduleInst);

  @Transactional
  @Modifying
  @Query(
      value =
          """
                  update course_schedule_item_inst
                  set status = :status
                  where course_schedule_inst_id =
                  (select id from course_schedule_inst where student_id = :studentId and course_schedule_id = :courseScheduleId) and course_item_id = :stepId""",
      nativeQuery = true)
  int updateStepCompletionStatus(long studentId, long courseScheduleId, long stepId, String status);
}
