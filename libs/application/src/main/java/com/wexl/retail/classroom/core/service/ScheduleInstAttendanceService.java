package com.wexl.retail.classroom.core.service;

import static com.wexl.retail.classroom.core.dto.ScheduleInstAttendanceStatus.*;
import static com.wexl.retail.classroom.core.dto.ScheduleInstAttendanceStatus.CLOSED;
import static com.wexl.retail.task.domain.TaskStatus.*;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.classroom.core.dto.*;
import com.wexl.retail.classroom.core.model.ClassroomSchedule;
import com.wexl.retail.classroom.core.model.ClassroomScheduleInst;
import com.wexl.retail.classroom.core.model.ScheduleInstAttendance;
import com.wexl.retail.classroom.core.model.ScheduleInstAttendanceDetails;
import com.wexl.retail.classroom.core.repository.ClassroomRepository;
import com.wexl.retail.classroom.core.repository.ClassroomScheduleInstRepository;
import com.wexl.retail.classroom.core.repository.ScheduleInstAttendanceDetailsRepository;
import com.wexl.retail.classroom.core.repository.ScheduleInstAttendanceRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.erp.attendance.domain.CalenderDetails;
import com.wexl.retail.erp.attendance.domain.CompletionStatus;
import com.wexl.retail.erp.attendance.repository.CalenderRepository;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.services.StudentService;
import com.wexl.retail.task.domain.TaskInst;
import com.wexl.retail.task.domain.TaskType;
import com.wexl.retail.task.repository.TaskInstRepository;
import com.wexl.retail.task.service.TaskInstService;
import jakarta.transaction.Transactional;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class ScheduleInstAttendanceService {

  private final ClassroomScheduleInstRepository classroomScheduleInstRepository;
  private final ScheduleInstAttendanceRepository scheduleInstAttendanceRepository;

  private final AuthService authService;
  private final TeacherRepository teacherRepository;

  private final StudentRepository studentRepository;
  private final CalenderRepository calenderRepository;
  private final DateTimeUtil dateTimeUtil;
  private final ClassroomRepository classroomRepository;
  private final StudentService studentService;
  private final TaskInstService taskInstService;
  private final TaskInstRepository taskInstRepository;
  private final ScheduleInstAttendanceDetailsRepository attendanceDetailsRepository;

  @Transactional
  public void markAttendance(
      Long id, String orgSlug, ScheduleInstAttendanceRequest scheduleInstAttendanceRequest) {
    var classroomScheduleInst = classroomScheduleInstRepository.findByIdAndOrgSlug(id, orgSlug);

    var studentList =
        scheduleInstAttendanceRequest.getStudents().stream()
            .map(StudentRepositoryDetails::getStudent)
            .toList();

    final List<Student> allStudents = studentRepository.findAllById(studentList);
    validateIfStudentsBelongToThisOrg(allStudents, orgSlug);

    final Optional<ScheduleInstAttendance> possibleScheduleInstAttendance =
        scheduleInstAttendanceRepository.findByClassroomScheduleInst(classroomScheduleInst);

    ScheduleInstAttendance attendance;
    if (possibleScheduleInstAttendance.isPresent()) {
      attendance = possibleScheduleInstAttendance.get();
      attendance.setScheduleInstAttendanceDetails(
          constructExistingScheduleInstAttendanceDetails(
              scheduleInstAttendanceRequest, attendance));
    } else {
      attendance = new ScheduleInstAttendance();
      markAttendanceForAllStudentsInClassroom(
          scheduleInstAttendanceRequest, allStudents, attendance);
    }
    deleteTasksForAbsentees(classroomScheduleInst, scheduleInstAttendanceRequest);
    scheduleInstAttendanceRepository.save(
        buildScheduleInstAttendance(classroomScheduleInst, attendance));
  }

  private void deleteTasksForAbsentees(
      ClassroomScheduleInst classroomScheduleInst,
      ScheduleInstAttendanceRequest scheduleInstAttendanceRequest) {
    var taskInsts = taskInstRepository.getTaskInstsByScheduleInst(classroomScheduleInst.getId());

    taskInsts.forEach(
        inst -> {
          if (isStudentAbsent(scheduleInstAttendanceRequest, inst)
              && TaskType.REVISION.name().equals(inst.getTask().getTaskType().name())) {
            inst.setDeletedAt(new Date());
          } else {
            inst.setDeletedAt(null);
            updateTaskStatus(classroomScheduleInst, inst);
          }
          if (TaskType.REVISION.name().equals(inst.getTask().getTaskType().name())
              && inst.getDeletedAt() == null
              && ClassroomMeetingStatus.COMPLETED
                  .name()
                  .equals(classroomScheduleInst.getStatus().name())) {
            inst.setCompletionStatus(COMPLETED);
          }
        });
    taskInstRepository.saveAll(new ArrayList<>(taskInsts));
  }

  private void updateTaskStatus(ClassroomScheduleInst classroomScheduleInst, TaskInst taskInst) {
    if (ClassroomMeetingStatus.COMPLETED.equals(classroomScheduleInst.getStatus())
        && taskInst.getCompletionStatus().equals(DRAFT)) taskInst.setCompletionStatus(NOT_STARTED);
  }

  private boolean isStudentAbsent(
      ScheduleInstAttendanceRequest scheduleInstAttendanceRequest, TaskInst taskInst) {
    return scheduleInstAttendanceRequest.getStudents().stream()
        .anyMatch(
            request ->
                request.getStudent() == taskInst.getStudent().getId()
                    && ScheduleInstAttendanceStatus.ABSENT.equals(request.getAttendanceStatus()));
  }

  private ScheduleInstAttendance buildScheduleInstAttendance(
      ClassroomScheduleInst classroomScheduleInst, ScheduleInstAttendance attendance) {
    attendance.setClassroomScheduleInst(classroomScheduleInst);
    attendance.setTeacher(getTeacherDetails());
    attendance.setStatus(CompletionStatus.COMPLETED);
    attendance.setCalenderDetails(constructCalendarDetails());
    attendance.setOrgSlug(classroomScheduleInst.getOrgSlug());
    return attendance;
  }

  private void markAttendanceForAllStudentsInClassroom(
      ScheduleInstAttendanceRequest scheduleInstAttendanceRequest,
      List<Student> allStudents,
      ScheduleInstAttendance attendance) {
    attendance.setScheduleInstAttendanceDetails(
        constructScheduleInstAttendanceDetails(
            scheduleInstAttendanceRequest, allStudents, attendance));
  }

  private List<ScheduleInstAttendanceDetails> constructExistingScheduleInstAttendanceDetails(
      ScheduleInstAttendanceRequest scheduleInstAttendanceRequest,
      ScheduleInstAttendance attendance) {
    Map<Long, ScheduleInstAttendanceStatus> currentAttendanceRequest = new HashMap<>();
    scheduleInstAttendanceRequest
        .getStudents()
        .forEach(s -> currentAttendanceRequest.put(s.getStudent(), s.getAttendanceStatus()));
    attendance
        .getScheduleInstAttendanceDetails()
        .forEach(
            scheduleInstAttendanceDetail -> {
              var studentId = scheduleInstAttendanceDetail.getStudent().getId();
              var attendanceStatus = PRESENT;
              if (currentAttendanceRequest.containsKey(studentId)) {
                attendanceStatus =
                    ScheduleInstAttendanceStatus.valueOf(
                        String.valueOf(currentAttendanceRequest.get(studentId)));
              }
              scheduleInstAttendanceDetail.setAttendanceStatus(attendanceStatus);
            });
    return attendance.getScheduleInstAttendanceDetails();
  }

  @NotNull
  private List<ScheduleInstAttendanceDetails> constructScheduleInstAttendanceDetails(
      ScheduleInstAttendanceRequest scheduleInstAttendanceRequest,
      List<Student> allStudents,
      ScheduleInstAttendance attendance) {
    return allStudents.stream()
        .map(
            student -> {
              ScheduleInstAttendanceDetails attendanceDetails = new ScheduleInstAttendanceDetails();
              attendanceDetails.setStudent(student);
              attendanceDetails.setAttendanceStatus(
                  getAttendanceDetail(scheduleInstAttendanceRequest, student));
              attendanceDetails.setScheduleInstAttendance(attendance);
              return attendanceDetails;
            })
        .toList();
  }

  @NotNull
  private Teacher getTeacherDetails() {
    var teacherId = authService.getTeacherDetails().getTeacherInfo().getId();
    final Optional<Teacher> teacherById = teacherRepository.findById(teacherId);
    if (teacherById.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Teacher.Exist");
    }
    return teacherById.get();
  }

  private void validateIfStudentsBelongToThisOrg(List<Student> allStudents, String orgSlug) {
    final List<Student> students =
        allStudents.stream()
            .filter(student -> student.getUserInfo().getOrganization().equals(orgSlug))
            .toList();
    if (students.size() != allStudents.size()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidStudentDetails");
    }
  }

  private ScheduleInstAttendanceStatus getAttendanceDetail(
      ScheduleInstAttendanceRequest scheduleInstAttendanceRequest, Student student) {
    final Optional<StudentRepositoryDetails> firstStudentDetail =
        scheduleInstAttendanceRequest.getStudents().stream()
            .filter(studentDetails -> studentDetails.getStudent() == student.getId())
            .findFirst();
    if (firstStudentDetail.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.UnknownStudentDetails");
    }
    return firstStudentDetail.get().getAttendanceStatus();
  }

  public CalenderDetails constructCalendarDetails() {
    var currentDate = dateTimeUtil.getCurrentDate();
    var convertedDate = dateTimeUtil.convertToIntegerFormat(currentDate);
    final Optional<CalenderDetails> possibleCalendarDetails =
        calenderRepository.findByDateId(convertedDate);
    if (possibleCalendarDetails.isEmpty()) {
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.Calendar");
    }
    return possibleCalendarDetails.get();
  }

  public List<ScheduleInstAttendanceResponse> getAttendanceDetails(Long id, String orgSlug) {

    var classroomScheduleInst = classroomScheduleInstRepository.findByIdAndOrgSlug(id, orgSlug);

    final Optional<ScheduleInstAttendance> possibleScheduleInstAttendance =
        scheduleInstAttendanceRepository.findByClassroomScheduleInstIdAndOrgSlug(id, orgSlug);
    if (possibleScheduleInstAttendance.isEmpty()) {
      return classroomScheduleInst.getClassroomSchedule().getClassroom().getStudents().stream()
          .filter(student -> student.getUserInfo().getDeletedAt() == null)
          .map(
              s ->
                  ScheduleInstAttendanceResponse.builder()
                      .student(s.getUserInfo().getId())
                      .attendanceStatus(PRESENT)
                      .fullName(
                          s.getUserInfo().getFirstName() + " " + s.getUserInfo().getLastName())
                      .build())
          .toList();
    }
    var attendanceDetails =
        possibleScheduleInstAttendance.stream()
            .map(ScheduleInstAttendance::getScheduleInstAttendanceDetails)
            .flatMap(List::stream)
            .toList();

    return attendanceDetails.stream()
        .filter(student -> student.getStudent().getDeletedAt() == null)
        .map(
            data ->
                ScheduleInstAttendanceResponse.builder()
                    .student(data.getStudent().getId())
                    .attendanceStatus(data.getAttendanceStatus())
                    .fullName(
                        data.getStudent().getUserInfo().getFirstName()
                            + " "
                            + data.getStudent().getUserInfo().getLastName())
                    .build())
        .toList();
  }

  @Transactional
  public void markAttendanceByCompletingClassroom(ClassroomScheduleInst scheduleInst) {
    var scheduleInstAttendance =
        scheduleInstAttendanceRepository.findByClassroomScheduleInst(scheduleInst);
    if (scheduleInstAttendance.isPresent()) {
      return;
    }
    var classroomStudents =
        scheduleInst.getClassroomSchedule().getClassroom().getStudents().stream()
            .filter(student -> student.getDeletedAt() == null)
            .toList();
    var studentIds = classroomStudents.stream().map(Student::getId).toList();

    ScheduleInstAttendance attendance = new ScheduleInstAttendance();
    markAttendanceForAllStudentsInClassroom(
        buildScheduleInstAttendanceRequest(studentIds, NOT_MARKED), classroomStudents, attendance);
    scheduleInstAttendanceRepository.save(buildScheduleInstAttendance(scheduleInst, attendance));
  }

  private ScheduleInstAttendanceRequest buildScheduleInstAttendanceRequest(
      List<Long> studentIds, ScheduleInstAttendanceStatus status) {

    var attendanceDetails =
        studentIds.stream()
            .map(
                studentId ->
                    StudentRepositoryDetails.builder()
                        .student(studentId)
                        .attendanceStatus(status)
                        .build())
            .toList();

    return ScheduleInstAttendanceRequest.builder().students(attendanceDetails).build();
  }

  public void migrateClassroomAttendance(Long date) {
    try {
      LocalDateTime startTime;
      LocalDateTime endTime;
      if (Objects.nonNull(date)) {
        var localDateTime = dateTimeUtil.convertEpochToIso8601(date);
        startTime = localDateTime.with(LocalTime.MIN);
        endTime = localDateTime.with(LocalTime.MAX);
      } else {
        startTime = LocalDate.now().minusDays(1).atStartOfDay();
        endTime = LocalDateTime.now().minusDays(1).with(LocalTime.MAX);
      }
      var classroomScheduleInsts =
          classroomScheduleInstRepository.findByStartTimeBetween(startTime, endTime);
      filterScheduleAttendanceByInsts(classroomScheduleInsts);
      if (classroomScheduleInsts.isEmpty()) {
        return;
      }
      CalenderDetails calenderDetails = constructCalendarDetails();
      scheduleInstAttendanceRepository.saveAll(
          new ArrayList<>(buildScheduleInstAttendances(classroomScheduleInsts, calenderDetails)));
    } catch (Exception e) {
      log.debug("Unable to Mark Attendance : " + e.getMessage(), e);
    }
  }

  private void filterScheduleAttendanceByInsts(List<ClassroomScheduleInst> classroomScheduleInsts) {
    var attendances =
        scheduleInstAttendanceRepository.findByClassroomScheduleInstIn(classroomScheduleInsts);
    var insts = attendances.stream().map(ScheduleInstAttendance::getClassroomScheduleInst).toList();
    classroomScheduleInsts.removeAll(insts);
    classroomScheduleInstRepository.saveAll(
        updateClassroomStatusToClosedIfNotStarted(classroomScheduleInsts));
  }

  private List<ClassroomScheduleInst> updateClassroomStatusToClosedIfNotStarted(
      List<ClassroomScheduleInst> classroomScheduleInsts) {
    classroomScheduleInsts.stream()
        .filter(csi -> ClassroomMeetingStatus.NOT_STARTED.equals(csi.getStatus()))
        .forEach(csi -> csi.setStatus(ClassroomMeetingStatus.CLOSED));
    return classroomScheduleInsts;
  }

  private List<ScheduleInstAttendance> buildScheduleInstAttendances(
      List<ClassroomScheduleInst> classroomScheduleInsts, CalenderDetails calenderDetails) {

    List<ScheduleInstAttendance> scheduleInstAttendances = new ArrayList<>();
    classroomScheduleInsts.forEach(
        inst -> {
          var result = scheduleInstAttendance(inst, calenderDetails);
          if (Objects.nonNull(result)) {
            scheduleInstAttendances.add(result);
          }
        });
    return scheduleInstAttendances;
  }

  private ScheduleInstAttendance scheduleInstAttendance(
      ClassroomScheduleInst inst, CalenderDetails calenderDetails) {
    var attendance = new ScheduleInstAttendance();
    try {
      var classroomStudents =
          studentService.getActiveStudents(
              inst.getClassroomSchedule().getClassroom().getStudents());
      if (Objects.isNull(classroomStudents)
          || classroomStudents.isEmpty()
          || inst.getClassroomSchedule().getClassroom().getTeachers().isEmpty()) {
        return null;
      }
      var studentIds =
          classroomStudents.stream()
              .filter(student -> student.getDeletedAt() == null)
              .map(Student::getId)
              .toList();
      markAttendanceForAllStudentsInClassroom(
          buildScheduleInstAttendanceRequest(studentIds, getAttendanceStatusByInstStatus(inst)),
          classroomStudents,
          attendance);
      attendance.setClassroomScheduleInst(inst);
      attendance.setTeacher(inst.getClassroomSchedule().getClassroom().getTeachers().get(0));
      attendance.setStatus(CompletionStatus.COMPLETED);
      attendance.setOrgSlug(inst.getOrgSlug());
      attendance.setCalenderDetails(calenderDetails);
    } catch (Exception e) {
      log.debug("Unable to Mark Attendance : " + e.getMessage(), e);
    }
    return attendance;
  }

  private ScheduleInstAttendanceStatus getAttendanceStatusByInstStatus(ClassroomScheduleInst inst) {
    return ClassroomMeetingStatus.COMPLETED.equals(inst.getStatus()) ? NOT_MARKED : CLOSED;
  }

  public void amsClassroomAttendanceMigration() {
    try {
      String amsOrgSlug = "aft620984";
      List<ClassroomScheduleInst> classroomScheduleInsts =
          classroomScheduleInstRepository.getInstsByDate(LocalDate.now().toString(), amsOrgSlug);
      updateClassroomStatusAccordingToAmsRequirements(classroomScheduleInsts);
    } catch (Exception e) {
      log.error("Unable to update status (AMS) : " + e.getMessage(), e);
    }
  }

  public List<List<ClassroomScheduleInst>> splitList(
      List<ClassroomScheduleInst> insts, int chunkSize) {
    return IntStream.range(0, (insts.size() + chunkSize - 1) / chunkSize)
        .mapToObj(i -> insts.subList(i * chunkSize, Math.min((i + 1) * chunkSize, insts.size())))
        .toList();
  }

  private void updateClassroomStatusAccordingToAmsRequirements(
      List<ClassroomScheduleInst> classroomScheduleInsts) {
    var instList = splitList(classroomScheduleInsts, 5000);
    List<ScheduleInstAttendance> scheduleInstAttendances = new ArrayList<>();
    instList.forEach(
        inst -> {
          var attendances = scheduleInstAttendanceRepository.findByClassroomScheduleInstIn(inst);
          scheduleInstAttendances.addAll(attendances);
        });
    Map<ClassroomScheduleInst, ScheduleInstAttendance> attendanceMap = new HashMap<>();

    scheduleInstAttendances.forEach(
        instAttendance ->
            attendanceMap.put(instAttendance.getClassroomScheduleInst(), instAttendance));

    classroomScheduleInsts.forEach(
        inst -> {
          var scheduleInstAttendance = attendanceMap.get(inst);
          if (attendanceMap.containsKey(inst)
              && isAttendanceMarked(scheduleInstAttendance)
              && ClassroomMeetingStatus.COMPLETED.equals(
                  inst.getStatus())) { // 1.  if attendance marked and classroom completed
            inst.setStatus(ClassroomMeetingStatus.COMPLETED);
          } else if (attendanceMap.containsKey(inst)
              && isAttendanceNotMarked(scheduleInstAttendance)
              && (ClassroomMeetingStatus.NOT_STARTED.equals(inst.getStatus())
                  || ClassroomMeetingStatus.CLOSED.equals(
                      inst.getStatus()))) { // 2. If Teacher didnt  Marked "Mark as Attendance" and
            // "Mark as Complete"
            inst.setStatus(ClassroomMeetingStatus.CLOSED);
            updateAttendanceDetails(scheduleInstAttendance, CLOSED);
          } else if (attendanceMap.containsKey(inst)
              && isAttendanceMarked(scheduleInstAttendance)
              && ClassroomMeetingStatus.NOT_STARTED.equals(
                  inst.getStatus())) { // 3. If Teacher Marks "Mark as Attendance" and didnt marked
            // "Mark as Complete"
            inst.setStatus(ClassroomMeetingStatus.NOT_STARTED);
          } else if (attendanceMap.containsKey(inst)
              && isAttendanceNotMarked(scheduleInstAttendance)
              && ClassroomMeetingStatus.COMPLETED.equals(
                  inst.getStatus())) { // 4. If Teacher didnt  Marked "Mark as Attendance" and
            // marks "Mark as Complete"
            inst.setStatus(ClassroomMeetingStatus.COMPLETED);
            updateAttendanceDetails(scheduleInstAttendance, NOT_MARKED);
          }
          if (Objects.nonNull(scheduleInstAttendance)) {
            scheduleInstAttendanceRepository.save(scheduleInstAttendance);
          }
          classroomScheduleInstRepository.save(inst);
        });
  }

  private boolean isAttendanceMarked(ScheduleInstAttendance scheduleInstAttendance) {
    return scheduleInstAttendance.getScheduleInstAttendanceDetails().stream()
        .anyMatch(siad -> List.of(PRESENT, ABSENT).contains(siad.getAttendanceStatus()));
  }

  private boolean isAttendanceNotMarked(ScheduleInstAttendance scheduleInstAttendance) {
    return scheduleInstAttendance.getScheduleInstAttendanceDetails().stream()
        .allMatch(siad -> List.of(NOT_MARKED, CLOSED).contains(siad.getAttendanceStatus()));
  }

  private void updateAttendanceDetails(
      ScheduleInstAttendance scheduleInstAttendance, ScheduleInstAttendanceStatus status) {
    if (Objects.isNull(scheduleInstAttendance)) {
      return;
    }
    scheduleInstAttendance
        .getScheduleInstAttendanceDetails()
        .forEach(
            detail -> {
              if (!List.of(PRESENT, ABSENT).contains(detail.getAttendanceStatus())) {
                detail.setAttendanceStatus(status);
              }
            });
  }

  public List<Student> getAbsentees(String orgSlug, int timeIntervalInDays) {
    LocalDateTime now = LocalDateTime.now();
    var classroomScheduleInsts =
        classroomScheduleInstRepository.findByOrgSlugAndDeletedAtIsNullAndStartTimeBetween(
            orgSlug, now.minusDays(timeIntervalInDays), now);
    var scheduleInstAttendances =
        scheduleInstAttendanceRepository.findByClassroomScheduleInstIn(classroomScheduleInsts);
    var scheduleInstIds =
        scheduleInstAttendances.stream().map(ScheduleInstAttendance::getId).toList();
    if (scheduleInstIds.isEmpty()) {
      return Collections.emptyList();
    }
    var scheduleInstAttendanceDetails =
        attendanceDetailsRepository.getByAttendanceInstsAndStatus(
            new ArrayList<>(scheduleInstIds), ABSENT.name());
    return scheduleInstAttendanceDetails.stream()
        .map(ScheduleInstAttendanceDetails::getStudent)
        .toList();
  }

  public List<Student> getLongAbsentees(String orgSlug) {

    List<Student> longAbsentees = new ArrayList<>();
    var classrooms = classroomRepository.findByOrgSlugAndDeletedAtIsNull(orgSlug);

    classrooms.forEach(
        classroom -> {
          var schedules = getActiveClassroomSchedules(classroom.getSchedules());

          var insts =
              schedules.stream()
                  .map(ClassroomSchedule::getClassroomScheduleInsts)
                  .flatMap(Collection::stream)
                  .toList();
          var attendanceDetails = getAttendanceDetailByInsts(insts);

          Map<Student, List<ScheduleInstAttendanceStatus>> studentAttendances =
              attendanceDetails.stream()
                  .collect(
                      Collectors.groupingBy(
                          ScheduleInstAttendanceDetails::getStudent,
                          Collectors.mapping(
                              ScheduleInstAttendanceDetails::getAttendanceStatus,
                              Collectors.toList())));

          studentAttendances.forEach(
              (student, attendanceStatuses) -> {
                boolean isLongAbsentee =
                    attendanceStatuses.stream()
                        .allMatch(attendanceStatus -> attendanceStatus.equals(ABSENT));
                if (isLongAbsentee) {
                  longAbsentees.add(student);
                }
              });
        });
    return longAbsentees;
  }

  private List<ScheduleInstAttendanceDetails> getAttendanceDetailByInsts(
      List<ClassroomScheduleInst> classroomScheduleInsts) {
    var validInsts =
        classroomScheduleInsts.stream()
            .filter(inst -> inst.getStartTime().isBefore(LocalDateTime.now()))
            .toList();
    var latestInsts =
        validInsts.stream()
            .sorted(Comparator.comparing(ClassroomScheduleInst::getStartTime).reversed())
            .limit(3)
            .toList();

    var attendances = scheduleInstAttendanceRepository.findByClassroomScheduleInstIn(latestInsts);

    return attendances.stream()
        .map(ScheduleInstAttendance::getScheduleInstAttendanceDetails)
        .flatMap(Collection::stream)
        .toList();
  }

  private List<ClassroomSchedule> getActiveClassroomSchedules(List<ClassroomSchedule> schedules) {
    if (schedules.isEmpty()) {
      return Collections.emptyList();
    }
    return schedules.stream()
        .filter(schedule -> schedule.getEndDate().isAfter(LocalDateTime.now()))
        .toList();
  }
}
