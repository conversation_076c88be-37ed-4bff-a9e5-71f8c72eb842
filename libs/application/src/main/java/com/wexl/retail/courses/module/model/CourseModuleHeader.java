package com.wexl.retail.courses.module.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Entity
@Data
@Table(name = "course_module_header")
public class CourseModuleHeader extends Model {
  @Id
  @GeneratedValue(
      strategy = GenerationType.SEQUENCE,
      generator = "course-module-header-sequence-generator")
  @SequenceGenerator(
      name = "course-module-header-sequence-generator",
      sequenceName = "course_module_header_seq",
      allocationSize = 1)
  private long id;

  private String description;

  @Column(name = "org_slug")
  private String orgSlug;
}
