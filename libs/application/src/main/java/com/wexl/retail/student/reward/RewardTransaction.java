package com.wexl.retail.student.reward;

import com.wexl.retail.model.Model;
import com.wexl.retail.model.User;
import jakarta.persistence.*;
import lombok.Data;

@Entity
@Data
@Table(name = "reward_transactions")
public class RewardTransaction extends Model {
  @Id
  @GeneratedValue(
      strategy = GenerationType.SEQUENCE,
      generator = "reward-transactions-sequence-generator")
  @SequenceGenerator(
      name = "reward-transactions-sequence-generator",
      sequenceName = "reward_transactions_seq",
      allocationSize = 1)
  private long id;

  private int points;
  private boolean isCredited;
  private String reference;
  @ManyToOne private User user;
}
