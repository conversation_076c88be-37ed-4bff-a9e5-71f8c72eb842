package com.wexl.retail.student.attributes.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.*;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "student_attribute_definitions", schema = "public")
public class StudentAttributeDefinitionModel extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "org_slug")
  private String orgSlug;

  private String name;
}
