package com.wexl.retail.attendance.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_DEFAULT)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingSummary {

  @JsonProperty("scheule_name")
  private String scheduleName;

  @JsonProperty("start_time")
  private long startTime;

  @JsonProperty("end_time")
  private long endTime;

  @JsonProperty("meeting_type")
  private String meetType;

  @JsonProperty("teacher_name")
  private String teacherName;
}
