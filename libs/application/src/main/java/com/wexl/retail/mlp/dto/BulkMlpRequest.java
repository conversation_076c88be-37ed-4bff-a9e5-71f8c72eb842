package com.wexl.retail.mlp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

@Builder
public record BulkMlpRequest(List<BulkMlpData> data) {
  @Builder
  public record BulkMlpData(
      @JsonProperty("student_name") String studentName,
      @JsonProperty("auth_user_id") String authUserId,
      List<Questions> questions) {}

  @Builder
  public record Questions(
      @JsonProperty("question_no") Long questionNo,
      @JsonProperty("question_uuid") String questionUuid,
      @JsonProperty("raw_answer") String rawAnswer,
      @JsonProperty("selected_answer") Integer selectedAnswer) {}
}
