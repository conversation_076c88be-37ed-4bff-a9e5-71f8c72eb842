package com.wexl.retail.student.studentdata;

import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/orgs")
@RestController
@Slf4j
@RequiredArgsConstructor
public class StudentDataController {

  @Autowired private final StudentDataService studentDataService;

  @IsOrgAdmin
  @GetMapping(value = "/{orgSlug}/students:csv", produces = "text/csv")
  public void getStudents(@PathVariable String orgSlug, HttpServletResponse httpServletResponse) {
    String csvFileName = "studentsDetails.csv";
    httpServletResponse.setContentType("text/csv");
    httpServletResponse.setHeader(
        HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + csvFileName);
    studentDataService.studentsFor(orgSlug, httpServletResponse);
  }
}
