package com.wexl.retail.teacher.orgs;

import com.wexl.retail.model.Teacher;
import com.wexl.retail.organization.model.Organization;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface TeacherOrgsRepository extends JpaRepository<TeacherOrgs, Long> {

  @Query(
      value =
          "select to from TeacherOrgs to where to.teacher = :teacher and to.childOrg = :childOrg")
  TeacherOrgs findByTeacherAndOrg(Organization childOrg, Teacher teacher);

  @Query(
      value =
          """
                      select o.slug from teacher_orgs torg
                                   inner join orgs o on torg.child_org = o.id
                                   where teacher_id = :teacherId""",
      nativeQuery = true)
  List<String> getChildOrgsByTeacherId(long teacherId);

  @Transactional
  @Modifying
  @Query(
      value = "delete from teacher_orgs where teacher_id = :teacherId and child_org = :childId",
      nativeQuery = true)
  void deleteTeacherOrgs(Long teacherId, Long childId);

  @Query(
      value =
          """
                      select case when count(*)>0 then true else false end from teacher_orgs tor
                      inner join teacher_details td on td.id = tor.teacher_id
                      inner join users u on u.id = td.user_id
                      inner join orgs o on o.id = tor.child_org
                      where o.slug = :childOrgSlug and u.id = :userId""",
      nativeQuery = true)
  boolean isTeacherAssociatedOrg(String childOrgSlug, Long userId);
}
