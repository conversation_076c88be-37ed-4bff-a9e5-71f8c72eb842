package com.wexl.retail.student.exam.mock;

import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.curriculum.service.OrgSettingsService;
import com.wexl.retail.elp.dto.ElpDto;
import com.wexl.retail.elp.service.ElpService;
import com.wexl.retail.student.answer.ExamAnswer;
import com.wexl.retail.student.answer.ExamRevisionEventPublisher;
import com.wexl.retail.student.answer.StudentAnswerService;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.ExamRepository;
import com.wexl.retail.student.exam.revision.domain.ExamRevisionRequest;
import com.wexl.retail.student.studentpublisher.StudentCreationEventListener;
import com.wexl.retail.test.school.dto.QuestionDto;
import com.wexl.retail.test.school.service.TestDefinitionService;
import com.wexl.retail.util.ValidationUtils;
import com.wexl.retail.v2.service.ScheduleTestStudentService;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class MockExamService {
  private final ValidationUtils validationUtils;
  private final ScheduleTestStudentService scheduleTestStudentService;
  private final ElpService elpService;
  private final ExamRepository examRepository;
  private final ExamRevisionEventPublisher examRevisionEventPublisher;
  private final TestDefinitionService testDefinitionService;
  private final StudentAnswerService studentAnswerService;
  private final OrgSettingsService orgSettingsService;
  private final StudentCreationEventListener studentCreationEventListener;

  public QuestionDto.QuestionResponse getQuestions(long examId) {
    var exam = validationUtils.findByExamId(examId);
    var testDefinition = validationUtils.validateTestDefinition(exam.getTestDefinition().getId());
    return testDefinitionService.getQuestionResponsePreconfigured(testDefinition, 0);
  }

  public void submitMockExam(ElpDto.StudentAnswerRequest studentAnswerRequest, String orgSlug) {
    submitExam(studentAnswerRequest, orgSlug);
  }

  public QuestionDto.StudentResultsResponse getExamResult(long examId) {
    var exam = validationUtils.findByExamId(examId);
    var testDefinition = exam.getTestDefinition();
    return elpService.buildExamResults(exam, testDefinition);
  }

  public QuestionDto.QuestionsResponse getInstructions(Long testDefinitionId) {
    var testDefinition = validationUtils.validateTestDefinition(testDefinitionId);
    return QuestionDto.QuestionsResponse.builder()
        .Instructions(testDefinition.getInstructions())
        .build();
  }

  private void submitExam(ElpDto.StudentAnswerRequest studentAnswerRequest, String orgSlug) {
    var exam = validationUtils.findByExamId(studentAnswerRequest.examId());
    var org = orgSettingsService.validateOrganizaiton(orgSlug);
    if ((exam.getExamAnswers() == null || exam.getExamAnswers().isEmpty())
        && exam.getEndTime() == null) {
      var examAnswers = elpService.buildExamAnswersEntity(studentAnswerRequest, exam, orgSlug);
      exam.setCompleted(true);
      exam.setExamAnswers(examAnswers);
      exam.setTotalMarks(
          (float) examAnswers.stream().mapToDouble(ExamAnswer::getMarksPerQuestion).sum());
      exam.setMarksScored(elpService.calculateMarkScored(examAnswers));
      exam.setEndTime(Timestamp.from(Instant.now()));
      exam.setCorrected(isSubjectiveQuestionExists(examAnswers));
      examRepository.save(exam);
      updateExamRevision(exam);
    }
  }

  private boolean isSubjectiveQuestionExists(List<ExamAnswer> examAnswers) {
    return examAnswers.stream()
        .noneMatch(ea -> QuestionType.SUBJECTIVE.getType().equalsIgnoreCase(ea.getType()));
  }

  private void updateExamRevision(Exam exam) {
    var inCorrectAnswers = exam.getExamAnswers().stream().filter(x -> !x.isCorrect()).toList();
    if (!inCorrectAnswers.isEmpty()) {
      inCorrectAnswers.forEach(
          examAnswer ->
              examRevisionEventPublisher.publishEvent(
                  ExamRevisionRequest.builder()
                      .student(exam.getStudent())
                      .exam(exam)
                      .questionUuid(examAnswer.getQuestionUuid())
                      .isCorrect(examAnswer.isCorrect())
                      .build()));
    }
  }
}
