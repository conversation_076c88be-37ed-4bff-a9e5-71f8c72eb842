package com.wexl.retail.task.domain;

import com.wexl.retail.classroom.core.model.ClassroomScheduleInst;
import com.wexl.retail.mlp.model.QuestionsAssigneeMode;
import com.wexl.retail.model.Model;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.test.school.domain.TestDefinition;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "tasks")
public class Task extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  private String name;

  private String description;

  private boolean status;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "assignee")
  private Teacher teacher;

  @Enumerated(EnumType.STRING)
  private TaskSource taskSource;

  @Enumerated(EnumType.STRING)
  private TaskType taskType;

  @OneToMany(fetch = FetchType.LAZY, mappedBy = "task", cascade = CascadeType.ALL)
  @Where(clause = "deleted_at IS NULL")
  private List<TaskInst> taskInsts;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "classroom_schedule_inst_id")
  private ClassroomScheduleInst classroomScheduleInst;

  @Column(name = "due_date")
  private LocalDateTime dueDate;

  @Column(name = "subject_id")
  private Long subjectId;

  @Column(name = "subject_slug")
  private String subjectSlug;

  @Column(name = "subject_name")
  private String subjectName;

  @Column(name = "chapter_id")
  private Long chapterId;

  @Column(name = "chapter_slug")
  private String chapterSlug;

  @Column(name = "chapter_name")
  private String chapterName;

  @Column(name = "subtopic_id")
  private Long subtopicId;

  @Column(name = "subtopic_slug")
  private String subtopicSlug;

  @Column(name = "subtopic_name")
  private String subtopicName;

  private Integer questionCount;

  @Column(name = "questions_assignee_mode")
  @Enumerated(EnumType.STRING)
  private QuestionsAssigneeMode questionsAssigneeMode;

  @Type(JsonType.class)
  @Column(name = "question_uuids", columnDefinition = "jsonb")
  private List<String> questionUuids;

  @Column(name = "video_title")
  private String videoTitle;

  @Column(name = "alt_vimeo_link")
  private String altVimeoLink;

  @Column(name = "video_slug")
  private String videoSlug;

  @Column(name = "video_source")
  private String videoSource;

  @Column(name = "synopsis_title")
  private String synopsisTitle;

  @Column(name = "synopsis_slug")
  private String synopsisSlug;

  @Column(name = "org_slug")
  private String orgSlug;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "test_definition_id")
  private TestDefinition testDefinition;

  @Column(name = "board_slug")
  private String boardSlug;

  @Column(name = "board_name")
  private String boardName;

  @Column(name = "grade_name")
  private String gradeName;

  @Column(name = "grade_slug")
  private String gradeSlug;

  @Column(name = "elp_slug")
  private String elpSlug;
}
