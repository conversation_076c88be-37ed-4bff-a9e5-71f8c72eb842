package com.wexl.retail.organization.admin;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class StudentPromotionRequest {
  @JsonProperty("grade")
  @NotBlank
  private String gradeSlug;

  @NotBlank
  @JsonProperty("board")
  private String boardSlug;

  @JsonProperty("academic_year")
  private String academicYear = "23-24";

  @NotBlank private String section;

  @JsonProperty("user_name")
  private String userName;
}
