package com.wexl.retail.v2.controller;

import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.student.exam.school.SchoolExamService;
import com.wexl.retail.student.exam.school.SchoolScheduledTestResponse;
import com.wexl.retail.test.school.dto.QuestionDto;
import com.wexl.retail.v2.dto.AnswerSheetTemplatesDto;
import com.wexl.retail.v2.dto.TestScheduleStudentAnswerDto;
import com.wexl.retail.v2.service.ScheduleTestStudentService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}")
@RequiredArgsConstructor
public class ScheduleTestStudentController {

  private final SchoolExamService schoolExamService;
  private final ScheduleTestStudentService scheduleTestStudentService;

  @GetMapping("/students/{studentAuthId}/test-schedules")
  public List<SchoolScheduledTestResponse> getStudentSchedules(
      @PathVariable String orgSlug, @PathVariable("studentAuthId") String studentId) {
    return schoolExamService.getAllScheduledTests();
  }

  @IsStudent
  @PostMapping("/students/{studentAuthId}/tss/{tssUuid}")
  public QuestionDto.QuestionsResponse startExam(
      @PathVariable String orgSlug,
      @PathVariable("studentAuthId") String studentId,
      @PathVariable("tssUuid") String uuid) {
    return scheduleTestStudentService.startMockExam(studentId, uuid);
  }

  @IsStudent
  @GetMapping("/students/{studentAuthId}/tss/{tssUuid}/instructions")
  public QuestionDto.QuestionsResponse getInstructions(
      @PathVariable("studentAuthId") String studentId, @PathVariable("tssUuid") String uuid) {
    return scheduleTestStudentService.getInstructions(studentId, uuid);
  }

  @IsStudent
  @PostMapping("/students/{studentAuthId}/tss/{tssUuid}/tssa/{tssaUuid}")
  public void saveV2Answer(
      @RequestBody
          TestScheduleStudentAnswerDto.TestScheduleStudentAnswerRequest
              testScheduleStudentAnswerRequest,
      @PathVariable("studentAuthId") String studentId,
      @PathVariable String tssUuid,
      @PathVariable("tssaUuid") String anserUuid) {

    scheduleTestStudentService.saveMockExamAnswer(
        studentId, anserUuid, testScheduleStudentAnswerRequest, tssUuid);
  }

  @IsStudent
  @PostMapping("/students/{studentAuthId}/tss/{tssUuid}:submit")
  public QuestionDto.SubmitMockExamResponse submitMockExam(
      @PathVariable("studentAuthId") String studentId, @PathVariable String tssUuid) {
    return scheduleTestStudentService.submitMockExam(studentId, tssUuid);
  }

  @IsStudent
  @GetMapping("/students/{studentAuthId}/tss/{tssUuid}/questions")
  public QuestionDto.QuestionResponse getExamQuestionResponse(
      @PathVariable("studentAuthId") String studentId, @PathVariable String tssUuid) {
    return scheduleTestStudentService.getExamQuestionResponse(studentId, tssUuid);
  }

  @GetMapping("/exams/{examId}/results")
  public QuestionDto.StudentResultsResponse getExamResult(@PathVariable long examId) {
    return scheduleTestStudentService.getExamResult(examId);
  }

  @GetMapping("/test-schedules/{scheduleTestId}/mock-analysis")
  public QuestionDto.MockExamAnalyticResponse getMockExamAnalytics(
      @PathVariable long scheduleTestId) {
    return scheduleTestStudentService.getMockExamAnalytics(scheduleTestId);
  }

  @GetMapping(
      value = "/students/{studentAuthId}/test-schedules/{testScheduleId}/report-cards",
      produces = MediaType.APPLICATION_PDF_VALUE)
  public byte[] downloadMockTestReportCard(
      @PathVariable String orgSlug,
      @PathVariable("studentAuthId") String studentAuthId,
      @PathVariable("testScheduleId") Long testScheduleId) {
    return scheduleTestStudentService.getMockTestReportCard(studentAuthId, testScheduleId);
  }

  @IsStudent
  @GetMapping(value = "/students/{studentAuthId}/bet-tests")
  public TestScheduleStudentAnswerDto.TestDetails getBetTests(
      @PathVariable String orgSlug, @PathVariable("studentAuthId") String studentAuthId) {
    return scheduleTestStudentService.getAllBetTestSchedules(orgSlug, studentAuthId);
  }

  @IsOrgAdminOrTeacher
  @GetMapping(
      value = "/students/{studentAuthId}/test-schedules/{testScheduleId}/participation-certificate",
      produces = MediaType.APPLICATION_PDF_VALUE)
  public byte[] downloadParticipationCertificate(
      @PathVariable String orgSlug,
      @PathVariable("studentAuthId") String studentAuthId,
      @PathVariable("testScheduleId") Long testScheduleId) {
    return scheduleTestStudentService.getParticipationCertificate(
        orgSlug, studentAuthId, testScheduleId);
  }

  @GetMapping(
      value = "/students/{studentAuthId}/exams/{examId}/participation-certificates",
      produces = MediaType.APPLICATION_PDF_VALUE)
  public byte[] getStudentParticipationCertificate(
      @PathVariable String orgSlug,
      @PathVariable("studentAuthId") String studentAuthId,
      @PathVariable("examId") Long examId) {
    return scheduleTestStudentService.getStudentParticipationCertificate(
        orgSlug, studentAuthId, examId);
  }

  @IsOrgAdminOrTeacher
  @GetMapping(
      value = "/test-schedules/{testScheduleId}/participation-certificate:bulk",
      produces = MediaType.APPLICATION_PDF_VALUE)
  public byte[] downloadOverallParticipationCertificate(
      @PathVariable String orgSlug, @PathVariable("testScheduleId") Long testScheduleId) {
    return scheduleTestStudentService.getOverallParticipationCertificate(orgSlug, testScheduleId);
  }

  @IsOrgAdminOrTeacher
  @GetMapping(value = "/sc-answer-sheet-templates")
  public List<AnswerSheetTemplatesDto.Response> getAllAnswerSheetTemplates() {
    return scheduleTestStudentService.getAllAnswerSheetTemplates();
  }

  @IsOrgAdminOrTeacher
  @GetMapping(
      value = "/test-schedules/{testScheduleId}/students/{studentAuthId}/sc-answer-sheet-templates",
      produces = MediaType.APPLICATION_PDF_VALUE)
  public byte[] getAnswerSheetTemplates(
      @PathVariable String orgSlug,
      @PathVariable("studentAuthId") String studentAuthId,
      @PathVariable("testScheduleId") Long testScheduleId) {
    return scheduleTestStudentService.getAnswerSheetTemplates(
        orgSlug, studentAuthId, testScheduleId);
  }

  @IsOrgAdminOrTeacher
  @GetMapping(
      value = "/test-schedules/{testScheduleId}/sc-answer-sheet-templates",
      produces = MediaType.APPLICATION_PDF_VALUE)
  public byte[] getOverallAnswerSheetTemplates(
      @PathVariable String orgSlug, @PathVariable("testScheduleId") Long testScheduleId) {
    return scheduleTestStudentService.getOverallAnswerSheetTemplates(orgSlug, testScheduleId);
  }

  @GetMapping("/exams/{examId}/enrichments")
  public QuestionDto.StudentPersonalizedWorkSheetResponse getStudentEnrichments(
      @PathVariable long examId) {
    return scheduleTestStudentService.getStudentEnrichments(examId);
  }
}
