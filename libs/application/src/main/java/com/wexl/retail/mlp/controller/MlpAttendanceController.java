package com.wexl.retail.mlp.controller;

import com.wexl.retail.mlp.dto.MlpAttendanceRequest;
import com.wexl.retail.mlp.service.MlpAttendanceService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/mlp-attendance")
public class MlpAttendanceController {
  private final MlpAttendanceService mlpAttendanceService;

  @PostMapping("/migration")
  public void insertStudentsByMlpIds(@RequestBody MlpAttendanceRequest mlpAttendanceRequest) {

    mlpAttendanceService.updateStudentRecordsByMlpIds(mlpAttendanceRequest.getMlpIds());
  }
}
