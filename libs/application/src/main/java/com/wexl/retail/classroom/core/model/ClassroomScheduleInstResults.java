package com.wexl.retail.classroom.core.model;

import java.time.LocalDateTime;

public interface ClassroomScheduleInstResults {

  Long getClassroomId();

  String getClassroomName();

  Long getScheduleId();

  Long getScheduleInstId();

  String getTitle();

  String getLink();

  LocalDateTime getStartTime();

  LocalDateTime getEndTime();

  LocalDateTime getStartDate();

  LocalDateTime getExpiryDate();

  LocalDateTime getHostJoinTime();

  LocalDateTime getCreatedAt();

  Long getMeetingRoomId();

  String getMeetingRoomName();

  String getDayOfWeek();

  Integer getStudentCount();

  String getStatus();
}
