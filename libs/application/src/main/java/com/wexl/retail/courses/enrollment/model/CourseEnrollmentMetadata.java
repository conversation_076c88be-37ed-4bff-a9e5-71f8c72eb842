package com.wexl.retail.courses.enrollment.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CourseEnrollmentMetadata {
  private List<SectionCourseEnrollmentMetadata> sections;

  private List<GradeCourseEnrollmentMetadata> grades;

  private TeamCourseEnrollmentMetadata team;

  @Data
  public static class SectionCourseEnrollmentMetadata {
    private String uuid;

    @JsonProperty("all_students")
    private boolean allStudents;
  }

  @Data
  public static class GradeCourseEnrollmentMetadata {
    private String slug;

    @JsonProperty("all_students")
    private boolean allStudents;
  }

  @Data
  public static class TeamCourseEnrollmentMetadata {

    @JsonProperty("team_id")
    private Long teamId;

    @JsonProperty("all_students")
    private Boolean allStudents;

    @JsonProperty("assign_to_new_members")
    private Boolean assignToNewlyOnboardedStudents;
  }
}
