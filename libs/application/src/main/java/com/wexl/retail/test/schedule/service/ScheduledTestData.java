package com.wexl.retail.test.schedule.service;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDateTime;

public interface ScheduledTestData {

  @JsonProperty("Id")
  long getId();

  @JsonProperty("institution")
  String getInstitution();

  @JsonProperty("organization")
  String getOrganization();

  @JsonProperty("teacherName")
  String getTeacherName();

  @JsonProperty("testName")
  String getTestName();

  @JsonProperty("grade")
  String getGradeSlug();

  @JsonProperty("subject")
  String getSubject();

  @JsonProperty("scheduledDate")
  LocalDateTime getScheduledDate();

  @JsonProperty("attempted")
  Integer getAttempted();

  @JsonProperty("notAttempted")
  Integer getNotAttempted();

  @JsonProperty("assigned")
  Integer getAssigned();

  @JsonProperty("totalMarks")
  Double getTotalMarks();

  @JsonProperty("highestMarksScored")
  Double getHighestMarksScored();

  @JsonProperty("leastMarksScored")
  Double getLeastMarksScored();
}
