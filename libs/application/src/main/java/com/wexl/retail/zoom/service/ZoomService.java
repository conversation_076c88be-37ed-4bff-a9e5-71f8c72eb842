package com.wexl.retail.zoom.service;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.AuthUtil;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.zoom.config.ZoomConfiguration;
import com.wexl.retail.zoom.config.ZoomConfiguration.ZoomCredential;
import com.wexl.retail.zoom.dto.MobileZoomResponse;
import com.wexl.retail.zoom.dto.ZoomRequest;
import com.wexl.retail.zoom.dto.ZoomResponse;
import jakarta.xml.bind.DatatypeConverter;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ZoomService {

  private static final int ROLE_TEACHER = 1;
  private static final int ROLE_STUDENT = 0;
  public static final String HMAC_SHA_256 = "HmacSHA256";
  private final ZoomConfiguration zoomConfiguration;
  private final AuthService authService;
  private final ZoomConfigService zoomConfigService;

  @SneakyThrows
  public static String generateSignature(
      ZoomCredential zoomCredential, String meetingNumber, Integer role) {
    var macHash = Mac.getInstance(HMAC_SHA_256);
    var timestamp = Long.toString(System.currentTimeMillis() - 30000);
    var msg = String.format("%s%s%s%d", zoomCredential.getApiKey(), meetingNumber, timestamp, role);

    macHash.init(new SecretKeySpec(zoomCredential.getApiSecret().getBytes(), HMAC_SHA_256));

    var message = Base64.getEncoder().encodeToString(msg.getBytes());
    byte[] hash = macHash.doFinal(message.getBytes());

    var hashBase64Str = DatatypeConverter.printBase64Binary(hash);
    var tmpString =
        String.format(
            "%s.%s.%s.%d.%s",
            zoomCredential.getApiKey(), meetingNumber, timestamp, role, hashBase64Str);
    var encodedString = Base64.getEncoder().encodeToString(tmpString.getBytes());

    return encodedString.replaceAll("\\=+$", "");
  }

  @SneakyThrows
  public String generateZoomSdkJwt(String appSdkKey, String appSdkSecret) {
    var macHash = Mac.getInstance(HMAC_SHA_256);
    macHash.init(new SecretKeySpec(appSdkSecret.getBytes(), HMAC_SHA_256));

    var time = System.currentTimeMillis() / 1000;
    var header = "{\"alg\": \"HS256\", \"typ\": \"JWT\"}";
    var payload =
        "{\"appKey\": \""
            + appSdkKey
            + "\""
            + ""
            + ", \"iat\": "
            + time
            + ", \"exp\": "
            + (time + 86400)
            + ", \"tokenExp\": "
            + (time + 86400)
            + "}";

    var headerEncoded = Base64.getEncoder().encodeToString(header.getBytes(StandardCharsets.UTF_8));
    var payloadEncoded =
        Base64.getEncoder().encodeToString(payload.getBytes(StandardCharsets.UTF_8));
    var message = "%s.%s".formatted(headerEncoded, payloadEncoded);
    var digest = macHash.doFinal(message.getBytes());
    var digestEncoded = Base64.getEncoder().encodeToString(digest);
    return "%s.%s.%s".formatted(headerEncoded, payloadEncoded, digestEncoded);
  }

  public ZoomResponse startZoom(String orgId, ZoomRequest request) {
    validateIfUserIsActive();
    isZoomConfiguredForOrg(orgId);

    var zoomCredentials = zoomConfiguration.getZoom().get(orgId);
    validateCredentials(orgId, zoomCredentials.getApiKey(), zoomCredentials.getApiSecret());
    String password =
        zoomConfigService.retrievePassword(zoomCredentials, request.getMeetingId(), orgId);

    int role = AuthUtil.isStudent(authService.getUserDetails()) ? ROLE_STUDENT : ROLE_TEACHER;
    String signature = generateSignature(zoomCredentials, request.getMeetingId(), role);
    return ZoomResponse.builder()
        .apiKey(zoomCredentials.getApiKey())
        .password(password)
        .signature(signature)
        .build();
  }

  public String getZoomPasswordForMeeting(String meetingId) {
    String orgId = authService.getUserDetails().getOrganization();
    isZoomConfiguredForOrg(orgId);

    var zoomCredentials = zoomConfiguration.getZoom().get(orgId);
    validateCredentials(orgId, zoomCredentials.getApiKey(), zoomCredentials.getApiSecret());
    return zoomConfigService.retrievePassword(zoomCredentials, meetingId, orgId);
  }

  public MobileZoomResponse startMobileZoom(String orgId, ZoomRequest request) {
    validateIfUserIsActive();
    isZoomConfiguredForOrg(orgId);

    var zoomCredentials = zoomConfiguration.getZoom().get(orgId);
    validateCredentials(orgId, zoomCredentials.getSdkKey(), zoomCredentials.getSdkSecret());
    String password =
        zoomConfigService.retrievePassword(zoomCredentials, request.getMeetingId(), orgId);
    String token = generateZoomSdkJwt(zoomCredentials.getSdkKey(), zoomCredentials.getSdkSecret());
    return MobileZoomResponse.builder().token(token).password(password).build();
  }

  private void isZoomConfiguredForOrg(String orgId) {
    if (!zoomConfiguration.getZoom().containsKey(orgId)) {
      throw new ApiException(
          InternalErrorCodes.SERVER_ERROR, "error.ZoomConfig.Organization", new String[] {orgId});
    }
  }

  private void validateIfUserIsActive() {
    var refreshedUser =
        authService.getUserByAuthUserId(authService.getUserDetails().getAuthUserId());
    if (refreshedUser.getDeletedAt() != null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.User.Inactive");
    }
  }

  private void validateCredentials(String orgId, String apiKey, String apiSecret) {
    if (StringUtils.isBlank(apiKey) || StringUtils.isBlank(apiSecret)) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.Organization.Config", new String[] {orgId});
    }
  }
}
