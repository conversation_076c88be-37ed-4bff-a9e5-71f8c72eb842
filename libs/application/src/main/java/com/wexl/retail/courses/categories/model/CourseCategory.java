package com.wexl.retail.courses.categories.model;

import com.wexl.retail.model.Model;
import com.wexl.retail.organization.model.Organization;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "course_categories")
public class CourseCategory extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private Boolean status;

  private Long orderId;

  @OneToOne
  @JoinColumn(name = "org_id")
  private Organization organization;

  private String name;
}
