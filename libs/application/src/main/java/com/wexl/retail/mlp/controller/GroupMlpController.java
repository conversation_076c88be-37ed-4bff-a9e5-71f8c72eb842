package com.wexl.retail.mlp.controller;

import com.wexl.retail.mlp.dto.GroupMlpDto;
import com.wexl.retail.mlp.service.MlpService;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@Slf4j
public class GroupMlpController {
  private final MlpService mlpService;

  @PostMapping(path = "/orgs/wexl-internal/mlp/group-mlp")
  public List<GroupMlpDto.GroupMlpResponse> createGroupMlp(
      @Valid @RequestBody GroupMlpDto.GroupMlpRequest groupMlpRequest) {

    return mlpService.groupMlpTest(groupMlpRequest);
  }
}
