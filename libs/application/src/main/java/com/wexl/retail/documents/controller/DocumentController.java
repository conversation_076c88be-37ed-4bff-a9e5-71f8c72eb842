package com.wexl.retail.documents.controller;

import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.commons.security.annotation.IsTeacher;
import com.wexl.retail.commons.storage.S3FileUploadResult;
import com.wexl.retail.documents.dto.DocumentType;
import com.wexl.retail.documents.dto.DocumentsDto;
import com.wexl.retail.documents.service.DocumentService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}")
@RequiredArgsConstructor
public class DocumentController {

  private final DocumentService documentService;

  @IsStudent
  @PostMapping(path = "/students/{studentAuthId}/documents")
  public void saveStudentDocuments(
      @RequestBody DocumentsDto.Request request,
      @PathVariable String orgSlug,
      @PathVariable String studentAuthId) {
    documentService.saveDocuments(request, orgSlug, studentAuthId);
  }

  @IsTeacher
  @PostMapping(path = "/teachers/{teacherAuthId}/documents")
  public void saveTeacherDocuments(
      @RequestBody DocumentsDto.Request request,
      @PathVariable String orgSlug,
      @PathVariable String teacherAuthId) {
    documentService.saveDocuments(request, orgSlug, teacherAuthId);
  }

  @IsStudent
  @GetMapping(path = "/students/{studentAuthId}/documents")
  public List<DocumentsDto.StudentDocumentResponse> getStudentDocuments(
      @PathVariable String studentAuthId,
      @PathVariable String orgSlug,
      @RequestParam(value = "document_type", required = false, defaultValue = "")
          List<DocumentType> documentType,
      @RequestParam(value = "chapter_slug", required = false, defaultValue = "")
          List<String> chapterSlug,
      @RequestParam(value = "subject_slug", required = false, defaultValue = "")
          List<String> subjectSlug) {
    return documentService.getStudentDocuments(
        studentAuthId, orgSlug, subjectSlug, chapterSlug, documentType);
  }

  @IsTeacher
  @GetMapping(path = "/teachers/{teacherAuthId}/documents")
  public List<DocumentsDto.TeacherDocumentResponse> getTeacherDocuments(
      @PathVariable String teacherAuthId,
      @PathVariable String orgSlug,
      @RequestParam(value = "document_type", required = false, defaultValue = "")
          List<DocumentType> documentType,
      @RequestParam(value = "chapter_slug", required = false, defaultValue = "")
          List<String> chapterSlug,
      @RequestParam(value = "subject_slug", required = false, defaultValue = "")
          List<String> subjectSlug,
      @RequestParam(value = "student_id", required = false, defaultValue = "") List<Long> studentId,
      @RequestParam(value = "classroom_name", required = false, defaultValue = "")
          List<String> classroomName) {
    return documentService.getTeacherDocuments(
        teacherAuthId, orgSlug, subjectSlug, chapterSlug, documentType, classroomName, studentId);
  }

  @PostMapping("users/{authUserId}/documents:upload")
  public S3FileUploadResult uploadDocument(
      @PathVariable String orgSlug,
      @RequestBody DocumentsDto.UploadDocumentRequest uploadDocumentRequest,
      @PathVariable String authUserId) {
    return documentService.uploadDocument(orgSlug, uploadDocumentRequest, authUserId);
  }

  @PutMapping("/teachers/{teacherAuthId}/documents/{documentId}")
  public void editTeacherDocument(
      @PathVariable("teacherAuthId") String authUserId,
      @PathVariable Long documentId,
      @RequestBody DocumentsDto.EditDocumentRequest editDocument) {
    documentService.editDocument(authUserId, editDocument, documentId);
  }

  @PutMapping("/students/{studentAuthId}/documents/{documentId}")
  public void editStudentDocument(
      @PathVariable("studentAuthId") String authUserId,
      @PathVariable Long documentId,
      @RequestBody DocumentsDto.EditDocumentRequest editDocument) {
    documentService.editDocument(authUserId, editDocument, documentId);
  }

  @DeleteMapping("/students/{studentAuthId}/documents/{documentId}")
  public void deleteStudentDocument(
      @PathVariable("studentAuthId") String authUserId, @PathVariable Long documentId) {
    documentService.deleteDocument(authUserId, documentId);
  }

  @DeleteMapping("/teachers/{teacherAuthId}/documents/{documentId}")
  public void deleteTeacherDocument(
      @PathVariable("teacherAuthId") String authUserId, @PathVariable Long documentId) {
    documentService.deleteDocument(authUserId, documentId);
  }
}
