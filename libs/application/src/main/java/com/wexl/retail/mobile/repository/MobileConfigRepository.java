package com.wexl.retail.mobile.repository;

import com.wexl.retail.mobile.model.MobileConfig;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface MobileConfigRepository extends JpaRepository<MobileConfig, Long> {

  Optional<MobileConfig> findByPackageName(String packageName);

  @Query(
      value =
          """
          select mc.package_name from mobile_config mc
          inner join orgs o on o.mobile_config_id=mc.id
          where slug = :orgSlug
          """,
      nativeQuery = true)
  Optional<String> getPackageName(String orgSlug);
}
