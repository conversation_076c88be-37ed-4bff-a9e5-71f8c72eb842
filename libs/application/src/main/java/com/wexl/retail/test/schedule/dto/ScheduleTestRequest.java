package com.wexl.retail.test.schedule.dto;

import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleTestRequest {
  private int duration;
  private long testDefinitionId;
  private List<Integer> studentId;
  private LocalDateTime startDate;
  private LocalDateTime endDate;
  private String status;
  private boolean allStudents;
  private String message;
  private String subjectSlug;
  private String gradeSlug;
  private long teacherId;
}
