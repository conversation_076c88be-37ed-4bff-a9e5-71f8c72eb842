package com.wexl.retail.qpgen.service;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.qpgen.dto.QpGenDto;
import com.wexl.retail.qpgen.dto.QpGenStatus;
import com.wexl.retail.qpgen.dto.QpType;
import com.wexl.retail.qpgen.model.QpGen;
import com.wexl.retail.qpgen.repository.QpGenRepository;
import com.wexl.retail.test.school.domain.*;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.test.school.service.TestDefinitionService;
import com.wexl.retail.util.ValidationUtils;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class QpGenService {
  private final QpGenRepository qpGenRepository;
  private final TestDefinitionService testDefinitionService;
  private final ContentService contentService;
  private final TestDefinitionRepository testDefinitionRepository;
  private final AuthService authService;
  private final ValidationUtils validationUtils;

  @Transactional
  public void saveQpGen(String orgSlug, QpGenDto.Request request, String bearerToken) {
    QpGen qpGen = buildAndSaveQpGen(orgSlug, request);
    var testDefinitionIds = generateAndPublishTestDefinitions(request, orgSlug, bearerToken);
    updateQpGen(qpGen, testDefinitionIds);
  }

  private QpGen buildAndSaveQpGen(String orgSlug, QpGenDto.Request request) {
    QpGen qpGen = buildQpGen(orgSlug, request);
    qpGen.setStatus(QpGenStatus.IN_PROGRESS);
    return qpGenRepository.save(qpGen);
  }

  private List<Long> generateAndPublishTestDefinitions(
      QpGenDto.Request request, String orgSlug, String bearerToken) {
    List<Long> testDefinitionIds = new ArrayList<>();
    long count = getCountForRequestType(request);
    for (int i = 0; i < count; i++) {
      TestDefinition testDefinition = createAndSaveTestDefinition(request, orgSlug);
      publishTestDefinition(testDefinition.getId(), bearerToken);
      testDefinitionIds.add(testDefinition.getId());
    }
    return testDefinitionIds;
  }

  private long getCountForRequestType(QpGenDto.Request request) {
    return request.type().equals(QpType.SHUFFLE) ? 1L : 5L;
  }

  private void publishTestDefinition(Long testDefinitionId, String bearerToken) {
    testDefinitionService.publishTestDefinitionById(testDefinitionId, true, bearerToken, true);
  }

  private QpGen buildQpGen(String orgSlug, QpGenDto.Request request) {
    return QpGen.builder()
        .marks(request.marks())
        .duration(request.duration())
        .orgSlug(orgSlug)
        .status(QpGenStatus.IN_PROGRESS)
        .title(request.title())
        .boardSlug(request.boardSlug())
        .boardName(request.boardName())
        .gradeName(request.gradeName())
        .gradeSlug(request.gradeSlug())
        .qpType(request.type())
        .subjectSlug(request.subjectSlug())
        .subjectName(request.subjectName())
        .sections(request.sections())
        .build();
  }

  private TestDefinition createAndSaveTestDefinition(QpGenDto.Request request, String orgSlug) {
    TestDefinition testDefinition = new TestDefinition();
    testDefinition.setBoardSlug(request.boardSlug());
    testDefinition.setGradeSlug(request.gradeSlug());
    testDefinition.setSubjectSlug(request.subjectSlug());
    testDefinition.setCategory(TestCategory.DEFAULT);
    testDefinition.setActive(Boolean.TRUE);
    testDefinition.setType(TestType.MOCK_TEST);
    testDefinition.setIsAutoEnabled(Boolean.TRUE);
    testDefinition.setOrganization(orgSlug);
    testDefinition.setTestName(request.title());
    testDefinition.setMessage(request.title());
    testDefinition.setMetadata(TestDefinitionMetadata.builder().build());
    testDefinition.setTestDefinitionSections(
        buildTestDefinitionSections(request, orgSlug, testDefinition));
    testDefinition.setTeacher(authService.getTeacherDetails());
    testDefinition.setNoOfQuestions(0);
    testDefinition.setTotalMarks(request.marks().intValue());
    return testDefinitionRepository.save(testDefinition);
  }

  private List<TestDefinitionSection> buildTestDefinitionSections(
      QpGenDto.Request request, String orgSlug, TestDefinition td) {
    List<TestDefinitionSection> sections = new ArrayList<>();
    var contentData = contentService.getQuestionsForQpGen(request.sections(), orgSlug);
    request
        .sections()
        .forEach(
            section -> {
              var sectionData =
                  contentData.stream()
                      .filter(x -> section.sectionName().equals(x.sectionName()))
                      .findFirst();
              TestDefinitionSection tds = new TestDefinitionSection();
              tds.setName(section.sectionName());
              tds.setSequenceNumber((long) request.sections().indexOf(section) + 1);
              tds.setTestQuestions(
                  sectionData
                      .map(qpGenResponse -> buildTestQuestions(qpGenResponse, tds))
                      .orElse(null));
              tds.setTestDefinition(td);
              sections.add(tds);
            });
    return sections;
  }

  private List<TestQuestion> buildTestQuestions(
      QpGenDto.QpGenResponse sectionData, TestDefinitionSection tds) {
    List<TestQuestion> testQuestionList = new ArrayList<>();
    sectionData
        .questionsList()
        .forEach(
            question ->
                testQuestionList.add(
                    TestQuestion.builder()
                        .marks(Math.toIntExact(question.marks()))
                        .questionUuid(question.uuid())
                        .type(question.type().toString())
                        .testDefinitionSection(tds)
                        .mcqAnswer(
                            question.type().equals(QuestionType.MCQ)
                                ? Long.valueOf(question.answer())
                                : null)
                        .natAnswer(
                            question.type().equals(QuestionType.NAT)
                                ? Float.valueOf((question.answer()))
                                : null)
                        .yesNo(
                            question.type().equals(QuestionType.YESNO)
                                ? getTrueFalseAnswer(question.answer())
                                : null)
                        .amcqAnswer(
                            question.type().equals(QuestionType.AMCQ)
                                ? Integer.valueOf(question.answer())
                                : null)
                        .fbqAnswer(
                            question.type().equals(QuestionType.FBQ) ? question.answer() : null)
                        .subjectiveAnswer(
                            question.type().equals(QuestionType.SUBJECTIVE)
                                ? question.answer()
                                : null)
                        .build()));
    return testQuestionList;
  }

  private void updateQpGen(QpGen qpGen, List<Long> testDefinitionIds) {
    qpGen.setTestDefinitionId(testDefinitionIds);
    qpGen.setStatus(QpGenStatus.COMPLETED);
    qpGen.setMarks(getTotalMarks(testDefinitionIds));
    qpGenRepository.save(qpGen);
  }

  private Long getTotalMarks(List<Long> testDefinitionIds) {
    var testDefinitions = testDefinitionRepository.findById(testDefinitionIds.get(0));
    return testDefinitions.stream().mapToLong(TestDefinition::getTotalMarks).sum();
  }

  private Boolean getTrueFalseAnswer(String answer) {
    return answer.equals("f") ? Boolean.FALSE : Boolean.TRUE;
  }

  public List<QpGenDto.Response> getAllQpGens(String orgSlug) {
    Organization organization;
    organization = validationUtils.isOrgValid(orgSlug);
    return buildQpGenResponse(qpGenRepository.findByOrgSlugOrderByIdDesc(organization.getSlug()));
  }

  private List<QpGenDto.Response> buildQpGenResponse(List<QpGen> qpGenList) {
    List<QpGenDto.Response> responseList = new ArrayList<>();
    qpGenList.forEach(
        qpGen ->
            responseList.add(
                QpGenDto.Response.builder()
                    .id(qpGen.getId())
                    .marks(qpGen.getMarks())
                    .title(qpGen.getTitle())
                    .subjectName(qpGen.getSubjectName())
                    .gradeName(qpGen.getGradeName())
                    .gradeSlug(qpGen.getGradeSlug())
                    .duration(qpGen.getDuration())
                    .status(qpGen.getStatus().name())
                    .createdAt(
                        DateTimeUtil.convertIso8601ToEpoch(qpGen.getCreatedAt().toLocalDateTime()))
                    .build()));

    return responseList;
  }

  public QpGenDto.SectionsResponse getQpGenById(Long id) {
    var optionalQpGen = qpGenRepository.findById(id);
    if (optionalQpGen.isEmpty()) {
      return QpGenDto.SectionsResponse.builder().build();
    }
    return buildSectionResponse(optionalQpGen.get());
  }

  private QpGenDto.SectionsResponse buildSectionResponse(QpGen qpGen) {
    testDefinitionService.validateTestDefinitions(qpGen.getTestDefinitionId());
    return QpGenDto.SectionsResponse.builder()
        .title(qpGen.getTitle())
        .marks(qpGen.getMarks())
        .duration(qpGen.getDuration())
        .boardName(qpGen.getBoardName())
        .gradeName(qpGen.getGradeName())
        .subjectName(qpGen.getSubjectName())
        .testDefinitionId(qpGen.getTestDefinitionId())
        .sections(qpGen.getSections())
        .build();
  }
}
