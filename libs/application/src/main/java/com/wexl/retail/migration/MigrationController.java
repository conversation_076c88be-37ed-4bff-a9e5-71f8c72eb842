package com.wexl.retail.migration;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/orgs/{orgSlug}/migrate")
public class MigrationController {

  private final MigrationService migrationService;

  @PostMapping("/subtopic")
  public MigrationDto.Counts migrateTaskSubtopic() {

    return migrationService.migrateTaskSubtopic();
  }

  @PostMapping("/test-definitions")
  public List<Long> migrateTestDefinitions(@PathVariable String orgSlug) {

    return migrationService.migrateTestDefinitions(orgSlug);
  }
}
