package com.wexl.retail.model;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "audit")
public class StudentAudit extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  @Column(name = "student_id")
  private long studentId;

  @Column(name = "teacher_id")
  private long teacherId;

  @Column(name = "teacher_name")
  private String teacherName;

  @Column(name = "action")
  private String action;
}
