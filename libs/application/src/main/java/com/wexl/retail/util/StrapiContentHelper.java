package com.wexl.retail.util;

import com.wexl.retail.content.model.Entity;
import com.wexl.retail.content.model.Grade;
import jakarta.annotation.PostConstruct;
import java.util.*;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class StrapiContentHelper {
  private Map<String, Entity> boardsMap;
  private Map<String, Grade> gradesMap;
  private Map<String, Entity> subjectsMap;

  @Autowired private StrapiService strapiService;

  // TODO: Verify do we need @PostConstruct

  @PostConstruct
  public void init() {
    gradesMap = new HashMap<>();
    boardsMap = new HashMap<>();
    subjectsMap = new HashMap<>();
  }

  @SneakyThrows
  public Map<String, Entity> getSubjectsMap() {
    strapiService.getAllSubjects().forEach(subject -> subjectsMap.put(subject.getSlug(), subject));
    return subjectsMap;
  }

  @SneakyThrows
  public Map<String, Grade> getGradesMap() {
    Map<String, Grade> gradeMap = new LinkedHashMap<>();
    strapiService.getAllGrades().stream()
        .sorted(Comparator.comparing(Grade::getOrder))
        .toList()
        .forEach(grade -> gradeMap.put(grade.getSlug(), grade));
    return gradeMap;
  }

  @SneakyThrows
  public Map<String, Entity> getBoardsMap() {
    strapiService.getAllBoards().forEach(board -> boardsMap.put(board.getSlug(), board));
    return boardsMap;
  }
}
