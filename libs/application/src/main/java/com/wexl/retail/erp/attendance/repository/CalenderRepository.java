package com.wexl.retail.erp.attendance.repository;

import com.wexl.retail.erp.attendance.domain.CalenderDetails;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CalenderRepository extends JpaRepository<CalenderDetails, Long> {

  @Query(value = "select * from calender_details where date_id >= :dateString", nativeQuery = true)
  List<CalenderDetails> findAllByDate(Integer dateString);

  Optional<CalenderDetails> findByDateId(Integer dateString);
}
