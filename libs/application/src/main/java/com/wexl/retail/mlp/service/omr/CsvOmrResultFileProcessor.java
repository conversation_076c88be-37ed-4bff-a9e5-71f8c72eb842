package com.wexl.retail.mlp.service.omr;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.mlp.dto.MlpFileResponse;
import com.wexl.retail.omr.OmrDto;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVRecord;
import org.springframework.stereotype.Component;

@Component
public class CsvOmrResultFileProcessor implements OmrResultFileProcessor {

  @Override
  public List<MlpFileResponse> process(InputStream inputStream) {

    String[] headers = {"NAME", "USERNAME", "Q1", "Q2", "Q3", "Q4", "Q5"};
    CSVFormat csvFormat =
        CSVFormat.DEFAULT.builder().setHeader(headers).setSkipHeaderRecord(true).build();
    List<MlpFileResponse> responses = new ArrayList<>();

    try {
      Iterable<CSVRecord> records = csvFormat.parse(new InputStreamReader(inputStream));
      for (CSVRecord csvRecord : records) {
        responses.add(
            MlpFileResponse.builder()
                .name(csvRecord.get(0))
                .userName(csvRecord.get(1))
                .selectedQuestions(
                    Map.of(
                        1,
                        csvRecord.get(2),
                        2,
                        csvRecord.get(3),
                        3,
                        csvRecord.get(4),
                        4,
                        csvRecord.get(5),
                        5,
                        csvRecord.get(6)))
                .build());
      }
    } catch (IOException e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Invalid.Csv", e);
    }

    return responses;
  }

  @Override
  public List<OmrDto.TestScheduleResponse> processTestScheduleOmr(
      InputStream inputStream, double questionCount) {
    List<OmrDto.TestScheduleResponse> responses = new ArrayList<>();
    List<String> headers = new ArrayList<>();
    headers.add("STDID");
    headers.add("STNAME");
    headers.add("SET");
    for (int i = 1; i <= questionCount; i++) {
      headers.add("Q" + i);
    }
    String[] headersArray = headers.toArray(new String[0]);

    CSVFormat csvFormat =
        CSVFormat.DEFAULT.builder().setHeader(headersArray).setSkipHeaderRecord(true).build();
    try {

      Iterable<CSVRecord> records = csvFormat.parse(new InputStreamReader(inputStream));

      for (CSVRecord csvRecord : records) {
        if (csvRecord.getRecordNumber() > 1 && !csvRecord.get(0).isEmpty()) {
          responses.add(
              OmrDto.TestScheduleResponse.builder()
                  .rollNumber(csvRecord.get(0))
                  .userName(csvRecord.get(1))
                  .selectedQuestions(buildSelectedQuestion(questionCount, csvRecord))
                  .build());
        }
      }
    } catch (IOException e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Invalid.Csv", e);
    }

    return responses;
  }

  private Map<Integer, String> buildSelectedQuestion(double questionCount, CSVRecord csvRecord) {
    Map<Integer, String> selectedQuestions = new HashMap<>();
    for (int i = 1; i <= questionCount; i++) {
      selectedQuestions.put(i, csvRecord.get(i + 2));
    }
    return selectedQuestions;
  }
}
