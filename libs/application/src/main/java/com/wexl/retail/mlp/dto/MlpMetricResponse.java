package com.wexl.retail.mlp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@RequiredArgsConstructor
public class MlpMetricResponse {

  private String name;
  private String slug;
  private int count;
  private long date;

  @JsonProperty("attendance_percentage")
  private Float attendancePercentage;
}
