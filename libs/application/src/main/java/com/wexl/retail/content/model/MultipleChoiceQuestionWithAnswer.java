package com.wexl.retail.content.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.student.answer.McqOption;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class MultipleChoiceQuestionWithAnswer extends MultipleChoiceQuestion {

  @JsonProperty("Answer")
  private Integer answer;

  @JsonProperty("Explanation")
  private String explanation;

  public String getElaborateAnswer(int option) {
    switch (McqOption.fromValue(option)) {
      case OPTION_ONE:
        return this.getOption1();
      case OPTION_TWO:
        return this.getOption2();
      case OPTION_THREE:
        return this.getOption3();
      case OPTION_FOUR:
        return this.getOption4();
      default:
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST, "Selected option is not " + "present");
    }
  }
}
