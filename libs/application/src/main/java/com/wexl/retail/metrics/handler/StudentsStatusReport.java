package com.wexl.retail.metrics.handler;

import com.wexl.retail.metrics.StudentsActivityReportService;
import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.section.service.SectionService;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class StudentsStatusReport extends AbstractMetricHandler {

  @Autowired SectionService sectionService;

  @Autowired StudentsActivityReportService studentsActivityReportService;

  @Override
  public String name() {
    return "active-inactive-users";
  }

  @Override
  public List<GenericMetricResponse> executeInternal(
      String orgSlug, GenericMetricRequest genericMetricRequest) {
    return studentsActivityReportService.getStudentActivityMetrics(orgSlug);
  }
}
