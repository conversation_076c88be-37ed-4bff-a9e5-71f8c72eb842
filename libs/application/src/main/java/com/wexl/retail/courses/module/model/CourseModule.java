package com.wexl.retail.courses.module.model;

import com.wexl.retail.courses.definition.model.CourseDefinition;
import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Entity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "course_module")
public class CourseModule extends Model {
  @Id
  @GeneratedValue(
      strategy = GenerationType.SEQUENCE,
      generator = "course-module-sequence-generator")
  @SequenceGenerator(
      name = "course-module-sequence-generator",
      sequenceName = "course_module_seq",
      allocationSize = 1)
  private long id;

  private String name;

  @ManyToOne
  @JoinColumn(updatable = false)
  private CourseDefinition courseDefinition;

  @Column(name = "seq_num")
  private int sequenceNumber;

  @Column(name = "org_slug")
  private String orgSlug;

  @Column(name = "published_at")
  private Timestamp publishedAt;
}
