package com.wexl.retail.telegram.handler;

import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.telegram.util.Constants;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class RandomUserByOrganizationHandler implements TelegramBotHandler {

  private final UserRepository userRepository;

  @Override
  public String handleCommand(String[] params) {
    if (!isValid(params, 3)) {
      return Constants.INVALID_ARGS.formatted(getHelpText());
    }

    var role = params[1];
    var organizationSlug = params[2];

    String roleToQuery = getRoleFromString(role);

    if (roleToQuery == null) {
      return "Role not found";
    }

    var randomUser =
        userRepository.getRandomUserByOrganizationAndRole(roleToQuery, organizationSlug);

    if (randomUser == null) {
      return "No organization or user exits with this role";
    }
    var userDetailMap = UserService.mapFromUser(randomUser);
    return convertToString(userDetailMap);
  }

  private String getRoleFromString(String role) {
    switch (role) {
      case "student":
        return "ROLE_ISTUDENT";
      case "parent":
        return "ROLE_PARENT";
      case "teacher":
        return "ROLE_ITEACHER";
      case "admin":
        return "ROLE_ORG_ADMIN";
      default:
        break;
    }
    return null;
  }

  @Override
  public String getCommandName() {
    return "/random";
  }

  @Override
  public String getHelpText() {
    return "/random [role: student | teacher | parent] [user-organization]";
  }
}
