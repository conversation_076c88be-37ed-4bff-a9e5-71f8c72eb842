package com.wexl.retail.thumbnail;

import static java.time.ZoneOffset.UTC;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.storage.StorageService;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ThumbnailService {

  private final StorageService storageService;
  private static final String DATE_TIME_FORMAT = "yyyyMMddhhmmssSSS";

  @Value("${app.validImageExtension}")
  private final List<String> validImageExtensions;

  public ThumbnailResponse uploadThumbnail(String orgSlug, ThumbnailRequest thumbnailRequest) {
    final String reference =
        DateTimeFormatter.ofPattern(DATE_TIME_FORMAT).format(LocalDateTime.now(UTC));
    String filePath = getFilePath(orgSlug, reference, thumbnailRequest);
    validateThumbnailExtension(thumbnailRequest);
    return ThumbnailResponse.builder()
        .url(storageService.generatePreSignedUrlForUpload(filePath))
        .previewUrl(storageService.generatePreSignedUrlForFetch(filePath))
        .path(filePath)
        .build();
  }

  private void validateThumbnailExtension(ThumbnailRequest thumbnailRequest) {
    if (!validImageExtensions.contains(thumbnailRequest.extension.trim())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidImageExtensions");
    }
  }

  public String getFilePath(String orgSlug, String reference, ThumbnailRequest thumbnailRequest) {
    return "%s/thumbnails/%s.%s".formatted(orgSlug, reference, thumbnailRequest.extension);
  }
}
