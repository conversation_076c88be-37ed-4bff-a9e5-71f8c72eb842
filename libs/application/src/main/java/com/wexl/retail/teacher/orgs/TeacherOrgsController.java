package com.wexl.retail.teacher.orgs;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.organization.dto.OrganizationResponse;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@Slf4j
public class TeacherOrgsController {

  private final TeacherOrgsService teacherOrgsService;

  @PostMapping("/orgs/{orgSlug}/teachers/{authUserId}/my-orgs")
  public void addOrgsToTeacher(
      @PathVariable String orgSlug,
      @PathVariable String authUserId,
      @RequestBody TeacherOrgsRequest teacherOrgsRequest) {
    teacherOrgsService.addOrgsToTeacher(orgSlug, authUserId, teacherOrgsRequest.getChildOrgs());
  }

  @GetMapping("/orgs/{orgSlug}/teachers/{authUserId}/my-orgs")
  public List<OrganizationResponse> getMyOrgs(
      @PathVariable String orgSlug, @PathVariable String authUserId) {
    return teacherOrgsService.getChildOrganizations(authUserId);
  }

  @DeleteMapping(path = "/orgs/{orgSlug}/teachers/{authUserId}/my-orgs/{childOrgSlug}")
  public void removeMappedOrgToTeacher(
      @PathVariable String authUserId, @PathVariable("childOrgSlug") String childOrg) {
    try {
      teacherOrgsService.deleteTeacherOrgs(authUserId, childOrg);
    } catch (Exception e) {
      log.error("Unable to delete orgs for given teacher", e);
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.OrgsDelete.Teacher", e);
    }
  }
}
