package com.wexl.retail.zoom.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ZoomEventObject {

  private String uuid;

  private String id;

  private int type;

  private String topic;

  @JsonProperty("host_id")
  private String hostId;

  private int duration;

  @JsonProperty("start_time")
  private String startTime;

  private String timezone;
}
