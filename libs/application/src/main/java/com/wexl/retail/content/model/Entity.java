package com.wexl.retail.content.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Entity {
  private int id;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("name")
  private String assetName;

  @JsonProperty("Status")
  private String status;

  private String slug;

  @JsonProperty("Icon")
  private List<Icon> icons;

  @JsonProperty("order")
  private Integer order;

  @JsonProperty("edu_board")
  private Entity eduBoardForChapter;

  @JsonProperty("grade")
  private Entity gradeForChapter;
}
