package com.wexl.retail.mobile.service;

import com.wexl.retail.mobile.dto.CountryCodeDto;
import com.wexl.retail.mobile.model.CountryCode;
import com.wexl.retail.mobile.repository.CountryCodeRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CountryCodeService {

  private final CountryCodeRepository countryCodeRepository;

  public List<CountryCodeDto.Response> getCountryCodes() {
    var countryCodesList = countryCodeRepository.findAll();
    return buildCountryCodesResponse(countryCodesList);
  }

  private List<CountryCodeDto.Response> buildCountryCodesResponse(
      List<CountryCode> countryCodesList) {
    return countryCodesList.stream()
        .map(
            countryCode ->
                CountryCodeDto.Response.builder()
                    .id(countryCode.getId())
                    .code(countryCode.getCode())
                    .name(countryCode.getName())
                    .sequenceNumber(countryCode.getSequenceNumber())
                    .build())
        .toList();
  }
}
