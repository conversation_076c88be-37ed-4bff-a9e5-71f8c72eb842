package com.wexl.retail.courses.module.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.courses.step.dto.CourseStepResponse;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_EMPTY)
public class CourseModuleResponse {

  private long id;
  private String name;

  @JsonProperty("seq_num")
  private int sequenceNumber;

  @JsonProperty("published_at")
  private Long publishedAt;

  @JsonProperty("course_definition_id")
  private long courseDefinitionId;

  private List<CourseStepResponse> steps;

  @JsonProperty("course_module_km")
  private Double courseModuleKM;
}
