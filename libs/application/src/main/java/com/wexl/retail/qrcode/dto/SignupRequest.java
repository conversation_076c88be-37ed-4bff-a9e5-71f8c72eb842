package com.wexl.retail.qrcode.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SignupRequest {
  @NotNull private String uuid;
  @NotNull private String fullName;
  @NotNull private String school;
  @NotNull private String mobileNumber;
  @NotNull private long otpId;
}
