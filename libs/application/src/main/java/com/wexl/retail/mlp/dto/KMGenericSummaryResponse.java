package com.wexl.retail.mlp.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class KMGenericSummaryResponse {

  @JsonProperty("attendance_meter")
  private KMSummaryResponse attendanceMeterList;

  @JsonProperty("knowledge_meter")
  private KMSummaryResponse knowledgeMeterList;
}
