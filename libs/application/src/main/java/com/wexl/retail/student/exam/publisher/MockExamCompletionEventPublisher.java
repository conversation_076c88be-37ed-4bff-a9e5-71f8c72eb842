package com.wexl.retail.student.exam.publisher;

import com.wexl.retail.test.schedule.domain.ScheduleTestStudent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
public class MockExamCompletionEventPublisher {

  @Autowired private ApplicationEventPublisher applicationEventPublisher;

  public void publishExamCompletion(final ScheduleTestStudent scheduleTestStudent) {
    MockExamCompletionEvent examCompletionEvent = new MockExamCompletionEvent(scheduleTestStudent);
    applicationEventPublisher.publishEvent(examCompletionEvent);
  }
}
