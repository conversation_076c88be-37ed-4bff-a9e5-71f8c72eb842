package com.wexl.retail.admin;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import com.wexl.retail.curriculum.service.CurriculumService;
import com.wexl.retail.email.EmailService;
import com.wexl.retail.guardian.dto.GuardianImportRequest;
import com.wexl.retail.guardian.dto.GuardianRequest;
import com.wexl.retail.guardian.service.GuardianService;
import com.wexl.retail.idp.UserIdpService;
import com.wexl.retail.model.EduBoard;
import com.wexl.retail.model.Grade;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.admin.StudentPromotionRequest;
import com.wexl.retail.organization.admin.StudentRequest;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.section.service.TeacherSubjectsService;
import com.wexl.retail.student.auth.BulkStudentSignup;
import com.wexl.retail.student.auth.StudentAuthService;
import com.wexl.retail.student.auth.StudentAuthTransformer;
import com.wexl.retail.teacher.auth.TeacherAuthService;
import com.wexl.retail.teacher.auth.TeacherSignupRequest;
import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/admin")
@IsOrgAdmin
@RequiredArgsConstructor
public class AdminController {

  public static final String IMPORT_STATUS_COMPLETED = "COMPLETED";
  public static final String IMPORT_STATUS_FAILED = "FAILED";
  private final UserIdpService userIdpService;
  private final EmailService emailService;
  private final AuthService authService;
  private final StudentAuthService studentAuthService;
  private final TeacherAuthService teacherAuthService;
  private final UserRepository userRepository;
  private final SectionService sectionService;
  private final TeacherRepository teacherRepository;
  private final StudentAuthTransformer studentAuthTransformer;
  private final TeacherSubjectsService teacherSubjectsService;
  private final CurriculumService curriculumService;
  private final GuardianService guardianService;

  @GetMapping("/users/{id}/subscription")
  public boolean getUserSubscription(@PathVariable("id") String authUserId) {
    return true;
  }

  @PostMapping("/org/sections")
  public ImportResponse addSections(
      @Valid @RequestBody AdminSectionCreateRequest adminSectionCreateRequest) {

    try {
      sectionService.createSection(
          adminSectionCreateRequest.getOrgSlug(), adminSectionCreateRequest);
      return ImportResponse.builder()
          .importStatus(IMPORT_STATUS_COMPLETED)
          .importErrorMessage("")
          .build();
    } catch (Exception ex) {
      return ImportResponse.builder()
          .importStatus(IMPORT_STATUS_FAILED)
          .importErrorMessage(ex.getMessage())
          .build();
    }
  }

  @PostMapping("/org/sections/teachers")
  public ImportResponse associateSectionTeacher(
      @Valid @RequestBody AdminSectionTeacherAssociationRequest request) {

    try {
      var existingSection =
          sectionService.getSectionByNameAndOrg(request.getSectionName(), request.getOrgSlug());

      if (existingSection == null) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST,
            "error.SectionFind.Organization",
            new String[] {request.getSectionName(), request.getOrgSlug()});
      }
      //
      Optional<User> teacherByEmail =
          userRepository.findByEmailAndOrganization(
              request.getTeacherEmailAddress(), request.getOrgSlug());
      if (teacherByEmail.isEmpty()) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST,
            "error.UserFind.Organization",
            new String[] {request.getTeacherEmailAddress(), request.getOrgSlug()});
      }

      Optional<Teacher> teacher = teacherRepository.findByUserInfo(teacherByEmail.get());
      if (teacher.isEmpty()) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST,
            "error.TeacherEmailFind.Organization",
            new String[] {request.getTeacherEmailAddress(), request.getOrgSlug()});
      }

      List<EduBoard> boardsHierarchy = curriculumService.getBoardsHierarchy(request.getOrgSlug());
      List<String> existingBoardsForOrg = boardsHierarchy.stream().map(EduBoard::getSlug).toList();
      if (!(existingBoardsForOrg.contains(request.getBoardSlug()))
          || request.getBoardSlug().isEmpty()) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST,
            "error.EduboardFind.Organization",
            new String[] {request.getBoardSlug()});
      }

      List<String> existingBoardForSection =
          boardsHierarchy.stream()
              .filter(
                  b -> {
                    Optional<Grade> first =
                        b.getGrades().stream()
                            .filter(g -> g.getId() == existingSection.getGradeId())
                            .findFirst();
                    return first.isPresent();
                  })
              .map(EduBoard::getSlug)
              .toList();
      if (!(existingBoardForSection.contains(request.getBoardSlug()))
          || existingBoardForSection.isEmpty()) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST,
            "error.EduboardFind.SectionAndGrade",
            new String[] {existingSection.getName(), existingSection.getGradeId().toString()});
      }

      if (StringUtils.isEmpty(request.getSubject1())) {
        sectionService.addTeacherToSectionById(
            teacher.get().getId(), existingSection.getUuid().toString());
        return ImportResponse.builder()
            .importStatus(IMPORT_STATUS_COMPLETED)
            .importErrorMessage("")
            .build();
      }

      Teacher foundTeacher = teacher.get();

      saveTeacherPreference(
          request,
          request.getBoardSlug(),
          foundTeacher.getUserInfo().getAuthUserId(),
          existingSection.getUuid().toString(),
          request.getSubject1());

      saveTeacherPreference(
          request,
          request.getBoardSlug(),
          foundTeacher.getUserInfo().getAuthUserId(),
          existingSection.getUuid().toString(),
          request.getSubject2());

      saveTeacherPreference(
          request,
          request.getBoardSlug(),
          foundTeacher.getUserInfo().getAuthUserId(),
          existingSection.getUuid().toString(),
          request.getSubject3());

      saveTeacherPreference(
          request,
          request.getBoardSlug(),
          foundTeacher.getUserInfo().getAuthUserId(),
          existingSection.getUuid().toString(),
          request.getSubject4());

      saveTeacherPreference(
          request,
          request.getBoardSlug(),
          foundTeacher.getUserInfo().getAuthUserId(),
          existingSection.getUuid().toString(),
          request.getSubject5());

      saveTeacherPreference(
          request,
          request.getBoardSlug(),
          foundTeacher.getUserInfo().getAuthUserId(),
          existingSection.getUuid().toString(),
          request.getSubject6());

      return ImportResponse.builder()
          .importStatus(IMPORT_STATUS_COMPLETED)
          .importErrorMessage("")
          .build();

    } catch (Exception ex) {
      return ImportResponse.builder()
          .importStatus(IMPORT_STATUS_FAILED)
          .importErrorMessage(ex.getMessage())
          .build();
    }
  }

  private void saveTeacherPreference(
      AdminSectionTeacherAssociationRequest request,
      String boardSlug,
      String teacherAuthUserId,
      String sectionUuid,
      String subjectSlug) {
    if (StringUtils.isEmpty(subjectSlug)) {
      return;
    }
    teacherSubjectsService.save(
        request.getOrgSlug(),
        boardSlug,
        teacherAuthUserId,
        sectionUuid,
        Collections.singletonList(subjectSlug),
        false);
  }

  @PostMapping("/users/student/bulkSignup")
  public ImportResponse bulkAddStudents(@Valid @RequestBody StudentRequest studentRequest) {
    var errorMessage = "";
    try {
      var studentSignupRequest = studentAuthTransformer.mapStudentSignupRequest(studentRequest);
      BulkStudentSignup bulkStudentSignup =
          studentAuthService.createStudentsInBulk(studentSignupRequest);
      return ImportResponse.builder()
          .importStatus(bulkStudentSignup.getImportStatus())
          .importErrorMessage(bulkStudentSignup.getImportErrorMessage())
          .build();
    } catch (Exception e) {
      log.error("Error in creating a student [" + e.getMessage() + "]");
      errorMessage = e.getMessage();
    }

    return ImportResponse.builder()
        .importStatus(IMPORT_STATUS_FAILED)
        .importErrorMessage(errorMessage)
        .build();
  }

  @PostMapping("/users/teacher/bulkSignup")
  public ImportResponse createTeacherProfile(
      @Valid @RequestBody TeacherSignupRequest teacherSignupRequest) {

    var errorMessage = "";
    try {
      var userExists = authService.isUserEmailExist(teacherSignupRequest.getEmailAddress());
      if (!userExists) {
        teacherAuthService.createTeacherInBulk(teacherSignupRequest);
        return ImportResponse.builder()
            .importStatus(IMPORT_STATUS_COMPLETED)
            .importErrorMessage("")
            .build();
      } else {
        errorMessage = "Email already registered!";
      }
    } catch (Exception e) {
      errorMessage = e.getMessage();
    }
    return ImportResponse.builder()
        .importStatus(IMPORT_STATUS_FAILED)
        .importErrorMessage(errorMessage)
        .build();
  }

  @PostMapping("/users/student/bulkStudentsPromotion")
  public ImportResponse bulkStudentsPromotion(
      @Valid @RequestBody StudentPromotionRequest studentPromotionRequest) {
    var errorMessage = "";
    try {
      User user = userRepository.getUserByAuthUserId(studentPromotionRequest.getUserName());
      studentAuthService.promoteStudent(
          user.getOrganization(), studentPromotionRequest.getUserName(), studentPromotionRequest);
      return ImportResponse.builder()
          .importStatus(IMPORT_STATUS_COMPLETED)
          .importErrorMessage("success")
          .build();
    } catch (Exception e) {
      log.error("Error in promoting a student [" + e.getMessage() + "]");
      errorMessage = e.getMessage();
    }

    return ImportResponse.builder()
        .importStatus(IMPORT_STATUS_FAILED)
        .importErrorMessage(errorMessage)
        .build();
  }

  @PostMapping("/users/{authUserId}/guardian/bulkSignup")
  public ImportResponse createGuardian(
      @PathVariable String authUserId, @RequestBody GuardianImportRequest guardianRequest) {
    var errorMessage = "";
    try {
      validateUser(authUserId, guardianRequest);
      List<GuardianRequest> guardianRequestList = new ArrayList<>();
      guardianRequestList.add(guardianRequest);
      guardianService.createGuardian(authUserId, guardianRequestList);
      return ImportResponse.builder()
          .importStatus(IMPORT_STATUS_COMPLETED)
          .importErrorMessage("")
          .build();
    } catch (Exception e) {
      log.error("Error in creating a guardian [" + e.getMessage() + "]");
      errorMessage = e.getMessage();
    }
    return ImportResponse.builder()
        .importStatus(IMPORT_STATUS_FAILED)
        .importErrorMessage(errorMessage)
        .build();
  }

  private User validateUser(String authUserId, GuardianImportRequest guardianRequest) {
    var user = userRepository.findByAuthUserId(authUserId);
    if (user.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.StudentNotFound");
    }
    if (!guardianRequest.getOrgSlug().equals(user.get().getOrganization())) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "error.StudentFind.Organization");
    }
    return user.get();
  }
}
