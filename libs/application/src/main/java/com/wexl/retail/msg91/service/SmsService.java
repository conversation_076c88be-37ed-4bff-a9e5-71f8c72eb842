package com.wexl.retail.msg91.service;

import com.wexl.retail.msg91.dto.Msg91Dto.Recipient;
import java.util.ArrayList;
import java.util.List;

public interface SmsService {
  void sendBulkMessage(String templateId, List<Recipient> recipients);

  default List<Recipient> filterInvalidPhoneNumbers(List<Recipient> originalRecipients) {

    List<Recipient> recipients =
        originalRecipients.stream().filter(recipient -> recipient.mobiles() != null).toList();

    List<Recipient> finalList = new ArrayList<>();
    recipients.forEach(
        r -> {
          if (r.mobiles().length() == 10 && r.mobiles().matches("\\d{10}")) {
            finalList.add(new Recipient("91" + r.mobiles(), r));
          } else if (r.mobiles().startsWith("+91")) {
            finalList.add(new Recipient("91" + r.mobiles().substring(3), r));
          }
        });
    return finalList;
  }
}
