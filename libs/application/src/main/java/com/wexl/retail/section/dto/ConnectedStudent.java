package com.wexl.retail.section.dto;

import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConnectedStudent {

  long id;
  String firstName;
  String lastName;
  String rollNumber;
  String emailId;
  String mobileNumber;
  long userId;
  Integer classId;
  String authUserId;
  Set<String> sectionUuid;
  String grade;
  String classRollNumber;
}
