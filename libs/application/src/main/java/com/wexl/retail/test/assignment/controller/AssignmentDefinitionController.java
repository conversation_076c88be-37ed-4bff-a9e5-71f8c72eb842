package com.wexl.retail.test.assignment.controller;

import com.wexl.retail.commons.security.annotation.IsTeacher;
import com.wexl.retail.test.assignment.dto.AssignmentResponse;
import com.wexl.retail.test.assignment.service.AssignmentService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@IsTeacher
@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/teachers/{teacherId}/assignment-defn")
public class AssignmentDefinitionController {

  private final AssignmentService assignmentService;

  @GetMapping
  public List<AssignmentResponse> getAssignmentDefinitionBySubtopic(
      @PathVariable String orgSlug, @RequestParam @NotNull String subtopic) {
    return assignmentService.getAssignmentBySubtopic(orgSlug, subtopic);
  }
}
