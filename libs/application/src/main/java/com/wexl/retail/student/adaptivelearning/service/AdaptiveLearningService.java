package com.wexl.retail.student.adaptivelearning.service;

import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.student.adaptivelearning.aiservice.AiMentor;
import com.wexl.retail.student.adaptivelearning.dto.AdaptiveLearningDto;
import com.wexl.retail.student.adaptivelearning.model.AdaptiveLearning;
import com.wexl.retail.student.adaptivelearning.repository.AdaptiveLearningRepository;
import com.wexl.retail.student.answer.ExamAnswer;
import com.wexl.retail.student.answer.StudentAnswerService;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.test.school.dto.QuestionDto;
import com.wexl.retail.util.ValidationUtils;
import jakarta.transaction.Transactional;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdaptiveLearningService {

  private final AdaptiveLearningRepository adaptiveLearningRepository;

  private final StudentAnswerService studentAnswerService;

  private final SpringTemplateEngine templateEngine;

  private final AiMentor aiMentor;

  private final ValidationUtils validationUtils;

  private final ContentService contentService;

  @Value("${app.contentToken}")
  private String contentBearerToken;

  @Async
  @Transactional
  public void performAdaptiveLearning(long examId) {
    var exam = validationUtils.findByExamId(examId);
    var section = exam.getStudent().getSection();
    var adaptiveLearningMcqs = buildAdaptiveLearningMcq(exam.getExamAnswers());
    if (adaptiveLearningMcqs.isEmpty()) {
      log.info(
          "The processing of adaptive learning was skipped as there were no incorrectly selected answers");
      return;
    }
    var adaptiveLearningBuilder =
        AdaptiveLearning.builder().examId(exam.getId()).studentId(exam.getStudent().getId());
    var learningAiRequest =
        AdaptiveLearningDto.AdaptiveLearningPromptRequest.builder()
            .board(section.getBoardName())
            .grade(section.getGradeName())
            .subject(exam.getSubjectName())
            .subtopic(exam.getSubtopicName())
            .mcqs(adaptiveLearningMcqs)
            .build();
    try {
      var response =
          aiMentor.performExamAnalysis(constructAdaptiveLearningPrompt(learningAiRequest));
      log.info("Adaptive learning ai response :{}", response);
      adaptiveLearningBuilder
          .analysis(response.analysis())
          .feedback(response.feedback())
          .summary(response.summary())
          .analysis(response.analysis());
      var question = createAiGeneratedQuestion(exam, response, adaptiveLearningBuilder);
      adaptiveLearningBuilder.content(
          AdaptiveLearningDto.AdaptiveLearningData.builder().questionUuids(question).build());
    } catch (Exception e) {
      adaptiveLearningBuilder.failureReason(e.getMessage());
      log.error(
          "Failed to generate/save questions while performing adaptive learning {}",
          e.getMessage(),
          e);
    } finally {
      var adaptiveLearning = adaptiveLearningRepository.save(adaptiveLearningBuilder.build());
      log.info("Saved adaptive learning with id :{}", adaptiveLearning.getId());
    }
  }

  private List<String> createAiGeneratedQuestion(
      Exam exam,
      AdaptiveLearningDto.AdaptiveLearningAiResponse response,
      AdaptiveLearning.AdaptiveLearningBuilder adaptiveLearningBuilder) {
    if (Objects.isNull(response.mcqs()) || response.mcqs().isEmpty()) {
      return Collections.emptyList();
    }
    var questionUuids = new ArrayList<String>();
    var student = exam.getStudent();
    response
        .mcqs()
        .forEach(
            mcq -> {
              try {
                var uuid = UUID.randomUUID().toString();
                contentService.createQuestion(
                    buildQuestionRequest(exam, mcq, uuid),
                    student.getSection().getOrganization(),
                    contentBearerToken);
                questionUuids.add(uuid);
              } catch (Exception e) {
                adaptiveLearningBuilder.failureReason(e.getMessage());
              }
            });
    return questionUuids;
  }

  private QuestionDto.QuestionRequest buildQuestionRequest(
      Exam exam, AdaptiveLearningDto.Mcq mcq, String uuid) {
    return QuestionDto.QuestionRequest.builder()
        .question(mcq.question())
        .explanation(mcq.explanation())
        .uuid(uuid)
        .mcq(
            QuestionDto.Mcq.builder()
                .option1(mcq.option1())
                .option2(mcq.option2())
                .option3(mcq.option3())
                .option4(mcq.option4())
                .answer(mcq.answer())
                .build())
        .marks(mcq.marks())
        .category("1")
        .active(true)
        .bloomsTaxonomyId(1)
        .type(QuestionType.MCQ)
        .chapterSlug(exam.getChapterSlug())
        .subtopicSlug(exam.getSubtopicSlug())
        .build();
  }

  private List<AdaptiveLearningDto.AdaptiveLearningMcq> buildAdaptiveLearningMcq(
      List<ExamAnswer> examAnswers) {
    return examAnswers.stream()
        .filter(ea -> !ea.isCorrect())
        .limit(10)
        .map(
            examAnswer -> {
              var question =
                  studentAnswerService.getContentServiceQuestionByUuid(
                      contentBearerToken,
                      examAnswer.getExam(),
                      QuestionType.getByType(examAnswer.getType()),
                      examAnswer.getQuestionUuid());

              return AdaptiveLearningDto.AdaptiveLearningMcq.builder()
                  .question(question.getQuestions())
                  .option1(question.getOption1())
                  .option2(question.getOption2())
                  .option3(question.getOption3())
                  .option4(question.getOption4())
                  .correctAnswer(question.getAnswer())
                  .selectedAnswer(examAnswer.getSelectedOption())
                  .complexity(question.getComplexity())
                  .build();
            })
        .toList();
  }

  private String constructAdaptiveLearningPrompt(
      AdaptiveLearningDto.AdaptiveLearningPromptRequest request) {
    var context = new Context();
    context.setVariable("str", request);
    context.setVariable("mcqs", request.mcqs());
    return templateEngine.process("prompts/adaptive-learning-prompt", context);
  }
}
