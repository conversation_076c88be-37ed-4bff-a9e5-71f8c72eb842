package com.wexl.retail.task.service;

import static com.wexl.retail.util.Constants.WEXL_INTERNAL;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.classroom.core.repository.ScheduleInstAttendanceDetailsRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.ExamRepository;
import com.wexl.retail.task.domain.Task;
import com.wexl.retail.task.domain.TaskInst;
import com.wexl.retail.task.domain.TaskStatus;
import com.wexl.retail.task.domain.TaskType;
import com.wexl.retail.task.dto.StudentTasksResponse;
import com.wexl.retail.task.mapper.StudentTasksResponseMapper;
import com.wexl.retail.task.repository.TaskInstRepository;
import com.wexl.retail.task.repository.TaskRepository;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.util.ValidationUtils;
import jakarta.transaction.Transactional;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class TaskInstService {

  private final TaskInstRepository taskInstRepository;
  private final TaskRepository taskRepository;

  private final AuthService authService;

  private final UserRepository userRepository;

  private final StudentRepository studentRepository;

  private final DateTimeUtil dateTimeUtil;

  private final ExamRepository examRepository;

  private final StudentTasksResponseMapper studentTasksResponseMapper;
  private final ScheduleInstAttendanceDetailsRepository scheduleInstAttendanceDetailsRepository;
  private final ValidationUtils validationUtils;
  private final TestDefinitionRepository testDefinitionRepository;
  private final StorageService storageService;
  private final TeacherRepository teacherRepository;

  @Transactional
  public void deleteTaskInst(String orgSlug, long taskInstId) {
    var taskInst =
        taskInstRepository
            .findByIdAndDeletedAtIsNull(taskInstId)
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST,
                        "error.InvalidTaskInst",
                        new String[] {String.valueOf(taskInstId)}));
    if (Objects.equals(TaskStatus.COMPLETED, taskInst.getCompletionStatus())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.completedTask");
    }
    validateTaskInst(orgSlug, taskInst);
    taskInstRepository.delete(taskInst);
  }

  public void validateTaskInst(String orgSlug, TaskInst taskInst) {
    var task =
        taskRepository
            .findById(taskInst.getTask().getId())
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST,
                        "error.TaskNotFound",
                        new String[] {String.valueOf(taskInst.getTask().getId())}));
    if (!task.getOrgSlug().equals(orgSlug)) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.InvalidTask",
          new String[] {String.valueOf(orgSlug)});
    }
  }

  public void updateTaskInst(Exam exam) {
    Optional<Task> optionalTask =
        taskRepository.findByIdAndOrgSlug(
            exam.getTaskId(), authService.getUserDetails().getOrganization());
    if (optionalTask.isEmpty()) {
      return;
    }

    var task = optionalTask.get();

    var taskInst =
        task.getTaskInsts().stream()
            .filter(
                inst ->
                    Objects.nonNull(inst.getExam())
                        && inst.getExam().getRef().equals(exam.getRef()))
            .findFirst()
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidTask"));
    taskInst.setCompletionStatus(TaskStatus.COMPLETED);
    taskInst.setCompletedAt(LocalDateTime.now());
    taskInstRepository.save(taskInst);
  }

  public void updateTaskStatusIfPresent(Exam exam, TaskStatus taskStatus) {
    if (exam == null || exam.getTaskId() == null) {
      return;
    }
    var optionalTaskInst =
        taskInstRepository
            .findAllByStudentIdOrderByCompletionStatus(
                exam.getStudentId(), Sort.by(Sort.Direction.DESC, "completedAt"))
            .stream()
            .filter(taskInst1 -> taskInst1.getTask().getId() == exam.getTaskId())
            .findFirst();

    if (optionalTaskInst.isEmpty()) {
      return;
    }
    var taskInst = optionalTaskInst.get();
    taskInst.setExam(exam);
    taskInst.setCompletionStatus(taskStatus);
    if (TaskStatus.COMPLETED.equals(taskStatus)) {
      taskInst.setCompletedAt(LocalDateTime.now());
    }
    taskInstRepository.save(taskInst);
  }

  public List<StudentTasksResponse> getStudentActivitiesByDates(
      String studentAuthId, Long epochDate, int limit) {
    User user = userRepository.getUserByAuthUserId(studentAuthId);
    Student student = studentRepository.findByUserId(user.getId());

    var date = dateTimeUtil.convertEpochToIso8601Legacy(epochDate).toLocalDate();
    var taskInsts =
        taskInstRepository.getStudentActivitiesByDate(student.getId(), date.toString(), limit);
    if (taskInsts.isEmpty()) {
      return Collections.emptyList();
    }
    return taskInsts.stream()
        .map(
            ti -> {
              if (TaskType.VIDEO.equals(ti.getTask().getTaskType())
                  && "S3".equals(ti.getTask().getVideoSource())) {
                ti.getTask()
                    .setVideoSlug(
                        storageService.generatePreSignedUrlForFetch(ti.getTask().getVideoSlug()));
              }
              return studentTasksResponseMapper.taskInstToStudentTasksResponse(ti);
            })
        .toList();
  }

  @Transactional
  public void unSubmitAssignment(String orgSlug, long taskInstId) {
    var taskInst =
        taskInstRepository
            .findById(taskInstId)
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST,
                        "error.InvalidClassroomScheduleInst",
                        new String[] {String.valueOf(taskInstId)}));
    validateTaskInst(orgSlug, taskInst);

    if (TaskType.ASSIGNMENT.name().equals(taskInst.getTask().getTaskType().name())
        && !TaskStatus.PENDING.name().equals(taskInst.getCompletionStatus().name())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.FailedUnSubmitExam");
    }
    var exam =
        examRepository
            .findById(taskInst.getExam().getId())
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.ExamId"));

    var timeStamp = Timestamp.valueOf(LocalDateTime.now()).getTime();
    exam.setDeletedAt(new Date(timeStamp));
    examRepository.save(exam);

    taskInst.setExam(null);
    taskInst.setCompletionStatus(TaskStatus.NOT_STARTED);
    taskInstRepository.save(taskInst);
  }

  public List<TaskInst> buildTaskInsts(
      List<Long> taskIds, List<Student> students, Boolean isNewStudent) {
    List<TaskInst> taskInstList = new ArrayList<>();

    for (Long taskId : taskIds) {
      var task = validationUtils.getTaskById(taskId);
      task.setDueDate(LocalDateTime.now());

      try {
        if (Boolean.TRUE.equals(isNewStudent)) {
          var adminTeachers =
              teacherRepository.getAllAdminsByOrg(
                  students.getFirst().getUserInfo().getOrganization());
          if (!adminTeachers.isEmpty()) {
            task.setTeacher(adminTeachers.getFirst());
          }
        } else {
          var teacherDetails = authService.getTeacherDetails();
          if (teacherDetails != null && teacherDetails.getTeacherInfo() != null) {
            task.setTeacher(teacherDetails.getTeacherInfo());
          } else {
            var adminTeachers =
                teacherRepository.getAllAdminsByOrg(
                    students.getFirst().getUserInfo().getOrganization());
            if (!adminTeachers.isEmpty()) {
              task.setTeacher(adminTeachers.getFirst());
            }
          }
        }
      } catch (Exception e) {
        var adminTeachers =
            teacherRepository.getAllAdminsByOrg(
                students.getFirst().getUserInfo().getOrganization());
        if (!adminTeachers.isEmpty()) {
          task.setTeacher(adminTeachers.getFirst());
        }
      }

      for (Student student : students) {
        if (Boolean.TRUE.equals(validateIsExistsByStudentAndTask(student, task))) {
          continue;
        }

        if (Boolean.TRUE.equals(isNewStudent)) {
          var testDefinition =
              testDefinitionRepository
                  .findTop1ByTestNameAndOrganizationAndPublishedAtNotNullAndDeletedAtIsNull(
                      task.getElpSlug(), WEXL_INTERNAL);
          if (testDefinition.isEmpty()) {
            continue;
          }
        }
        taskInstList.add(
            TaskInst.builder()
                .task(task)
                .completionStatus(TaskStatus.PENDING)
                .student(student)
                .build());
      }
    }

    return taskInstList;
  }

  private Boolean validateIsExistsByStudentAndTask(Student student, Task task) {
    return taskInstRepository.existsByStudentAndTask(student, task);
  }
}
