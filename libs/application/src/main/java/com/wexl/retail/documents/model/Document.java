package com.wexl.retail.documents.model;

import com.wexl.retail.documents.dto.ClassGroupType;
import com.wexl.retail.documents.dto.DocumentType;
import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "documents")
public class Document extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @NotNull
  @Column(name = "document_type")
  private DocumentType type;

  @Column(name = "subject_name")
  private String subjectName;

  @Column(name = "subject_slug")
  private String subjectSlug;

  @Column(name = "chapter_name")
  private String chapterName;

  @Column(name = "chapter_slug")
  private String chapterSlug;

  @Column(name = "file_type")
  private String fileType;

  private String title;

  @Column(name = "org_slug")
  private String orgSlug;

  @NotNull
  @Column(name = "uploaded_by")
  private String uploadedBy;

  @Column(name = "is_teacher")
  private Boolean isTeacher;

  @Column(name = "is_student")
  private Boolean isStudent;

  @Column(name = "class_group_type")
  private ClassGroupType classGroupType;

  @Column(name = "class_group_id")
  private Long classGroupId;

  @Column(name = "class_group_name")
  private String classGroupName;

  @NotNull private String path;

  @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinColumn(name = "document_id")
  private List<DocumentStudent> documentStudents;

  @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinColumn(name = "document_id")
  private List<DocumentTeacher> documentTeachers;
}
