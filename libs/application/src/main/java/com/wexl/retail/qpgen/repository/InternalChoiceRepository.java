package com.wexl.retail.qpgen.repository;

import com.wexl.retail.qpgen.model.InternalChoice;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface InternalChoiceRepository extends JpaRepository<InternalChoice, Long> {
  InternalChoice findByTestSectionIdAndTestQuestionUUidAndTestQuestionId(
      long testSectionId, String questionUuid, long testQuestionId);

  Optional<InternalChoice> findByTestSectionIdAndTestQuestionUUid(
      long testDefinitionSectionId, String uuid);

  Optional<InternalChoice> findByTestSectionIdAndTestQuestionUUidAndQuestionUuid(
      long testDefinitionSectionId, String questionUuid, String internalQuestionUuid);
}
