package com.wexl.retail.courses.categories.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.courses.categories.dto.CourseCategoryDto;
import com.wexl.retail.courses.categories.model.CourseCategory;
import com.wexl.retail.courses.categories.repository.CourseCategoryRepository;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CourseCategoryService {

  private final OrganizationRepository organizationRepository;

  private final CourseCategoryRepository courseCategoryRepository;

  public void createCategory(String org, CourseCategoryDto.CourseCategoryRequest categoryRequest) {
    Organization organization = organizationRepository.findBySlug(org);
    if (organization == null) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.OrganizationFind.Slug", new String[] {org});
    }

    courseCategoryRepository.save(
        CourseCategory.builder()
            .organization(organization)
            .status(Boolean.TRUE)
            .name(categoryRequest.name())
            .orderId(categoryRequest.order())
            .build());
  }

  public List<CourseCategoryDto.CourseCategories> getAllCategories(String org) {
    Organization organization = organizationRepository.findBySlug(org);
    List<CourseCategory> courseCategoryList =
        courseCategoryRepository.findAllByOrganizationOrderByIdDesc(organization);
    return courseCategoryList.stream()
        .map(
            r ->
                CourseCategoryDto.CourseCategories.builder()
                    .name(r.getName())
                    .id(r.getId())
                    .status(r.getStatus())
                    .build())
        .toList();
  }

  public void editCategory(String org, Long categoryId, String name) {
    Organization organization = organizationRepository.findBySlug(org);
    CourseCategory courseCategory =
        courseCategoryRepository.findByIdAndOrganization(categoryId, organization);
    if (courseCategory == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidCategory");
    }
    courseCategory.setName(name);
    courseCategoryRepository.save(courseCategory);
  }

  public void deleteCategory(String org, Long categoryId) {
    Organization organization = organizationRepository.findBySlug(org);
    CourseCategory courseCategory =
        courseCategoryRepository.findByIdAndOrganization(categoryId, organization);
    if (courseCategory == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidCategory");
    }
    courseCategoryRepository.delete(courseCategory);
  }

  public CourseCategoryDto.CourseCategories getCategoriesById(String orgSlug, Long categoryId) {
    Organization organization = organizationRepository.findBySlug(orgSlug);
    CourseCategory courseCategoryById =
        courseCategoryRepository.findByIdAndOrganization(categoryId, organization);
    return CourseCategoryDto.CourseCategories.builder()
        .id(courseCategoryById.getId())
        .name(courseCategoryById.getName())
        .status(courseCategoryById.getStatus())
        .build();
  }
}
