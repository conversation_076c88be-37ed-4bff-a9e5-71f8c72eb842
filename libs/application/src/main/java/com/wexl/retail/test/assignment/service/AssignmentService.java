package com.wexl.retail.test.assignment.service;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;
import static java.time.ZoneOffset.UTC;

import com.wexl.retail.classroom.core.model.Classroom;
import com.wexl.retail.classroom.core.repository.ClassroomRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.task.repository.TaskRepository;
import com.wexl.retail.test.assignment.dto.*;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.domain.TestType;
import com.wexl.retail.test.school.dto.TestDefinitionRequest;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.test.school.service.TestDefinitionService;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AssignmentService {

  private static final String DATE_TIME_FORMAT = "yyyyMMddhhmmssSSS";
  private static final String QUESTION = "question";
  private static final String SOLUTION = "solution";
  private final ContentService contentService;
  private final TestDefinitionService testDefinitionService;
  private final UserRepository userRepository;
  private final StorageService storageService;
  private final TestDefinitionRepository testDefinitionRepository;
  private final DateTimeUtil dateTimeUtil;
  private final TeacherRepository teacherRepository;
  private final ClassroomRepository classroomRepository;
  private final TaskRepository taskRepository;

  public TestDefinition createAssignment(AssignmentRequest assignmentRequest, String bearerToken) {
    var testDefinitionRequest = TestDefinitionRequest.builder().build();
    BeanUtils.copyProperties(assignmentRequest, testDefinitionRequest);
    testDefinitionRequest.setTestType(assignmentRequest.getType());
    testDefinitionRequest.setQuestions(
        contentService.createAssignment(bearerToken, assignmentRequest));

    if (Boolean.TRUE.equals(assignmentRequest.getQuestionUploaded())) {
      testDefinitionRequest.setQuestionPath(
          createUploadPath(assignmentRequest, assignmentRequest.getQuestionUploaded(), QUESTION));
    }

    if (Boolean.TRUE.equals(assignmentRequest.getSolutionUploaded())) {
      testDefinitionRequest.setSolutionPath(
          createUploadPath(assignmentRequest, assignmentRequest.getSolutionUploaded(), SOLUTION));
    }

    return testDefinitionService.createTestDefinition(
        testDefinitionRequest, null, assignmentRequest.getOrganization());
  }

  private String createUploadPath(
      AssignmentRequest assignmentRequest, Boolean isUploaded, String assetType) {
    final String assetsReference = assignmentRequest.getArtifactReference();

    if (Objects.isNull(assetsReference) || Boolean.FALSE.equals(isUploaded)) {
      return null;
    }

    return createAssetsPath(
        assetsReference,
        assignmentRequest.getOrganization(),
        assignmentRequest.getGradeSlug(),
        assignmentRequest.getSubjectSlug(),
        assetType);
  }

  public AssetsReference createAssetsReference(
      String orgSlug, String gradeSlug, String subjectSlug) {
    final String reference =
        DateTimeFormatter.ofPattern(DATE_TIME_FORMAT).format(LocalDateTime.now(UTC));

    String questionAssetsPath =
        createAssetsPath(reference, orgSlug, gradeSlug, subjectSlug, QUESTION);
    String answerAssetsPath =
        createAssetsPath(reference, orgSlug, gradeSlug, subjectSlug, SOLUTION);

    return AssetsReference.builder()
        .reference(reference)
        .questionUploadUrl(getUploadUrl(questionAssetsPath))
        .solutionUploadUrl(getUploadUrl(answerAssetsPath))
        .questionPreviewUrl(getFetchUrl(questionAssetsPath))
        .solutionPreviewUrl(getFetchUrl(answerAssetsPath))
        .build();
  }

  private String getUploadUrl(String assetUrl) {
    return storageService.generatePreSignedUrlForUpload(assetUrl);
  }

  private String getFetchUrl(String assetUrl) {
    return storageService.generatePreSignedUrlForFetch(assetUrl);
  }

  private String createAssetsPath(
      String reference, String orgSlug, String gradeSlug, String subjectSlug, String assetType) {
    return "%s/%s/%s_testdefn/%s/%s.pdf"
        .formatted(orgSlug, gradeSlug, subjectSlug, reference, assetType);
  }

  public List<AssignmentResponse> getAssignmentBySubtopic(String orgSlug, String subtopicSlug) {
    String testType = TestType.ASSIGNMENT.name();
    List<TestDefinition> allAssignment =
        testDefinitionRepository.getAllAssignments(testType, orgSlug, subtopicSlug);

    return allAssignment.stream()
        .map(
            testDefinition ->
                AssignmentResponse.builder()
                    .id(testDefinition.getId())
                    .name(testDefinition.getTestName())
                    .build())
        .toList();
  }

  public List<Assignment.AssignmentResponse> getAssignments(
      String orgSlug,
      String teacherId,
      Long studentId,
      String name,
      String subject,
      String chapter,
      String topic,
      String status,
      Long fromDateInEpoch,
      Long toDateInEpoch,
      int limit) {

    var user =
        userRepository
            .findByAuthUserId(teacherId)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.UserNotFound"));

    var teacher =
        teacherRepository
            .findByUserInfo(user)
            .orElseThrow(
                () ->
                    new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.TeacherNotFound"));
    var fromDate = dateTimeUtil.convertEpochToIso8601Legacy(fromDateInEpoch);
    var toDate = dateTimeUtil.convertEpochToIso8601Legacy(toDateInEpoch);
    List<Long> classroomIds =
        classroomRepository
            .findByTeachersInAndOrgSlugAndDeletedAtIsNull(List.of(teacher), orgSlug)
            .stream()
            .map(Classroom::getId)
            .toList();
    List<Long> taskIds = taskRepository.getTaskIdByClassroom(classroomIds);
    var allAssignmentStatus =
        testDefinitionRepository.getAllAssignmentStatus(
            orgSlug,
            user.getId(),
            fromDate.toLocalDate(),
            toDate.toLocalDate().plusDays(1),
            taskIds);

    if (Objects.nonNull(studentId))
      allAssignmentStatus =
          allAssignmentStatus.stream()
              .filter(student -> Objects.equals(student.getStudentId(), studentId))
              .toList();
    if (Objects.nonNull(name) && !name.isEmpty())
      allAssignmentStatus =
          allAssignmentStatus.stream()
              .filter(student -> Objects.equals(student.getName(), name))
              .toList();
    if (Objects.nonNull(subject) && !subject.isEmpty())
      allAssignmentStatus =
          allAssignmentStatus.stream()
              .filter(student -> Objects.equals(student.getSubjectSlug(), subject))
              .toList();
    if (Objects.nonNull(chapter) && !chapter.isEmpty())
      allAssignmentStatus =
          allAssignmentStatus.stream()
              .filter(student -> Objects.equals(student.getChapterSlug(), chapter))
              .toList();
    if (Objects.nonNull(topic) && !topic.isEmpty())
      allAssignmentStatus =
          allAssignmentStatus.stream()
              .filter(student -> Objects.equals(student.getSubtopicSlug(), topic))
              .toList();
    if (Objects.nonNull(status) && !status.isEmpty())
      allAssignmentStatus =
          allAssignmentStatus.stream()
              .filter(student -> Objects.equals(student.getStatus(), status))
              .toList();

    return allAssignmentStatus.stream().limit(limit).map(this::buildAssignmentResponse).toList();
  }

  public Integer getCountOfAssignmentByStatus(String orgSlug, String teacherId, String status) {
    var user =
        userRepository
            .findByAuthUserId(teacherId)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.UserNotFound"));
    var teacher =
        teacherRepository
            .findByUserInfo(user)
            .orElseThrow(
                () ->
                    new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.TeacherNotFound"));
    List<Classroom> classrooms =
        classroomRepository.findByTeachersInAndOrgSlugAndDeletedAtIsNull(List.of(teacher), orgSlug);
    List<Long> classroomIds = classrooms.stream().map(Classroom::getId).toList();
    List<Long> taskIds = taskRepository.getTaskIdByClassroom(classroomIds);

    return testDefinitionRepository.getAssignmentCountByStatus(
        orgSlug, user.getId(), status, taskIds);
  }

  private Assignment.AssignmentResponse buildAssignmentResponse(AssignmentsResponse response) {
    return Assignment.AssignmentResponse.builder()
        .studentId(response.getStudentId())
        .examID(response.getExamId())
        .testDefinitionId(response.getTestDefinitionId())
        .name(response.getName())
        .subjectName(response.getSubjectName())
        .subjectSlug(response.getSubjectSlug())
        .assignmentDate(convertIso8601ToEpoch(response.getAssignmentDate()))
        .chapterName(response.getChapterName())
        .chapterSlug(response.getChapterSlug())
        .subtopicName(response.getSubtopicName())
        .subtopicSlug(response.getSubtopicSlug())
        .status(response.getStatus())
        .marksScored(response.getMarksScored())
        .totalMarks(response.getTotalMarks())
        .activityType(response.getActivityType())
        .lastUpdated(convertIso8601ToEpoch(response.getLastUpdated()))
        .build();
  }
}
