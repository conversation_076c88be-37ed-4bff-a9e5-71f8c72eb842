package com.wexl.retail.telegram.handler;

import com.wexl.retail.model.User;
import com.wexl.retail.telegram.service.UserService;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
public class UserByUserNameHandler extends UserBotHandler {

  @Override
  protected void handleInternal(User user, Map<String, Object> result) {
    Map<String, Object> map = UserService.mapFromUser(user);
    result.putAll(map);
  }

  @Override
  public String getCommandName() {
    return "/user";
  }

  @Override
  public String getHelpText() {
    return "/user [user-name]";
  }
}
