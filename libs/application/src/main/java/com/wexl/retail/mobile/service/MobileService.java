package com.wexl.retail.mobile.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.mobile.dto.MobileConfigDto;
import com.wexl.retail.mobile.model.MobileConfigKeys;
import com.wexl.retail.mobile.model.MobileConfigValues;
import com.wexl.retail.mobile.repository.MobileConfigKeysRepository;
import com.wexl.retail.mobile.repository.MobileConfigRepository;
import com.wexl.retail.mobile.repository.MobileConfigValuesRepository;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class MobileService {

  private final MobileConfigRepository mobileRepository;
  private final MobileConfigValuesRepository mobileConfigValuesRepository;
  private final MobileConfigKeysRepository mobileConfigKeysRepository;

  public List<MobileConfigDto.PackageResponse> getPackages() {
    var configData = mobileRepository.findAll();
    return configData.stream()
        .map(
            d ->
                MobileConfigDto.PackageResponse.builder()
                    .id(d.getId())
                    .description(d.getPackageDescription())
                    .packageName(d.getPackageDescription())
                    .build())
        .toList();
  }

  public MobileConfigDto.PackageResponse getKeyValuesById(String packageName) {
    var possibleConfigData = mobileRepository.findByPackageName(packageName);
    if (possibleConfigData.isEmpty()) {
      possibleConfigData = mobileRepository.findByPackageName("com.wexledu.mobile.school");
    }
    if (possibleConfigData.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.InvalidPackageId", new String[] {packageName});
    }

    var configData = possibleConfigData.get();
    return MobileConfigDto.PackageResponse.builder()
        .id(configData.getId())
        .packageName(configData.getPackageName())
        .description(configData.getPackageDescription())
        .data(buildData(configData.getId()))
        .build();
  }

  private List<MobileConfigDto.KeyValueResponse> buildData(Long packageId) {
    var configKeys = mobileConfigKeysRepository.findAll();
    var keys = configKeys.stream().map(MobileConfigKeys::getId).toList();
    var configValues =
        mobileConfigValuesRepository.findByPackageIdAndMobileConfigKeys(packageId, keys);
    return configKeys.stream()
        .map(
            d -> {
              var value =
                  configValues.stream()
                      .filter(m -> m.getMobileConfigKey().getId() == d.getId())
                      .findFirst();

              return MobileConfigDto.KeyValueResponse.builder()
                  .keyId(d.getId())
                  .key(d.getName())
                  .valueId(value.isEmpty() ? null : value.get().getId())
                  .value(value.map(MobileConfigValues::getName).orElse(null))
                  .status(
                      Boolean.TRUE.equals(value.map(MobileConfigValues::getStatus).orElse(null)))
                  .build();
            })
        .toList();
  }
}
