package com.wexl.retail.metrics.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

@Data
public class MetricResponse {
  private String id;

  @JsonProperty("metric_name")
  private String metricName;

  @JsonProperty("start_time")
  private long startTime;

  @JsonProperty("end_time")
  private long endTime;

  @JsonProperty("status_code")
  private String statusCode;

  private List<DataPoint> datapoints;
}
