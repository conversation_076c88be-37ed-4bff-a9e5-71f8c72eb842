package com.wexl.retail.test.competitive.validators;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.test.schedule.domain.TestScheduleStudentAnswer;
import com.wexl.retail.test.schedule.dto.StudentTestAttemptStatus;
import com.wexl.retail.test.school.domain.TestCategory;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.domain.TestDefinitionSection;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class NEETTestDefinitionValidator implements TestDefinitionValidator {

  private final String[] sectionNames =
      new String[] {
        "Physics Section A",
        "Physics Section B",
        "Chemistry Section A",
        "Chemistry Section B",
        "Botany Section A",
        "Botany Section B",
        "Zoology Section A",
        "Zoology Section B"
      };
  private final Integer[] sectionQuestionCount = new Integer[] {35, 15, 35, 15, 35, 15, 35, 15};

  @Override
  public void validate(TestDefinition testDefinition) {
    if (testDefinition.getTestDefinitionSections().size() != 8) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Invalid.Sections.NEET");
    }
    List<TestDefinitionSection> sortedSections =
        testDefinition.getTestDefinitionSections().stream()
            .sorted(Comparator.comparingLong(TestDefinitionSection::getSequenceNumber))
            .toList();
    for (int i = 0; i < testDefinition.getTestDefinitionSections().size(); i++) {
      if (!sortedSections.get(i).getName().trim().equals(sectionNames[i])) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST, "error.Invalid.SectionNames.NEET");
      }
    }
    for (int i = 0; i < testDefinition.getTestDefinitionSections().size(); i++) {
      if (sortedSections.get(i).getTestQuestions().size() != sectionQuestionCount[i]) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST, "error.Invalid.SectionQuestionCount.NEET");
      }
    }

    for (int i = 0; i < testDefinition.getTestDefinitionSections().size(); i++) {
      final TestDefinitionSection testDefinitionSection = sortedSections.get(i);
      testDefinitionSection
          .getTestQuestions()
          .forEach(
              testQuestion -> {
                if (!testQuestion.getType().equals("MCQ")) {
                  throw new ApiException(
                      InternalErrorCodes.INVALID_REQUEST,
                      "error.Invalid.QuestionType.NEET",
                      new String[] {testDefinitionSection.getName(), "MCQ"});
                }

                if (testQuestion.getMarks() != 4 || testQuestion.getNegativeMarks() != 1) {
                  throw new ApiException(
                      InternalErrorCodes.INVALID_REQUEST,
                      "error.Invalid.QuestionMarks",
                      new String[] {"NEET", testDefinitionSection.getName()});
                }
              });
    }
  }

  @Override
  public boolean supports(TestCategory name) {
    return TestCategory.NEET.equals(name);
  }

  @Override
  public List<TestScheduleStudentAnswer> processOptionalQuestions(
      List<TestScheduleStudentAnswer> tssa, List<TestDefinitionSection> sections) {
    List<TestScheduleStudentAnswer> tssaResponse = new ArrayList<>();
    sections.forEach(
        section -> {
          var answers =
              tssa.stream()
                  .filter(x -> x.getSectionId().equals(section.getId()))
                  .sorted(Comparator.comparing(TestScheduleStudentAnswer::getUpdatedAt))
                  .toList();
          if (section.getTestQuestions().size() == 35) {
            tssaResponse.addAll(answers);
          } else {
            List<TestScheduleStudentAnswer> sortedAnswerList =
                answers.stream()
                    .sorted(
                        Comparator.comparing(
                            answer -> {
                              StudentTestAttemptStatus status = answer.getAttemptStatus();
                              if (status == StudentTestAttemptStatus.ANSWERED) {
                                return 1;
                              } else if (status == StudentTestAttemptStatus.ANSWERED_MARKED) {
                                return 2;
                              } else if (status == StudentTestAttemptStatus.NOT_MARKED) {
                                return 3;
                              } else if (status == StudentTestAttemptStatus.MARKED) {
                                return 4;
                              } else if (status == StudentTestAttemptStatus.NOT_VISITED) {
                                return 5;
                              } else {
                                return 6;
                              }
                            }))
                    .limit(10)
                    .toList();

            tssaResponse.addAll(sortedAnswerList);
          }
        });
    return tssaResponse;
  }
}
