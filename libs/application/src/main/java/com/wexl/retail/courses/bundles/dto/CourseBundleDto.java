package com.wexl.retail.courses.bundles.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record CourseBundleDto() {
  public record Request(
      String name,
      @JsonProperty("course_def_ids") List<Long> courseDefinitionIds,
      @JsonProperty("thumb_nail") String thumbnail) {}

  @Builder
  public record Response(
      Long id,
      String name,
      @JsonProperty("course_definitions") List<CourseDefinition> courseDefinitions,
      @JsonProperty("thumb_nail") String thumbNail,
      @JsonProperty("course_bundle_km") Double courseBundleKm) {}

  @Builder
  public record CourseDefinition(
      Long id,
      String name,
      @JsonProperty("course_km") Double courseKm,
      @JsonProperty("thumbnail") String thumbNail) {}
}
