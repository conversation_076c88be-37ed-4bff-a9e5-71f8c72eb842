package com.wexl.retail.courses.step.model;

import com.wexl.retail.courses.module.model.CourseModule;
import com.wexl.retail.model.Model;
import com.wexl.retail.test.school.domain.TestDefinition;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

@EqualsAndHashCode(callSuper = true)
@Entity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "course_item")
public class CourseItem extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "course-item-sequence-generator")
  @SequenceGenerator(
      name = "course-item-sequence-generator",
      sequenceName = "course_item_seq",
      allocationSize = 1)
  private long id;

  @Column(name = "item_type")
  @Enumerated(EnumType.STRING)
  private CourseItemType itemType;

  private String title;

  @ManyToOne private CourseModule courseModule;

  @ManyToOne private CoursePage coursePage;

  @ManyToOne private TestDefinition testDefinition;

  @Column(name = "asset_slug")
  private String assetSlug;

  @Column(name = "file_id")
  private Long fileId;

  @Column(name = "org_slug")
  private String orgSlug;

  @Column(name = "seq_num")
  private int sequenceNumber;

  @Column(name = "published_at")
  private Timestamp publishedAt;

  @Column(name = "s3_object_key")
  private String s3ObjectKey;

  @Column(name = "concept_video_uuid")
  private String conceptVideoUuid;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb")
  private CourseItemAttributesDto.Attributes attributes;
}
