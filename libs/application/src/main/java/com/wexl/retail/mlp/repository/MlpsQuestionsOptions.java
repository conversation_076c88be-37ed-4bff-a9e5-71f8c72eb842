package com.wexl.retail.mlp.repository;

import com.fasterxml.jackson.annotation.JsonProperty;

public interface MlpsQuestionsOptions {

  @JsonProperty("mlp_ids")
  public Long getMlpId();

  @JsonProperty("question_uuids")
  public String getQuestionUuid();

  @JsonProperty("option_1_count")
  public Integer getOptionOneCount();

  @JsonProperty("option_2_count")
  public Integer getOptionTwoCount();

  @JsonProperty("option_3_count")
  public Integer getOptionThreeCount();

  @JsonProperty("option_4_count")
  public Integer getOptionFourCount();
}
