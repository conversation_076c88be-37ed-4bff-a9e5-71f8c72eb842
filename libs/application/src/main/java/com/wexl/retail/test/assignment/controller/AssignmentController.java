package com.wexl.retail.test.assignment.controller;

import static com.wexl.retail.test.school.domain.TestType.ASSIGNMENT;
import static com.wexl.retail.util.Constants.AUTHORIZATION_HEADER;

import com.wexl.retail.commons.security.annotation.IsTeacher;
import com.wexl.retail.test.assignment.dto.*;
import com.wexl.retail.test.assignment.service.AssignmentService;
import com.wexl.retail.test.school.domain.TestDefinition;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@IsTeacher
@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/teachers/{teacherId}")
public class AssignmentController {

  private final AssignmentService assignmentService;

  @PostMapping("/assignments")
  public ResponseEntity<TestDefinition> createAssignment(
      @RequestBody AssignmentRequest assignmentRequest,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken,
      @PathVariable String orgSlug) {

    assignmentRequest.setOrganization(orgSlug);
    assignmentRequest.setType(ASSIGNMENT);
    return ResponseEntity.ok(assignmentService.createAssignment(assignmentRequest, bearerToken));
  }

  @GetMapping(path = "/assignments/artifacts")
  public ResponseEntity<AssetsReference> getAssetsReference(
      @PathVariable String orgSlug,
      @RequestParam String gradeSlug,
      @RequestParam String subjectSlug) {
    return ResponseEntity.ok(
        assignmentService.createAssetsReference(orgSlug, gradeSlug, subjectSlug));
  }

  @GetMapping("/assignments")
  public List<AssignmentResponse> getAssignmentBySubtopic(
      @PathVariable String orgSlug, @RequestParam @NotNull String subtopic) {
    return assignmentService.getAssignmentBySubtopic(orgSlug, subtopic);
  }

  @GetMapping("/V2/assignments")
  public List<Assignment.AssignmentResponse> getAssignments(
      @PathVariable String orgSlug,
      @PathVariable String teacherId,
      @RequestParam(required = false) Long studentId,
      @RequestParam(required = false) String name,
      @RequestParam(required = false) String subject,
      @RequestParam(required = false) String chapter,
      @RequestParam(required = false) String topic,
      @RequestParam(required = false) String status,
      @RequestParam Long fromDateInEpoch,
      @RequestParam Long toDateInEpoch,
      @RequestParam int limit) {

    return assignmentService.getAssignments(
        orgSlug,
        teacherId,
        studentId,
        name,
        subject,
        chapter,
        topic,
        status,
        fromDateInEpoch,
        toDateInEpoch,
        limit);
  }

  @GetMapping("/assignments:count")
  public Integer getCountOfAssignmentByStatus(
      @PathVariable String orgSlug, @PathVariable String teacherId, @RequestParam String status) {
    return assignmentService.getCountOfAssignmentByStatus(orgSlug, teacherId, status);
  }
}
