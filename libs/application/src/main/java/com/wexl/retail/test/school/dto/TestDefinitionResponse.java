package com.wexl.retail.test.school.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.content.model.Question;
import com.wexl.retail.liveworksheet.dto.LiveWorkSheetDto;
import com.wexl.retail.test.school.domain.TestData;
import com.wexl.retail.test.school.domain.TestQuestion;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TestDefinitionResponse extends TestData {
  private List<TestQuestionRequest> questions;
  private String chapterNames;
  private String testName;
  private Long testDefinitionId;
  private List<TestQuestion> allQuestions;
  private String gradeName;
  private String subjectName;
  private String subjectSlug;
  private int subjectId;
  private int duration;
  private LocalDateTime startDate;
  private LocalDateTime endDate;
  private String status;
  private List<Question> questionsAndOptions;
  private String questionPreviewUrl;
  private String solutionPreviewUrl;
  private long createdAtDate;
  private long updatedAtDate;
  private String instructions;
  private Timestamp publishedAt;
  private List<QuestionDto.Question> questionsList;
  private List<LiveWorkSheetDto.LiveWorksheetAnswers> liveWorksheetAnswers;
  private String theme;

  @JsonProperty("isQpGenPresent")
  private boolean isQpGenPresent;

  @JsonProperty("created_by")
  private String createdBy;

  @JsonProperty("explanation_video_link")
  private String explanationVideoLink;

  @JsonProperty("asset_slug")
  private String assetSlug;
}
