package com.wexl.retail.mlp.repository;

import com.wexl.retail.mlp.model.StudentKMeter;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;

public interface StudentKMeterRepository extends JpaRepository<StudentKMeter, Long> {

  List<StudentKMeter> findAllByStudentIdAndSubTopicSlugAndExamType(
      Long id, String subTopicSlug, Long type);

  List<StudentKMeter> findAllByStudentIdAndChapterSlugAndExamType(
      Long id, String chapterSlug, Long type);

  List<StudentKMeter> findAllByStudentIdAndSubjectSlugAndExamType(
      Long id, String subjectSlug, Long examType);

  Optional<StudentKMeter> findByStudentIdAndExamTypeAndExamId(Long id, Long examType, Long examId);
}
