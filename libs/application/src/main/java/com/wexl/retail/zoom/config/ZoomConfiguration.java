package com.wexl.retail.zoom.config;

import java.util.Map;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "secrets")
public class ZoomConfiguration {
  private Map<String, ZoomCredential> zoom;

  public Map<String, ZoomCredential> getZoom() {
    return zoom;
  }

  public void setZoom(Map<String, ZoomCredential> zoom) {
    this.zoom = zoom;
  }

  @Data
  public static class ZoomCredential {
    private String apiKey;
    private String apiSecret;
    private String sdkKey;
    private String sdkSecret;
  }
}
