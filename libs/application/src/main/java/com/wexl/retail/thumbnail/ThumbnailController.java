package com.wexl.retail.thumbnail;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}/thumbnail:upload")
@RequiredArgsConstructor
public class ThumbnailController {

  private final ThumbnailService thumbnailService;

  @PostMapping()
  public ThumbnailResponse uploadThumbnail(
      @PathVariable String orgSlug, @RequestBody ThumbnailRequest thumbnailRequest) {
    return thumbnailService.uploadThumbnail(orgSlug, thumbnailRequest);
  }
}
