package com.wexl.retail.generic;

import com.wexl.retail.ai.AiQuestionAnalysis;
import com.wexl.retail.ai.dto.ExamAnalysis.AiQuestionAnalysisResponseList;
import com.wexl.retail.ai.dto.ExamAnalysis.PromptAnswerContent;
import com.wexl.retail.ai.dto.ExamAnalysis.PromptQuestionContent;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/public/ai")
@RequiredArgsConstructor
public class AiController {
  private final List<AiQuestionAnalysis> aiQuestionAnalysis;

  @GetMapping("/test123")
  public AiQuestionAnalysisResponseList getTest() {
    List<PromptQuestionContent> promptQuestionContents = new ArrayList<>();
    var question1 =
        PromptQuestionContent.builder()
            .questionNumber(1L)
            .marks(1)
            .text("What is the capital of Telangana")
            .answer("The capital of telangana is Hyerabad")
            .build();
    promptQuestionContents.add(question1);

    var question2 =
        PromptQuestionContent.builder()
            .questionNumber(2L)
            .marks(2)
            .text("What is the capital of Karnataka?")
            .answer("The capital of Karnataka is Bangalore.")
            .build();
    promptQuestionContents.add(question2);

    var question3 =
        PromptQuestionContent.builder()
            .questionNumber(3L)
            .marks(3)
            .text("What is the capital of Maharashtra?")
            .answer("The capital of Maharashtra is Mumbai.")
            .build();
    promptQuestionContents.add(question3);

    List<PromptAnswerContent> promptAnswerContents = new ArrayList<>();

    var answer1 = PromptAnswerContent.builder().questionNumber(1L).answer("Hyderabad").build();
    promptAnswerContents.add(answer1);

    var answer2 = PromptAnswerContent.builder().questionNumber(2L).answer("Bangalore").build();
    promptAnswerContents.add(answer2);

    var answer3 = PromptAnswerContent.builder().questionNumber(3L).answer("Mumbai").build();
    promptAnswerContents.add(answer3);
    var result =
        aiQuestionAnalysis
            .getFirst()
            .analyzeQuestions(promptQuestionContents, promptAnswerContents);
    log.info("Result: {}", result);
    result.response().forEach(r -> log.info("Response: {}", r));

    return result;
  }
}
