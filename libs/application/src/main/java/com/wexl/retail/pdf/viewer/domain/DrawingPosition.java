package com.wexl.retail.pdf.viewer.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Entity
@Accessors(chain = true)
@Table(name = "pdf_drawing_positions")
public class DrawingPosition implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Column(name = "id", updatable = false)
  private long id;

  @Column(name = "annotation_id", nullable = false)
  private Long annotationId;

  @Column(name = "coordinate", nullable = false)
  private String coordinate;
}
