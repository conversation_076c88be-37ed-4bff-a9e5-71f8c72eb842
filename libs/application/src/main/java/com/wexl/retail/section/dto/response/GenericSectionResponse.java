package com.wexl.retail.section.dto.response;

import com.wexl.retail.section.domain.SectionStatus;
import java.sql.Timestamp;
import java.util.Date;
import java.util.UUID;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class GenericSectionResponse {
  private long id;
  private String name;
  private SectionStatus status;
  private String remarks;
  private UUID uuid;
  private String organization;
  private Integer gradeId;
  private Timestamp createdAt;
  private Timestamp updatedAt;
  private Date deletedAt;
}
