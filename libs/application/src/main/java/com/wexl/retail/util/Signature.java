package com.wexl.retail.util;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import jakarta.xml.bind.DatatypeConverter;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

public class Signature {

  private static final String HMAC_SHA256_ALGORITHM = "HmacSHA256";
  private static final String HMAC_SHA512_ALGORITHM = "HmacSHA512";

  /**
   * * Computes RFC 2104-compliant HMAC signature. * * @param data * The data to be signed. * @param
   * key * The signing key. * @return * The Base64-encoded RFC 2104-compliant HMAC signature.
   * * @throws * java.security.SignatureException when signature generation fails
   */
  public static String gethmacsha256(String data, String secret) {
    return generateSignature(data, secret, HMAC_SHA256_ALGORITHM);
  }

  public static String gethmacsha512(String data, String secret)
      throws java.security.SignatureException {
    return generateSignature(data, secret, HMAC_SHA512_ALGORITHM);
  }

  private static String generateSignature(String data, String secret, String hmacShaAlgorithm) {
    try {
      // get an hmac_sha256 key from the raw secret bytes
      var signingKey = new SecretKeySpec(secret.getBytes(), hmacShaAlgorithm);
      // get an hmac_sha256 Mac instance and initialize with the signing key
      var mac = Mac.getInstance(hmacShaAlgorithm);
      mac.init(signingKey);
      // compute the hmac on input data bytes
      byte[] rawHmac = mac.doFinal(data.getBytes());
      // base64-encode the hmac
      return DatatypeConverter.printHexBinary(rawHmac).toLowerCase();
    } catch (Exception e) {
      throw new ApiException(
          InternalErrorCodes.UN_AUTHORIZED, "error.HMAC.Generation", new String[] {e.getMessage()});
    }
  }
}
