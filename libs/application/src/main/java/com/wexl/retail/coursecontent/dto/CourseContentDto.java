package com.wexl.retail.coursecontent.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Builder;

public record CourseContentDto() {

  @Builder
  public record CourseContentRequest(
      @NotNull String name,
      @NotNull String extension,
      @JsonProperty("link_source") @NotNull String linkSource,
      @JsonProperty("sub_topics") @NotNull List<String> subTopics,
      @JsonProperty("ref_key") String refKey,
      String link) {}

  @Builder
  public record ScormFileContent(String title, String schemaVersion, String href) {}
}
