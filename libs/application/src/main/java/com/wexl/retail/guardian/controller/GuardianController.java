package com.wexl.retail.guardian.controller;

import com.wexl.retail.guardian.dto.GuardianDto;
import com.wexl.retail.guardian.dto.GuardianRequest;
import com.wexl.retail.guardian.service.GuardianService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("orgs/{orgSlug}/users/{authUserId}/guardians")
public class GuardianController {

  @Autowired private GuardianService guardianService;

  @PostMapping()
  public void createGuardian(
      @PathVariable String authUserId, @RequestBody List<GuardianRequest> guardianRequest) {
    guardianService.createGuardian(authUserId, guardianRequest);
  }

  @GetMapping()
  public List<GuardianDto.Response> getGuardians(@PathVariable String authUserId) {
    return guardianService.fetchGuardiansDetails(authUserId);
  }

  @PutMapping("/{guardiansId}")
  public void editGuardian(
      @PathVariable String authUserId,
      @PathVariable Long guardiansId,
      @RequestBody GuardianRequest guardianRequest) {
    guardianService.editGuardian(guardianRequest, guardiansId, authUserId);
  }

  @DeleteMapping("/{guardiansId}")
  public void deleteGuardian(@PathVariable String authUserId, @PathVariable Long guardiansId) {
    guardianService.deleteGuardian(guardiansId, authUserId);
  }
}
