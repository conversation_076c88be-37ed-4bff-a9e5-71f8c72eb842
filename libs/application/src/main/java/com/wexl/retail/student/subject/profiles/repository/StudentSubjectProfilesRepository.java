package com.wexl.retail.student.subject.profiles.repository;

import com.wexl.retail.model.Student;
import com.wexl.retail.student.subject.profiles.domain.StudentSubjectProfile;
import com.wexl.retail.student.subject.profiles.domain.SubjectProfiles;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface StudentSubjectProfilesRepository
    extends JpaRepository<StudentSubjectProfile, Long> {

  Optional<StudentSubjectProfile> findFirstBySubjectProfileAndDeletedAt(
      SubjectProfiles subjectProfiles, @Nullable Date date);

  Optional<StudentSubjectProfile> findFirstByStudentAndSubjectProfileAndDeletedAt(
      Student student, SubjectProfiles subjectProfiles, @Nullable Date date);

  List<StudentSubjectProfile> findAllByStudent(Student student);

  List<StudentSubjectProfile> findAllByStudentAndDeletedAtIsNull(Student student);

  @Query(
      value =
          "select distinct student_id from student_subject_profiles where student_id in (:students) order by student_id",
      nativeQuery = true)
  List<Long> getStudentsWithSubjectProfiles(List<Long> students);
}
