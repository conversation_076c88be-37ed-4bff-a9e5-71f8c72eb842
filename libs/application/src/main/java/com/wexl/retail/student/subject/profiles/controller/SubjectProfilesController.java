package com.wexl.retail.student.subject.profiles.controller;

import com.wexl.retail.admin.ImportResponse;
import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import com.wexl.retail.organization.dto.OrganizationResponse;
import com.wexl.retail.student.subject.profiles.dto.DetailedSubjectProfileResponse;
import com.wexl.retail.student.subject.profiles.dto.SubjectProfileRequest;
import com.wexl.retail.student.subject.profiles.dto.SubjectProfileResponse;
import com.wexl.retail.student.subject.profiles.dto.TargetStudentSubjectProfileRequest;
import com.wexl.retail.student.subject.profiles.service.SubjectProfileMigrationService;
import com.wexl.retail.student.subject.profiles.service.SubjectProfilesService;
import com.wexl.retail.user.domain.OrganizationsRequest;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class SubjectProfilesController {

  private final SubjectProfileMigrationService subjectProfileMigrationService;
  private final SubjectProfilesService subjectProfileService;

  @PostMapping("/orgs/{orgSlug}/subject-profiles/")
  public void createSubjectProfile(
      @PathVariable String orgSlug, @RequestBody SubjectProfileRequest subjectProfileRequest) {

    subjectProfileService.createSubjectProfile(orgSlug, subjectProfileRequest);
  }

  @GetMapping("/orgs/{orgSlug}/subject-profiles/")
  public List<SubjectProfileResponse> getSubjectProfileResponse(@PathVariable String orgSlug) {
    return subjectProfileService.getAllSubjectProfilesOfOrg(orgSlug);
  }

  @GetMapping("/orgs/{orgSlug}/subject-profiles/{id}")
  public DetailedSubjectProfileResponse getSubjectProfileById(
      @PathVariable String orgSlug, @PathVariable("id") Long subjectProfileId) {
    return subjectProfileService.viewSubjectProfile(orgSlug, subjectProfileId);
  }

  @DeleteMapping("/orgs/{orgSlug}/subject-profiles/{id}")
  public ResponseEntity<Void> deleteSubjectProfile(
      @PathVariable String orgSlug, @PathVariable Long id) {
    subjectProfileService.deleteSubjectProfile(orgSlug, id);
    return ResponseEntity.ok().build();
  }

  @PostMapping("/orgs/{orgSlug}/subject-profiles/{id}/details")
  public void addNewSubjectToProfile(
      @PathVariable String orgSlug,
      @PathVariable("id") Long subjectProfileId,
      @RequestBody SubjectProfileRequest subjectProfileRequest) {

    subjectProfileService.addNewSubjectToProfile(orgSlug, subjectProfileRequest, subjectProfileId);
  }

  @DeleteMapping("/orgs/{orgSlug}/subject-profiles/{subjectProfileId}/details/{id}")
  public ResponseEntity<Void> deleteSubjectFromProfile(
      @PathVariable String orgSlug,
      @PathVariable Long subjectProfileId,
      @PathVariable("id") Long subjectProfileDetailsId) {

    subjectProfileService.deleteASubjectFromProfile(
        orgSlug, subjectProfileId, subjectProfileDetailsId);
    return ResponseEntity.ok().build();
  }

  @PostMapping("/orgs/{orgSlug}/students/{userName}/subject-profiles/{id}")
  public void mapSubjectProfileToStudent(
      @PathVariable String orgSlug, @PathVariable String userName, @PathVariable Long id) {

    subjectProfileService.mapSubjectProfileToStudent(orgSlug, userName, id);
  }

  @GetMapping("/orgs/{orgSlug}/students/{userName}/subject-profiles")
  public List<SubjectProfileResponse> getSubjectProfilesOfStudent(@PathVariable String userName) {

    return subjectProfileService.getSubjectProfilesOfStudent(userName);
  }

  @DeleteMapping("/orgs/{orgSlug}/students/{userName}/subject-profiles/{id}")
  public void deleteAProfileOfStudent(
      @PathVariable String orgSlug, @PathVariable String userName, @PathVariable Long id) {

    subjectProfileService.deleteAProfileOfStudent(orgSlug, userName, id);
  }

  @GetMapping("orgs/{orgSlug}/subject-profile-orgs")
  public List<OrganizationResponse> getSubjectProfileOrgs(@PathVariable String orgSlug) {
    return subjectProfileService.getSubjectProfileOrgs(orgSlug);
  }

  @IsOrgAdmin
  @PostMapping("orgs/{orgSlug}/students/{username}/subject-profiles")
  public ImportResponse manageStudentSubjectProfile(
      @PathVariable String orgSlug,
      @PathVariable String username,
      @Valid @RequestBody TargetStudentSubjectProfileRequest targetStudentSubjectProfileRequest) {
    return subjectProfileService.manageStudentSubjectProfile(
        orgSlug, username, targetStudentSubjectProfileRequest);
  }

  @PostMapping("/orgs/wexl-internal/students-subject-profiles/validation")
  public List<Long> validateSubjectProfiles(
      @RequestBody OrganizationsRequest organizationsRequest) {

    return subjectProfileMigrationService.validateStudentSubjectProfiles(
        organizationsRequest.getOrgs());
  }
}
