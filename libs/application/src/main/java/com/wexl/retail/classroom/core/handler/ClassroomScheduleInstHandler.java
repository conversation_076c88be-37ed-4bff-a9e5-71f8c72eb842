package com.wexl.retail.classroom.core.handler;

import com.wexl.retail.classroom.core.dto.ClassroomScheduleInstResponse;
import com.wexl.retail.model.User;
import com.wexl.retail.task.dto.StudentScheduleResponse;
import java.util.List;

public interface ClassroomScheduleInstHandler {

  List<ClassroomScheduleInstResponse> getClassroomSchedules(
      User user, Long fromDateInEpoch, Long toDateInEpoch, String orgSlug);

  List<StudentScheduleResponse> getStudentSchedules(
      String orgSlug, Long studentId, Integer limit, Long date);

  List<Long> getClassroomDates(String studentId, Integer limit);

  List<StudentScheduleResponse> getStudentClassroomsByDate(
      String studentId, String orgSlug, Long date, Integer limit);
}
