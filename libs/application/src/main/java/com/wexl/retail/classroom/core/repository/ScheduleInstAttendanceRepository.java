package com.wexl.retail.classroom.core.repository;

import com.wexl.retail.classroom.core.model.ClassroomScheduleInst;
import com.wexl.retail.classroom.core.model.ScheduleInstAttendance;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface ScheduleInstAttendanceRepository
    extends JpaRepository<ScheduleInstAttendance, Long> {

  Optional<ScheduleInstAttendance> findByClassroomScheduleInst(ClassroomScheduleInst scheduleInst);

  Optional<ScheduleInstAttendance> findByClassroomScheduleInstId(long scheduleInst);

  Optional<ScheduleInstAttendance> findByClassroomScheduleInstIdAndOrgSlug(Long id, String orgSlug);

  List<ScheduleInstAttendance> findByClassroomScheduleInstIn(
      List<ClassroomScheduleInst> classroomScheduleInsts);

  @Query(
      value =
          """
          select sia.*  from  schedule_inst_attendance sia
             join classroom_schedule_inst csi on csi.id = sia.classroom_schedule_inst_id
             join classroom_schedules cs  on cs.id = csi.classroom_schedule_id
             join classrooms c  on c.id  = cs.classroom_id
            where   csi.start_time BETWEEN :fromDate and :toDate  and c.org_slug= :orgSlug
            and (cast((:classRoomName) as varChar) is null or c.name =(:classRoomName))""",
      nativeQuery = true)
  List<ScheduleInstAttendance> getScheduleInstAttendanceByDate(
      String orgSlug, LocalDateTime fromDate, LocalDateTime toDate, String classRoomName);
}
