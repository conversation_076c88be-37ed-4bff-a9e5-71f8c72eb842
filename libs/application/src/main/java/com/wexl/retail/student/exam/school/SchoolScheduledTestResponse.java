package com.wexl.retail.student.exam.school;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.test.school.domain.TestType;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class SchoolScheduledTestResponse {
  private long testDefinitionId;
  private String testName;
  private String subjectName;
  private long startDate;
  private long endDate;
  private long scheduleTestId;
  private String status;
  private String category;

  @JsonProperty("schedule_test_state")
  private String testState;

  @JsonProperty("schedule_test_uuid")
  private String scheduleTestUuid;

  @JsonProperty("test_type")
  private TestType testType;
}
