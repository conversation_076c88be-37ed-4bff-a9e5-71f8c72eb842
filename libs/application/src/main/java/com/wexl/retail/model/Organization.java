package com.wexl.retail.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Organization {

  private Long id;

  @JsonProperty("Name")
  private String name;

  private String slug;

  @JsonProperty("Status")
  private Boolean status;

  private String type;

  @JsonProperty("org_type")
  private OrgType orgType;

  private OrgSettings settings;
}
