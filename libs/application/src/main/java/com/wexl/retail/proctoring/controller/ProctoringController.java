package com.wexl.retail.proctoring.controller;

import com.wexl.retail.proctoring.dto.ProctoringDto;
import com.wexl.retail.proctoring.service.ProctoringService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/proctoring")
public class ProctoringController {

  private final ProctoringService proctoringService;

  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public ProctoringDto.Response startProctoringSession(@RequestBody ProctoringDto.Request request) {
    return proctoringService.startProctoringSession(request);
  }

  @PostMapping("/{proctoringId}/results")
  public ProctoringDto.ProctoringSessionResponse updateProctoringResult(
      @PathVariable("orgSlug") String orgSlug, @PathVariable("proctoringId") Long proctoringId) {
    return proctoringService.updateProctoringResult(orgSlug, proctoringId);
  }
}
