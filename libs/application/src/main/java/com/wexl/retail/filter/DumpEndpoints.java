package com.wexl.retail.filter;

import com.wexl.retail.monitoring.service.EndpointService;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

@Slf4j
@Component
@RequiredArgsConstructor
public class DumpEndpoints implements ApplicationListener<ContextRefreshedEvent> {

  private final EndpointService endpointService;

  @Override
  public void onApplicationEvent(ContextRefreshedEvent event) {
    if (!endpointService.populateEndpointPatterns()) {
      return;
    }
    log.info("The table endpointPatterns is empty. Populating it with the current endpoints");
    ApplicationContext applicationContext = event.getApplicationContext();
    RequestMappingHandlerMapping requestMappingHandlerMapping =
        applicationContext.getBean(
            "requestMappingHandlerMapping", RequestMappingHandlerMapping.class);
    Map<RequestMappingInfo, HandlerMethod> map = requestMappingHandlerMapping.getHandlerMethods();
    Set<String> endpoints = new HashSet<>();
    map.keySet().forEach(key -> endpoints.add(key.getActivePatternsCondition().toString()));
    endpointService.saveEndpointPatterns(endpoints);
    log.info("Population complete");
  }
}
