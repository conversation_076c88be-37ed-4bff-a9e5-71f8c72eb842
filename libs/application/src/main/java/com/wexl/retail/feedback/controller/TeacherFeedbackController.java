package com.wexl.retail.feedback.controller;

import com.wexl.retail.feedback.dto.TeacherFeedbackDto;
import com.wexl.retail.feedback.service.TeacherFeedbackService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Valid
@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/teacher/{teacherAuthId}")
public class TeacherFeedbackController {
  private final TeacherFeedbackService teacherFeedbackService;

  @GetMapping("/feedback-summary")
  public TeacherFeedbackDto.TeacherFeedbackSummary getFeedbackSummary(
      @PathVariable String teacherAuthId,
      @PathVariable String orgSlug,
      @RequestParam(name = "feedback_type") String feedbackType,
      @RequestParam(name = "from_date") Long fromDate,
      @RequestParam(name = "to_date") Long toDate) {
    return teacherFeedbackService.getFeedbackSummary(
        orgSlug, fromDate, toDate, teacherAuthId, feedbackType);
  }
}
