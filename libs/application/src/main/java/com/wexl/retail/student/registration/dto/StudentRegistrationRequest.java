package com.wexl.retail.student.registration.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.model.Gender;
import jakarta.validation.constraints.NotNull;
import lombok.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StudentRegistrationRequest {

  @NonNull private String email;

  @JsonProperty("full_name")
  @NotNull
  private String fullName;

  @JsonProperty("phone_number")
  @NotNull
  private String phoneNumber;

  @JsonProperty("school_name")
  private String schoolName;

  @JsonProperty("grade_slug")
  private String gradeSlug;

  private Gender gender;

  @JsonProperty("date_of_birth")
  private String dateOfBirth;

  @JsonProperty("blood_group")
  private String bloodGroup;

  @JsonProperty("principal_name")
  private String principalName;

  @JsonProperty("school_address")
  private String schoolAddress;

  @JsonProperty("school_city")
  private String schoolCity;

  @JsonProperty("school_state")
  private String schoolState;

  @JsonProperty("school_pin")
  private String schoolPin;

  @JsonProperty("captcha_code")
  private String captchaCode;

  @JsonProperty("profile_image_reference")
  private String profileImageReference;

  @JsonProperty("profile_image_extension")
  private String imageExtension;

  @JsonProperty("ext_ref")
  private String extRef;

  @JsonProperty("android_app_url")
  private String androidAppUrl;

  @JsonProperty("web_app_url")
  private String webAppUrl;
}
