package com.wexl.retail.repository;

import com.wexl.retail.feedback.repository.TeacherFeedbackDetails;
import com.wexl.retail.model.OrgTeacher;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.section.domain.Section;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface TeacherRepository extends JpaRepository<Teacher, Long> {

  @Query(
      value =
          """
          SELECT DISTINCT ts.teacher FROM TeacherSection ts \
          WHERE ts.section IN (:sections) AND ts.deletedAt IS NULL\
          """)
  Set<Teacher> getTeachersBySections(final @Param("sections") Set<Section> sections);

  Optional<Teacher> findByUserInfo(User userInfo);

  @Query(
      value =
          """
                  SELECT u.email as email,u.first_name as firstName,u.last_name as lastName,u.mobile_number as mobileNumber,
                  u.auth_user_id as authUserId, o.name as orgName,t.id as teacherId,rt."template" as admin,u.last_login as lastLoginTime,
                  string_agg(cast(subjects.subject as text), ', ') as subjects
                  FROM teacher_details t INNER JOIN users u ON t.user_id = u.id
                  LEFT JOIN role_templates rt ON rt.id = t.role_template_id
                  JOIN orgs o on u.organization =o.slug
                  LEFT JOIN LATERAL (SELECT jsonb_array_elements_text(t.metadata->'subjects') as subject)
                  subjects ON true WHERE u.organization =:organizationSlug AND u.deleted_at IS NULL
                  GROUP BY u.email, u.first_name, u.last_name, u.mobile_number, u.auth_user_id, o.name, t.id, rt."template", u.last_login
                  ORDER BY t.id DESC
                      """,
      nativeQuery = true)
  List<OrgTeacher> findAllTeachers(String organizationSlug);

  @Query(
      value =
          """
        select td.* from teacher_details td
        inner join users u on u.id = td.user_id
        where u.organization = :orgSlug and td.id in(:teacherIds)""",
      nativeQuery = true)
  List<Teacher> findAllByIdsAndOrgSlug(String orgSlug, List<Long> teacherIds);

  @Query(
      value =
          """
                  select t.id as teacherId,u.email as email, u.first_name as firstName,
                  u.last_name as lastName, u.mobile_number as mobileNumber,
                  u.auth_user_id as authUserId, u.user_name as username,
                  u.deleted_at as deletedAt
                  from teacher_details t
                  inner join users u on t.user_id = u.id
                  inner join role_templates rt on rt.id = t.role_template_id
                  where u.organization=:organizationSlug and rt.template = 'ADMIN' limit 1""",
      nativeQuery = true)
  OrgTeacher getAllOrgAdminTeachers(String organizationSlug);

  @Query(
      value =
          """
                          select distinct(td.*) from students s join sections se
                          on s.section_id = se.id
                          join teacher_sections ts on ts.section_id = se.id
                          join teacher_details td on td.id = ts.teacher_id
                          where s.id in (:studentList) and organization = :orgSlug
                          """,
      nativeQuery = true)
  List<Teacher> findTeacherByStudentIds(List<Long> studentList, String orgSlug);

  @Query(
      value =
          """
                      select td.* from teacher_details td join users u on u.id = td.user_id
                      join role_templates rt on rt.id = td.role_template_id  where
                      u.organization = :organizationSlug and rt.template = 'ADMIN' and u.deleted_at is null and td.deleted_at is null
                    """,
      nativeQuery = true)
  List<Teacher> getAllAdminsByOrg(String organizationSlug);

  @Query(
      value =
          """
                          select distinct td.id as teacherId,concat(u.first_name, ' ', u.last_name) as fullName,
                           u.auth_user_id as authUserId from classrooms c
                          join classroom_teachers ct on c.id = ct.classroom_id
                          join teacher_details td on td.id = ct.teacher_id
                          join users u on u.id = td.user_id where  org_slug = :orgSlug
                                  """,
      nativeQuery = true)
  List<TeacherFeedbackDetails> getAllClassroomTeacherId(String orgSlug);

  @Query(
      value =
          """
                      select td.* from teacher_details td join users u on u.id = td.user_id
                      join role_templates rt on rt.id = td.role_template_id  where
                      u.organization = :orgSlug and rt.template = :role
                      """,
      nativeQuery = true)
  List<Teacher> getAllTeacherByOrgSlug(String orgSlug, String role);

  @Query(
      value =
          """

                                      select count(td.*) from users u join teacher_details td on u.id = td.user_id
                                       where u.organization  = :orgSlug and u.deleted_at is null and td.deleted_at is null
                                  """,
      nativeQuery = true)
  Long getTeacherCountByOrg(String orgSlug);
}
