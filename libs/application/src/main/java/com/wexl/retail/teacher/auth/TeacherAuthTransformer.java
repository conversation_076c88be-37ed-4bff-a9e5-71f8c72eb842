package com.wexl.retail.teacher.auth;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.globalprofile.model.AppTemplate;
import com.wexl.retail.globalprofile.model.RoleTemplate;
import com.wexl.retail.globalprofile.repository.RoleTemplateRepository;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.TeacherMetadata;
import com.wexl.retail.model.User;
import com.wexl.retail.model.UserVerificationStatus;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.util.StrapiService;
import java.util.Arrays;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class TeacherAuthTransformer {
  private final StrapiService strapiService;

  private final PasswordEncoder passwordEncoder;

  @Value("${app.teacher.defaultSubjects}")
  private final String[] defaultSubjectList;

  private final OrganizationRepository organizationRepository;
  private final RoleTemplateRepository roleTemplateRepository;
  private final UserService userService;

  public TeacherSignupResponse mapTeacherSignUpResponse(User teacher, long otpId) {
    return TeacherSignupResponse.builder()
        .userId(teacher.getId())
        .otpId(otpId)
        .firstName(teacher.getFirstName())
        .lastName(teacher.getLastName())
        .email(teacher.getEmail())
        .mobileNumber(teacher.getMobileNumber())
        .build();
  }

  public Teacher mapTeacherSignupRequest(
      TeacherSignupRequest teacherInfo,
      String userName,
      boolean isEmailVerified,
      UserVerificationStatus userVerificationStatus) {

    var user = new User();
    user.setFirstName(teacherInfo.getFirstName());
    user.setLastName(teacherInfo.getLastName());
    user.setEmail(teacherInfo.getEmailAddress());
    user.setEmailVerified(isEmailVerified);
    user.setUserName(teacherInfo.getEmailAddress());
    user.setMobileNumber(teacherInfo.getMobileNumber());
    user.setAuthUserId(userName);
    user.setVerificationStatus(userVerificationStatus);
    user.setPassword(passwordEncoder.encode(teacherInfo.getPassword()));
    user.setExternalRef(teacherInfo.externalRef);
    user.setCountryCode(userService.validateCountryCode(teacherInfo.countryCode));

    String orgSlug = strapiService.getOrganizationBySlug(teacherInfo.getOrgSlug()).getSlug();
    user.setOrganization(orgSlug);
    var teacher = new Teacher();
    teacher.setReferenceFrom(teacherInfo.hearAboutFrom);
    teacher.setUserInfo(user);
    teacher.setRoleTemplate(
        teacherInfo.getRoleTemplate() != null
            ? teacherInfo.getRoleTemplate()
            : getRoleTemplateByOrgSlug(teacherInfo.orgSlug));
    teacher.setMetadata(
        TeacherMetadata.builder().subjects(Arrays.asList(defaultSubjectList)).build());
    teacher.setInstituteName(teacherInfo.instituteName);
    teacher.setTeachingDetails(teacherInfo.principalName);
    user.setTeacherInfo(teacher);
    return teacher;
  }

  public RoleTemplate getRoleTemplateByOrgSlug(String orgSlug) {
    var org = organizationRepository.findBySlug(orgSlug);
    if (org == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.OrganizationNotFound");
    }
    return roleTemplateRepository
        .getTeacherRoleTemplateByGlobalProfileAndTemplate(
            org.getProfile().getId(), AppTemplate.TEACHER.name())
        .stream()
        .findFirst()
        .orElseThrow(
            () ->
                new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.RoleTemplateNotFound"));
  }
}
