package com.wexl.retail.filter;

import com.wexl.retail.auth.AuthService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.filter.OncePerRequestFilter;

@Service
@Slf4j
public class OrgValidationFilter extends OncePerRequestFilter {

  private static final String ORG_PREFIX = "/api/orgs/";
  private static final String SLASH = "/";
  private static final List<String> BY_PASS_URLS = new ArrayList<>();

  static {
    BY_PASS_URLS.add("/api/orgs/wexl-academy/curriculum");
    BY_PASS_URLS.add("%s".formatted(ORG_PREFIX));
  }

  @Autowired private AuthService authService;

  @Override
  protected void doFilterInternal(
      HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
      throws ServletException, IOException {
    String uri = request.getRequestURI();
    var organization = StringUtils.substringBetween(uri, ORG_PREFIX, SLASH);
    if (BY_PASS_URLS.parallelStream().noneMatch(url -> url.endsWith(uri))) {
      var userDetails = authService.getUserDetails();
      if (userDetails == null
          || userDetails.getOrganization() == null
          || (!userDetails.getOrganization().equals(organization)
              && !authService.isTeacherAssociatedOrg(organization, userDetails)
              && !authService.isSubjectProfileOrg(userDetails.getOrganization(), organization))) {
        response.sendError(HttpServletResponse.SC_FORBIDDEN);
        return;
      }
    }

    filterChain.doFilter(request, response);
  }

  @Override
  protected boolean shouldNotFilter(HttpServletRequest request) {
    String path = request.getRequestURI();
    log.trace("Filter the request if it is meant to be for /orgs/" + path);
    boolean shouldFilter = path.contains(ORG_PREFIX);
    return !shouldFilter;
  }
}
