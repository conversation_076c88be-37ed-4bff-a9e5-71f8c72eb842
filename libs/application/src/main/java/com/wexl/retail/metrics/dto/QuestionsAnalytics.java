package com.wexl.retail.metrics.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.content.model.Question;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionsAnalytics {

  @JsonProperty("title")
  private String title;

  @JsonProperty("teacher_name")
  private String teacherName;

  @JsonProperty("created_date")
  private long createdDate;

  @JsonProperty("subject")
  private String subject;

  @JsonProperty("chapter")
  private List<String> chapter;

  @JsonProperty("sub_topic")
  private List<String> subTopic;

  @JsonProperty("total_students_count")
  private int totalStudentsCount;

  @JsonProperty("attempted_student_count")
  private int attemptedStudentCount;

  @JsonProperty("question_details")
  private List<QuestionDetails> questionDetails;

  @JsonProperty("question_data")
  private List<Question> questionData;
}
