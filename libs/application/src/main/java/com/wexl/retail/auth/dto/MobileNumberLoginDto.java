package com.wexl.retail.auth.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Builder;

public record MobileNumberLoginDto() {

  @Builder
  public record MobileNumberLoginOtpRequest(String countryCode, String mobileNumber) {}

  @Builder
  public record MobileNumberLoginRequest(
      long otpId,
      Long otp,
      @NotNull String appContext,
      String firebaseToken,
      @JsonProperty("mobile_number") String mobileNumber) {}

  @Builder
  public record MobileNumberLoginResponse(
      String linkJwtToken, String userStatus, List<LoginResponse> users) {}

  public record UpdateMobileNumberRequest(
      String userName,
      String password,
      String mobileNumberJwt,
      @NotNull String appContext,
      @NotNull String firebaseToken) {}

  public record PingRequest(String userJwt) {}

  @Builder
  public record PingResponse(String status, String message) {}
}
