package com.wexl.retail.filter;

import static java.util.Objects.isNull;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.validator.*;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.teacher.orgs.TeacherOrgsRepository;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.filter.OncePerRequestFilter;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserRoleValidationFilter extends OncePerRequestFilter {
  private static final String SLASH = "/";
  private static final String STUDENT_PREFIX = "/students/";
  private static final String TEACHER_PREFIX = "/teachers/";
  private static final String CHILD_ORG_PREFIX = "/child-orgs/";
  private final Map<String, RoleValidator> roleValidators = new HashMap<>();

  private final AuthService authService;
  private final UserRepository userRepository;
  private final TeacherOrgsRepository teacherOrgsRepository;

  @Override
  public void afterPropertiesSet() throws ServletException {
    super.afterPropertiesSet();
    roleValidators.put(STUDENT_PREFIX, new StudentValidator(userRepository));
    roleValidators.put(TEACHER_PREFIX, new TeacherValidator());
    roleValidators.put(
        CHILD_ORG_PREFIX, new ChildOrgValidator(teacherOrgsRepository, userRepository));
  }

  @Override
  protected void doFilterInternal(
      HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
      throws ServletException, IOException {
    String uri = request.getRequestURI();

    if (!isValidRole(uri)) {
      response.sendError(HttpServletResponse.SC_FORBIDDEN);
      return;
    }

    filterChain.doFilter(request, response);
  }

  protected boolean isValidRole(String uri) {
    String validRolePrefix = getValidRole(uri);
    if (!isNull(validRolePrefix)) {
      String id =
          Optional.ofNullable(StringUtils.substringBetween(uri, validRolePrefix, SLASH))
              .orElse(StringUtils.substringAfter(uri, validRolePrefix));
      return roleValidators.get(validRolePrefix).isValidRole(authService.getUserDetails(), id);
    }
    return false;
  }

  private String getValidRole(String uri) {
    HashMap<String, Integer> matchedRolesMap = new HashMap<>();

    for (Entry<String, RoleValidator> entry : roleValidators.entrySet()) {
      String rolePrefix = entry.getKey();
      if (uri.contains(rolePrefix)) {
        matchedRolesMap.put(rolePrefix, uri.indexOf(rolePrefix));
      }
    }

    Entry<String, Integer> validRoleEntry = null;
    for (Entry<String, Integer> entry : matchedRolesMap.entrySet()) {
      if (validRoleEntry == null
          || (validRoleEntry.getValue() > entry.getValue() && validRoleEntry.getValue() > 0)) {
        validRoleEntry = entry;
      }
    }
    return validRoleEntry != null ? validRoleEntry.getKey() : null;
  }

  @Override
  protected boolean shouldNotFilter(HttpServletRequest request) {
    String path = request.getRequestURI();
    log.trace("Filter the request if it is meant to be for " + "/students/ or /teachers/: " + path);
    boolean shouldFilter =
        path.contains(STUDENT_PREFIX)
            || path.contains(TEACHER_PREFIX)
            || path.contains(CHILD_ORG_PREFIX);
    return !shouldFilter;
  }
}
