package com.wexl.retail.student.answer;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.content.model.Question;
import com.wexl.retail.test.school.dto.QuestionDto;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExamQuestion {
  private long examAnswerId;
  private long questionId;
  private String questionUuid;
  private int selectedAnswer;
  private boolean isCorrect;
  private Question questionDetails;
  private QuestionDto.Question questionsResponse;
  private String type;
  private Float marksScored;
  private List<String> attachments;
  private String answer;
  private Integer marks;
  private String feedback;

  @JsonProperty("answer_uuid")
  private String answerUuid;

  @JsonProperty("worksheet_answer_type")
  private String answerType;

  @JsonProperty("elaborate_answer")
  private String elaborateAnswer;

  @JsonProperty("elaborate_selected_answer")
  private String elaborateSelectedAnswer;

  @JsonProperty("ai_analysis")
  private String aiAnalysis;

  @JsonProperty("ai_marks")
  private Float aiMarks;

  @JsonProperty("subjective_written_answer")
  private String subjectiveWrittenAnswer;

  public String getType() {
    if (Objects.isNull(type)) {
      return "mcq";
    }
    return type;
  }

  public List<String> getAttachments() {
    if (Objects.isNull(attachments)) {
      return new ArrayList<>();
    }
    return attachments;
  }
}
