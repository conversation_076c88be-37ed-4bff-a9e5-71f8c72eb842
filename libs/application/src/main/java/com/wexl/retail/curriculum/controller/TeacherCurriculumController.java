package com.wexl.retail.curriculum.controller;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.caching.CacheConstants;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.curriculum.service.StudentCurriculumService;
import com.wexl.retail.curriculum.service.TeacherCurriculumService;
import com.wexl.retail.model.EduBoard;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.UserRepository;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.http.CacheControl;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class TeacherCurriculumController {

  private final TeacherCurriculumService teacherCurriculumService;
  private final AuthService authService;

  private final StudentCurriculumService studentCurriculumService;
  private final UserRepository userRepository;

  @GetMapping("/orgs/{orgSlug}/teachers/{authUserId}/curriculum")
  public ResponseEntity<List<EduBoard>> getTeacherSpecificCurriculum(
      @PathVariable String authUserId) {

    return ResponseEntity.ok()
        .cacheControl(CacheControl.maxAge(CacheConstants.MEDIUM))
        .body(teacherCurriculumService.transformBoardHierarchy(authUserId));
  }

  @GetMapping("/orgs/{orgSlug}/teachers/{teacherAuthUserId}/student-curriculum")
  public ResponseEntity<List<EduBoard>> getStudentCurriculum(
      @PathVariable String orgSlug,
      @RequestParam(value = "userName", required = true) String studentUserName) {
    User user = userRepository.findByAuthUserIdAndOrganization(studentUserName, orgSlug);
    if (Objects.isNull(user)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.UserNotFound");
    }
    return ResponseEntity.ok()
        .cacheControl(CacheControl.maxAge(CacheConstants.MEDIUM))
        .body(studentCurriculumService.getStudentCurriculum(user));
  }
}
