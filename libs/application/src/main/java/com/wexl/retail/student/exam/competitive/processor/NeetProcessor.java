package com.wexl.retail.student.exam.competitive.processor;

import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.student.exam.competitive.dto.CompetitiveExamsDto;
import com.wexl.retail.test.school.domain.*;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class NeetProcessor extends AbstractCompetitiveExamProcessor {
  private static final int TOTAL_MARKS = 720;
  private final String[] sectionNames =
      new String[] {
        "Physics Section A",
        "Physics Section B",
        "Chemistry Section A",
        "Chemistry Section B",
        "Botany Section A",
        "Botany Section B",
        "Zoology Section A",
        "Zoology Section B"
      };

  @Override
  public boolean supports(TestCategory testCategory) {
    return TestCategory.NEET.equals(testCategory);
  }

  @Override
  public int getTotalMarks() {
    return TOTAL_MARKS;
  }

  protected List<TestDefinitionSection> buildTestDefinitionSections(
      TestDefinition testDefinition, List<CompetitiveExamsDto.AnswerKeys> answerKeys) {
    List<TestDefinitionSection> sections = new ArrayList<>();
    testDefinition.setTotalMarks(800);
    testDefinition.setNoOfQuestions(200);
    for (int i = 0; i < 8; i++) {
      if (i == 0 || i == 2 || i == 4 || i == 6) {
        sections.add(
            buildSections(
                testDefinition, sectionNames[i], 35L, QuestionType.MCQ, answerKeys, i + 1L));
      } else {
        sections.add(
            buildSections(
                testDefinition, sectionNames[i], 15L, QuestionType.MCQ, answerKeys, i + 1L));
      }
    }
    return sections;
  }

  @Override
  protected List<TestQuestion> buildTestQuestions(
      TestDefinitionSection testDefinitionSection,
      String sectionName,
      Long questionCount,
      QuestionType type,
      List<CompetitiveExamsDto.AnswerKeys> answerKeys) {
    List<TestQuestion> questions = new ArrayList<>();
    var answerKeyStartIndex = getAnswerKeyCount(sectionName);
    for (int i = 0; i < questionCount; i++) {
      questions.add(
          TestQuestion.builder()
              .testDefinitionSection(testDefinitionSection)
              .mcqAnswer(Long.valueOf(answerKeys.get(answerKeyStartIndex).answer()))
              .negativeMarks(1)
              .questionUuid(
                  generateMcqQuestionUuid(
                      Long.valueOf(answerKeys.get(answerKeyStartIndex).answer()),
                      answerKeys.get(answerKeyStartIndex).questionNumber()))
              .type(QuestionType.MCQ.name())
              .marks(4)
              .build());
      answerKeyStartIndex = answerKeyStartIndex + 1;
    }
    return questions;
  }

  private int getAnswerKeyCount(String sectionName) {
    return switch (sectionName) {
      case "Physics Section A" -> 0;
      case "Physics Section B" -> 35;
      case "Chemistry Section A" -> 50;
      case "Chemistry Section B" -> 85;
      case "Botany Section A" -> 100;
      case "Botany Section B" -> 135;
      case "Zoology Section A" -> 150;
      case "Zoology Section B" -> 185;
      default -> 200;
    };
  }
}
