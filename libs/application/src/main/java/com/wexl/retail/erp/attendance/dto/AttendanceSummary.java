package com.wexl.retail.erp.attendance.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public interface AttendanceSummary {

  @JsonProperty("attendance_id")
  Long getattendanceId();

  @JsonProperty("date_id")
  Long getDateId();

  @JsonProperty("section_name")
  String getsectionName();

  @JsonProperty("grade_name")
  String getgradeName();

  @JsonProperty("present_count")
  Long getpresentCount();

  @JsonProperty("absent_count")
  Long getabsentCount();

  @JsonProperty("status")
  String getstatus();

  @JsonProperty("section_uuid")
  String getsectionUuid();

  @JsonProperty("section_id")
  Long getsectionId();

  @JsonProperty("grade_slug")
  String getGradeSlug();
}
