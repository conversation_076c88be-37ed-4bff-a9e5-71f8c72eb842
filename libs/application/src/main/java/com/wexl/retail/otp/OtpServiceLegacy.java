package com.wexl.retail.otp;

import static com.wexl.retail.util.Constants.MOBILE_VERIFICATION_REFERENCE;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.email.EmailService;
import com.wexl.retail.email.TwilioSmsService;
import com.wexl.retail.generic.ProfileUtils;
import com.wexl.retail.model.User;
import com.wexl.retail.util.Validation;
import java.security.SecureRandom;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OtpServiceLegacy {

  private final OtpRepository otpRepository;
  private final EmailService emailService;
  private final TwilioSmsService twilioSmsService;
  private final ProfileUtils profileUtils;

  public OtpServiceLegacy(
      OtpRepository otpRepository,
      EmailService emailService,
      TwilioSmsService twilioSmsService,
      ProfileUtils profileUtils) {
    this.otpRepository = otpRepository;
    this.emailService = emailService;
    this.twilioSmsService = twilioSmsService;
    this.profileUtils = profileUtils;
  }

  public Otp saveOtp(String mobile) {
    String otp = this.generateOtp();
    var newOtp = new Otp();
    newOtp.setOtp(otp);
    newOtp.setReference(MOBILE_VERIFICATION_REFERENCE);
    newOtp.setExpireAt(getExpiryDate());
    newOtp.setDeletedAt(null);
    newOtp.setTarget(mobile);
    return otpRepository.save(newOtp);
  }

  public OtpResponse sendOtp(
      String email, String firstName, String lastName, String reference, User user) {
    log.debug("Send Otp called by " + user.getId());
    String otp = this.generateOtp();
    deleteOtpIfAlreadyActive(otp, user.getId());

    var newOtp = new Otp();
    newOtp.setOtp(otp);
    newOtp.setReference(reference);
    newOtp.setUser(user);
    newOtp.setExpireAt(getExpiryDate());
    newOtp.setDeletedAt(null);
    newOtp = otpRepository.save(newOtp);

    if (!profileUtils.isDev()) {
      emailService.sendEmailAfterOtp(firstName, lastName, email, otp);
    }

    return OtpResponse.builder()
        .status(true)
        .message("Generated new otp")
        .otpId(newOtp.getId())
        .validFor("30 mins")
        .build();
  }

  private Date getExpiryDate() {
    return new Date(System.currentTimeMillis() + 1000 * 60 * 30);
  }

  public void deleteOtpIfAlreadyActive(String otp, long userId) {
    Optional<Otp> possibleOtpResponse = otpRepository.findByOtpUserIdDeletedAtNull(otp, userId);
    if (possibleOtpResponse.isEmpty()) {
      return;
    }

    var otpResponse = possibleOtpResponse.get();
    otpResponse.setDeletedAt(new Date());
    otpRepository.save(otpResponse);
  }

  public String generateOtp() {
    SecureRandom random = new SecureRandom();
    var otp = random.nextInt(123456, 999999);
    return String.valueOf(otp);
  }

  public boolean verifyOtp(String otpString, long otpId) {

    if (profileUtils.isDev()) {
      return true;
    }

    log.info("Verifying OTP for otpId : {} with otp : {}", otpId, otpString);
    Optional<Otp> possibleOtp = otpRepository.findById(otpId);

    return possibleOtp
        .filter(
            otp ->
                otpString.equals(otp.getOtp())
                    && otp.getExpireAt().getTime() > System.currentTimeMillis()
                    && Objects.isNull(otp.getDeletedAt()))
        .isPresent();
  }

  public void deleteOtpIfAlreadyActiveByTarget(String otp, String target) {
    Optional<Otp> possibleOtpResponse = otpRepository.findByOtpTargetDeletedAtNull(otp, target);
    if (possibleOtpResponse.isEmpty()) {
      return;
    }
    var otpResponse = possibleOtpResponse.get();
    otpResponse.setDeletedAt(new Date());
    otpRepository.save(otpResponse);
  }

  public OtpResponse sendOtpToTarget(String target, String reference) {
    log.debug("Send Otp to target called by " + target);
    String otp = this.generateOtp();
    deleteOtpIfAlreadyActiveByTarget(otp, target);
    var newOtp = new Otp();
    newOtp.setOtp(otp);
    newOtp.setReference(reference);
    newOtp.setTarget(target);
    newOtp.setExpireAt(getExpiryDate());
    newOtp.setDeletedAt(null);
    newOtp = otpRepository.save(newOtp);
    if (Validation.isValidEmail(target)) {
      emailService.sendEmailOtp(target, otp);
    } else {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.EmailNotFound");
    }
    return OtpResponse.builder()
        .status(true)
        .message("Generated new otp")
        .otpId(newOtp.getId())
        .validFor("30 mins")
        .build();
  }
}
