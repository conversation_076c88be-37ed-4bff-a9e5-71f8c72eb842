package com.wexl.retail.maths.service;

import com.wexl.retail.maths.dto.CcssDto;
import com.wexl.retail.maths.model.CcssCluster;
import com.wexl.retail.maths.model.CcssDomain;
import com.wexl.retail.maths.model.CcssStandard;
import com.wexl.retail.maths.repository.CcssGradeRepository;
import com.wexl.retail.maths.repository.CcssStandardRepository;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class CcssService {
  private final CcssGradeRepository ccssGradeRepository;
  private final CcssStandardRepository ccssStandardRepository;

  public CcssDto.CcssResponse getCcssCurriculum() {
    List<CcssDto.CcssGradeResponse> response = new ArrayList<>();
    var ccssGrades = ccssGradeRepository.findAll();
    ccssGrades.forEach(
        ccssGrade ->
            response.add(
                CcssDto.CcssGradeResponse.builder()
                    .id(ccssGrade.getId())
                    .name(ccssGrade.getName())
                    .gradeSlug(ccssGrade.getGradeSlug())
                    .ccssDomains(buildCcssDomains(ccssGrade.getCcssDomains()))
                    .build()));
    return CcssDto.CcssResponse.builder().ccssGradeResponse(response).build();
  }

  private List<CcssDto.CcssDomainResponse> buildCcssDomains(List<CcssDomain> ccssDomains) {
    List<CcssDto.CcssDomainResponse> ccssDomainResponse = new ArrayList<>();
    for (CcssDomain ccssDomain : ccssDomains) {
      ccssDomainResponse.add(
          CcssDto.CcssDomainResponse.builder()
              .id(ccssDomain.getId())
              .domainName(ccssDomain.getName())
              .domainSlug(ccssDomain.getSlug())
              .ccssClusters(buildCcssClusters(ccssDomain.getCcssClusters()))
              .build());
    }
    return ccssDomainResponse;
  }

  private List<CcssDto.CcssClusterResponse> buildCcssClusters(List<CcssCluster> ccssClusters) {
    List<CcssDto.CcssClusterResponse> ccssClusterResponse = new ArrayList<>();
    for (CcssCluster ccssCluster : ccssClusters) {
      ccssClusterResponse.add(
          CcssDto.CcssClusterResponse.builder()
              .id(ccssCluster.getId())
              .clusterName(ccssCluster.getName())
              .build());
    }
    return ccssClusterResponse;
  }

  public CcssDto.CcssStandards getStandardsByGradeAndDomainAndCluster(
      String ccssGradeSlug, Long domainId, Long clusterId) {
    var ccssStandards =
        ccssStandardRepository.getCcssStandardsByGradeSlugAndDomainAndCluster(
            ccssGradeSlug, domainId, clusterId);
    if (ccssStandards.isEmpty()) {
      return CcssDto.CcssStandards.builder().build();
    }
    return CcssDto.CcssStandards.builder()
        .ccssStandardResponses(buildCcssStandards(ccssStandards))
        .build();
  }

  private List<CcssDto.CcssStandardResponse> buildCcssStandards(List<CcssStandard> ccssStandards) {
    List<CcssDto.CcssStandardResponse> ccssStandardResponse = new ArrayList<>();
    for (CcssStandard ccssStandard : ccssStandards) {
      var ccssDomain = ccssStandard.getCcssCluster().getCcssDomain();
      var ccssGrade = ccssDomain.getCcssGrade();
      ccssStandardResponse.add(
          CcssDto.CcssStandardResponse.builder()
              .id(ccssStandard.getId())
              .standardName(ccssStandard.getName())
              .standardSlug(ccssStandard.getSlug())
              .clusterId(ccssStandard.getCcssCluster().getId())
              .clusterName(ccssStandard.getCcssCluster().getName())
              .domainId(ccssDomain.getId())
              .domainSlug(ccssDomain.getName())
              .domainName(ccssDomain.getName())
              .ccssGradeId(ccssGrade.getId())
              .ccssGradeSlug(ccssGrade.getGradeSlug())
              .ccssGradeName(ccssGrade.getName())
              .build());
    }
    return ccssStandardResponse;
  }
}
