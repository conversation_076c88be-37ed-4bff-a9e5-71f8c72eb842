package com.wexl.retail.documents.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Builder;

public record DocumentsDto() {

  public record Request(
      @JsonProperty("document_type") DocumentType documentType,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("chapter_name") String chapterName,
      @JsonProperty("chapter_slug") String chapterSlug,
      @JsonProperty("file_type") String fileType,
      String title,
      @JsonProperty("is_teacher") Boolean isTeacher,
      @JsonProperty("is_student") Boolean isStudent,
      @JsonProperty("class_group_type") ClassGroupType classGroupType,
      @JsonProperty("class_group_name") String classGroupName,
      @JsonProperty("class_group_id") Long classGroupId,
      @JsonProperty("student_id") List<Long> studentIds,
      String path) {}

  @Builder
  public record StudentDocumentResponse(
      @JsonProperty("document_type") DocumentType documentType,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("chapter_name") String chapterName,
      @JsonProperty("chapter_slug") String chapterSlug,
      @JsonProperty("file_type") String fileType,
      String title,
      @JsonProperty("is_teacher") Boolean isTeacher,
      @JsonProperty("is_student") Boolean isStudent,
      @JsonProperty("class_group_type") ClassGroupType classGroupType,
      @JsonProperty("class_group_name") String classGroupName,
      @JsonProperty("class_group_id") Long classGroupId,
      @JsonProperty("created_at") Long createdAt,
      @JsonProperty("document_id") Long documentId,
      String path) {}

  @Builder
  public record TeacherDocumentResponse(
      @JsonProperty("document_type") DocumentType documentType,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("chapter_name") String chapterName,
      @JsonProperty("chapter_slug") String chapterSlug,
      @JsonProperty("file_type") String fileType,
      String title,
      @JsonProperty("is_teacher") Boolean isTeacher,
      @JsonProperty("is_student") Boolean isStudent,
      @JsonProperty("class_group_type") ClassGroupType classGroupType,
      @JsonProperty("class_group_name") String classGroupName,
      @JsonProperty("class_group_id") Long classGroupId,
      @JsonProperty("uploaded_by") String uploadedBy,
      @JsonProperty("uploaded_auth_id") String uploadedAuthId,
      @JsonProperty("created_at") Long createdAt,
      @JsonProperty("document_id") Long documentId,
      @JsonProperty("student_details") List<StudentDetails> studentDetails,
      String path) {}

  @Builder
  public record UploadDocumentRequest(
      @NotNull String name,
      @NotNull String extension,
      @JsonProperty("link_source") @NotNull String linkSource) {}

  public record EditDocumentRequest(@NotNull String title) {}

  @Builder
  public record StudentDetails(Long id, String name, @JsonProperty("auth_id") String authId) {}
}
