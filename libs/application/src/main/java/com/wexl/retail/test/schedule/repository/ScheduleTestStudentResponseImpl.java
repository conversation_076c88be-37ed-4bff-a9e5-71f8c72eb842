package com.wexl.retail.test.schedule.repository;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.test.schedule.dto.ScheduleTestStudentResponse;
import com.wexl.retail.test.school.dto.TestDefinitionsDto;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleTestStudentResponseImpl implements ScheduleTestStudentResponse {

  private Long scheduleTestId;
  private Long userId;
  private String userName;
  private String firstName;
  private String lastName;
  private Long studentId;
  private Boolean testTaken;
  private Long sectionId;
  private String sectionName;
  private Long examId;
  private Boolean examEvaluated;
  private String instituteName;
  private Float marksScored;
  private Integer totalMarks;
  private Double percentage;
  private Long startTime;
  private Long endTime;
  private String rollNumber;
  private Long classRollNumber;

  @JsonProperty("status")
  private String studentTestStatus;

  private List<TestDefinitionsDto.SectionsResponse> testDefinitionSections;
}
