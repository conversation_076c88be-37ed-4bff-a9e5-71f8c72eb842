package com.wexl.retail.courses.module.repository;

import com.wexl.retail.courses.module.model.CourseModule;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CourseModuleRepository extends JpaRepository<CourseModule, Long> {

  @Query(
      value =
          """
          select * from course_module \
          where course_definition_id = :courseDefId \
          order by seq_num\
          """,
      nativeQuery = true)
  List<CourseModule> getAllModulesAssociatedToCourse(long courseDefId);

  @Query(
      value =
          """
          select (count(seq_num) + 1) as seq_num \
          from course_module where course_definition_id = :courseDefId\
          """,
      nativeQuery = true)
  int getNextSeqNumInModule(long courseDefId);

  @Query(
      value =
          """
                      with moduleCount as (select m.course_definition_id, count (m.id) over (partition by m.course_definition_id) as moduleCount
                                                from course_module m)\s
                                    select coalesce(mc.moduleCount,0) from moduleCount mc\s
                                    where mc.course_definition_id = :courseDefinitionId""",
      nativeQuery = true)
  Integer getModuleCount(long courseDefinitionId);
}
