package com.wexl.retail.courses.enrollment.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CourseEnrollmentPersonRequest {

  @JsonProperty("start_date")
  private long startDate;

  @JsonProperty("end_date")
  private long endDate;

  @NonNull private Long days;

  @JsonProperty("team_id")
  @NonNull
  private Long teamId;

  private List<Long> students;

  @JsonProperty("assignee_mode")
  @NonNull
  private AssigneeMode assigneeMode;

  @JsonProperty("course_category")
  Long courseCategory;
}
