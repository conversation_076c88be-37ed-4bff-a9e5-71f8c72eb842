package com.wexl.retail.student.exam.revision.service;

import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.Question;
import com.wexl.retail.content.model.QuestionResponse;
import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.mlp.model.Mlp;
import com.wexl.retail.mlp.repository.MlpRepository;
import com.wexl.retail.model.Student;
import com.wexl.retail.services.StudentService;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.revision.domain.ExamRevision;
import com.wexl.retail.student.exam.revision.domain.ExamRevisionRequest;
import com.wexl.retail.student.exam.revision.domain.ExamRevisionStatus;
import com.wexl.retail.student.exam.revision.dto.RevisionQuestionsCount;
import com.wexl.retail.student.exam.revision.dto.RevisionQuestionsCountQueryResult;
import com.wexl.retail.student.exam.revision.repository.ExamRevisionRepository;
import java.sql.Timestamp;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ExamRevisionService {

  private final ExamRevisionRepository examRevisionRepository;
  private final MlpRepository mlpRepository;
  private final StudentService studentService;
  private final ContentService contentService;

  public void addQuestionToExamRevision(ExamRevisionRequest examRevisionRequest) {
    Exam exam = examRevisionRequest.getExam();

    List<ExamRevision> optionalExamRevision =
        examRevisionRepository.findByStudentAndQuestionUuid(
            examRevisionRequest.getStudent(), examRevisionRequest.getQuestionUuid());
    if (!optionalExamRevision.isEmpty()) {
      ExamRevision examRevision = optionalExamRevision.getFirst();
      if (Objects.equals(examRevision.getExamRevisionStatus(), ExamRevisionStatus.NOT_COMPLETED)
          && Boolean.TRUE.equals(examRevisionRequest.getIsCorrect())) {
        examRevision.setExamRevisionStatus(ExamRevisionStatus.COMPLETED);
        examRevision.setUpdatedAt(new Timestamp(new Date().getTime()));
        examRevision.setRevisionExam(exam);
        examRevisionRepository.save(examRevision);
      }

      if (Objects.equals(examRevision.getExamRevisionStatus(), ExamRevisionStatus.COMPLETED)
          && Boolean.FALSE.equals(examRevisionRequest.getIsCorrect())) {
        examRevision.setExamRevisionStatus(ExamRevisionStatus.NOT_COMPLETED);
        examRevision.setUpdatedAt(new Timestamp(new Date().getTime()));
        examRevision.setExam(exam);
        examRevisionRepository.save(examRevision);
      }
      return;
    }
    addQuestionToRevision(examRevisionRequest);
  }

  private void addQuestionToRevision(ExamRevisionRequest examRevisionRequest) {
    if (Boolean.FALSE.equals(examRevisionRequest.getIsCorrect())) {
      Optional<Mlp> mlp = mlpRepository.findFirstByExamRef(examRevisionRequest.getExam().getRef());
      examRevisionRepository.save(
          ExamRevision.builder()
              .questionUuid(examRevisionRequest.getQuestionUuid())
              .exam(examRevisionRequest.getExam())
              .student(examRevisionRequest.getStudent())
              .mlp(mlp.orElse(null))
              .examRevisionStatus(ExamRevisionStatus.NOT_COMPLETED)
              .build());
    }
  }

  public QuestionResponse getRevisionQuestions(
      String chapterSlug, String subjectSlug, String bearerToken) {
    Student student = studentService.fetchStudentUsingAuthToken();
    List<String> revisionQuestionUuids =
        examRevisionRepository.findQuestionsByStudentAndChapter(
            student.getId(), chapterSlug, String.valueOf(ExamRevisionStatus.NOT_COMPLETED));
    if (revisionQuestionUuids.isEmpty()) {
      return QuestionResponse.builder().message("No Data Found").build();
    }

    List<Question> questions = new ArrayList<>();

    for (String qUuid : revisionQuestionUuids) {
      questions.add(
          contentService.getQuestionBySubjectSlugAndUuidForStudent(
              bearerToken, QuestionType.MCQ, subjectSlug, qUuid, Boolean.TRUE));
    }

    return QuestionResponse.builder().data(questions).message("Success").status(true).build();
  }

  public List<RevisionQuestionsCount> getRevisionQuestionsCountByChapter(String subjectSlug) {
    Student student = studentService.fetchStudentUsingAuthToken();
    List<RevisionQuestionsCountQueryResult> queryResultList =
        examRevisionRepository.findQuestionsCountByChapter(
            student.getId(), subjectSlug, ExamRevisionStatus.NOT_COMPLETED.toString());

    return queryResultList.stream()
        .map(
            q ->
                RevisionQuestionsCount.builder()
                    .count(q.getCount())
                    .chapterSlug(q.getChapter())
                    .build())
        .toList();
  }
}
