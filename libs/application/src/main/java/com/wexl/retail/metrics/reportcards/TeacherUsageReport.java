package com.wexl.retail.metrics.reportcards;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.handler.AbstractMetricHandler;
import com.wexl.retail.metrics.handler.MetricHandler;
import com.wexl.retail.organization.auth.OrganizationAuthService;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class TeacherUsageReport extends AbstractMetricHandler implements MetricHandler {
  private final OrganizationAuthService organizationAuthService;

  @Override
  public String name() {
    return "teacher-usage-report";
  }

  @Override
  public List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    String fromDate =
        Optional.ofNullable(genericMetricRequest.getInput().get(FROM_DATE))
            .map(Object::toString)
            .orElse(null);
    String toDate =
        Optional.ofNullable(genericMetricRequest.getInput().get(TO_DATE))
            .map(Object::toString)
            .orElse(null);
    return organizationAuthService.getTeacherLoginDetails(org, fromDate, toDate);
  }
}
