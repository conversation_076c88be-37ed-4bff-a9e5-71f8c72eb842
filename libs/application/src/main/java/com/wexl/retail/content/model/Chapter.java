package com.wexl.retail.content.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class Chapter {
  private int id;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("Status")
  private String status;

  private Entity grade;
  private Entity subject;

  @JsonProperty("edu_board")
  private Entity eduBoard;

  private String slug;

  @JsonProperty("Icon")
  private List<Icon> icons;
}
