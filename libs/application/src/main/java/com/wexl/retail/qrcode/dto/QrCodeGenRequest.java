package com.wexl.retail.qrcode.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class QrCodeGenRequest {
  @JsonProperty("board_slug")
  @NonNull
  private String boardSlug;

  @JsonProperty("grade_slug")
  @NonNull
  private String gradeSlug;

  @JsonProperty("org_slug")
  @NonNull
  private String orgSlug;

  @JsonProperty("qrs_required")
  private int qrsRequired;
}
