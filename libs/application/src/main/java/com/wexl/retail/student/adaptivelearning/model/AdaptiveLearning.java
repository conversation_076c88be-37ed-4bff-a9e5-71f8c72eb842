package com.wexl.retail.student.adaptivelearning.model;

import com.wexl.retail.model.Model;
import com.wexl.retail.student.adaptivelearning.dto.AdaptiveLearningDto;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.hibernate.annotations.Type;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "adaptive_learnings")
public class AdaptiveLearning extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private Long studentId;

  private Long examId;

  @Column(columnDefinition = "TEXT")
  private String summary;

  @Column(columnDefinition = "TEXT")
  private String analysis;

  @Column(columnDefinition = "TEXT")
  private String feedback;

  private String failureReason;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb")
  private AdaptiveLearningDto.AdaptiveLearningData content;
}
