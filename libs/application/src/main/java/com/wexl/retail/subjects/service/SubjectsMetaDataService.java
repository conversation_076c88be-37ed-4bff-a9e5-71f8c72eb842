package com.wexl.retail.subjects.service;

import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.content.model.Entity;
import com.wexl.retail.content.model.Grade;
import com.wexl.retail.model.Student;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.subjects.model.*;
import com.wexl.retail.subjects.model.SubjectsDto.BulkRequest;
import com.wexl.retail.subjects.repository.SubjectsMetaDataRepository;
import com.wexl.retail.subjects.repository.SubjectsMetadataStudentsRepository;
import com.wexl.retail.util.StrapiService;
import com.wexl.retail.util.ValidationUtils;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class SubjectsMetaDataService {

  private final SubjectsMetaDataRepository subjectsMetaDataRepository;
  private final SubjectsMetadataStudentsRepository subjectsMetadataStudentsRepository;
  private final ValidationUtils validationUtils;
  private final StrapiService strapiService;
  private final StudentRepository studentRepository;
  private final SectionService sectionService;

  public void saveSubjectsMetaData(SubjectsDto.Request request, String orgSlug) {
    subjectsMetaDataRepository.save(buildRequest(request, orgSlug));
  }

  private SubjectsMetaData buildRequest(SubjectsDto.Request request, String orgSlug) {
    return SubjectsMetaData.builder()
        .categoryEnum(request.subjectsCategoryEnum())
        .status(Boolean.TRUE)
        .wexlSubjectSlug(request.wexlSubjectSlug())
        .gradeSlug(request.gradeSlug())
        .boardSlug(request.boardSlug())
        .SubjectsTypeEnum(request.type())
        .seqNo(request.SeqNo())
        .name(request.name())
        .orgSlug(orgSlug)
        .build();
  }

  public List<SubjectsDto.Response> getSubjectsMetaData(
      String boardSlug,
      String gradeSlug,
      SubjectsCategoryEnum subjectsCategory,
      SubjectsTypeEnum subjectType,
      String orgSlug) {
    List<SubjectsDto.Response> responseList = new ArrayList<>();
    List<SubjectsMetaData> data;
    data =
        subjectsMetaDataRepository.getSubjects(
            boardSlug,
            gradeSlug,
            subjectsCategory == null ? null : subjectsCategory.name(),
            subjectType == null ? null : subjectType.name(),
            orgSlug);
    if (data.isEmpty()) {
      return responseList;
    }

    return buildResponse(data).stream()
        .sorted(
            Comparator.comparing(SubjectsDto.Response::gradeOrder)
                .thenComparingInt(response -> Math.toIntExact(response.SeqNo())))
        .collect(Collectors.toList());
  }

  private List<SubjectsDto.Response> buildResponse(List<SubjectsMetaData> subjectsList) {
    List<SubjectsDto.Response> responseList = new ArrayList<>();
    subjectsList.forEach(subject -> responseList.add(buildStudentResponse(subject)));
    return responseList;
  }

  public void deleteSubjectMetaData(Long subjectId) {
    validateSubjectId(subjectId);
    subjectsMetaDataRepository.deleteById(subjectId);
  }

  public SubjectsMetaData validateSubjectId(Long subjectMetadataId) {
    var subjectMetaData = subjectsMetaDataRepository.findById(subjectMetadataId);
    if (subjectMetaData.isEmpty()) {
      throw new ApiException(
          com.wexl.retail.commons.errorcodes.InternalErrorCodes.INVALID_REQUEST,
          "Invalid SubjectMetaData id" + subjectMetadataId);
    }
    return subjectMetaData.get();
  }

  public void editSubjectsMetaData(SubjectsDto.Request request, Long subjectId) {
    var subjectMetaData = validateSubjectId(subjectId);
    subjectMetaData.setStatus(request.status());
    subjectMetaData.setSubjectsTypeEnum(request.type());
    subjectMetaData.setBoardSlug(request.boardSlug());
    subjectMetaData.setGradeSlug(request.gradeSlug());
    subjectMetaData.setWexlSubjectSlug(request.wexlSubjectSlug());
    subjectMetaData.setCategoryEnum(request.subjectsCategoryEnum());
    subjectMetaData.setSeqNo(request.SeqNo());
    subjectMetaData.setName(request.name());
    subjectsMetaDataRepository.save(subjectMetaData);
  }

  public void assignToStudents(SubjectsDto.StudentsRequest request, Long subjectId) {
    if (request.studentIdsList().isEmpty()) {
      return;
    }
    SubjectsMetaData subjectMetaData = validateSubjectId(subjectId);
    List<SubjectsMetadataStudents> studentsList =
        buildStudentsList(request.studentIdsList(), subjectMetaData);

    subjectMetaData.setSubjectsMetadataStudents(studentsList);
    subjectsMetaDataRepository.save(subjectMetaData);
  }

  private List<SubjectsMetadataStudents> buildStudentsList(
      List<Long> studentIds, SubjectsMetaData subjectMetaData) {
    List<SubjectsMetadataStudents> studentsList = new ArrayList<>();

    studentIds.forEach(
        id -> {
          var isExist =
              subjectsMetadataStudentsRepository.findByStudentIdAndSubjectsMetaData(
                  id, subjectMetaData);
          if (isExist.isEmpty()) {
            SubjectsMetadataStudents student =
                SubjectsMetadataStudents.builder()
                    .studentId(id)
                    .subjectsMetaData(subjectMetaData)
                    .build();
            studentsList.add(student);
          }
        });

    return studentsList;
  }

  public SubjectsDto.StudentsResponse getStudentsAssigned(Long subjectId, String sectionUuid) {
    SubjectsMetaData subjectMetaData = validateSubjectId(subjectId);
    Long sectionId = null;
    if (sectionUuid != null) {
      sectionId = sectionService.findByUuid(sectionUuid).getId();
    }
    var subjectMetaDataStudents =
        subjectsMetadataStudentsRepository.getSubjectMetaDataStudents(
            subjectMetaData.getId(), sectionId);
    return SubjectsDto.StudentsResponse.builder()
        .response(buildStudentResponse(subjectMetaData))
        .students(
            subjectMetaDataStudents.isEmpty() ? null : buildStudentData(subjectMetaDataStudents))
        .build();
  }

  private List<SubjectsDto.Students> buildStudentData(
      List<SubjectsMetadataStudents> metadataStudents) {

    List<Long> studentIds =
        metadataStudents.stream().map(SubjectsMetadataStudents::getStudentId).toList();
    List<Student> students = studentRepository.findAllByIdInAndDeletedAtIsNull(studentIds);

    List<SubjectsDto.Students> studentsList =
        students.stream()
            .map(
                student -> {
                  var userDetails = student.getUserInfo();
                  return SubjectsDto.Students.builder()
                      .id(student.getId())
                      .sectionName(student.getSection().getName())
                      .name(userDetails.getFirstName() + " " + userDetails.getLastName())
                      .rollNumber(student.getRollNumber())
                      .classRollNumber(student.getClassRollNumber())
                      .authUserId(userDetails.getAuthUserId())
                      .build();
                })
            .sorted(
                Comparator.comparing(
                    student ->
                        StringUtils.isNumeric(student.classRollNumber())
                            ? Long.valueOf(student.classRollNumber())
                            : null,
                    Comparator.nullsLast(Comparator.naturalOrder())))
            .toList();

    return studentsList;
  }

  private SubjectsDto.Response buildStudentResponse(SubjectsMetaData subject) {
    var gradeEntity = strapiService.getAllGrades();
    var boardEntity = strapiService.getAllBoards();
    var grade = validationUtils.findGradeBySlug(gradeEntity, subject.getGradeSlug());
    var board = validationUtils.findBoardBySlug(boardEntity, subject.getBoardSlug());
    return SubjectsDto.Response.builder()
        .id(subject.getId())
        .gradeId(grade.getId())
        .name(subject.getName())
        .studentsAssociated(!subject.getSubjectsMetadataStudents().isEmpty())
        .subjectsCategoryEnum(subject.getCategoryEnum())
        .status(subject.getStatus())
        .wexlSubjectSlug(subject.getWexlSubjectSlug())
        .gradeName(grade.getName())
        .gradeSlug(subject.getGradeSlug())
        .gradeOrder(grade.getOrder())
        .boardSlug(subject.getBoardSlug())
        .boardName(board.getAssetName())
        .type(subject.getSubjectsTypeEnum())
        .SeqNo(subject.getSeqNo())
        .build();
  }

  public void deleteStudentSubject(Long subjectMetadataId, String studentAuthId) {
    SubjectsMetaData subjectMetaData = validateSubjectId(subjectMetadataId);
    var studentUser = validationUtils.isValidUser(studentAuthId);
    final List<SubjectsMetadataStudents> byStudentIdAndSubjectsMetaData =
        subjectsMetadataStudentsRepository.findByStudentIdAndSubjectsMetaData(
            studentUser.getStudentInfo().getId(), subjectMetaData);

    if (byStudentIdAndSubjectsMetaData.isEmpty()) {
      return;
    }

    subjectsMetadataStudentsRepository.deleteAll(byStudentIdAndSubjectsMetaData);
  }

  public List<String> associateSubjectsToStudent(String orgSlug, BulkRequest bulkRequest) {
    if (StringUtils.isBlank(bulkRequest.username())) {
      throw new ApiException(
          com.wexl.retail.commons.errorcodes.InternalErrorCodes.INVALID_REQUEST,
          "Invalid Username");
    }

    if (bulkRequest.subjects().isEmpty()) {
      throw new ApiException(
          com.wexl.retail.commons.errorcodes.InternalErrorCodes.INVALID_REQUEST,
          "Invalid Subjects");
    }
    List<String> errorMessages = new ArrayList<>();
    var user = validationUtils.isValidUser(bulkRequest.username());
    final List<Grade> allGrades = strapiService.getAllGrades();
    final List<Entity> allBoards = strapiService.getAllBoards();
    String studentBoard = getStudentBoardSlug(user.getStudentInfo().getBoardId(), allBoards);
    String studentGrade = getStudentGradeSlug(user.getStudentInfo().getClassId(), allGrades);

    List<SubjectsMetadataStudents> students = new ArrayList<>();
    bulkRequest
        .subjects()
        .forEach(
            subject -> {
              if (StringUtils.isBlank(subject)) {
                return;
              }
              var subjectMetaData =
                  subjectsMetaDataRepository.findByOrgSlugAndNameAndGradeSlugAndBoardSlug(
                      orgSlug, subject, studentGrade, studentBoard);
              if (subjectMetaData.isEmpty()) {
                errorMessages.add("Invalid Subject:" + subject);
                return;
              }
              final long studentId = user.getStudentInfo().getId();
              final SubjectsMetaData subjectsMetaData = subjectMetaData.get();
              final List<SubjectsMetadataStudents> byStudentIdAndSubjectsMetaData =
                  subjectsMetadataStudentsRepository.findByStudentIdAndSubjectsMetaData(
                      studentId, subjectsMetaData);
              if (!byStudentIdAndSubjectsMetaData.isEmpty()) {
                return;
              }
              SubjectsMetadataStudents subjectsMetadataStudent =
                  SubjectsMetadataStudents.builder()
                      .studentId(studentId)
                      .subjectsMetaData(subjectsMetaData)
                      .build();
              students.add(subjectsMetadataStudent);
            });

    if (!students.isEmpty()) {
      subjectsMetadataStudentsRepository.saveAll(students);
    }
    return errorMessages;
  }

  private String getStudentBoardSlug(int boardId, List<Entity> allBoards) {
    return allBoards.stream().filter(x -> x.getId() == boardId).findFirst().get().getSlug();
  }

  private String getStudentGradeSlug(int classId, List<Grade> grades) {
    return grades.stream().filter(x -> x.getId() == classId).findFirst().get().getSlug();
  }

  public void mapSubjectMetaData(Student student) {
    var presentSubjectsMetadataStudents =
        subjectsMetadataStudentsRepository.findByStudentId(student.getPrevStudentId());
    if (presentSubjectsMetadataStudents.isEmpty()) {
      return;
    }
    var metadataSubjects =
        presentSubjectsMetadataStudents.stream()
            .map(SubjectsMetadataStudents::getSubjectsMetaData)
            .map(SubjectsMetaData::getWexlSubjectSlug)
            .toList();
    var studentSection = student.getSection();
    var subjectsMetaData =
        subjectsMetaDataRepository.findByOrgSlugAndWexlSubjectSlugInAndGradeSlugAndBoardSlug(
            studentSection.getOrganization(),
            metadataSubjects,
            studentSection.getGradeSlug(),
            studentSection.getBoardSlug());
    subjectsMetaData.forEach(
        smd -> {
          var optionalSubjectsMetadataStudents =
              smd.getSubjectsMetadataStudents().stream()
                  .filter(sms -> sms.getStudentId().equals(student.getId()))
                  .findAny();
          if (optionalSubjectsMetadataStudents.isEmpty()) {
            smd.getSubjectsMetadataStudents()
                .add(
                    SubjectsMetadataStudents.builder()
                        .subjectsMetaData(smd)
                        .studentId(student.getId())
                        .build());
          }
        });
    subjectsMetaDataRepository.saveAll(subjectsMetaData);
  }
}
