package com.wexl.retail.mobile.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.Data;

@Data
@Entity
@Table(name = "country_codes", schema = "public")
public class CountryCode extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String name;

  @Column(name = "code")
  private String code;

  @Column(name = "seq_num")
  private int sequenceNumber;
}
