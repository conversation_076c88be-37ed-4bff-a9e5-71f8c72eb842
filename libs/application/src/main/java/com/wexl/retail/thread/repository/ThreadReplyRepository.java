package com.wexl.retail.thread.repository;

import com.wexl.retail.model.Teacher;
import com.wexl.retail.thread.model.ThreadReply;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ThreadReplyRepository extends JpaRepository<ThreadReply, Long> {
  Optional<ThreadReply> findByIdAndTeacher(Long threadReplyId, Teacher teacher);
}
