package com.wexl.retail.metrics.handler;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class StudentClassroomReport extends AbstractMetricHandler implements MetricHandler {
  @Override
  public String name() {
    return "student-classroom-report";
  }

  @Override
  public List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    Long fromDate =
        Optional.ofNullable(genericMetricRequest.getInput().get(FROM_DATE))
            .map(Long.class::cast)
            .orElse(null);
    Long endDate =
        Optional.ofNullable(genericMetricRequest.getInput().get(TO_DATE))
            .map(Long.class::cast)
            .orElse(null);
    List<String> classroomId =
        (Optional.ofNullable(genericMetricRequest.getInput().get(CLASSROM_ID))
            .map(List.class::cast)
            .orElse(Collections.emptyList()));
    return classroomAttendanceService.getAttendanceDetails(org, fromDate, endDate, classroomId);
  }
}
