package com.wexl.retail.telegram.handler;

import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class HelpHandler implements TelegramBotHandler {
  @Autowired private List<TelegramBotHandler> botHandlers;

  @Override
  public String handleCommand(String[] params) {
    return botHandlers.stream()
        .map(TelegramBotHandler::getHelpText)
        .collect(Collectors.joining("\n"));
  }

  @Override
  public String getCommandName() {
    return "/help";
  }

  @Override
  public String getHelpText() {
    return "/help";
  }
}
