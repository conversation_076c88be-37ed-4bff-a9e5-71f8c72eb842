package com.wexl.retail.pdf.viewer.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CommentsReviewHistoryRequest {
  private long id;
  private long oldId;
  private String modified;
  private String oldModified;
  private String status;
  private long commentId;
  private String reviewedBy;
  private Timestamp dateReviewed;
}
