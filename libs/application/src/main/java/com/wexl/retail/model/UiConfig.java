package com.wexl.retail.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UiConfig {
  private String logo;
  private String theme;
  private String website;
  private String mobileLogo;
  private String mobileAppUrl;
  private String studentScheduleUrl;
  private String studentUrl;
  private String logoutUrl;
  private String instituteName;
  private String storeUrl;
  private Boolean sendSms = Boolean.FALSE;

  @JsonProperty("org_profile")
  private Long orgProfile;

  @JsonProperty("global_profile_id")
  private Long profileId;
}
