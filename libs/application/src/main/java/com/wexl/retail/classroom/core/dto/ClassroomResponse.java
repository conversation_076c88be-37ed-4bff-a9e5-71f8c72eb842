package com.wexl.retail.classroom.core.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.model.Student;
import com.wexl.retail.util.Status;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ClassroomResponse {

  private Long id;

  private String name;

  @JsonProperty("classroom_id")
  private Long classroomId;

  @JsonProperty("teacher_count")
  private int teacherCount;

  @JsonProperty("student_count")
  private int studentCount;

  @JsonProperty("slot_count")
  private int slotCount;

  @JsonProperty("teacher_name")
  private List<String> teacherName;

  @JsonProperty("created_date")
  private Long createdAt;

  @JsonProperty("user_name")
  private String studentUserName;

  private ClassRoomDto.Extensions extensions;

  @JsonProperty("first_name")
  private String studentFirstName;

  @JsonProperty("roll_number")
  private String studentRollNumber;

  @JsonProperty("parent_name")
  private String studentParentName;

  @JsonProperty("mobile_number")
  private String mobileNumber;

  @JsonProperty("grade")
  private String grade;

  @JsonProperty("LastLogin")
  private Date lastLogin;

  @JsonProperty("status")
  private Status status;

  @JsonProperty("section_uuid")
  private UUID sectionUuid;

  @JsonProperty("section_name")
  private String sectionName;

  @JsonProperty("class_id")
  private int classId;

  @JsonProperty("board_id")
  private int boardId;

  @JsonProperty("start_time")
  private LocalDateTime startTime;

  @JsonProperty("org_slug")
  private String orgSlug;

  @JsonProperty("guardian_details")
  private List<GuardianMetaData> guardianDetails;

  @Builder
  @Data
  public static class GuardianMetaData {
    private String firstName;
    private String lastName;
    private String email;
    private String mobileNumber;
    private GuardianRole relationType;
    private Student student;
  }
}
