package com.wexl.retail.organization.admin;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.classroom.core.dto.ClassRoomDto;
import com.wexl.retail.classroom.core.service.ClassroomService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.metrics.reportcards.WeeklyReportService;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.service.NotificationsService;
import com.wexl.retail.services.StudentService;
import com.wexl.retail.student.auth.StudentAuthService;
import jakarta.validation.Valid;
import java.util.Collections;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RequestMapping("/orgs")
@RestController
@Slf4j
@RequiredArgsConstructor
public class StudentController {

  private static final String HARD_DELETE_FAILED = "Failed to delete student";
  @Autowired private StudentAuthService studentAuthService;

  private final NotificationsService notificationsService;
  private final StudentService studentService;
  private final ClassroomService classroomService;
  private final AuthService authService;
  private final UserRoleHelper userRoleHelper;
  private final WeeklyReportService weeklyReportService;

  @IsOrgAdmin
  @PostMapping("/{orgId}/students")
  public StudentResponse createStudent(
      @PathVariable String orgId, @RequestBody @Valid StudentRequest studentRequest) {
    return studentAuthService.createOrgStudent(studentRequest, orgId);
  }

  @GetMapping("/{orgId}/students")
  public List<StudentResponse> getStudents(
      @PathVariable String orgId,
      @RequestParam(required = false, defaultValue = "false") boolean isClassroom,
      @RequestParam(required = false) String boardSlug,
      @RequestParam(required = false) String gradeSlug,
      @RequestParam(required = false) String sectionUuid) {
    User user = authService.getUserDetails();
    if (Boolean.TRUE.equals(userRoleHelper.isManager(user))) {
      Teacher teacher = weeklyReportService.getTeacherByUser(user);
      var childOrgs = teacher.getChildOrgs();
      return studentAuthService.getChildOrgStudentsDetails(childOrgs);
    } else if (Boolean.TRUE.equals(
        userRoleHelper.isOrgAdmin(user)
            || Boolean.TRUE.equals(userRoleHelper.isStudentAdmin(user)))) {
      if (isClassroom) {
        return classroomService.getClassroomStudents(orgId);
      } else if (boardSlug != null || gradeSlug != null || sectionUuid != null) {
        return studentAuthService.getFilteredStudents(orgId, boardSlug, gradeSlug, sectionUuid);
      }
      return studentAuthService.studentsFor(orgId);
    }
    return Collections.emptyList();
  }

  @IsOrgAdmin
  @GetMapping("/{orgId}/all-students")
  public List<StudentResponse> getAllStudents(
      @PathVariable String orgId, @RequestParam(required = false) Long grade) {
    return studentAuthService.allStudents(orgId, grade);
  }

  @IsOrgAdmin
  @GetMapping("/{orgId}/students/{studentId}")
  public ResponseEntity<StudentResponse> getStudent(
      @PathVariable String orgId, @PathVariable String studentId) {
    try {
      return ResponseEntity.ok(studentAuthService.getStudent(orgId, studentId));
    } catch (Exception exception) {
      log.error("Failed to get student information", exception);
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.StudentsGetInfo.Failed");
    }
  }

  @IsOrgAdmin
  @PutMapping("/{orgId}/students/{studentId}")
  public ResponseEntity<StudentResponse> editStudent(
      @PathVariable String orgId,
      @PathVariable String studentId,
      @RequestBody @Valid StudentRequest studentRequest) {
    try {
      return ResponseEntity.ok(studentAuthService.editStudent(orgId, studentId, studentRequest));
    } catch (Exception exception) {
      log.error("Failed to update student information", exception);
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.StudentsUpdateInfo.Failed");
    }
  }

  @IsOrgAdmin
  @DeleteMapping("/{orgId}/students/{studentId}")
  public ResponseEntity<Void> deleteStudent(
      @PathVariable String orgId, @PathVariable String studentId) {
    try {
      studentAuthService.deleteStudent(orgId, studentId);
      return ResponseEntity.ok().build();
    } catch (Exception exception) {
      log.error(HARD_DELETE_FAILED, exception);
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.StudentDelete.Failed");
    }
  }

  @IsOrgAdmin
  @PostMapping("/{orgId}/students/{studentId}:undelete")
  public ResponseEntity<Void> undeleteStudent(
      @PathVariable String orgId, @PathVariable String studentId) {
    try {
      studentAuthService.undeleteStudent(orgId, studentId);
      return ResponseEntity.ok().build();
    } catch (Exception exception) {
      log.error("Failed to undelete student", exception);
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.StudentUndelete.Failed");
    }
  }

  @IsOrgAdmin
  @PostMapping("/{orgId}/students/{studentId}/roll-numbers")
  @ResponseStatus(HttpStatus.ACCEPTED)
  public void updateStudentRollNumber(
      @PathVariable String orgId,
      @PathVariable String studentId,
      @RequestBody @Valid StudentDto.UpdateRollNumberRequest updateRollNumberRequest) {
    studentAuthService.updateRollNumber(orgId, studentId, updateRollNumberRequest.rollNumber());
  }

  @IsOrgAdmin
  @PostMapping("/{orgId}/students/{studentId}/passwords")
  @ResponseStatus(HttpStatus.ACCEPTED)
  public void updateStudentPassword(
      @PathVariable String orgId,
      @PathVariable String studentId,
      @RequestBody @Valid StudentDto.UpdatePasswordRequest updateRollNumberRequest) {
    try {
      studentAuthService.updateStudentPassword(
          orgId, studentId, updateRollNumberRequest.newPassword());
    } catch (Exception exception) {
      log.error("Failed to update student information", exception);
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.StudentsUpdateInfo.Failed");
    }
  }

  @IsStudent
  @GetMapping("/{orgId}/students-notifications")
  public List<NotificationDto.StudentNotificationResponse> getStudentNotifications(
      @PathVariable String orgId,
      @RequestParam(name = "from_date", required = false) Long fromDate,
      @RequestParam(name = "limit", required = false, defaultValue = "100") Long limit) {

    return notificationsService.getStudentNotifications(orgId, fromDate, null, limit);
  }

  @ResponseStatus(HttpStatus.CREATED)
  @IsOrgAdmin
  @PostMapping("/{orgId}/students/{studentAuthId}:promotion")
  public void promoteStudent(
      @PathVariable String orgId,
      @PathVariable String studentAuthId,
      @Valid @RequestBody StudentPromotionRequest studentPromotionRequest) {
    studentAuthService.promoteStudent(orgId, studentAuthId, studentPromotionRequest);
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/{orgId}/students/{studentAuthId}/classrooms")
  public List<ClassRoomDto.ClassRoomResponse> getClassRoomsByStudentAuthId(
      @PathVariable("studentAuthId") String studentId) {
    return studentService.getClassRoomsByStudentAuthId(studentId);
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/{orgSlug}/teachers/{teacherId}/mapped-students")
  public Long studentCount(
      @PathVariable("orgSlug") String orgSlug, @PathVariable("teacherId") String teacherAuthId) {
    return studentService.getStudentCount(orgSlug, teacherAuthId);
  }
}
