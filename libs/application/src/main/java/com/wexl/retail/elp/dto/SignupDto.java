package com.wexl.retail.elp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.model.Gender;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;

public record SignupDto() {

  public record CodeValidateRequest(
      @NotNull String code, @NotNull String mobile, String captchaCode) {}

  public record SignUpRequest(
      @JsonProperty("first_name") String firstName,
      @JsonProperty("last_name") String lastName,
      String email,
      @NotNull String password,
      @NotNull String grade,
      String school,
      @NotNull String mobile,
      Gender gender,
      @JsonProperty("scratch_code") String code,
      @NotNull @JsonProperty("otp_id") Long otpId,
      @JsonProperty("captcha_code") String captchaCode) {}

  public record ScratchCodeGenerationRequest(
      @NotNull @JsonProperty("org_slug") String orgSlug, Integer range) {}

  @Builder
  public record ScratchCodeResponse(
      Long id,
      @JsonProperty("org_slug") String orgSlug,
      String code,
      Long startDate,
      Long expiryDate,
      @JsonProperty("is_used") boolean isUsed) {}

  @Builder
  public record ElpStudentRequest(
      String customerId,
      @JsonProperty("first_name") String firstName,
      @JsonProperty("last_name") String lastName,
      String phone,
      String orgSlug,
      String email,
      String grade,
      String board,
      String env,
      String discountOrg,
      @JsonProperty("android_app_url") String androidAppUrl,
      @JsonProperty("web_app_url") String webAppUrl) {}

  @Builder
  public record ElpStudentResponse(String authUserId, String orgSlug, String orgName) {}
}
