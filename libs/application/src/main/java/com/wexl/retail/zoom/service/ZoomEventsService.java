package com.wexl.retail.zoom.service;

import static com.wexl.retail.zoom.domain.ZoomEventType.MEETING_ENDED_EVENT;
import static com.wexl.retail.zoom.domain.ZoomEventType.MEETING_STARTED_EVENT;
import static com.wexl.retail.zoom.domain.ZoomEventType.PARTICIPANT_JOIN_EVENT;
import static com.wexl.retail.zoom.domain.ZoomEventType.PARTICIPANT_LEFT_EVENT;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.zoom.domain.LcEvent;
import com.wexl.retail.zoom.domain.ParticipantEvent;
import com.wexl.retail.zoom.domain.ZoomEventType;
import com.wexl.retail.zoom.dto.*;
import com.wexl.retail.zoom.repository.LcEventRepository;
import com.wexl.retail.zoom.repository.ParticipantEventRepository;
import java.sql.Date;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.logging.log4j.util.Strings;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ZoomEventsService {

  private final DateTimeUtil dateTimeUtil;
  private final ParticipantEventRepository participantEventRepository;
  private final LcEventRepository lcEventRepository;

  public ParticipantEvent processJoinEvent(JoinEventRequest joinEventRequest) {

    ParticipantEvent event = new ParticipantEvent();
    event.setUuid(joinEventRequest.getPayload().getObject().getUuid());
    event.setExternalEventId(joinEventRequest.getPayload().getObject().getId());
    event.setEventType(ZoomEventType.fromValue(joinEventRequest.getEvent()));
    event.setEventTs(dateTimeUtil.convertEpochToTimestamp(joinEventRequest.getEventTs()));
    event.setAccountId(joinEventRequest.getPayload().getAccountId());
    event.setParticipantId(joinEventRequest.getPayload().getObject().getParticipant().getId());
    event.setUserId(joinEventRequest.getPayload().getObject().getParticipant().getUserId());
    event.setUserName(joinEventRequest.getPayload().getObject().getParticipant().getUserName());
    event.setEmail(joinEventRequest.getPayload().getObject().getParticipant().getEmail());
    event.setCustomerKey(
        Objects.requireNonNullElse(
            joinEventRequest.getPayload().getObject().getParticipant().getCustomerKey(),
            Strings.EMPTY));
    event.setJoinTime(
        dateTimeUtil.convertStringToTimestamp(
            joinEventRequest.getPayload().getObject().getParticipant().getJoinTime()));
    event.setEventTz(joinEventRequest.getPayload().getObject().getTimezone());
    event.setStartTime(
        dateTimeUtil.convertStringToTimestamp(
            joinEventRequest.getPayload().getObject().getStartTime()));
    return participantEventRepository.save(event);
  }

  public ParticipantEvent processLeftEvent(LeftEventRequest leftEventRequest) {

    ParticipantEvent event = new ParticipantEvent();
    event.setUuid(leftEventRequest.getPayload().getObject().getUuid());
    event.setExternalEventId(leftEventRequest.getPayload().getObject().getId());
    event.setEventType(ZoomEventType.fromValue(leftEventRequest.getEvent()));
    event.setEventTs(dateTimeUtil.convertEpochToTimestamp(leftEventRequest.getEventTs()));
    event.setAccountId(leftEventRequest.getPayload().getAccountId());
    event.setParticipantId(leftEventRequest.getPayload().getObject().getParticipant().getId());
    event.setUserId(leftEventRequest.getPayload().getObject().getParticipant().getUserId());
    event.setUserName(leftEventRequest.getPayload().getObject().getParticipant().getUserName());
    event.setEmail(leftEventRequest.getPayload().getObject().getParticipant().getEmail());
    event.setCustomerKey(
        Objects.requireNonNullElse(
            leftEventRequest.getPayload().getObject().getParticipant().getCustomerKey(),
            Strings.EMPTY));
    event.setLeaveTime(
        dateTimeUtil.convertStringToTimestamp(
            leftEventRequest.getPayload().getObject().getParticipant().getLeaveTime()));
    event.setLeaveReason(
        leftEventRequest.getPayload().getObject().getParticipant().getLeaveReason());
    event.setEventTz(leftEventRequest.getPayload().getObject().getTimezone());
    event.setStartTime(
        dateTimeUtil.convertStringToTimestamp(
            leftEventRequest.getPayload().getObject().getStartTime()));
    return participantEventRepository.save(event);
  }

  public LcEvent processMeetingStartEvent(MeetingStartedEventRequest meetingEvent) {

    final var payload = meetingEvent.getPayload();
    LcEvent event =
        processMeetingStartEvents(
            payload.getObject(),
            meetingEvent.getEvent(),
            meetingEvent.getEventTs(),
            payload.getAccountId());

    return lcEventRepository.save(event);
  }

  public Object processMeetingEndEvent(MeetingEndedEventRequest meetingEvent) {
    final var payload = meetingEvent.getPayload();
    LcEvent event =
        processMeetingStartEvents(
            meetingEvent.getPayload().getObject(),
            meetingEvent.getEvent(),
            meetingEvent.getEventTs(),
            payload.getAccountId());
    event.setEndTime(
        dateTimeUtil.convertStringToTimestamp(meetingEvent.getPayload().getObject().getEndTime()));

    return lcEventRepository.save(event);
  }

  public LcEvent processMeetingStartEvents(
      ZoomEventObject eventObject, String eventType, long eventTs, String accountId) {
    LcEvent event = new LcEvent();
    event.setUuid(eventObject.getUuid());
    event.setExternalEventId(eventObject.getId());
    event.setEventType(ZoomEventType.fromValue(eventType));
    event.setEventTs(dateTimeUtil.convertEpochToTimestamp(eventTs));
    event.setAccountId(accountId);
    event.setHostId(eventObject.getHostId());
    event.setEventDuration(eventObject.getDuration());
    event.setEventTopic(eventObject.getTopic());
    event.setStartTime(dateTimeUtil.convertStringToTimestamp(eventObject.getStartTime()));
    event.setEventTz(eventObject.getTimezone());
    return lcEventRepository.save(event);
  }

  public List<ParticipantEventResponse> fetchUserAttendance(
      String externalEventId, String customerKey) {
    var currentDate = new Date(System.currentTimeMillis());
    return participantEventRepository
        .getAttendeeMeetingEvents(customerKey, currentDate, externalEventId)
        .stream()
        .map(
            participantEvent ->
                ParticipantEventResponse.builder()
                    .externalEventId(participantEvent.getExternalEventId())
                    .eventType(participantEvent.getEventType())
                    .customerKey(participantEvent.getCustomerKey())
                    .eventTime(
                        Objects.nonNull(participantEvent.getJoinTime())
                            ? participantEvent.getJoinTime()
                            : participantEvent.getLeaveTime())
                    .meetingStartTime(participantEvent.getStartTime())
                    .email(participantEvent.getEmail())
                    .userName(participantEvent.getUserName())
                    .build())
        .toList();
  }

  @SneakyThrows
  public void processZoomEvents(String zoomEvent) {
    JSONObject jsonObject = new JSONObject(zoomEvent);
    String getEventName = jsonObject.getString("event");

    ObjectMapper objectMapper = new ObjectMapper();
    if (PARTICIPANT_LEFT_EVENT.equals(ZoomEventType.fromValue(getEventName))) {
      processLeftEvent(objectMapper.readValue(zoomEvent, LeftEventRequest.class));
    } else if (PARTICIPANT_JOIN_EVENT.equals(ZoomEventType.fromValue(getEventName))) {
      processJoinEvent(objectMapper.readValue(zoomEvent, JoinEventRequest.class));
    } else if (MEETING_STARTED_EVENT.equals(ZoomEventType.fromValue(getEventName))) {
      processMeetingStartEvent(objectMapper.readValue(zoomEvent, MeetingStartedEventRequest.class));
    } else if (MEETING_ENDED_EVENT.equals(ZoomEventType.fromValue(getEventName))) {
      processMeetingEndEvent(objectMapper.readValue(zoomEvent, MeetingEndedEventRequest.class));
    }
  }
}
