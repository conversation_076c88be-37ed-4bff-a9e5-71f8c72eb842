package com.wexl.retail.test.school.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wexl.retail.model.Model;
import com.wexl.retail.model.User;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Data
@MappedSuperclass
@EqualsAndHashCode(callSuper = true)
@EntityListeners(AuditingEntityListener.class)
public class TestData extends Model {
  @Id
  @GeneratedValue(
      strategy = GenerationType.SEQUENCE,
      generator = "test_definition-sequence-generator")
  @SequenceGenerator(
      name = "test_definition-sequence-generator",
      sequenceName = "test_definitions_seq",
      allocationSize = 1)
  private long id;

  private String gradeSlug;
  private String subjectSlug;
  private String testName;
  private String complexityLevelSlug;
  private String message;
  private Boolean active;
  private Boolean isAutoEnabled;
  private String boardSlug;
  private String boardName;
  private Integer noOfQuestions;
  private String organization;

  @JsonIgnore
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "teacher_id")
  private User teacher;

  @Enumerated(EnumType.STRING)
  private TestType type;
}
