package com.wexl.retail.elp.service;

import com.wexl.retail.speech.SpeechService;
import com.wexl.retail.speech.dto.SpeechEvaluation.SpeechResponse;
import com.wexl.retail.speech.dto.SpeechEvaluation.SpeechTaskResponse;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

@Order(1000)
@Service
public class DefaultSpeechService implements SpeechService {

  @Override
  public SpeechTaskResponse pronunciationAssessment(
      String text, String audioUrl, String reference, Boolean isImpromptuSpeech) {
    return null;
  }

  @Override
  public SpeechResponse pronunciationAssessment(String reference) {
    return null;
  }

  @Override
  public SpeechResponse migrateSpeechTask(String speechRef, long speechTaskId) {
    return null;
  }
}
