package com.wexl.retail.msg91.controller;

import com.wexl.retail.msg91.dto.Msg91Dto;
import com.wexl.retail.msg91.dto.Msg91Dto.Recipient;
import com.wexl.retail.msg91.service.DelegatingEmailService;
import com.wexl.retail.msg91.service.Msg91SmsService;
import com.wexl.retail.msg91.service.TextLocalSmsService;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/msg91")
public class Msg91Controller {

  private final Msg91SmsService msg91SmsService;

  private final TextLocalSmsService textLocalSmsService;

  private final DelegatingEmailService delegatingEmailService;

  @GetMapping("/sms")
  public void sendSms() {
    // This is just for testing.  Will be removed later
    // send a message using Msg91SmsService
    var templateId =
        """
        Dear Parent

        Greetings from DPS Nacharam!

        Please note that Friday 5th April is a holiday on account of Babu Jagjivan Ram Jayanti. Saturday 6th April is a normal working day.

        Regards
        Sr Principal
        """;
    textLocalSmsService.sendBulkMessage(
        templateId,
        Arrays.asList(
            Recipient.builder().orgname("WEXL-internal").name("test").mobiles("**********").build(),
            Recipient.builder()
                .orgname("WEXL-internal")
                .name("got")
                .mobiles("**********")
                .build()));
  }

  // This is just for testing.  Will be removed later
  @PostMapping("/emails/{templateId}")
  public void sendEmail(
      @PathVariable("orgSlug") String orgSlug,
      @PathVariable("templateId") String templateId,
      @RequestBody List<Msg91Dto.EmailTo> emails) {
    delegatingEmailService.get(orgSlug).sendEmail(templateId, emails);
  }

  @PostMapping("/sms/{templateId}")
  public void testAttendanceSms() {
    // This is just for testing.  Will be removed later
    // send a message using Msg91SmsService
    var templateId = "66b0a680d6fc0525c0523082";
    final Recipient recipient =
        Recipient.builder()
            .mobiles("+91**********")
            .name("Keerthana A")
            .teacherName("Bharat A")
            .sectionName("8C")
            .date(new SimpleDateFormat().format(new Date()))
            .orgname("PORPS")
            .build();
    msg91SmsService.sendBulkMessage(templateId, List.of(recipient));
    log.info("Done!");
  }
}
