package com.wexl.retail.term.repository;

import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.model.TermAssessmentCategory;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface TermAssessmentCategoryRepository
    extends JpaRepository<TermAssessmentCategory, Long> {
  List<TermAssessmentCategory> findAllByOrgSlugAndTermAssessmentOrderBySeqNoDesc(
      String orgSlug, TermAssessment termAssessment);

  Optional<TermAssessmentCategory> findAllByOrgSlugAndTermAssessmentAndName(
      String orgSlug, TermAssessment termAssessment, String name);

  Optional<TermAssessmentCategory> findByIdAndTermAssessment(
      Long id, TermAssessment termAssessment);

  List<TermAssessmentCategory> findByIdIn(List<Long> ids);

  List<TermAssessmentCategory> findAllByNameInAndOrgSlug(
      List<String> assessmentCategories, String orgSlug);
}
