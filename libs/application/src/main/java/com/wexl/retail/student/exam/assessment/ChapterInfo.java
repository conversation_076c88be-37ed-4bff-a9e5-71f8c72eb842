package com.wexl.retail.student.exam.assessment;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ChapterInfo {

  @JsonProperty("id")
  private Long chapterId;

  @JsonProperty("Name")
  private String chapterName;

  @JsonProperty("grade")
  private Long gradeId;

  @JsonProperty("subject")
  private Long subjectId;

  @JsonProperty("edu_board")
  private Long eduBoardId;
}
