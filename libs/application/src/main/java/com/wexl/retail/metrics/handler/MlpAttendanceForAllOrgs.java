package com.wexl.retail.metrics.handler;

import static com.wexl.retail.util.Constants.WEXL_INTERNAL;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MlpAttendanceForAllOrgs extends AbstractMetricHandler implements MetricHandler {
  private final OrganizationRepository organizationRepository;

  @Value("${app.excludeOrgsForMlpAttendance}")
  private List<String> excludeOrgsForMlpAttendance;

  @Override
  public String name() {
    return "mlp-attendance-for-all-orgs";
  }

  @Override
  public List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    if (!WEXL_INTERNAL.equals(org)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Invalid Organization");
    }
    List<Organization> childOrgs =
        organizationRepository.findOrgsForMlpAttendance(this.excludeOrgsForMlpAttendance);
    List<String> allOrgSlugs = new ArrayList<>();
    for (var orgSlug : childOrgs) {
      allOrgSlugs.add(orgSlug.getSlug());
    }

    return mlpAttendanceService.getMlpAttendanceAndUserLoginByChildOrg(
        allOrgSlugs, genericMetricRequest.getTimePeriod());
  }
}
