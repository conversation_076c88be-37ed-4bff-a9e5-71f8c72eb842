package com.wexl.retail.metrics.handler;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ElpLeaderBoardResult extends AbstractMetricHandler {
  @Override
  public String name() {
    return "elp-leader-board-result";
  }

  @Override
  protected List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {

    List<Long> taskIds =
        Optional.ofNullable(genericMetricRequest.getInput().get(TASK_IDS))
            .map(List.class::cast)
            .orElse(Collections.emptyList());
    List<String> sections =
        Optional.ofNullable(genericMetricRequest.getInput().get(SECTIONS))
            .map(List.class::cast)
            .orElse(Collections.emptyList());
    return elpService.getLeaderBoardResults(taskIds, sections);
  }
}
