package com.wexl.retail.elp.service;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.dto.MobileNumberLoginDto;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.curriculum.service.CurriculumService;
import com.wexl.retail.elp.dto.SignupDto;
import com.wexl.retail.elp.repository.StudentRegistrationCodeRepository;
import com.wexl.retail.email.EmailService;
import com.wexl.retail.model.*;
import com.wexl.retail.msg91.service.Msg91SmsService;
import com.wexl.retail.organization.admin.StudentRequest;
import com.wexl.retail.organization.auth.OrganizationAuthService;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.otp.OtpResponse;
import com.wexl.retail.otp.OtpService;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.student.auth.StudentAuthService;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.util.ReCaptchaService;
import com.wexl.retail.util.ValidationUtils;
import jakarta.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.IntStream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class StudentSignupService {

  private final ReCaptchaService reCaptchaService;
  private final UserRepository userRepository;
  private final StudentAuthService studentAuthService;
  private final OrganizationRepository organizationRepository;
  private final CurriculumService curriculumService;
  private final Msg91SmsService msg91SmsService;
  private final SectionRepository sectionRepository;
  private final AuthService authService;
  private final OtpService otpService;
  private final StudentRegistrationCodeRepository studentRegistrationCodeRepository;
  private final OrganizationAuthService organizationAuthService;
  private final ValidationUtils validationUtils;
  private final EmailService emailService;
  private final UserService userService;

  @Value("${app.latestAcademicYear}")
  private String latestAcademicYear;

  public void signUpElpStudent(String orgSlug, SignupDto.SignUpRequest signUpRequest) {
    try {
      if (!reCaptchaService.verify(signUpRequest.captchaCode())) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.ReCaptcha.Invalid");
      }
      otpService.validateMobileByOtp(validateMobile(signUpRequest.mobile()), signUpRequest.otpId());
      studentAuthService.createStudent(buildStudentRequest(orgSlug, signUpRequest), orgSlug);
      StudentRegistrationCode registrationCode =
          studentRegistrationCodeRepository
              .findByCodeAndOrgSlugAndExpiryDateAfter(
                  signUpRequest.code(), orgSlug, LocalDateTime.now())
              .orElseThrow(
                  () ->
                      new ApiException(
                          InternalErrorCodes.INVALID_REQUEST, "error.InvalidScratchCode"));
      registrationCode.setIsUsed(Boolean.TRUE);
      studentRegistrationCodeRepository.save(registrationCode);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  private String validateMobile(String mobile) {
    return authService.validateMobileNumber(
        MobileNumberLoginDto.MobileNumberLoginOtpRequest.builder()
            .countryCode("IN")
            .mobileNumber(mobile)
            .build());
  }

  private StudentRequest buildStudentRequest(
      String orgSlug, SignupDto.SignUpRequest signUpRequest) {
    var organization = organizationRepository.findBySlug(orgSlug);
    if (Objects.isNull(organization)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.OrganizationNotFound");
    }
    EduBoard eduBoard =
        curriculumService.getBoardByGrade(organization.getSlug(), signUpRequest.grade());

    List<Section> sections =
        sectionRepository.getSectionsUsingGradeSlugs(
            List.of(signUpRequest.grade()), organization.getSlug());
    if (Objects.isNull(sections) || sections.isEmpty()) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "error.SectionFind.Grade");
    }
    return StudentRequest.builder()
        .academicYearSlug(latestAcademicYear)
        .email(signUpRequest.email())
        .firstName(signUpRequest.firstName())
        .lastName(signUpRequest.lastName())
        .userName(authService.getUniqueUserName(signUpRequest.firstName(), signUpRequest.mobile()))
        .mobileNumber(signUpRequest.mobile())
        .schoolName(organization.getName())
        .password(signUpRequest.password())
        .gender(Objects.nonNull(signUpRequest.gender()) ? signUpRequest.gender() : Gender.MALE)
        .gradeSlug(signUpRequest.grade())
        .boardSlug(eduBoard.getSlug())
        .parentFirstName("")
        .parentLastName("")
        .parentEmail("")
        .parentMobileNumber("")
        .section(String.valueOf(sections.getFirst().getName()))
        .orgSlug(organization.getSlug())
        .build();
  }

  @Transactional
  public OtpResponse validateScratchCodeAndSendOtp(
      String orgSlug, SignupDto.CodeValidateRequest validateRequest) {
    if (!reCaptchaService.verify(validateRequest.captchaCode())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.ReCaptcha.Invalid");
    }
    var organization = organizationRepository.findBySlug(orgSlug);
    if (Objects.nonNull(organization)
        && (Objects.isNull(organization.getSelfSignup())
            || Boolean.FALSE.equals(organization.getSelfSignup()))) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidOrg.SelfSignup");
    }
    if (userRepository.existsByUserName(validateRequest.mobile())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Mobile.Registered");
    }
    validateScratchCodeByOrgSlug(validateRequest.code(), orgSlug);
    var validatedMobileNumber =
        authService.validateMobileNumber(
            MobileNumberLoginDto.MobileNumberLoginOtpRequest.builder()
                .countryCode("IN")
                .mobileNumber(validateRequest.mobile())
                .build());

    return otpService.sendOtpByMsg91(validatedMobileNumber);
  }

  @Transactional
  private void validateScratchCodeByOrgSlug(String code, String orgSlug) {
    try {
      StudentRegistrationCode registrationCode =
          studentRegistrationCodeRepository
              .findByCodeAndOrgSlugAndExpiryDateAfter(code, orgSlug, LocalDateTime.now())
              .orElseThrow(
                  () ->
                      new ApiException(
                          InternalErrorCodes.INVALID_REQUEST, "error.InvalidScratchCode"));
      if (Boolean.TRUE.equals(registrationCode.getIsUsed())) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.ScratchCode.AlreadyUsed");
      }
    } catch (Exception e) {
      log.error("There was an error while validating scratch code " + e.getMessage(), e);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  public String populateRegistrationCodes(
      SignupDto.ScratchCodeGenerationRequest codeGenerationRequest) {
    var organization = organizationRepository.findBySlug(codeGenerationRequest.orgSlug());
    if (!Objects.equals(Boolean.TRUE, organization.getSelfSignup())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidOrg.SelfSignup");
    }
    var range = Objects.isNull(codeGenerationRequest.range()) ? 50 : codeGenerationRequest.range();
    var scratchCodes = saveStudentRegistrationCodes(codeGenerationRequest.orgSlug(), range);
    return scratchCodes.toString();
  }

  private List<String> saveStudentRegistrationCodes(String orgSlug, int range) {
    var scratchCodes = validateScratchCodes(orgSlug, range, new ArrayList<>());
    var studentRegistrationCodes =
        scratchCodes.stream()
            .map(scratchCode -> buildStudentRegistrationCode(orgSlug, scratchCode))
            .toList();
    studentRegistrationCodeRepository.saveAll(studentRegistrationCodes);
    return scratchCodes;
  }

  private List<String> validateScratchCodes(String orgSlug, int range, List<String> scratchCodes) {
    IntStream.range(0, range)
        .forEach(
            r -> {
              String randomNumber = organizationAuthService.getRandomNumber(6);
              scratchCodes.add(randomNumber);
            });
    var studentRegistrationCodes =
        studentRegistrationCodeRepository.getAllByOrgSlugAndCodesAndIsNotUsed(
            scratchCodes, orgSlug);
    List<String> duplicatedCodes =
        studentRegistrationCodes.stream().map(StudentRegistrationCode::getCode).toList();
    if (duplicatedCodes.isEmpty()) {
      return scratchCodes;
    }
    scratchCodes.removeAll(duplicatedCodes);
    validateScratchCodes(orgSlug, duplicatedCodes.size(), scratchCodes);
    return scratchCodes;
  }

  private StudentRegistrationCode buildStudentRegistrationCode(String orgSlug, String scratchCode) {
    var localDateTime = LocalDateTime.now();
    return StudentRegistrationCode.builder()
        .code(scratchCode)
        .startDate(localDateTime)
        .expiryDate(localDateTime.plusYears(1))
        .orgSlug(orgSlug)
        .isUsed(false)
        .build();
  }

  public List<SignupDto.ScratchCodeResponse> getRegistrationCodes(String orgSlug, int limit) {
    Pageable pageable = PageRequest.of(0, limit);
    var studentRegistrationCodes =
        studentRegistrationCodeRepository.findAllByOrgSlugOrderByIdDesc(orgSlug, pageable);
    return studentRegistrationCodes.stream()
        .map(
            registrationCode ->
                SignupDto.ScratchCodeResponse.builder()
                    .id(registrationCode.getId())
                    .code(registrationCode.getCode())
                    .isUsed(registrationCode.getIsUsed())
                    .startDate(DateTimeUtil.convertIso8601ToEpoch(registrationCode.getStartDate()))
                    .expiryDate(
                        DateTimeUtil.convertIso8601ToEpoch(registrationCode.getExpiryDate()))
                    .orgSlug(orgSlug)
                    .build())
        .toList();
  }

  public SignupDto.ElpStudentResponse storeStudentElpSignUp(SignupDto.ElpStudentRequest elpSignUp) {
    String validOrgSlug;
    validOrgSlug = validateDiscountOrg(elpSignUp);
    Organization organization;
    Student student;
    StudentRequest studentRequest;
    User user;
    organization = validationUtils.isOrgValid(validOrgSlug);
    studentRequest = buildStudentRequest(elpSignUp, organization);
    student = studentAuthService.createStudent(studentRequest, organization.getSlug());
    user = student.getUserInfo();
    emailService.sendCommerceStudentSignupEmail(
        userService.getNameByUserInfo(user),
        user.getEmail(),
        studentRequest.getPassword(),
        user.getUserName(),
        elpSignUp.androidAppUrl(),
        elpSignUp.webAppUrl());
    return buildElpStudentResponse(organization, user.getAuthUserId());
  }

  private SignupDto.ElpStudentResponse buildElpStudentResponse(
      Organization org, String authUserId) {

    return SignupDto.ElpStudentResponse.builder()
        .orgName(org.getName())
        .orgSlug(org.getSlug())
        .authUserId(authUserId)
        .build();
  }

  private String validateDiscountOrg(SignupDto.ElpStudentRequest elpSignUp) {
    var discountOrg = organizationRepository.findBySlug(elpSignUp.discountOrg().toLowerCase());
    return discountOrg == null ? elpSignUp.orgSlug() : elpSignUp.discountOrg();
  }

  private StudentRequest buildStudentRequest(
      SignupDto.ElpStudentRequest signUpRequest, Organization org) {

    return StudentRequest.builder()
        .academicYearSlug(latestAcademicYear)
        .email(signUpRequest.email())
        .firstName(signUpRequest.firstName())
        .lastName(signUpRequest.lastName())
        .userName(authService.getUniqueUserName(signUpRequest.firstName(), signUpRequest.phone()))
        .mobileNumber(signUpRequest.phone())
        .schoolName(org.getName())
        .password("password@123")
        .gender(Gender.MALE)
        .gradeSlug(signUpRequest.grade())
        .boardSlug(signUpRequest.board())
        .parentFirstName("")
        .parentLastName("")
        .parentEmail("")
        .parentMobileNumber("")
        .section(getSectionByGrade(org, signUpRequest.grade()))
        .orgSlug(org.getSlug())
        .build();
  }

  private String getSectionByGrade(Organization org, String grade) {

    var sections = sectionRepository.getSectionsUsingGradeSlugs(List.of(grade), org.getSlug());
    if (!sections.isEmpty()) {
      return sections.get(0).getName();
    }
    throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.SectionNotFound");
  }
}
