package com.wexl.retail.test.school.controller;

import com.wexl.retail.test.school.service.TestDefinitionMigrationService;
import java.io.IOException;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/public/migration/test-definitions")
public class TestDefinitionMigrationController {
  private final TestDefinitionMigrationService testDefinitionMigrationService;

  @PostMapping
  public void publishAllTestDefinitions() throws IOException {
    // Logic to publish all test definitions
    String orgSlug = "wexl-internal";
    // for each and every test definition in the org which is of TestType MOCK_TEST,
    // run TestDefinitionService refreshTestDefinitionById
    String bearerToken =
        "eyJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YPzmd87jrmPThQko_Ua-zzEsCOIantN961cPgrPVGgc";
    testDefinitionMigrationService.migrateTestDefinitions(orgSlug, bearerToken);
  }
}
