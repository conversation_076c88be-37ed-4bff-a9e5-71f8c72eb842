package com.wexl.retail.metrics.handler;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.test.schedule.service.ScheduleTestReportService;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ScheduledTestsInstitute extends AbstractMetricHandler implements MetricHandler {

  public final ScheduleTestReportService scheduleTestReportService;

  @Override
  public String name() {
    return "scheduled-test-institute";
  }

  @Override
  protected List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {

    List<String> orgList =
        Optional.ofNullable(genericMetricRequest.getInput().get(ORG_KEY))
            .map(List.class::cast)
            .orElse(Collections.emptyList());

    List<String> subject =
        Optional.ofNullable(genericMetricRequest.getInput().get(SUBJECT))
            .map(List.class::cast)
            .orElse(Collections.emptyList());
    List<String> gradeList =
        Optional.ofNullable(genericMetricRequest.getInput().get(GRADE))
            .map(List.class::cast)
            .orElse(Collections.emptyList());

    if (orgList.isEmpty()) {
      return scheduleTestReportService.getScheduledTestsByInstitute(
          Collections.singletonList(org), subject, gradeList, genericMetricRequest.getTimePeriod());
    }
    return scheduleTestReportService.getScheduledTestsByInstitute(
        orgList, subject, gradeList, genericMetricRequest.getTimePeriod());
  }
}
