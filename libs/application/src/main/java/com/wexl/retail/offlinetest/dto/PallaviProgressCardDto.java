package com.wexl.retail.offlinetest.dto;

import java.util.List;
import lombok.Builder;

public record PallaviProgressCardDto() {

  @Builder
  public record Response(Header header, Body body) {}

  @Builder
  public record Header(String academicSection, String schoolName) {}

  @Builder
  public record Body(
      String rollNumber,
      String name,
      String motherName,
      String dateOfBirth,
      String fatherName,
      String orgSlug,
      String classAndSection,
      List<FirstTable> firstTable,
      List<FirstTable> secondTable,
      List<FirstTable> thirdTable,
      Attendance attendance,
      String promotedClass,
      String place,
      String date,
      String gradingScale,
      String gradeSlug) {}

  @Builder
  public record Attendance(
      Long workingDays, Long daysPresent, Double attendancePercentage, String remarks) {}

  @Builder
  public record FirstTable(
      Long sno,
      String subjectName,
      String pa1,
      String pa2,
      String half,
      String term1Marks,
      String term1Grade,
      String pa3,
      String pa4,
      String finalMarks,
      String term2Marks,
      String term2Grade,
      String totalMarks,
      String overallMarks,
      String overallGrade) {}
}
