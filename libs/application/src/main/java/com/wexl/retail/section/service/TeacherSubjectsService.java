package com.wexl.retail.section.service;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.content.model.Entity;
import com.wexl.retail.content.model.Grade;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.domain.TeacherSubjects;
import com.wexl.retail.section.dto.response.TeacherCurriculumResponse;
import com.wexl.retail.section.dto.response.TeacherSubjectsResponse;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.section.repository.TeacherSectionRepository;
import com.wexl.retail.section.repository.TeacherSubjectsRepository;
import com.wexl.retail.teacher.profile.TeacherProfileService;
import com.wexl.retail.util.StrapiService;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class TeacherSubjectsService {

  private static final String TEACHER_NOT_FOUND = "teacher not found";
  private static final String UNKNOWN = "unknown";
  private final TeacherSubjectsRepository teacherSubjectsRepository;
  private final TeacherProfileService teacherService;
  private final AuthService authService;
  private final TeacherSectionRepository teacherSectionRepository;
  private final SectionService sectionService;
  private final StrapiService strapiService;
  private final TeacherSectionService teacherSectionService;
  private final SectionRepository sectionRepository;
  private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;

  @Value("${app.sql.getTeacherCurriculum}")
  private String getTeacherCurriculum;

  @Value("${app.subjectsToIgnore:science,english}")
  private List<String> subjectsToIgnoreCaseCheck;

  public void save(
      String orgId,
      String boardSlug,
      String authUserId,
      String sectionUuid,
      List<String> subjectSlug,
      Boolean classTeacher) {

    final Optional<User> user = Optional.ofNullable(authService.getUserByAuthUserId(authUserId));

    if (user.isEmpty() || !orgId.equals(user.get().getOrganization())) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "error.TeacherNotFound");
    }

    if (boardSlug == null || subjectSlug == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.BoardOrSubject.Null");
    }

    final var teacher = teacherService.findByUserInfo(user.get());
    final var section = sectionService.findByUuid(sectionUuid);

    if (!teacherSectionService.isSectionMappedToTeacher(teacher, section)) {
      sectionService.addTeacherToSectionById(teacher.getId(), sectionUuid);
    }

    if (Boolean.TRUE.equals(classTeacher)) {
      validateClassTeacher(section, teacher);
      section.setClassTeacher(teacher);
      sectionRepository.save(section);
    }

    /*
       This check should skip slugs which were created before case validation was added.
    */
    for (var subject : subjectSlug)
      if (!subjectsToIgnoreCaseCheck.contains(subject)) {
        subject = subject.trim().toLowerCase();
        boardSlug = boardSlug.trim().toLowerCase();

        if (isSubjectPresentForTeacher(teacher, section, subject, boardSlug)) {
          throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.SubjectAlreadyMapped");
        } else {
          teacherSubjectsRepository.save(
              TeacherSubjects.builder()
                  .board(boardSlug)
                  .teacher(teacher)
                  .section(section)
                  .subject(subject)
                  .build());
        }
      }
  }

  private void validateClassTeacher(Section section, Teacher teacher) {
    var classTeacher = section.getClassTeacher();
    if (classTeacher != null && classTeacher.getId() != teacher.getId()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.classTeacherSection");
    }
    var newClassTeacher =
        teacher.getSections().stream()
            .filter(x -> x.getClassTeacher() != null && x.getClassTeacher().equals(teacher))
            .findAny();
    if (newClassTeacher.isPresent()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.classTeacherSection");
    }
  }

  public List<TeacherSubjectsResponse> findByTeacher(String authUserId) {
    User user = authService.getUserByAuthUserId(authUserId);
    List<TeacherSubjects> teacherSubjects = teacherSubjectsRepository.findByTeacher(authUserId);
    if (teacherSubjects.isEmpty()) {
      return Collections.emptyList();
    }
    return transformTeacherSubjectResponse(teacherSubjects, user);
  }

  private List<TeacherSubjectsResponse> transformTeacherSubjectResponse(
      List<TeacherSubjects> teacherSubjects, User user) {

    try {
      final var eduBoardsMapBySlug = transformStrapiEntityToMapBySlug(strapiService.getAllBoards());
      final var gradesMapBySlug = transformGradeEntityToMapById(strapiService.getAllGrades());
      final var subjectsMapBySlug =
          transformStrapiEntityToMapBySlug(strapiService.getAllSubjects());
      List<TeacherSubjectsResponse> teacherSubjectsResponses = new ArrayList<>();
      teacherSubjects.forEach(
          teacherSubject -> {
            var isClassTeacher =
                teacherSubject.getSection().getClassTeacher() == null
                    ? Boolean.FALSE
                    : teacherSubject.getSection().getClassTeacher() == user.getTeacherInfo();
            TeacherSubjectsResponse teacherSubjectsResponse = new TeacherSubjectsResponse();
            teacherSubjectsResponse.setSubjectSlug(teacherSubject.getSubject());
            teacherSubjectsResponse.setSubjectName(
                Objects.isNull(subjectsMapBySlug.get(teacherSubject.getSubject()))
                    ? UNKNOWN
                    : subjectsMapBySlug.get(teacherSubject.getSubject()).getName());
            teacherSubjectsResponse.setBoardSlug(teacherSubject.getBoard());
            teacherSubjectsResponse.setBoardName(
                Objects.isNull(eduBoardsMapBySlug.get(teacherSubject.getBoard()))
                    ? UNKNOWN
                    : eduBoardsMapBySlug.get(teacherSubject.getBoard()).getAssetName());
            teacherSubjectsResponse.setGradeName(
                Objects.isNull(gradesMapBySlug.get(teacherSubject.getSection().getGradeId()))
                    ? UNKNOWN
                    : gradesMapBySlug.get(teacherSubject.getSection().getGradeId()).getName());
            teacherSubjectsResponse.setSectionName(teacherSubject.getSection().getName());
            teacherSubjectsResponse.setSectionUuid(teacherSubject.getSection().getUuid());
            teacherSubjectsResponse.setIsClassTeacher(isClassTeacher);

            teacherSubjectsResponses.add(teacherSubjectsResponse);
          });
      return teacherSubjectsResponses;
    } catch (Exception ex) {
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.Metadata.Fetch", ex);
    }
  }

  public void deleteTeacherSectionSubject(
      String orgId, String boardSlug, String authUserId, String sectionUuid, String subjectSlug) {

    final Optional<User> user = Optional.ofNullable(authService.getUserByAuthUserId(authUserId));

    if (user.isEmpty() || !orgId.equals(user.get().getOrganization())) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "error.TeacherNotFound");
    }
    var teacher = teacherService.findByUserInfo(user.get());
    final var section = sectionService.findByUuid(sectionUuid);
    teacherSubjectsRepository.deleteByTeacherSectionSubject(
        boardSlug, teacher.getId(), section.getId(), subjectSlug);
    deleteFromTeacherSections(teacher, section);
  }

  private void deleteFromTeacherSections(Teacher teacher, Section section) {
    var teacherSubjects =
        teacherSubjectsRepository.findByTeacher(teacher.getUserInfo().getAuthUserId());
    var teacherSubjectSection =
        teacherSubjects.stream()
            .map(TeacherSubjects::getSection)
            .map(Section::getId)
            .distinct()
            .toList();
    var isSectionMappedToTeacher =
        teacherSubjectSection.stream()
            .filter(teacherSubSection -> teacherSubSection.equals(section.getId()))
            .toList();
    if (isSectionMappedToTeacher.isEmpty()) {
      section.setClassTeacher(null);
      sectionRepository.save(section);
    }

    var teacherSections =
        teacherSectionRepository.getTeacherSectionsByTeacherAndDeletedAtIsNull(teacher.getId());
    var filterSections =
        teacherSections.stream()
            .filter(
                teacherSection ->
                    !teacherSubjectSection.contains(teacherSection.getSection().getId()))
            .toList();
    if (!filterSections.isEmpty()) {
      teacherSectionRepository.deleteAll(filterSections);
    }
  }

  private boolean isSubjectPresentForTeacher(
      Teacher teacher, Section section, String subjectSlug, String boardSlug) {
    return teacherSubjectsRepository
        .findFirstByTeacherAndSectionAndSubjectAndBoard(teacher, section, subjectSlug, boardSlug)
        .isPresent();
  }

  public List<TeacherCurriculumResponse> getTeacherSubjects(String authUserId) {

    User user = authService.getUserByAuthUserId(authUserId);
    final var teacher = user.getTeacherInfo();

    MapSqlParameterSource parameterSource = new MapSqlParameterSource();
    parameterSource.addValue("teacherId", teacher.getId());

    return namedParameterJdbcTemplate.query(
        getTeacherCurriculum,
        parameterSource,
        new BeanPropertyRowMapper<>(TeacherCurriculumResponse.class));
  }

  public Map<String, Entity> transformStrapiEntityToMapBySlug(final List<Entity> strapiEntities) {
    return strapiEntities.parallelStream()
        .collect(
            HashMap::new,
            (map, strapiEntity) -> map.put(strapiEntity.getSlug().toLowerCase(), strapiEntity),
            Map::putAll);
  }

  public Map<Integer, Entity> transformStrapiEntityToMapById(final List<Entity> strapiEntities) {
    return strapiEntities.parallelStream()
        .collect(
            HashMap::new,
            (map, strapiEntity) -> map.put(strapiEntity.getId(), strapiEntity),
            Map::putAll);
  }

  public Map<Integer, Grade> transformGradeEntityToMapById(final List<Grade> strapiEntities) {
    return strapiEntities.parallelStream()
        .collect(
            HashMap::new,
            (map, strapiEntity) -> map.put(strapiEntity.getId(), strapiEntity),
            Map::putAll);
  }
}
