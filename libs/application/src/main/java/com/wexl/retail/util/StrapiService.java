package com.wexl.retail.util;

import static com.wexl.retail.util.Constants.WEXL_INTERNAL;
import static java.util.Objects.isNull;
import static java.util.Objects.requireNonNull;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.Subjects;
import com.wexl.retail.content.model.Chapter;
import com.wexl.retail.content.model.ChapterResponse;
import com.wexl.retail.content.model.Entity;
import com.wexl.retail.content.model.Grade;
import com.wexl.retail.content.model.Icon;
import com.wexl.retail.content.model.SubTopic;
import com.wexl.retail.content.model.SubTopicResponse;
import com.wexl.retail.content.model.ZoomConfig;
import com.wexl.retail.courses.step.dto.AssetResponse;
import com.wexl.retail.generic.SqlQueryResponse;
import com.wexl.retail.model.Organization;
import com.wexl.retail.student.goalplan.SubjectResponse;
import com.wexl.retail.util.dto.Board;
import com.wexl.retail.util.dto.SubjectDetailResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service
@RequiredArgsConstructor
public class StrapiService {

  private static final String DOES_NOT_EXIST = "] doesn't exist.  Ignoring silently";

  @Value("${app.contentToken}")
  private String contentBearerToken;

  @Value("${urls.content}")
  private String url;

  @Value("${urls.content}")
  private String contentServiceUrl;

  private final ContentService contentService;
  private final AuthService authService;
  private final SqlQueryStrapiUtil sqlQueryStrapiUtil;
  private final RestTemplate restTemplate;

  private static final String ERROR_STRAPI_SERVICE = "error.Strapi.Service";
  private static final String ERROR_FIND_BOARD = "error.EduBoardFind.Board";

  public List<Entity> getAllSubjects() {
    var subjectDetailResponses = contentService.getAllSubjects();
    return subjectDetailResponses.stream().map(this::buildEntityBySubjectDetails).toList();
  }

  private Entity buildEntityBySubjectDetails(SubjectDetailResponse subject) {
    return Entity.builder()
        .id(Math.toIntExact(subject.getSubjectId()))
        .name(subject.getSubjectName())
        .slug(subject.getSubjectSlug())
        .status(subject.getStatus())
        .icons(getIcons(subject))
        .build();
  }

  @NotNull
  private static List<Icon> getIcons(SubjectDetailResponse subject) {
    if (subject.getIconUrl() == null) {
      return new ArrayList<>();
    }
    return List.of(Icon.builder().url(subject.getIconUrl()).build());
  }

  public List<Map<String, Object>> getSubTopic() {
    return new ArrayList<>();
  }

  public SubTopic getSubTopicById(long subTopicId) {
    try {
      SubTopicResponse response =
          contentService.getSubTopicById(WEXL_INTERNAL, subTopicId, contentBearerToken);
      return buildSubTopicResponse(response);

    } catch (Exception e) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.SubTopicNotFound" + e.getMessage(), e);
    }
  }

  private SubTopic buildSubTopicResponse(SubTopicResponse response) {
    return SubTopic.builder()
        .id(response.getId())
        .name(response.getName())
        .premium(response.getPremium())
        .slug(response.getSlug())
        .build();
  }

  public SqlQueryResponse getSqlQueryUsingKey(String key) {
    final String sqlQuery = sqlQueryStrapiUtil.getSqlQuery(key);
    return SqlQueryResponse.builder().key(key).query(sqlQuery).status(true).build();
  }

  public void validateSlug(String slug) {
    var slugRegex = "^[a-z][a-z0-9]+(-[a-z]*+)*$";
    if (slug == null || slug.isEmpty() || slug.length() > 100 || !slug.matches(slugRegex)) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.InvalidSlug", new String[] {slug});
    }
  }

  public Grade getGradeById(int gradeId) {
    final Grade response = contentService.getGradeById(gradeId);
    if (Objects.nonNull(response)) {
      return response;
    }
    throw new ApiException(
        InternalErrorCodes.INVALID_REQUEST,
        "error.GradeGet.Id",
        new String[] {Integer.toString(gradeId)});
  }

  public Entity getEduBoardById(long eduBoardId) {
    try {
      String endPoint = "%sorgs/%s/boards?id=%s".formatted(url, WEXL_INTERNAL, eduBoardId);
      ResponseEntity<List<com.wexl.retail.util.dto.Board>> responseEntity =
          restTemplate.exchange(
              endPoint,
              HttpMethod.GET,
              getRequestEntity(contentBearerToken),
              new ParameterizedTypeReference<>() {});
      validateResponseEntity(responseEntity);
      return buildEntity(requireNonNull(responseEntity.getBody()).getFirst());
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.EduBoardFind.EduBoardId");
    }
  }

  private void validateResponseEntity(ResponseEntity<List<Board>> responseEntity) {
    if (!responseEntity.getStatusCode().is2xxSuccessful()
        || requireNonNull(responseEntity.getBody()).isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, ERROR_FIND_BOARD);
    }
  }

  private Entity buildEntity(Board board) {
    return Entity.builder()
        .id((int) board.getId())
        .name(board.getName())
        .slug(board.getSlug())
        .status(String.valueOf(board.isStatus()))
        .build();
  }

  public Entity getEduBoardBySlug(String boardSlug) {
    try {
      String endPoint = "%sorgs/%s/boards?slug=%s".formatted(url, WEXL_INTERNAL, boardSlug);
      ResponseEntity<List<com.wexl.retail.util.dto.Board>> responseEntity =
          restTemplate.exchange(
              endPoint,
              HttpMethod.GET,
              getRequestEntity(contentBearerToken),
              new ParameterizedTypeReference<>() {});
      validateResponseEntity(responseEntity);
      return buildEntity(requireNonNull(responseEntity.getBody()).getFirst());
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, ERROR_FIND_BOARD);
    }
  }

  @SneakyThrows
  public Entity getChapterBySlug(String chapterSlug) {
    if (chapterSlug == null) {
      return null;
    }
    ChapterResponse chapter = contentService.getChapterBySlug(chapterSlug);
    if (isNull(chapter)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidChapter");
    }
    return Entity.builder()
        .id(Math.toIntExact(chapter.getId()))
        .name(chapter.getName())
        .slug(chapter.getChapterSlug())
        .build();
  }

  public Chapter getChapterById(long chapterId) {
    ChapterResponse chapter = contentService.getChapterById(chapterId);
    if (Objects.nonNull(chapter)) {
      return Chapter.builder()
          .id(Math.toIntExact(chapter.getId()))
          .name(chapter.getName())
          .slug(chapter.getChapterSlug())
          .subject(getSubjectBySlug(chapter.getSubjectSlug()))
          .grade(getGradeDetails(chapter.getGradeId()))
          .eduBoard(getEduBoardById(chapter.getEduboardId()))
          .build();
    } else {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.CannotGetChapter",
          new String[] {Long.toString(chapterId)});
    }
  }

  public Entity getGradeDetails(int id) {
    Grade grade = getGradeById(id);
    if (grade == null) {
      return null;
    }
    return Entity.builder()
        .id(grade.getId())
        .slug(grade.getSlug())
        .name(grade.getName())
        .status(grade.getStatus())
        .build();
  }

  public Entity getSubjectBySlug(String subjectSlug) {
    String endPoint = "%s/subjects/%s".formatted(url, subjectSlug);
    ResponseEntity<Subjects> responseEntity =
        restTemplate.exchange(
            endPoint, HttpMethod.GET, getRequestEntity(contentBearerToken), Subjects.class);

    if (HttpStatus.OK.equals(responseEntity.getStatusCode())) {
      Subjects subjectResponse = responseEntity.getBody();
      assert subjectResponse != null;
      return Entity.builder()
          .id(Math.toIntExact(subjectResponse.getSubjectId()))
          .name(subjectResponse.getSubjectName())
          .slug(subjectResponse.getSubjectSlug())
          .build();
    } else {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "Invalid SubjectSlug", new String[] {subjectSlug});
    }
  }

  public Entity getSubjectById(long subjectId) {
    SubjectResponse subject = contentService.getSubjectBySubjectId(subjectId);
    if (subject == null) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.SubjectGet.Id",
          new String[] {Long.toString(subjectId)});
    }
    return Entity.builder()
        .id(Math.toIntExact(subject.getId()))
        .name(subject.getName())
        .slug(subject.getSlug())
        .build();
  }

  public List<Entity> getAllBoards() {
    String endpoint = "%sorgs/wexl-internal/boards?showReferenceBoards=%s".formatted(url, false);
    try {
      ResponseEntity<List<Entity>> response =
          restTemplate.exchange(
              endpoint,
              HttpMethod.GET,
              getRequestEntity(contentBearerToken),
              new ParameterizedTypeReference<>() {});
      if (HttpStatus.OK.equals(response.getStatusCode())) {
        return response.getBody();
      } else {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, ERROR_FIND_BOARD);
      }
    } catch (Exception exception) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, exception.getMessage(), exception);
    }
  }

  private HttpEntity<String> getRequestEntity(String bearerToken) {
    var headers = new org.springframework.http.HttpHeaders();
    headers.add(HttpHeaders.AUTHORIZATION, bearerToken);
    return new HttpEntity<>(null, headers);
  }

  public List<Grade> getAllGrades() {
    return fetchAllGradesFromContentService();
  }

  public ZoomConfig[] getZoomConfigByMeetingId() {
    return new ZoomConfig[] {};
  }

  public Entity getAssetBySlug(String assetSlug) {
    if (assetSlug == null) {
      return null;
    }
    AssetResponse assetResponse = contentService.getAssetBySlug(assetSlug);
    if (assetResponse == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, ERROR_STRAPI_SERVICE);
    }
    return Entity.builder()
        .id(Math.toIntExact(assetResponse.getId()))
        .assetName(assetResponse.getName())
        .status(String.valueOf(assetResponse.getStatus()))
        .slug(assetResponse.getSlug())
        .build();
  }

  @SneakyThrows
  public Entity getSubTopicBySlug(String subTopicSlug) {
    try {
      var orgSlug = authService.getUserDetails().getOrganization();
      SubTopicResponse subTopicDetails =
          contentService.getSubTopicBySlug(orgSlug, subTopicSlug, contentBearerToken);
      if (subTopicDetails == null) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidSubTopic");
      }
      return Entity.builder()
          .id(subTopicDetails.getId())
          .slug(subTopicDetails.getSlug())
          .status(String.valueOf(subTopicDetails.getStatus()))
          .name(subTopicDetails.getName())
          .build();
    } catch (Exception e) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.SubTopicNotFound" + e.getMessage(), e);
    }
  }

  public String getSubTopicNameBySlug(String subTopicSlug) {
    if (ObjectUtils.isEmpty(subTopicSlug)) {
      return "";
    }
    try {
      Entity result = getSubTopicBySlug(subTopicSlug);
      return result == null ? "" : result.getName();
    } catch (Exception ex) {
      log.debug("Looks like the subtopic [" + subTopicSlug + DOES_NOT_EXIST, ex);
    }
    return "";
  }

  public String getSubjectNameBySlug(String subjectSlug) {
    if (ObjectUtils.isEmpty(subjectSlug)) {
      return "";
    }
    try {
      Entity result = getSubjectBySlug(subjectSlug);
      return result == null ? "" : result.getName();
    } catch (Exception ex) {
      log.debug("Looks like the subject [" + subjectSlug + DOES_NOT_EXIST, ex);
    }
    return "";
  }

  public Map<String, Entity> transformStrapiEntityToMapBySlug(final List<Entity> strapiEntities) {
    return strapiEntities.parallelStream()
        .collect(
            HashMap::new,
            (map, strapiEntity) -> map.put(strapiEntity.getSlug(), strapiEntity),
            Map::putAll);
  }

  public String getBoardUsingChapter(String chapterSlug) {
    ChapterResponse chapter = contentService.getChapterBySlug(chapterSlug);
    if (chapter == null) {
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.ChapterNotFound");
    }
    return chapter.getEduboardSlug();
  }

  private List<Grade> fetchAllGradesFromContentService() {
    String endPoint = "%s/orgs/%s/grades".formatted(contentServiceUrl, WEXL_INTERNAL);
    try {
      return restTemplate
          .exchange(
              endPoint,
              HttpMethod.GET,
              getRequestEntity(contentBearerToken),
              new ParameterizedTypeReference<List<Grade>>() {})
          .getBody();

    } catch (Exception e) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "Unable to fetch grades" + e.getMessage());
    }
  }

  public Map<String, Grade> transformGradeEntityToMapBySlug(final List<Grade> strapiEntities) {
    return strapiEntities.parallelStream()
        .collect(
            HashMap::new,
            (map, strapiEntity) -> map.put(strapiEntity.getSlug(), strapiEntity),
            Map::putAll);
  }

  public List<Organization> getAllOrganizations() {
    List<Organization> orgList = contentService.getAllOrganizations();
    if (!orgList.isEmpty() && Boolean.TRUE.equals(Objects.nonNull(orgList.getFirst()))) {
      return orgList;
    }
    throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidOrganization");
  }

  public Organization getOrganizationBySlug(String slug) {
    validateSlug(slug);
    try {
      var organization = contentService.getOrganizationBySlug(slug);
      if (organization == null) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.OrganizationNotFound");
      }
      return organization;
    } catch (Exception exp) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.OrganizationNotFound", exp);
    }
  }
}
