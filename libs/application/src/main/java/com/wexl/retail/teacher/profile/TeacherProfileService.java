package com.wexl.retail.teacher.profile;

import com.wexl.retail.idp.UserIdpService;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.AddressRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.util.StrapiService;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class TeacherProfileService {

  private final StrapiService strapiService;
  private final UserRepository profileRepository;
  private final AddressRepository addressRepository;
  private final TeacherRepository teacherRepository;
  private final TeacherProfileTransformer teacherProfileTransformer;
  private final UserIdpService userIdpService;

  public TeacherProfileResponse updateProfile(
      TeacherProfileRequest teacherProfileRequest, User teacherUserDetails) {
    teacherUserDetails = profileRepository.getById(teacherUserDetails.getId());
    var teacherDetails = teacherUserDetails.getTeacherInfo();
    if (teacherProfileRequest.getContactDetails() != null) {
      User updateInfo =
          teacherProfileTransformer.mapContactDetails(
              teacherUserDetails, teacherProfileRequest.getContactDetails());
      if (teacherUserDetails.getAddresses() == null) {
        addressRepository.save(updateInfo.getAddresses());
      }
      teacherUserDetails = profileRepository.save(updateInfo);
    }
    teacherDetails =
        teacherRepository.save(
            teacherProfileTransformer.mapTeacherDetails(teacherDetails, teacherProfileRequest));
    return teacherProfileTransformer.mapTeacherProfileResponse(teacherUserDetails, teacherDetails);
  }

  public TeacherProfileResponse getProfileDetails(User teacherUserDetails) {
    teacherUserDetails = profileRepository.getById(teacherUserDetails.getId());
    var teacherDetails = teacherUserDetails.getTeacherInfo();
    return teacherProfileTransformer.mapTeacherProfileResponse(teacherUserDetails, teacherDetails);
  }

  public Teacher findByUserInfo(final User userInfo) {
    return teacherRepository.findByUserInfo(userInfo).orElse(null);
  }

  public Teacher getOne(final Long id) {
    return teacherRepository.getById(id);
  }

  public Teacher save(final Teacher teacher) {
    return teacherRepository.save(teacher);
  }

  public Set<Teacher> getTeachersBySections(Set<Section> sections) {
    return teacherRepository.getTeachersBySections(sections);
  }
}
