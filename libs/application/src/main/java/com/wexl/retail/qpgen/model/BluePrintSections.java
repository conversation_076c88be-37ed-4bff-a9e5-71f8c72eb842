package com.wexl.retail.qpgen.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "qp_gen_blueprint_sections")
public class BluePrintSections extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @JsonProperty("question_count")
  private Long questionCount;

  String complexity;
  String category;

  QuestionType type;
  private double marks;

  @JsonProperty("section_name")
  private String sectionName;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "blue_print_id")
  private BluePrint bluePrint;

  @Column(columnDefinition = "TEXT")
  private String instructions;

  @JsonProperty("tags")
  private String tags;
}
