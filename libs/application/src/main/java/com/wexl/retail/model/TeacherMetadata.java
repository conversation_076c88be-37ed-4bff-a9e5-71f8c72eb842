package com.wexl.retail.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TeacherMetadata {
  @JsonProperty("subjects")
  private List<String> subjects;

  @JsonProperty("education_qualification")
  private String educationQualification;
}
