package com.wexl.retail.courses.step.service;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.courses.step.dto.CoursePageRequest;
import com.wexl.retail.courses.step.dto.CoursePageResponse;
import com.wexl.retail.courses.step.model.CoursePage;
import com.wexl.retail.courses.step.repository.CoursePageRepository;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class CoursePageService {

  private final AuthService authService;
  private final CoursePageRepository coursePageRepository;

  public CoursePageResponse createCoursePage(CoursePageRequest request) {
    var coursePage = coursePageRepository.save(buildCoursePage(request));
    return buildCoursePageResponse(coursePage);
  }

  @SneakyThrows
  public CoursePage findCoursePageById(long pageId) {
    return coursePageRepository
        .findById(pageId)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST,
                    "Course Page Not Found for %s".formatted(pageId)));
  }

  public CoursePageResponse updateCoursePageById(CoursePageRequest request, long pageId) {
    var coursePage = findCoursePageById(pageId);
    coursePage.setTitle(request.getTitle());
    coursePage.setContent(request.getContent());
    return buildCoursePageResponse(coursePageRepository.save(coursePage));
  }

  public CoursePageResponse getCoursePageById(long pageId) {
    var coursePage = findCoursePageById(pageId);
    return buildCoursePageResponse(coursePage);
  }

  private CoursePage buildCoursePage(CoursePageRequest request) {
    return CoursePage.builder()
        .title(request.getTitle())
        .content(request.getContent())
        .orgSlug(authService.getUserDetails().getOrganization())
        .build();
  }

  private CoursePageResponse buildCoursePageResponse(CoursePage coursePage) {
    return CoursePageResponse.builder()
        .id(coursePage.getId())
        .title(coursePage.getTitle())
        .content(coursePage.getContent())
        .build();
  }
}
