package com.wexl.retail.mlp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record StudentsDetailsByMlpDto() {
  @Builder
  public record StudentsDetailsByMlp(
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("section_name") String sectionName,
      @JsonProperty("full_name") String fullName,
      @JsonProperty("user_name") String userName,
      @JsonProperty("board_id") Integer boardId,
      @JsonProperty("mlp_details") List<MlpDetails> mlpDetails) {}

  @Builder
  public record MlpDetails(
      @JsonProperty("mlp_name") String mlpName,
      @JsonProperty("mlp_id") Long mlpId,
      @JsonProperty("date") Long date,
      @JsonProperty("exam_id") Long examId,
      @JsonProperty("attendance_percentage") Double attendancePercentage,
      @JsonProperty("knowledge_percentage") Double knowledgePercentage,
      @JsonProperty("practice_status") String practiceStatus,
      @JsonProperty("video_status") String videoStatus,
      @JsonProperty("synopsis_status") String synopsisStatus) {}
}
