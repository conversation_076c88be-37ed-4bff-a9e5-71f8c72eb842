package com.wexl.retail.section.dto.response;

import com.wexl.retail.section.domain.SectionStatus;
import java.sql.Timestamp;
import java.util.Date;
import java.util.UUID;
import lombok.Builder;

public record SectionEntityDto() {

  @Builder
  public record Response(
      Long id,
      String name,
      Long studentCount,
      SectionStatus status,
      UUID uuid,
      String remarks,
      String organization,
      Integer gradeId,
      String gradeName,
      String gradeSlug,
      String boardName,
      String boardSlug,
      Timestamp createdAt,
      Date deletedAt,
      Timestamp updatedAt) {}
}
