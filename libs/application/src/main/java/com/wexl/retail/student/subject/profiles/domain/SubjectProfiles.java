package com.wexl.retail.student.subject.profiles.domain;

import com.wexl.retail.model.Model;
import com.wexl.retail.organization.model.Organization;
import jakarta.persistence.*;
import java.util.List;
import lombok.*;

@Getter
@Setter
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(
    name = "subject_profiles",
    uniqueConstraints = {@UniqueConstraint(columnNames = {"name", "org_id"})})
public class SubjectProfiles extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String name;

  private Boolean status;

  @OneToMany(fetch = FetchType.LAZY, mappedBy = "subjectProfiles", cascade = CascadeType.ALL)
  private List<SubjectProfileDetails> subjectProfileDetails;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "org_id")
  private Organization org;
}
