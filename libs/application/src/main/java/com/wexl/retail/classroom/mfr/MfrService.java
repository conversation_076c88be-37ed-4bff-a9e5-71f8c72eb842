package com.wexl.retail.classroom.mfr;

import com.wexl.retail.classroom.core.dto.ScheduleInstAttendanceStatus;
import com.wexl.retail.classroom.core.repository.ScheduleInstAttendanceDetailsRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.Entity;
import com.wexl.retail.erp.attendance.domain.CalenderDetails;
import com.wexl.retail.model.Student;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.studentfeedback.domain.StudentFeedback;
import com.wexl.retail.studentfeedback.model.FeedbackType;
import com.wexl.retail.studentfeedback.repository.StudentFeedbackRepository;
import com.wexl.retail.studentfeedback.service.StudentFeedbackService;
import com.wexl.retail.task.domain.TaskInst;
import com.wexl.retail.task.domain.TaskStatus;
import com.wexl.retail.task.domain.TaskType;
import com.wexl.retail.task.repository.TaskInstRepository;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.StrapiService;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class MfrService {
  private final UserRepository userRepository;
  private final DateTimeUtil dateTimeUtil;
  private final TaskInstRepository taskInstRepository;
  private final StrapiService strapiService;
  private final ContentService contentService;
  private final StudentFeedbackRepository studentFeedbackRepository;
  private final StudentFeedbackService studentFeedbackService;
  private final ScheduleInstAttendanceDetailsRepository scheduleInstAttendanceDetailsRepository;

  @Value("${app.latestAcademicYear}")
  private String latestAcademicYear;

  public MfrDto.Response getMfr(String authUserId, Long fromDateInEpoch, Long toDateInEpoch) {
    var user = userRepository.findByAuthUserId(authUserId);
    if (user.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.StudentNotFound");
    }
    Student student = user.get().getStudentInfo();
    LocalDateTime fDate = dateTimeUtil.convertEpochToIso8601Legacy(fromDateInEpoch);
    LocalDateTime tDate = dateTimeUtil.convertEpochToIso8601Legacy(toDateInEpoch);
    final CalenderDetails calenderDetailsFromEpoch =
        studentFeedbackService.getCalenderDetailsFromEpoch(toDateInEpoch);
    int month = calenderDetailsFromEpoch.getMonth();
    var taskInsts =
        taskInstRepository.getStudentActivitiesByTaskType(
            student.getId(), fDate.with(LocalTime.MIN), tDate.with(LocalTime.MAX));
    final Entity board = strapiService.getEduBoardById(student.getBoardId());
    var overallFeedback =
        studentFeedbackRepository.getOverAllFeedBack(
            student.getId(), FeedbackType.SUMMARY.toString(), month);
    return MfrDto.Response.builder()
        .authUserId(authUserId)
        .rollNo(student.getRollNumber())
        .fromDateInEpoc(fromDateInEpoch)
        .boardName(board.getName())
        .overAllFeedBack(
            Objects.isNull(overallFeedback) ? Strings.EMPTY : overallFeedback.getMessage())
        .gradeName(contentService.getGradeById(student.getClassId()).getName())
        .attendancePercentage(calculateAttendancePercentage(student.getId(), fDate, tDate))
        .toDateInEpoc(toDateInEpoch)
        .studentName(user.get().getFirstName() + " " + user.get().getLastName())
        .subjectsList(buildSubjectData(taskInsts, student, month))
        .build();
  }

  private Double calculateAttendancePercentage(
      Long studentId, LocalDateTime fDate, LocalDateTime tDate) {
    var attendanceDetails =
        scheduleInstAttendanceDetailsRepository.findStudentAttendanceByDate(
            studentId, fDate, tDate);
    if (attendanceDetails.isEmpty()) {
      return 0.0;
    }
    var totalCount =
        Double.valueOf(
            attendanceDetails.stream()
                .filter(
                    attendance ->
                        attendance
                                .getAttendanceStatus()
                                .equals(ScheduleInstAttendanceStatus.PRESENT)
                            || attendance
                                .getAttendanceStatus()
                                .equals(ScheduleInstAttendanceStatus.ABSENT))
                .count());
    var attendedCount =
        Double.valueOf(
            attendanceDetails.stream()
                .filter(
                    attendance ->
                        attendance
                            .getAttendanceStatus()
                            .equals(ScheduleInstAttendanceStatus.PRESENT))
                .count());

    return attendedCount > 0.0
        ? Double.parseDouble(Constants.DECIMAL_FORMAT.format((attendedCount / totalCount) * 100))
        : attendedCount;
  }

  private List<MfrDto.Subjects> buildSubjectData(
      List<TaskInst> taskInsts, Student student, int month) {
    List<MfrDto.Subjects> subjectsList = new ArrayList<>();
    var subjectsSlugList =
        taskInsts.stream()
            .filter(
                tasks ->
                    tasks.getTask().getSubtopicSlug() != null
                        && Objects.nonNull(tasks.getTask().getSubjectSlug()))
            .map(tasks -> tasks.getTask().getSubjectSlug())
            .distinct()
            .toList();
    subjectsSlugList.forEach(
        subjectSlug -> {
          var subjectData =
              taskInsts.stream()
                  .filter(taskInst -> subjectSlug.equals(taskInst.getTask().getSubjectSlug()))
                  .toList();
          var feedback =
              studentFeedbackRepository.findAllByStudentAndCalenderDetailsAndTypeAndSubjectSlug(
                  student.getId(),
                  FeedbackType.SUBJECT.toString(),
                  subjectSlug,
                  month,
                  latestAcademicYear);

          subjectsList.add(
              MfrDto.Subjects.builder()
                  .subjectName(subjectData.getFirst().getTask().getSubjectName())
                  .subjectRemarks(feedback.isEmpty() ? "" : feedback.get(0).getMessage())
                  .chaptersList(buildChapterData(subjectData, student))
                  .build());
        });
    return subjectsList;
  }

  private List<MfrDto.Chapters> buildChapterData(List<TaskInst> subjectData, Student student) {
    List<MfrDto.Chapters> chaptersList = new ArrayList<>();
    var chapterList =
        subjectData.stream()
            .map(taskInst -> taskInst.getTask().getChapterSlug())
            .distinct()
            .toList();

    chapterList.forEach(
        chapterSlug -> {
          var chapterData =
              subjectData.stream()
                  .filter(taskInst -> taskInst.getTask().getChapterSlug().equals(chapterSlug))
                  .toList();
          var task = chapterData.getFirst().getTask();

          chaptersList.add(
              MfrDto.Chapters.builder()
                  .subTopicsList(buildSubtopicData(chapterData))
                  .chapterName(task.getChapterName())
                  .chapterSlug(task.getChapterSlug())
                  .testPercentage(calculateTestPercentage(chapterData))
                  .chapterRemarks(buildFeedBackMessage(student, chapterData))
                  .build());
        });
    chaptersList.sort(Comparator.comparing(MfrDto.Chapters::chapterName));
    return chaptersList;
  }

  private String buildFeedBackMessage(Student student, List<TaskInst> chapterData) {
    var getTestData =
        chapterData.stream()
            .filter(
                x -> x.getTask().getTaskType().equals(TaskType.TEST) && x.getCompletedAt() != null)
            .max(Comparator.comparing(TaskInst::getCompletedAt));

    if (getTestData.isPresent()) {
      var taskInst = getTestData.get().getId();
      Optional<StudentFeedback> feedback =
          studentFeedbackRepository.findByStudentAndTaskInst(student, taskInst);
      return feedback.isEmpty() ? "" : feedback.get().getMessage();
    }
    return "";
  }

  private String calculateTestPercentage(List<TaskInst> chapterData) {
    var chapterTests =
        chapterData.stream()
            .filter(taskInst -> taskInst.getTask().getTaskType().equals(TaskType.TEST));

    var completedTests =
        chapterTests
            .filter(
                taskInst ->
                    taskInst.getTask().getTaskType().equals(TaskType.TEST)
                        && taskInst.getCompletedAt() != null)
            .max(Comparator.comparing(TaskInst::getCompletedAt));
    if (completedTests.isPresent()) {
      var exam = completedTests.get().getExam();
      return exam.getMarksScored() == 0
          ? "0"
          : String.valueOf(Math.round(exam.getMarksScored() / exam.getTotalMarks() * 100));
    }
    return "-";
  }

  private String calculateAssignmentMarks(List<TaskInst> completedAssignments) {
    double marksScored = 0;
    float count = 0;
    Double totalMarks = 0.0;
    Double totalMarksSum = 0.0;
    Integer totalCount = 0;
    if (completedAssignments.isEmpty()) {
      return "-";
    }
    for (TaskInst taskInst : completedAssignments) {
      if (Objects.nonNull(taskInst.getExam())) {
        marksScored = taskInst.getExam().getMarksScored();
        count = taskInst.getExam().getTotalMarks();
        totalMarks =
            marksScored > 0
                ? Double.parseDouble(Constants.DECIMAL_FORMAT.format((marksScored / count) * 100))
                : 0;
        totalMarksSum = totalMarksSum + totalMarks;
        totalCount = totalCount + 1;
      }
    }
    return totalMarksSum > 0 ? String.valueOf(Math.round(totalMarksSum / totalCount)) : "0";
  }

  private List<MfrDto.SubTopics> buildSubtopicData(List<TaskInst> chapterData) {
    List<MfrDto.SubTopics> subTopicsList = new ArrayList<>();
    var revisions =
        chapterData.stream()
            .filter(tasks -> tasks.getTask().getSubtopicSlug() != null)
            .filter(
                taskInst ->
                    taskInst.getTask().getTaskType().equals(TaskType.REVISION)
                        || taskInst.getTask().getTaskType().equals(TaskType.PRACTICE)
                        || taskInst.getTask().getTaskType().equals(TaskType.ASSIGNMENT))
            .toList();
    if (!revisions.isEmpty()) {
      var subTopics =
          revisions.stream()
              .map(taskInst -> taskInst.getTask().getSubtopicSlug())
              .filter(Objects::nonNull)
              .distinct()
              .toList();
      subTopics.forEach(
          subTopic -> {
            var revisionData =
                revisions.stream()
                    .filter(
                        taskInst ->
                            taskInst.getTask().getSubtopicSlug().equals(subTopic)
                                && taskInst.getTask().getTaskType().equals(TaskType.REVISION))
                    .toList();
            var assignmentsAndPractice =
                revisions.stream()
                    .filter(
                        taskInst ->
                            (taskInst.getTask().getTaskType().equals(TaskType.PRACTICE)
                                    || taskInst.getTask().getTaskType().equals(TaskType.ASSIGNMENT))
                                && taskInst.getTask().getSubtopicSlug().equals(subTopic))
                    .toList();
            var completedAssignmentsAndPractice =
                assignmentsAndPractice.stream()
                    .filter(
                        taskInst ->
                            taskInst.getCompletionStatus().equals(TaskStatus.COMPLETED)
                                || taskInst.getCompletionStatus().equals(TaskStatus.PENDING))
                    .toList();
            var calAssignmentsAndPractice =
                assignmentsAndPractice.stream()
                    .filter(taskInst -> taskInst.getCompletionStatus().equals(TaskStatus.COMPLETED))
                    .toList();
            var task =
                !revisionData.isEmpty()
                    ? revisionData.getFirst().getTask()
                    : assignmentsAndPractice.getFirst().getTask();
            var revisionCount = (revisionData.isEmpty() ? "-" : revisionData.size()).toString();
            subTopicsList.add(
                MfrDto.SubTopics.builder()
                    .revisionCount(revisionCount)
                    .totalAssignmentsCompleted(completedAssignmentsAndPractice.size())
                    .subTopicName(task.getSubtopicName())
                    .subTopicSlug(task.getSubtopicSlug())
                    .marksScored(calculateAssignmentMarks(calAssignmentsAndPractice))
                    .totalAssignmentsGiven(assignmentsAndPractice.size())
                    .build());
          });
    }
    subTopicsList.sort(Comparator.comparing(MfrDto.SubTopics::subTopicName));
    return subTopicsList;
  }
}
