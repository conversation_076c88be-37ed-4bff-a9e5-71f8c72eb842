package com.wexl.retail.metrics;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.student.exam.SectionWiseData;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StudentsTestData {
  @JsonProperty("user_id")
  private long userId;

  @JsonProperty("test_name")
  private String testName;

  @JsonProperty("auth_user_id")
  private String authUserId;

  @JsonProperty("student_name")
  private String studentName;

  @JsonProperty("total_marks")
  private double totalMarks;

  @JsonProperty("secured_marks")
  private float securedMarks;

  @JsonProperty("school_name")
  private String schoolName;

  private List<SectionWiseData.SectionWiseTestData> sections;

  @JsonProperty("exam_id")
  private Long examId;
}
