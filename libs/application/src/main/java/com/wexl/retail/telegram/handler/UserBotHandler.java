package com.wexl.retail.telegram.handler;

import com.wexl.retail.model.User;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.telegram.util.Constants;
import java.util.HashMap;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class UserBotHandler implements TelegramBotHandler {
  @Autowired private UserRepository userRepository;
  @Autowired private TeacherRepository teacherRepository;

  @Override
  public String handleCommand(String[] params) {
    if (!isValid(params, 2)) {
      return Constants.INVALID_ARGS.formatted(getHelpText());
    }
    String authUserId = params[1];
    User user = userRepository.getUserByAuthUserId(authUserId);
    if (user == null) {
      return "User with [" + authUserId + "] not found";
    }
    Map<String, Object> result = new HashMap<>();
    handleInternal(user, result);
    return convertToString(result);
  }

  /*
    Handle the service logic and optionally
    populate map with useful messages.
  */
  protected abstract void handleInternal(User user, Map<String, Object> result);
}
