package com.wexl.retail.pdf.viewer.domain;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.sql.Timestamp;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "pdf_annotations")
public class Annotation {
  @Id
  @Column(name = "id", updatable = false)
  private long id;

  @Column(name = "page_index", nullable = false)
  private Long pageIndex;

  @Column(name = "page_width", nullable = false)
  private Long pageWidth;

  @Column(name = "page_height", nullable = false)
  private Long pageHeight;

  @Column(name = "annotation_type_id", nullable = false)
  private Long annotationTypeId;

  @Column(name = "coordinate", nullable = false)
  private String coordinate;

  @Column(name = "form_field_name")
  private String formFieldName;

  @Column(name = "form_field_value")
  private String formFieldValue;

  @Column(name = "text", columnDefinition = "TEXT", nullable = false)
  private String text;

  @Column(name = "font_size", nullable = false)
  private Long fontSize;

  @Column(name = "font", nullable = false)
  private String font;

  @Column(name = "line_width")
  private Long lineWidth;

  @Column(name = "opacity")
  private Double opacity;

  @Column(name = "color", nullable = false)
  private String color;

  @Column(name = "background_color", nullable = false)
  private String backgroundColor;

  @Column(name = "icon", nullable = false)
  private String icon;

  @Column(name = "doc_id", nullable = false)
  private String docId;

  @Column(name = "read_only", nullable = false)
  private Boolean readOnly = Boolean.FALSE;

  @Column(name = "read_only_comment", nullable = false)
  private Boolean readOnlyComment = Boolean.FALSE;

  @Column(name = "calibration_label")
  private String calibrationLabel;

  @Column(name = "calibration_value", nullable = false)
  private Double calibrationValue = 0D;

  @Column(name = "calibration_measurement_type_id")
  private Long calibrationMeasurementTypeId;

  @Column(name = "measurement_type_id", nullable = false)
  private Long measurementTypeId = 1L;

  @Column(name = "line_style_id", nullable = false)
  private Long lineStyleId = 1L;

  @Column(nullable = false)
  private Timestamp createdAt;

  @Column(nullable = false)
  private Timestamp updatedAt;

  @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
  @JoinColumn(name = "annotation_id")
  private List<Comment> comments;

  @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
  @JoinColumn(name = "annotation_id")
  private List<DrawingPosition> drawingPositions;

  @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
  @JoinColumn(name = "annotation_id")
  private List<HighlightTextRect> highlightTextRects;
}
