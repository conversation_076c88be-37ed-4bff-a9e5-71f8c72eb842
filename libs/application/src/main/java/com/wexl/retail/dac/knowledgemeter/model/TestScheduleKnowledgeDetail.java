package com.wexl.retail.dac.knowledgemeter.model;

import com.wexl.retail.erp.attendance.domain.CalenderDetails;
import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "test_schedule_knowledge_details")
public class TestScheduleKnowledgeDetail extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  private TestScheduleKnowledge testScheduleKnowledge;

  private double marks;
  private double totalMarks;
  private double knowledgePercentage;
  private long testScheduleId;
  private Long examId;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "calender_details_id")
  private CalenderDetails calenderDetails;
}
