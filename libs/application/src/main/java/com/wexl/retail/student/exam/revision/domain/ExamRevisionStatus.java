package com.wexl.retail.student.exam.revision.domain;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum ExamRevisionStatus {
  COMPLETED("COMPLETED"),
  NOT_COMPLETED("NOT_COMPLETED");

  private final String value;

  public static ExamRevisionStatus fromValue(String value) {
    if (value == null || "".equals(value)) {
      throw new IllegalArgumentException("Value cannot be null or empty!");
    }

    for (ExamRevisionStatus enumEntry : ExamRevisionStatus.values()) {
      if (enumEntry.toString().equalsIgnoreCase(value)) {
        return enumEntry;
      }
    }

    throw new IllegalArgumentException("Do not understand the Type " + value);
  }

  @Override
  public String toString() {
    return this.value;
  }
}
