package com.wexl.retail.organization.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.model.OtpVerificationRequest;
import com.wexl.retail.model.UiConfig;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class OrganizationSignupRequest {
  @NotNull String organizationName;
  @NotNull String email;
  OtpVerificationRequest emailOtp;
  @NotNull String mobileNumber;
  OtpVerificationRequest mobileOtp;
  @NotNull String password;
  String organizationBoard;
  String captchaCode;
  Boolean termsAndConditions;
  String firstName;
  String lastName;
  private String principalName;
  private boolean isParent;
  private boolean isPublisher;
  private String parentOrg;
  private String publisherSlug;
  private long branchStrength;
  private String branchName;
  private List<OrgCurriculumRequest> boards;

  @JsonProperty("profile_id")
  @NotNull
  private Long profileId;

  @JsonProperty("is_self_signup")
  private boolean isSelfSignup;

  @JsonProperty("ui_config")
  private UiConfig uiConfig;

  @JsonProperty("is_corporate")
  private Boolean isCorporate;
}
