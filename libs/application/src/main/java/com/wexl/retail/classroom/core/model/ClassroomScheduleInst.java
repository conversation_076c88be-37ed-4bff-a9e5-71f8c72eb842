package com.wexl.retail.classroom.core.model;

import com.wexl.retail.classroom.core.dto.ClassRoomDto;
import com.wexl.retail.classroom.core.dto.ClassroomMeetingStatus;
import com.wexl.retail.meetingroom.domain.MeetingRoom;
import com.wexl.retail.model.Model;
import com.wexl.retail.task.domain.Task;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.List;
import lombok.*;
import org.hibernate.annotations.Type;

@EqualsAndHashCode
@Data
@Builder
@RequiredArgsConstructor
@Entity
@Table(name = "classroom_schedule_inst")
@AllArgsConstructor
public class ClassroomScheduleInst extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String title;

  @ManyToOne
  @JoinColumn(name = "classroom_schedule_id")
  private ClassroomSchedule classroomSchedule;

  @Enumerated(EnumType.STRING)
  private DayOfWeek dayOfWeek;

  @Column(name = "start_time")
  private LocalDateTime startTime;

  @Column(name = "end_time")
  private LocalDateTime endTime;

  @OneToMany(fetch = FetchType.LAZY, mappedBy = "classroomScheduleInst", cascade = CascadeType.ALL)
  private List<Task> tasks;

  @JoinColumn(name = "org_slug")
  private String orgSlug;

  @Enumerated(EnumType.STRING)
  private ClassroomMeetingStatus status = ClassroomMeetingStatus.NOT_STARTED;

  @Column(name = "host_join_time", columnDefinition = "TIMESTAMP WITH TIME ZONE")
  private OffsetDateTime hostJoinTime;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb")
  private ClassRoomDto.Tutors tutors;

  @ManyToOne
  @JoinColumn(name = "meeting_room_id")
  private MeetingRoom meetingRoom;
}
