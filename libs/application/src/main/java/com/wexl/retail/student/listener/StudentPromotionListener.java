package com.wexl.retail.student.listener;

import com.wexl.retail.model.Student;
import com.wexl.retail.student.publisher.StudentPromotionEvent;
import com.wexl.retail.subjects.service.SubjectsMetaDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class StudentPromotionListener implements ApplicationListener<StudentPromotionEvent> {

  private final SubjectsMetaDataService subjectsMetaDataService;

  @Override
  public void onApplicationEvent(StudentPromotionEvent studentPromotionEvent) {
    Object source = studentPromotionEvent.getSource();
    if (source instanceof Student student) {
      subjectsMetaDataService.mapSubjectMetaData(student);
    }
  }
}
