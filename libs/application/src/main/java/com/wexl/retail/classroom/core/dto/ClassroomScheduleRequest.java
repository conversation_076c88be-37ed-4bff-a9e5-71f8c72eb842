package com.wexl.retail.classroom.core.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.DayOfWeek;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
public class ClassroomScheduleRequest {

  private String title;

  @JsonProperty("meeting_room_id")
  private Long meetingRoomId;

  private long startTime;

  private long endTime;

  @JsonProperty("start_date")
  private long startDate;

  @JsonProperty("expiry_date")
  private long expiryDate;

  private DayOfWeek dayOfWeek;
}
