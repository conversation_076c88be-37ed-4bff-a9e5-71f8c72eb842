package com.wexl.retail.migration;

import com.wexl.retail.task.domain.TaskType;
import com.wexl.retail.task.repository.TaskRepository;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.util.StrapiService;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class MigrationService {

  private final TaskRepository taskRepository;
  private final StrapiService strapiService;
  private final TestDefinitionRepository testDefinitionRepository;

  public MigrationDto.Counts migrateTaskSubtopic() {
    var beforeMigrationCount =
        taskRepository.countByTaskTypeAndSubtopicSlugIsNull(TaskType.ASSIGNMENT);
    var migrationData = taskRepository.getMigrationData();
    migrationData.forEach(
        data -> {
          var subTopic = strapiService.getSubTopicBySlug(data.getSubtopicSlug());
          var optionalTask = taskRepository.findById(data.getTaskId());
          if (optionalTask.isEmpty()) {
            return;
          }
          var task = optionalTask.get();
          task.setSubtopicId((long) subTopic.getId());
          task.setSubtopicName(subTopic.getName());
          task.setSubtopicSlug(subTopic.getSlug());
          taskRepository.save(task);
        });
    var afterMigrationCount =
        taskRepository.countByTaskTypeAndSubtopicSlugIsNull(TaskType.ASSIGNMENT);
    return MigrationDto.Counts.builder()
        .beforeMigrationCount(beforeMigrationCount)
        .afterMigrationCount(afterMigrationCount)
        .build();
  }

  public List<Long> migrateTestDefinitions(String orgSlug) {
    List<Long> responseList = new ArrayList<>();
    List<TestDefinition> testDefinitionList;
    testDefinitionList = testDefinitionRepository.getTestDefinitionsDataForMigration(orgSlug);
    testDefinitionList.forEach(
        testDef -> {
          var subjectSlugList =
              testDefinitionRepository.getSubjectSlugForMigration(testDef.getId());
          if (!subjectSlugList.isEmpty()) {
            testDef.setSubjectSlug(subjectSlugList.get(0));
            testDefinitionRepository.save(testDef);
            responseList.add(testDef.getId());
          }
        });
    return responseList;
  }
}
