package com.wexl.retail.student.answer;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class ExamDetails {
  private long examId;
  private long examAnswerId;
  private String testType;
  private int noOfQuestions;
  private int noOfCorrectAnswer;
  private double percentageSecured;
  private boolean isGoalAchieved;
  private boolean corrected;
  private boolean hasPdfAnswerSheet;
  private String pdfAnswerSheetUrl;
  private String uploadCorrectedAnswerSheetUrl;
  private String predefinedPdfSolutionSheetUrl;
  private String pdfQuestionSheetUrl;
  private String testName;
  private Long totalMarks;
  private String grade;
  private String subject;
  private String chapter;
  private String studentName;
  private String explanationVideoUrl;
  private String explanationVideoSha;

  @JsonProperty("asset_slug")
  private String assetSlug;
}
