package com.wexl.retail.maths.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Entity
@Data
@Table(name = "ccss_clusters")
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
public class CcssCluster extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String name;
  @ManyToOne private CcssDomain ccssDomain;

  @OneToMany(fetch = FetchType.LAZY, mappedBy = "ccssCluster", cascade = CascadeType.ALL)
  private List<CcssStandard> ccssStandards;
}
