package com.wexl.retail.notification.service;

import com.google.gson.Gson;
import com.wexl.retail.announcement.dto.AnnouncementRequest;
import com.wexl.retail.announcement.dto.AssigneeMode;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.classroom.core.model.Classroom;
import com.wexl.retail.classroom.core.service.ClassroomService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.erp.attendance.repository.SectionAttendanceDetailRepository;
import com.wexl.retail.generic.ProfileUtils;
import com.wexl.retail.guardian.model.Guardian;
import com.wexl.retail.guardian.repository.GuardianRepository;
import com.wexl.retail.messagetemplate.model.MessageTemplate;
import com.wexl.retail.messagetemplate.service.MessageTemplateService;
import com.wexl.retail.mlp.dto.AdvancedMlpRequest;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.msg91.dto.Msg91Dto;
import com.wexl.retail.msg91.service.DelegatingEmailService;
import com.wexl.retail.msg91.service.DelegatingSmsService;
import com.wexl.retail.notification.dto.NotificationMessage;
import com.wexl.retail.notification.dto.TimeBombRequest;
import com.wexl.retail.notification.model.AvailableTimeBombJob;
import com.wexl.retail.notification.model.ReferenceType;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.dto.NotificationDto.NotificationRequest;
import com.wexl.retail.notifications.dto.NotificationType;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.services.StudentService;
import com.wexl.retail.test.schedule.domain.ScheduleTestStudent;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import com.wexl.retail.test.schedule.service.ScheduleTestService;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.ValidationUtils;
import com.wexl.retail.whatsapp.WhatsAppService;
import com.wexl.retail.whatsapp.interakt.dto.Request.Recipient;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class EventNotificationService {

  private static final String NOTIFICATION_MESSAGE = "Your teacher has assigned you a new %s";
  private final AuthService authService;

  @Value("${app.mobile.notifications.newMlpNotification.notificationTitle}")
  private String notificationTitle;

  @Value("${app.mobile.notifications.newMlpNotification.notificationText}")
  private String notificationText;

  @Value("${app.mobile.notifications.wexlIconImage}")
  private String notificationImageUrl;

  private final WhatsAppService whatsAppService;
  private final StudentService studentService;
  private final StudentRepository studentRepository;
  private final SectionService sectionService;
  private final ScheduleTestService scheduleTestService;
  private final ValidationUtils validationUtils;
  private final PushNotificationService pushNotificationService;
  private final ClassroomService classroomService;
  private final MessageTemplateService messageTemplateService;
  private final DelegatingSmsService delegatingSmsService;
  private final TestDefinitionRepository testDefinitionRepository;
  private final DelegatingEmailService delegatingEmailService;
  private final GuardianRepository guardianRepository;
  private final SectionAttendanceDetailRepository sectionAttendanceDetailRepository;
  private final SimpleDateFormat sdf = new SimpleDateFormat("MM-dd-yyyy");
  private final ScheduleTestStudentRepository scheduleTestStudentRepository;
  private final ProfileUtils profileUtils;

  public void triggerMlpNotification(AdvancedMlpRequest advancedMlpRequest) {
    try {

      List<String> deviceTokens;
      if (advancedMlpRequest.getStudentIds() != null
          && !advancedMlpRequest.getStudentIds().isEmpty()) {

        List<Student> students =
            studentRepository.findStudentListById(advancedMlpRequest.getStudentIds());
        deviceTokens =
            students.stream()
                .filter(student -> student.getUserInfo().getDeletedAt() == null)
                .map(student -> student.getUserInfo().getGuid())
                .collect(Collectors.toList());

      } else {
        Set<Section> mlpAssignedSections =
            Objects.isNull(advancedMlpRequest.getSectionUuid())
                ? advancedMlpRequest.getSectionUuids().stream()
                    .map(sectionService::findByUuid)
                    .collect(Collectors.toSet())
                : Set.of(sectionService.findByUuid(advancedMlpRequest.getSectionUuid()));

        deviceTokens =
            studentService.getStudentsBySections(mlpAssignedSections).stream()
                .map(student -> student.getUserInfo().getGuid())
                .toList();
      }

      NotificationMessage notification =
          NotificationMessage.builder()
              .notificationText(notificationText)
              .notificationTitle(notificationTitle)
              .notificationImageLink(notificationImageUrl)
              .deviceTokens(deviceTokens)
              .build();

      var timebombRequest =
          buildTimeBombRequest(notification, "mlp", AvailableTimeBombJob.PUSH_NOTIFICATION);

      pushNotificationService.createTimeBombEntry(timebombRequest);
    } catch (Exception e) {
      log.error(e.getMessage());
    }
  }

  public TimeBombRequest buildTimeBombRequest(
      NotificationMessage notification, String notificationType, AvailableTimeBombJob job) {

    return TimeBombRequest.builder()
        .id(UUID.randomUUID().toString())
        .type(ReferenceType.TEST_SCHEDULE)
        .jobToRun(job)
        .expiredAt(Instant.now().plus(Duration.ofSeconds(10)))
        .jobParams(
            Map.of("type", notificationType, "notification", new Gson().toJson(notification)))
        .build();
  }

  private NotificationMessage buildNotificationMessage(
      List<String> deviceTokens, NotificationDto.NotificationRequest notificationRequest) {

    return NotificationMessage.builder()
        .notificationText(notificationRequest.message())
        .notificationTitle(notificationRequest.title())
        .notificationImageLink(notificationImageUrl)
        .deviceTokens(deviceTokens)
        .build();
  }

  public void triggerNotification(
      NotificationDto.NotificationRequest notificationRequest, String orgSlug, Long date) {
    triggerPushNotification(notificationRequest, orgSlug);
    if (notificationRequest.messageTemplateId() != null) {
      triggerExternalSystemNotification(notificationRequest, orgSlug, date);
    }
  }

  private List<Msg91Dto.EmailTo> buildEmailRecipients(List<Student> students) {
    return students.stream()
        .filter(s -> Objects.nonNull(s.getUserInfo()) && !s.getGuardians().isEmpty())
        .map(s -> getPrimaryGuardian(s).getEmail())
        .filter(Objects::nonNull)
        .map(email -> Msg91Dto.EmailTo.builder().email(email).build())
        .toList();
  }

  private List<Student> getStudentDetails(NotificationRequest notificationRequest, String orgSlug) {
    if (Objects.nonNull(notificationRequest.studentIds())
        && !notificationRequest.studentIds().isEmpty()) {
      return studentService.getStudentsByIdsAndOrgSlug(
          orgSlug, notificationRequest.studentIds(), true);
    }
    if (Objects.nonNull(notificationRequest.sectionUuids())
        && !notificationRequest.sectionUuids().isEmpty()) {
      return studentService.getStudentsBySectionUuidsAndOrgSlug(
          notificationRequest.sectionUuids(), orgSlug);
    }

    if (Objects.nonNull(notificationRequest.classroomIds())
        && !notificationRequest.classroomIds().isEmpty()) {
      return classroomService
          .getClassroomsByIdsAndOrgSlug(notificationRequest.classroomIds())
          .stream()
          .map(Classroom::getStudents)
          .flatMap(List::stream)
          .toList();
    }
    return new ArrayList<>();
  }

  private void triggerExternalSystemNotification(
      NotificationRequest notificationRequest, String orgSlug, Long date) {
    if (profileUtils.isDev()) {
      log.info("triggerExternalSystemNotification will not work in DEV");
      return;
    }
    var user = authService.getUserDetails();
    final MessageTemplate messageTemplate =
        messageTemplateService.getMessageTemplate(
            user.getOrganization(), notificationRequest.messageTemplateId());

    List<Student> students = getStudentDetails(notificationRequest, orgSlug);
    if (students.isEmpty()) {
      return;
    }

    if (StringUtils.isNotEmpty(messageTemplate.getSmsDltTemplateId())) {
      sendSms(orgSlug, students, messageTemplate.getSmsDltTemplateId(), date);
    }

    if (StringUtils.isNotEmpty(messageTemplate.getWhatsAppTemplateId())) {
      sendWhatsApp(students, messageTemplate.getWhatsAppTemplateId());
    }

    if (StringUtils.isNotEmpty(messageTemplate.getEmailTemplateId())) {
      delegatingEmailService
          .get(orgSlug)
          .sendEmail(messageTemplate.getEmailTemplateId(), buildEmailRecipients(students));
    }
  }

  private void sendWhatsApp(List<Student> students, String whatsAppTemplateId) {
    var validStudents = getStudentsWithGuardians(students);
    final List<Recipient> recipients =
        validStudents.stream()
            .filter(student -> Objects.nonNull(student.getUserInfo()))
            .map(
                s -> {
                  var primaryGuardian = getPrimaryGuardian(s);
                  User user = s.getUserInfo();
                  return Recipient.builder()
                      .mobileNumber(primaryGuardian.getMobileNumber())
                      .name(user.getFirstName())
                      .date(sdf.format(new Date()))
                      .build();
                })
            .toList();
    if (!recipients.isEmpty()) {
      whatsAppService.sendWhatsAppMessage(whatsAppTemplateId, recipients);
    }
  }

  private void sendSms(String orgSlug, List<Student> students, String messageTemplate, Long date) {
    var recipientList = buildRecipients(students, date);
    if (!recipientList.isEmpty()) {
      delegatingSmsService.get(orgSlug).sendBulkMessage(messageTemplate, recipientList);
    }
  }

  public List<Msg91Dto.Recipient> buildRecipients(List<Student> students, Long epochDate) {
    var validStudents = getStudentsWithGuardians(students);
    var date =
        Objects.nonNull(epochDate)
            ? DateTimeUtil.convertEpochFormatToDate(epochDate, Constants.DATE_FORMAT)
            : new SimpleDateFormat("dd-MM-yyyy")
                .format(
                    Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant()));
    var recipientList =
        validStudents.stream()
            .filter(student -> Objects.nonNull(student.getUserInfo()))
            .map(
                s -> {
                  var primaryGuardian = getPrimaryGuardian(s);
                  User user = s.getUserInfo();

                  var section = s.getSection().getName();
                  Teacher teacher =
                      Objects.isNull(s.getSection().getClassTeacher())
                          ? null
                          : s.getSection().getClassTeacher();
                  var teacherName =
                      teacher == null
                          ? "-"
                          : teacher.getUserInfo().getFirstName()
                              + teacher.getUserInfo().getLastName();
                  return Msg91Dto.Recipient.builder()
                      .mobiles(primaryGuardian.getMobileNumber())
                      .name(user.getFirstName())
                      .teacherName(teacherName)
                      .sectionName(section)
                      .date(date)
                      .orgname(students.getFirst().getSchoolName())
                      .build();
                })
            .toList();
    return recipientList.stream().filter(recipient -> recipient.mobiles() != null).toList();
  }

  private List<Student> getStudentsWithGuardians(List<Student> students) {
    return students.stream().filter(x -> !x.getGuardians().isEmpty()).toList();
  }

  public Guardian getPrimaryGuardian(Student student) {
    Guardian guardian = guardianRepository.findByStudentAndIsPrimary(student, true);
    if (guardian != null) {
      return guardian;
    }
    return student.getGuardians().getFirst();
  }

  public void triggerPushNotification(
      NotificationDto.NotificationRequest notificationRequest, String orgSlug) {

    try {
      List<String> deviceTokens = getDeviceTokens(notificationRequest, orgSlug);

      NotificationMessage notificationMessage =
          buildNotificationMessage(deviceTokens, notificationRequest);
      var timebombRequest =
          buildTimeBombRequest(
              notificationMessage,
              notificationRequest.notificationType().name(),
              AvailableTimeBombJob.PUSH_NOTIFICATION);

      pushNotificationService.createTimeBombEntry(timebombRequest);
    } catch (Exception e) {
      log.error(e.getMessage());
    }
  }

  private List<String> getDeviceTokens(
      NotificationDto.NotificationRequest notificationRequest, String orgSlug) {
    final List<Student> studentDetails = getStudentDetails(notificationRequest, orgSlug);
    return studentDetails.stream().map(Student::getUserInfo).map(User::getGuid).toList();
  }

  private void triggerAttendancePushNotification(List<Student> absenteeStudents, String orgSlug) {
    NotificationDto.NotificationRequest notificationRequest =
        NotificationRequest.builder()
            .notificationType(NotificationType.INDIVIDUAL)
            .message("D/P Your ward was absent to the school on " + sdf.format(new Date()) + ".")
            .title("Absentee Notification")
            .sectionUuids(null)
            .classroomIds(null)
            .studentIds(absenteeStudents.stream().map(Student::getId).toList())
            .build();

    triggerPushNotification(notificationRequest, orgSlug);
  }

  public void triggerAnnouncementNotification(
      AnnouncementRequest announcementRequest, String orgSlug, String sectionUuid) {
    var studentIds = getStudentIds(announcementRequest, sectionUuid, orgSlug);
    triggerNotification(
        NotificationRequest.builder()
            .notificationType(NotificationType.INDIVIDUAL)
            .studentIds(studentIds)
            .message(
                NOTIFICATION_MESSAGE.formatted(
                    announcementRequest
                        .getMaterial()
                        .getAnnouncementType()
                        .toString()
                        .toLowerCase()))
            .title("New Announcement")
            .build(),
        orgSlug,
        null);
  }

  private List<Long> getStudentIds(
      AnnouncementRequest announcementRequest, String sectionUuid, String orgSlug) {
    if (announcementRequest.getAssigneeMode().equals(AssigneeMode.ALL_STUDENTS.toString())) {
      var section = validationUtils.findSectionByUuid(sectionUuid);
      var students = studentRepository.getStudentsBySectionAndDeletedAtIsNull(section);
      return students.stream().map(Student::getId).toList();
    }
    return studentService.getStudentIdsByAuthUserId(
        announcementRequest.getIndividualStudentOptions().getStudentAuthIds(), orgSlug);
  }

  @Async
  public void triggerScheduleTestPushNotification(long scheduleTestId) {
    try {
      var scheduleTest = scheduleTestService.getScheduledTest(scheduleTestId);
      var testDef = testDefinitionRepository.findById(scheduleTest.getTestdefinationId());
      List<User> studentUser =
          scheduleTestStudentRepository
              .getAllStudentsByScheduledIds(Collections.singletonList(scheduleTest.getId()))
              .stream()
              .map(ScheduleTestStudent::getStudent)
              .toList();
      var deviceTokens = studentUser.stream().map(User::getGuid).toList();
      NotificationMessage notification =
          NotificationMessage.builder()
              .notificationTitle(notificationTitle)
              .notificationText(notificationText)
              .deviceTokens(deviceTokens)
              .notificationType(
                  testDef.map(testDefinition -> testDefinition.getType().name()).orElse(null))
              .build();
      var timebombRequest =
          buildTimeBombRequest(
              notification,
              notification.getNotificationType(),
              AvailableTimeBombJob.PUSH_NOTIFICATION);

      pushNotificationService.createTimeBombEntry(timebombRequest);
    } catch (Exception e) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.UnableToSendPushNotification", e);
    }
  }
}
