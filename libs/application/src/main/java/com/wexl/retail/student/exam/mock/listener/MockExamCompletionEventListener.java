package com.wexl.retail.student.exam.mock.listener;

import com.wexl.retail.student.exam.migration.ExamMigrationService;
import com.wexl.retail.student.exam.publisher.MockExamCompletionEvent;
import com.wexl.retail.test.schedule.domain.ScheduleTestStudent;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Slf4j
@AllArgsConstructor
@Component
public class MockExamCompletionEventListener
    implements ApplicationListener<MockExamCompletionEvent> {

  private final ExamMigrationService examMigrationService;

  @Override
  public void onApplicationEvent(MockExamCompletionEvent mockExamCompletionEvent) {
    Object source = mockExamCompletionEvent.getSource();
    if (source instanceof ScheduleTestStudent scheduleTestStudent) {
      List<ScheduleTestStudent> scheduleTestStudentList = new ArrayList<>();
      scheduleTestStudentList.add(scheduleTestStudent);
      examMigrationService.migrateSubmittedExams(scheduleTestStudentList);
    }
  }
}
