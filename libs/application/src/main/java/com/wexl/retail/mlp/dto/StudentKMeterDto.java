package com.wexl.retail.mlp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record StudentKMeterDto() {
  @Builder
  public record StudentKMeterResponse(
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("subject_kmeter_percentage") Double subjectKMeterPercentage,
      @JsonProperty("chapter_kmeter") List<StudentKMeterResponseByChapter> chapterKMeter) {}

  @Builder
  public record StudentKMeterResponseByChapter(
      @JsonProperty("chapter_slug") String chapterSlug,
      @JsonProperty("chapter_name") String chapterName,
      @JsonProperty("exam_id") long examID,
      @JsonProperty("chapter_kmeter_percenetage") Double chapterKMeterPercentage,
      @JsonProperty("subtopic_kmeter") List<StudentKMeterResponseBySubTopic> subTopicKMeter) {}

  @Builder
  public record StudentKMeterResponseBySubTopic(
      @JsonProperty("subtopic_slug") String subTopicSlug,
      @JsonProperty("subtopic_name") String subTopicName,
      @JsonProperty("subtopic_kmeter_percentage") Double subTopicKMeterPercentage,
      @JsonProperty("exam_id") long examID) {}
}
