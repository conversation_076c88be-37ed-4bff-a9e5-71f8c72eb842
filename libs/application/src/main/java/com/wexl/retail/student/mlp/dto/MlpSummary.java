package com.wexl.retail.student.mlp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
public class MlpSummary {

  private String organization;

  private String grade;

  private String section;

  private String subject;

  private String month;

  @JsonProperty("total_mlps_assigned")
  private Integer totalMlpsAssigned;

  @JsonProperty("total_students")
  private Integer totalStudents;

  @JsonProperty("mlp_details")
  private List<MlpDetails> mlpDetails;
}
