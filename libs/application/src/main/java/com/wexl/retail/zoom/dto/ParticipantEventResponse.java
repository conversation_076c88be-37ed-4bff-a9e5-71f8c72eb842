package com.wexl.retail.zoom.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.zoom.domain.ZoomEventType;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class ParticipantEventResponse {

  @JsonProperty("external_event_id")
  private String externalEventId;

  @JsonProperty("event_type")
  private ZoomEventType eventType;

  @JsonProperty("user_name")
  private String userName;

  private String email;

  @JsonProperty("customer_key")
  private String customerKey;

  @JsonProperty("event_time")
  private Timestamp eventTime;

  @JsonProperty("meeting_start_time")
  private Timestamp meetingStartTime;
}
