package com.wexl.retail.student.auth;

import com.wexl.retail.auth.AuthResponse;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import com.wexl.retail.model.LoginMethod;
import com.wexl.retail.model.OtpVerificationRequest;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.util.MobileAppUtil;
import com.wexl.retail.util.ReCaptchaService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/auth/student")
public class StudentAuthController {

  @Autowired AuthService authService;
  @Autowired UserRepository userRepository;
  @Autowired StudentAuthService studentAuthService;
  @Autowired ReCaptchaService captchaService;

  @PostMapping("/verify_email")
  public AuthResponse verifyEmailOtp(
      @Valid @RequestBody OtpVerificationRequest verificationRequest,
      @RequestHeader(HttpHeaders.USER_AGENT) String userAgent) {

    User student = studentAuthService.verifyEmailOtp(verificationRequest);
    if (student != null) {
      var requestComingFromMobileApp = MobileAppUtil.requestComingFromMobileApp(userAgent);
      String jwtToken =
          authService.generateStudentAccessToken(
              requestComingFromMobileApp, student, LoginMethod.USERNAME_PASSWORD);
      return AuthResponse.builder().accessToken(jwtToken).build();
    } else {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidVerificationCode");
    }
  }

  @IsOrgAdmin
  @DeleteMapping("/{studentAuthId}:hardDelete")
  public ResponseEntity<Void> deleteStudent(@PathVariable String studentAuthId) {

    studentAuthService.deleteStudent(studentAuthId);
    return ResponseEntity.ok().build();
  }
}
