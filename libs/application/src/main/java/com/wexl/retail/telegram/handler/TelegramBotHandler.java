package com.wexl.retail.telegram.handler;

import java.util.Map;
import java.util.stream.Collectors;

public interface TelegramBotHandler {

  /*
     Provides logic to handle the incoming request
  */
  String handleCommand(String[] params);

  /*
     The command name,
     used by client to invoke handleCommand()
  */
  String getCommandName();

  /*
     Returns the format of the present handler command
  */
  String getHelpText();

  default String convertToString(Map<String, Object> map) {
    return map.keySet().stream()
        .map(key -> formattedKey(key) + ":" + map.get(key))
        .collect(Collectors.joining("\n"));
  }

  default String formattedKey(String keyName) {
    return "<b>" + keyName + "</b>";
  }

  default boolean isValid(String[] params, int expectedLength) {
    return params != null && params.length == expectedLength;
  }
}
