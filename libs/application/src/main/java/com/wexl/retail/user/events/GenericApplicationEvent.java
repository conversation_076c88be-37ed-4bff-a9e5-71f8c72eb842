package com.wexl.retail.user.events;

import com.wexl.retail.model.User;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class GenericApplicationEvent extends ApplicationEvent {

  private String name;
  private transient User badgeUser;

  public GenericApplicationEvent(Object source, User badgeUser, String name) {
    super(source);
    this.name = name;
    this.badgeUser = badgeUser;
  }
}
