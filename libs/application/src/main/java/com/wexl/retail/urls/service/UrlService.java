package com.wexl.retail.urls.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.scorm.service.ScormService;
import com.wexl.retail.urls.dto.UrlsDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class UrlService {

  private final ScormService scormService;

  public UrlsDto.UrlResponse createScormUrl(UrlsDto.UrlRequest request) {
    var scormDefId = validateRequest(request);
    try {
      var scormPlayerUrl = scormService.createScormPlayerUrl();
      return UrlsDto.UrlResponse.builder().url(scormPlayerUrl).build();

    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.urls.generate", e);
    }
  }

  public String validateRequest(UrlsDto.UrlRequest request) {
    if (request == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidUrl");
    }
    String[] path;
    path = request.name().split(":");
    if (path.length != 3) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidUrl");
    }
    return path[2];
  }
}
