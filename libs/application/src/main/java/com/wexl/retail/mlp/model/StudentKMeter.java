package com.wexl.retail.mlp.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "students_knowledge_meter")
public class StudentKMeter extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private Long studentId;

  private Long examId;

  private Long examType;

  private Double knowledgePercentage;

  private LocalDateTime examCreatedAt;

  private LocalDateTime examCompletedAt;

  private Float marksScored;

  private Float totalMarks;

  private String subjectSlug;

  private String subjectName;

  private String chapterSlug;

  private String chapterName;

  private String subTopicSlug;

  private String subTopicName;

  private String orgSlug;
}
