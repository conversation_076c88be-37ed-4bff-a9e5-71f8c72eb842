package com.wexl.retail.device.model;

import com.wexl.retail.model.Model;
import com.wexl.retail.model.User;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.Type;

@Entity
@Data
@Table(name = "device_info")
public class Device extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "device-info-sequence-generator")
  @SequenceGenerator(
      name = "device-info-sequence-generator",
      sequenceName = "device_info_seq",
      allocationSize = 1)
  private long id;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "userId")
  private User userDetails;

  private String appName;
  private String appVersion;
  private String appBuildNumber;
  private String appPackageName;
  private String deviceName;
  private String deviceVersion;
  private String deviceModel;

  @Type(JsonType.class)
  @Column(name = "device_metadata", columnDefinition = "jsonb")
  private DeviceMetadata deviceMetadata;

  private String guid;
}
