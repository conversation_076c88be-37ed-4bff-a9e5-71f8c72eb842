package com.wexl.retail.student.exam.competitive.processor;

import com.wexl.retail.student.exam.competitive.dto.CompetitiveExamsDto;
import com.wexl.retail.test.school.domain.TestCategory;
import com.wexl.retail.test.school.domain.TestDefinition;

public interface CompetitiveExamProcessor {
  boolean supports(TestCategory testCategory);

  TestDefinition buildTestDefinition(CompetitiveExamsDto.Request request, String orgSlug);

  int getTotalMarks();
}
