package com.wexl.retail.content.rest.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Subscription {
  @JsonProperty("PlanAmount")
  private int planAmount;

  @JsonProperty("PlanName")
  private String planName;

  private int id;

  @JsonProperty("NumberOfDays")
  private int numberOfDays;
}
