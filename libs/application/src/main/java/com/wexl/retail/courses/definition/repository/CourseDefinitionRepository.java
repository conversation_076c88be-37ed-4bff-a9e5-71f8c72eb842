package com.wexl.retail.courses.definition.repository;

import com.wexl.retail.courses.definition.model.CourseDefinition;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CourseDefinitionRepository extends JpaRepository<CourseDefinition, Long> {

  @Query(
      value =
          """
                  select * from course_definition where org_slug = :orgSlug\s
                  and deleted_at is null and is_private = false\s
                  and (cast(:categoryId as varchar) is null or
                                course_category_id =:categoryId) order by created_at desc
                  """,
      nativeQuery = true)
  List<CourseDefinition> getAllCourseDefinitions(String orgSlug, Long categoryId);

  @Query(
      value =
          """
          select cd from CourseDefinition cd \
          left join fetch cd.owner own where own.id = :teacherId
          order by cd.updatedAt desc\
          """)
  List<CourseDefinition> getCoursesCreatedByTeacher(long teacherId);

  @Query(
      value =
          """
          select cd.* from  course_definition cd
          inner join course_schedule cs on cd.id= cs.course_definition_id
          inner join course_schedule_inst csi on csi.course_schedule_id=cs.id
          where csi.student_id =:studentId
                          """,
      nativeQuery = true)
  List<CourseDefinition> getCourseForStudent(long studentId);

  @Query(
      value =
          """
          select cd.* from  course_definition cd
          where id in (:courseDefinitionIds) order by name asc
                          """,
      nativeQuery = true)
  List<CourseDefinition> getByIdOrderByNameAsc(List<Long> courseDefinitionIds);
}
