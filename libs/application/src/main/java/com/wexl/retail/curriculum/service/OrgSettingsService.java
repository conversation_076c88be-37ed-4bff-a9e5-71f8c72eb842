package com.wexl.retail.curriculum.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.model.OrgSettings;
import com.wexl.retail.model.Organization;
import com.wexl.retail.model.UiConfig;
import com.wexl.retail.organization.auth.OrgCurriculumRequest;
import com.wexl.retail.organization.dto.Curriculum;
import com.wexl.retail.organization.dto.CurriculumBoard;
import com.wexl.retail.organization.dto.CurriculumGrade;
import com.wexl.retail.organization.dto.CurriculumSubject;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.util.StrapiService;
import java.util.*;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
@Data
public class OrgSettingsService {
  private final StrapiService strapiService;
  private final OrganizationRepository organizationRepository;

  public Curriculum transformBoards(List<OrgCurriculumRequest> eduBoards) {
    Set<String> boardSlugs =
        eduBoards.stream().map(OrgCurriculumRequest::getBoardSlug).collect(Collectors.toSet());

    List<CurriculumBoard> boardHierarchy = new ArrayList<>();
    boardSlugs.parallelStream()
        .forEach(
            boardSlug -> {
              CurriculumBoard eduBoard = new CurriculumBoard();
              eduBoard.setSlug(boardSlug);
              Set<String> mappedGradeSlugs = getMappedGradeSlugs(boardSlug, eduBoards);
              eduBoard.setGrades(mapGradeToBoard(mappedGradeSlugs, boardSlug, eduBoards));
              boardHierarchy.add(eduBoard);
            });

    return Curriculum.builder().boards(boardHierarchy).build();
  }

  public Set<String> getMappedGradeSlugs(String boardSlug, List<OrgCurriculumRequest> eduBoards) {
    return eduBoards.stream()
        .filter(eduBoard -> eduBoard.getBoardSlug().equals(boardSlug))
        .map(OrgCurriculumRequest::getGradeSlug)
        .sorted(Comparator.naturalOrder())
        .collect(Collectors.toCollection(LinkedHashSet::new));
  }

  public List<CurriculumGrade> mapGradeToBoard(
      Set<String> gradeSlugs, String boardSlug, List<OrgCurriculumRequest> eduBoards) {
    List<CurriculumGrade> grades = new ArrayList<>();
    gradeSlugs.forEach(
        gradeSlug ->
            grades.add(
                CurriculumGrade.builder()
                    .slug(gradeSlug)
                    .subjects(
                        mapSubjectToGradeAndBoard(
                            getMappedSubjectSlugs(boardSlug, gradeSlug, eduBoards)))
                    .build()));
    return grades;
  }

  public Set<String> getMappedSubjectSlugs(
      String boardSlug, String gradeSlug, List<OrgCurriculumRequest> eduBoards) {

    Set<String> subjectSlugs = new HashSet<>();
    eduBoards.forEach(
        eduBoard -> {
          if ((Objects.equals(eduBoard.getGradeSlug(), gradeSlug))
              && (Objects.equals(eduBoard.getBoardSlug(), boardSlug))) {
            subjectSlugs.add(eduBoard.getSubjectSlug());
          }
        });
    return subjectSlugs;
  }

  public List<CurriculumSubject> mapSubjectToGradeAndBoard(Set<String> subjectSlugs) {
    List<CurriculumSubject> subjects = new ArrayList<>();
    subjectSlugs.forEach(
        subjectSlug -> subjects.add(CurriculumSubject.builder().slug(subjectSlug).build()));
    return subjects;
  }

  public UiConfig getUiConfigByOrgSlug(String org) {
    var organization = validateOrganizaiton(org);
    try {
      if (Boolean.TRUE.equals(organization.getSettingsMigration())) {
        return buildUiConfigResponse(organization);
      }
      Organization strapiOrg = strapiService.getOrganizationBySlug(org);
      var uiConfig = buildUiConfig(strapiOrg);
      uiConfig.setOrgProfile(organization.getProfile().getId());
      organization.setLogo(uiConfig.getLogo());
      organization.setLogoutUrl(uiConfig.getLogoutUrl());
      organization.setMobileLogo(uiConfig.getMobileLogo());
      organization.setTheme(uiConfig.getTheme());
      organization.setWebsite(uiConfig.getWebsite());
      organization.setStudentUrl(uiConfig.getStudentUrl());
      organization.setOrgProfile(uiConfig.getOrgProfile());
      organization.setSettingsMigration(Boolean.TRUE);
      organizationRepository.save(organization);
      return buildUiConfigResponse(organization);
    } catch (Exception e) {
      log.error("Error occurred while getting UI Config :" + e.getMessage(), e);
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.OrganizationUiConfig.Slug",
          new String[] {organization.getName()});
    }
  }

  private UiConfig buildUiConfigResponse(
      com.wexl.retail.organization.model.Organization organization) {
    return UiConfig.builder()
        .logo(organization.getLogo())
        .theme(organization.getTheme())
        .logoutUrl(organization.getLogoutUrl())
        .mobileLogo(organization.getMobileLogo())
        .website(organization.getWebsite())
        .studentUrl(organization.getStudentUrl())
        .instituteName(organization.getName())
        .orgProfile(organization.getOrgProfile())
        .profileId(organization.getProfile().getId())
        .build();
  }

  private UiConfig buildUiConfig(Organization organization) {
    OrgSettings settings = organization.getSettings();
    if (settings == null) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.OrganizationFind.Slug",
          new String[] {organization.getName()});
    }
    if (settings.getAppConfig() != null) {
      settings.getAppConfig().setInstituteName(organization.getName());
    }
    return settings.getAppConfig();
  }

  public com.wexl.retail.organization.model.Organization validateOrganizaiton(String org) {
    var organization = organizationRepository.findBySlug(org);
    if (Objects.isNull(org)) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.OrganizationFind.Slug", new String[] {org});
    }
    return organization;
  }
}
