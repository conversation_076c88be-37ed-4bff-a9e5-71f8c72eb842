package com.wexl.retail.student.mlp.service;

import com.wexl.retail.content.ContentService;
import com.wexl.retail.courses.step.dto.AssetResponse;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Order(100)
@Component
@RequiredArgsConstructor
public class MlpDefaultAssetHandler implements MlpAssetHandler {
  private final ContentService contentService;

  @Override
  public List<AssetResponse> getAssets(String orgSlug, String teacherAuthId, String chapterSlug) {
    return contentService.getAllAssets(chapterSlug, teacherAuthId, orgSlug);
  }
}
