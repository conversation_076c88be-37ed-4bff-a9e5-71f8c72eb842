package com.wexl.retail.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wexl.retail.device.model.Device;
import com.wexl.retail.globalprofile.model.RoleTemplate;
import com.wexl.retail.util.Constants;
import jakarta.persistence.*;
import java.sql.Timestamp;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "users", schema = "public")
public class User extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "user-sequence-generator")
  @SequenceGenerator(
      name = "user-sequence-generator",
      sequenceName = "users_seq",
      allocationSize = 1)
  private long id;

  private String authUserId;
  private String firstName;
  private String lastName;
  private String email;
  private Integer age;
  private String userName;
  private String password;
  private String profilePicture;
  private String mobileNumber;
  private Boolean emailVerified;
  private Boolean mobileVerified;
  private String organization;
  private Boolean isDeleted;
  private String firebaseToken;

  private Timestamp expiredAt;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
  private Date lastLogin;

  private String guid;

  private String profileImage;

  @Enumerated(EnumType.STRING)
  private Gender gender;

  @Enumerated(EnumType.STRING)
  @Column(length = 20)
  private UserVerificationStatus verificationStatus;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "modified_by")
  private User modifiedBy;

  @OneToOne(fetch = FetchType.LAZY, mappedBy = "user", cascade = CascadeType.ALL)
  private Addresses addresses;

  @OneToOne(fetch = FetchType.LAZY, mappedBy = "userInfo", cascade = CascadeType.ALL)
  private Student studentInfo;

  @OneToOne(fetch = FetchType.LAZY, mappedBy = "userInfo", cascade = CascadeType.ALL)
  private Teacher teacherInfo;

  @OneToOne(fetch = FetchType.LAZY, mappedBy = "userDetails", cascade = CascadeType.ALL)
  private Device deviceInfo;

  @ManyToOne(fetch = FetchType.LAZY)
  private RoleTemplate roleTemplate;

  private String countryCode = Constants.DEFAULT_COUNTRY_CODE;

  private String externalRef;

  @Column(name = "bet_user_type_id")
  private Long betUserType;

  @Column(name = "modules_unlocked")
  private Boolean modulesUnlocked = Boolean.FALSE;
}
