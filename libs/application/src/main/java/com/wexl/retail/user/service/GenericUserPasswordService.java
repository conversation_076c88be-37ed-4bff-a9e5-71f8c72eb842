package com.wexl.retail.user.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.idp.UserPasswordService;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.UserRepository;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import java.security.Key;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Base64;
import java.util.Date;
import java.util.Objects;
import javax.crypto.spec.SecretKeySpec;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class GenericUserPasswordService implements UserPasswordService {

  public static final String LOGIN_ERROR_MESSAGE =
      "Unable to process login credentials. Please logout and login again";
  public static final String UUID_REGEX =
      "^[0-9a-fA-F]{8}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{12}$";

  private final UserRepository userRepository;
  private final PasswordEncoder passwordEncoder;

  @Value("${jwt.tokenSecret}")
  private String tokenSecret;

  @Override
  public String getAuthUserId(String username) {
    final User userByUserName = userRepository.getUserByUserName(username);
    return userByUserName.getAuthUserId();
  }

  @Override
  public void updateUserPassword(String username, String encodedPassword) {
    User user;
    // username follows the regex for authUserId
    if (username.matches(UUID_REGEX)) {
      user = userRepository.getUserByAuthUserId(username);
    } else {
      user = userRepository.getUserByUserName(username);
    }

    if (Objects.isNull(user) || StringUtils.isBlank(encodedPassword)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.invalidInput");
    }
    user.setPassword(encodedPassword);
    userRepository.save(user);
  }

  @Override
  public String retrieveUserNameFromToken(String token) {
    Key signingKey =
        new SecretKeySpec(
            Base64.getDecoder().decode(tokenSecret), SignatureAlgorithm.HS256.getJcaName());
    try {
      var jwtClaims =
          Jwts.parserBuilder().setSigningKey(signingKey).build().parseClaimsJws(token).getBody();
      return jwtClaims.getSubject();
    } catch (Exception exception) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, LOGIN_ERROR_MESSAGE, exception);
    }
  }

  @Override
  public String createJwtToken(String username, String password) {
    final User user = userRepository.getUserByUserName(username);
    if (user == null) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, LOGIN_ERROR_MESSAGE);
    }
    final String encodedPassword = user.getPassword();

    if (!passwordEncoder.matches(password, encodedPassword)) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, LOGIN_ERROR_MESSAGE);
    }

    return getIdTokenForUser(user);
  }

  private String getIdTokenForUser(User user) {
    var tokenSignInKey =
        new SecretKeySpec(
            Base64.getDecoder().decode(tokenSecret), SignatureAlgorithm.HS256.getJcaName());
    var currentDate = Instant.now();
    return Jwts.builder()
        .setSubject(user.getAuthUserId())
        .setId(String.valueOf(user.getId()))
        .setIssuedAt(Date.from(currentDate))
        .setExpiration(Date.from(currentDate.plus(10, ChronoUnit.MINUTES)))
        .signWith(tokenSignInKey, SignatureAlgorithm.HS256)
        .compact();
  }
}
