package com.wexl.retail.qrcode.service;

import static com.wexl.retail.util.Constants.*;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.*;
import com.wexl.retail.commons.storage.S3FileUploadResult;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.email.TwilioSmsService;
import com.wexl.retail.mobile.repository.MobileConfigRepository;
import com.wexl.retail.model.Gender;
import com.wexl.retail.model.Organization;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.UserVerificationStatus;
import com.wexl.retail.organization.admin.StudentRequest;
import com.wexl.retail.qrcode.domain.QrCode;
import com.wexl.retail.qrcode.dto.QrCodeDetails;
import com.wexl.retail.qrcode.dto.QrCodeGenRequest;
import com.wexl.retail.qrcode.dto.QrCodeStatus;
import com.wexl.retail.qrcode.dto.SignupRequest;
import com.wexl.retail.qrcode.dto.StudentSignInDetails;
import com.wexl.retail.qrcode.repository.QrCodeRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.student.auth.StudentAuthService;
import com.wexl.retail.util.StrapiService;
import java.io.*;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class QrCodeService {

  public static final String QR_CODES = "qr_codes";
  private final DateTimeUtil dateTimeUtil;
  private final StorageService storageService;
  private final QrCodeRepository qrCodeRepository;
  private final StudentAuthService studentAuthService;
  private final StrapiService strapiService;
  private final StudentRepository studentRepository;
  private final TwilioSmsService twilioSmsService;

  private final SectionRepository sectionRepository;
  private final MobileConfigRepository mobileConfigRepository;
  private final ContentService contentService;

  @Value("${app.qrCode.url}")
  private String qrCodeUrl;

  @Value("${app.latestAcademicYear}")
  private String latestAcademicYear;

  public StudentSignInDetails createStudent(SignupRequest signupRequest) {
    Optional<QrCode> optionalQrCode = qrCodeRepository.findFirstByUuid(signupRequest.getUuid());
    if (optionalQrCode.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidQRCode");
    }

    QrCode qrCode = optionalQrCode.get();
    List<Section> sections =
        sectionRepository.getSectionsUsingGradeSlugs(
            List.of(qrCode.getGradeSlug()), qrCode.getOrgSlug());
    if (Objects.isNull(sections) || sections.isEmpty()) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "error.SectionFind.Grade");
    }
    String password = generateNumericPassword();
    final StudentRequest studentRequest =
        StudentRequest.builder()
            .academicYearSlug(latestAcademicYear)
            .firstName(signupRequest.getFullName())
            .lastName(" ")
            .userName(signupRequest.getMobileNumber())
            .schoolName(signupRequest.getSchool())
            .gender(Gender.valueOf("OTHER"))
            .gradeSlug(qrCode.getGradeSlug())
            .boardSlug(qrCode.getBoardSlug())
            .password(password)
            .parentFirstName("")
            .parentLastName("")
            .parentEmail("")
            .parentMobileNumber("")
            .section(sections.getFirst().getName())
            .orgSlug(qrCode.getOrgSlug())
            .build();

    Student student =
        studentAuthService.createStudent(studentRequest, optionalQrCode.get().getOrgSlug());
    student.setQrCode(optionalQrCode.get());
    student.getUserInfo().setVerificationStatus(UserVerificationStatus.VERIFIED);
    studentRepository.save(student);

    qrCodeRepository.updateQrCodeStatus(
        optionalQrCode.get().getUuid(), QrCodeStatus.REGISTERED.toString());

    twilioSmsService.sendSmsToMobileNo(
        signupRequest.getMobileNumber(),
        generateSms(
            signupRequest.getMobileNumber(), password, student.getUserInfo().getOrganization()));
    return StudentSignInDetails.builder()
        .userName(studentRequest.getUserName())
        .firstName(studentRequest.getFirstName())
        .lastName(studentRequest.getLastName())
        .password(password)
        .build();
  }

  public String generateNumericPassword() {
    long otp = ThreadLocalRandom.current().nextInt(12345678, 99999999);
    return String.valueOf(otp);
  }

  public String generateSms(String userName, String password, String orgSlug) {
    var optionalPackageName = mobileConfigRepository.getPackageName(orgSlug);
    String packageName = optionalPackageName.orElse("com.wexledu.mobile.school");
    return "Thank you !\n"
        + "https://play.google.com/store/apps/details?id="
        + packageName
        + "\n"
        + "Username:"
        + userName
        + "Password:"
        + password;
  }

  public QrCodeDetails getStudentDetails(String uuid) {
    Optional<QrCode> qrCode = qrCodeRepository.findFirstByUuid(uuid);
    if (qrCode.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidQRCode");
    }
    Organization org;
    try {
      org = strapiService.getOrganizationBySlug(qrCode.get().getOrgSlug());
    } catch (ApiException e) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "error.OrganizationFind.Slug1", e);
    }
    return QrCodeDetails.builder()
        .organization(qrCode.get().getOrgSlug())
        .board(qrCode.get().getBoardSlug())
        .uuid(qrCode.get().getUuid())
        .grade(qrCode.get().getGradeSlug())
        .status(qrCode.get().getStatus())
        .boardName(strapiService.getEduBoardBySlug(qrCode.get().getBoardSlug()).getName())
        .gradeName(contentService.getGradeBySlug(qrCode.get().getGradeSlug()).getName())
        .publisherName(org.getName())
        .build();
  }

  @SneakyThrows
  public S3FileUploadResult genQrCode(QrCodeGenRequest request) {
    var filePath = constructQrCodeS3FilePath(request);
    var doc = new PDDocument();

    var qrCodeList = genQrImagesInPdDoc(request, filePath, doc);

    var outputStream = new ByteArrayOutputStream();
    doc.save(outputStream);
    storageService.uploadFile(
        outputStream.toByteArray(), filePath, MediaType.APPLICATION_PDF_VALUE);
    qrCodeRepository.saveAll(qrCodeList);
    doc.close();

    return S3FileUploadResult.builder()
        .path(filePath)
        .url(storageService.generatePreSignedUrlForFetch(filePath))
        .build();
  }

  private List<QrCode> genQrImagesInPdDoc(QrCodeGenRequest request, String filePath, PDDocument doc)
      throws IOException {
    List<QrCode> qrCodeList = new ArrayList<>();
    for (int i = 0; i < request.getQrsRequired(); i++) {
      var qrCode = genQrCodeFromRequest(request, filePath);
      var qrText = String.format(qrCodeUrl, qrCode.getUuid());
      qrCodeList.add(qrCode);

      drawQrImageToPdDocPage(doc, i, qrText);
    }
    return qrCodeList;
  }

  private void drawQrImageToPdDocPage(PDDocument doc, int qrCount, String qrText)
      throws IOException {
    if (qrCount % 4 == 0) {
      doc.addPage(new PDPage(PDRectangle.A4));
      var pdImage = PDImageXObject.createFromByteArray(doc, newQRCodeImage(qrText), Strings.EMPTY);
      var contents =
          new PDPageContentStream(
              doc, doc.getPage(qrCount / 4), PDPageContentStream.AppendMode.APPEND, false);
      contents.drawImage(
          pdImage,
          PDRectangle.A4.getUpperRightX() - PDRectangle.A4.getWidth(),
          PDRectangle.A4.getUpperRightY() - pdImage.getHeight());
      contents.close();
    } else if (qrCount % 4 == 1) {
      var pdImage = PDImageXObject.createFromByteArray(doc, newQRCodeImage(qrText), Strings.EMPTY);
      var contents =
          new PDPageContentStream(
              doc, doc.getPage(qrCount / 4), PDPageContentStream.AppendMode.APPEND, false);
      contents.drawImage(
          pdImage,
          PDRectangle.A4.getWidth() - pdImage.getWidth(),
          PDRectangle.A4.getUpperRightY() - pdImage.getHeight());
      contents.close();
    } else if (qrCount % 4 == 2) {
      var pdImage = PDImageXObject.createFromByteArray(doc, newQRCodeImage(qrText), Strings.EMPTY);
      var contents =
          new PDPageContentStream(
              doc, doc.getPage(qrCount / 4), PDPageContentStream.AppendMode.APPEND, false);
      contents.drawImage(pdImage, PDRectangle.A4.getLowerLeftX(), PDRectangle.A4.getLowerLeftY());
      contents.close();
    } else {
      var pdImage = PDImageXObject.createFromByteArray(doc, newQRCodeImage(qrText), Strings.EMPTY);
      var contents =
          new PDPageContentStream(
              doc, doc.getPage(qrCount / 4), PDPageContentStream.AppendMode.APPEND, false);
      contents.drawImage(
          pdImage, PDRectangle.A4.getWidth() - pdImage.getWidth(), PDRectangle.A4.getLowerLeftY());
      contents.close();
    }
  }

  private static byte[] newQRCodeImage(String barcodeText) {
    try (ByteArrayOutputStream image = new ByteArrayOutputStream()) {
      QRCodeWriter barcodeWriter = new QRCodeWriter();
      BitMatrix bitMatrix = barcodeWriter.encode(barcodeText, BarcodeFormat.QR_CODE, 200, 200);
      MatrixToImageWriter.writeToStream(bitMatrix, "png", image);
      return image.toByteArray();
    } catch (IOException | WriterException e) {
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.QRCodeGeneration", e);
    }
  }

  @NonNull
  private String constructQrCodeS3FilePath(QrCodeGenRequest request) {
    return request
        .getOrgSlug()
        .concat(BACK_SLASH + request.getBoardSlug())
        .concat(BACK_SLASH + request.getGradeSlug())
        .concat(BACK_SLASH + QR_CODES)
        .concat(BACK_SLASH + dateTimeUtil.getCurrentDate().toString());
  }

  private QrCode genQrCodeFromRequest(QrCodeGenRequest qrCodeGenRequest, String filePath) {
    return QrCode.builder()
        .boardSlug(qrCodeGenRequest.getBoardSlug())
        .gradeSlug(qrCodeGenRequest.getGradeSlug())
        .orgSlug(qrCodeGenRequest.getOrgSlug())
        .uuid(UUID.randomUUID().toString())
        .status(QrCodeStatus.NOT_REGISTERED)
        .s3FilePath(filePath)
        .build();
  }
}
