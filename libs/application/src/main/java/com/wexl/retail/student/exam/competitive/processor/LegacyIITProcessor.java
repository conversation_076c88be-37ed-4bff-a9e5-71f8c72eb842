package com.wexl.retail.student.exam.competitive.processor;

import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.student.exam.competitive.dto.CompetitiveExamsDto;
import com.wexl.retail.test.school.domain.*;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class LegacyIITProcessor extends AbstractCompetitiveExamProcessor {
  private static final int TOTAL_MARKS = 300;

  private final String[] sectionNames =
      new String[] {
        "Physics Section A",
        "Physics Section B",
        "Chemistry Section A",
        "Chemistry Section B",
        "Mathematics Section A",
        "Mathematics Section B"
      };

  @Override
  public boolean supports(TestCategory testCategory) {
    return TestCategory.LEGACYIIT.equals(testCategory);
  }

  @Override
  public int getTotalMarks() {
    return TOTAL_MARKS;
  }

  @Override
  protected List<TestDefinitionSection> buildTestDefinitionSections(
      TestDefinition testDefinition, List<CompetitiveExamsDto.AnswerKeys> answerKeys) {
    List<TestDefinitionSection> sections = new ArrayList<>();
    testDefinition.setTotalMarks(360);
    testDefinition.setNoOfQuestions(90);
    for (int i = 0; i < 6; i++) {
      if (i == 0 || i == 2 || i == 4) {
        sections.add(
            buildSections(
                testDefinition, sectionNames[i], 20L, QuestionType.MCQ, answerKeys, i + 1L));
      } else {
        sections.add(
            buildSections(
                testDefinition, sectionNames[i], 10L, QuestionType.NAT, answerKeys, i + 1L));
      }
    }
    return sections;
  }

  @Override
  protected List<TestQuestion> buildTestQuestions(
      TestDefinitionSection testDefinitionSection,
      String sectionName,
      Long questionCount,
      QuestionType type,
      List<CompetitiveExamsDto.AnswerKeys> answerKeys) {
    List<TestQuestion> questions = new ArrayList<>();
    var answerKeyCount = getAnswerKeyCount(sectionName);
    if (type.equals(QuestionType.MCQ)) {

      for (int i = 0; i < 20; i++) {
        questions.add(
            TestQuestion.builder()
                .testDefinitionSection(testDefinitionSection)
                .mcqAnswer(Long.valueOf(answerKeys.get(answerKeyCount).answer()))
                .negativeMarks(1)
                .questionUuid(
                    generateMcqQuestionUuid(
                        Long.valueOf(answerKeys.get(answerKeyCount).answer()),
                        answerKeys.get(answerKeyCount).questionNumber()))
                .type(QuestionType.MCQ.name())
                .marks(4)
                .build());
        answerKeyCount = answerKeyCount + 1;
      }
    } else {
      for (int i = 0; i < 10; i++) {
        questions.add(
            TestQuestion.builder()
                .testDefinitionSection(testDefinitionSection)
                .natAnswer(Float.valueOf(answerKeys.get(answerKeyCount).answer()))
                .negativeMarks(1)
                .questionUuid(
                    generateNatQuestionUuid(
                        Float.valueOf(answerKeys.get(answerKeyCount).answer()),
                        answerKeys.get(answerKeyCount).questionNumber()))
                .type(QuestionType.NAT.name())
                .marks(4)
                .build());
        answerKeyCount = answerKeyCount + 1;
      }
    }
    return questions;
  }

  private String generateNatQuestionUuid(Float answer, Long questionNumber) {
    DateFormat sdf = new SimpleDateFormat("ddMMyyyyHHmmssSSS");
    String simpleDate = sdf.format(new Date());
    return "%s_%s_%s_%s_%s".formatted("special", "Q" + questionNumber, answer, 4L, simpleDate);
  }

  private Integer getAnswerKeyCount(String sectionName) {
    return switch (sectionName) {
      case "Physics Section A" -> 0;
      case "Physics Section B" -> 20;
      case "Chemistry Section A" -> 30;
      case "Chemistry Section B" -> 50;
      case "Mathematics Section A" -> 60;
      case "Mathematics Section B" -> 80;
      default -> null;
    };
  }
}
