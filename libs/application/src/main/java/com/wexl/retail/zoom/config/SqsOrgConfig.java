package com.wexl.retail.zoom.config;

import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@Data
@ConfigurationProperties(prefix = "app.sqs.zoom")
public class SqsOrgConfig {

  private List<SqsZoomOrgConfig> orgConfig;

  @Data
  public static class SqsZoomOrgConfig {

    private String organization;
    private String authorization;
  }
}
