package com.wexl.retail.student.subject.profiles.dto;

import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@Data
@ConfigurationProperties(prefix = "app.subject-profile-orgs")
public class SubjectProfileOrgs {
  private List<SubjectProfileOrg> config;

  @Data
  public static class SubjectProfileOrg {
    private int id;
    private String source;
    private List<String> target;
  }
}
