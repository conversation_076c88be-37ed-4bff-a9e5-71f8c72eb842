package com.wexl.retail.model;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum LoginDevice {
  WEB("Web"),
  MOBILE("Mobile");

  private final String value;

  public static LoginDevice fromValue(String value) {
    if (value == null || "".equals(value)) {
      throw new IllegalArgumentException("Value cannot be null or empty!");
    }

    for (LoginDevice enumEntry : LoginDevice.values()) {
      if (enumEntry.toString().equalsIgnoreCase(value)) {
        return enumEntry;
      }
    }

    throw new IllegalArgumentException("Do not understand the Type " + value);
  }

  @Override
  public String toString() {
    return this.value;
  }
}
