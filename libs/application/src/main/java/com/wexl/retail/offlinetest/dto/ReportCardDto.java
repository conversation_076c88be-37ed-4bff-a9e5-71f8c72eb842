package com.wexl.retail.offlinetest.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public class ReportCardDto {

  @Builder
  public record Response(Header header, Body body) {}

  @Builder
  public record Header(
      String schoolName,
      String schoolLogo,
      String schoolWaterMark,
      String academicYear,
      String testName,
      String testType,
      String admissionNumber,
      String address,
      String affiliationData,
      String isoDetails,
      String isoData,
      String sectionName,
      Long studentId) {}

  @Builder
  public record Body(
      String name,
      String rollNumber,
      String className,
      String mothersName,
      String fathersName,
      String dateOfBirth,
      String orgSlug,
      String testName,
      String gradeSlug,
      String gradingScale,
      String startDate,
      String classTeacher,
      FirstTable firstTable,
      SecondTable secondTable,
      ThirdTable thirdTable,
      ThirdTable fourthTable,
      Attendance attendance,
      List<AnswerSheet> answerSheet) {}

  @Builder
  public record FirstTable(String title, List<Marks> marks, Totals totals) {}

  @Builder
  public record SecondTable(String title, List<Marks> marks) {}

  @Builder
  public record ThirdTable(String title, List<Marks> marks) {}

  @Builder
  public record Marks(
      String annualExam,
      String total,
      String maximumMarks,
      String marksObtained,
      String gradeObtained,
      Integer sno,
      String subject,
      String internalAssessment,
      Long seqNo,
      String grade,
      String grade1) {}

  @Builder
  public record Totals(
      Double annualExam,
      Long total,
      String grade,
      Double overallPercentage,
      String overallGrade,
      Long totalMaximumMarks,
      Double totalMarksObtained,
      String totalGradeObtained) {}

  @Builder
  public record Attendance(
      Long workingDays, Long daysPresent, Double attendancePercentage, String remarks) {}

  @Builder
  public record TableMarks(
      List<Marks> firstTableMarks,
      List<Marks> secondTableMarks,
      List<Marks> thirdTableMarks,
      List<Marks> forthTableMarks) {}

  @Builder
  public record Request(
      @JsonProperty("section_uuid") String sectionUuid,
      @JsonProperty("offline_test_definition_id") Long offlineTestDefinitionId,
      @JsonProperty("child_org") String childOrg,
      @JsonProperty("term_id") Long termId,
      @JsonProperty("with_marks") Boolean withMarks,
      @JsonProperty("student_auth_id") String studentAuthId,
      @JsonProperty("template_id") Long templateId) {}

  @Builder
  public record AnswerSheet(Long answerMarks, Long startingSequenceNo, Long endingSequenceNo) {}
}
