package com.wexl.retail.curriculum.controller;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.caching.CacheConstants;
import com.wexl.retail.curriculum.service.CurriculumService;
import com.wexl.retail.model.ContentProvider;
import com.wexl.retail.model.EduBoard;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.CacheControl;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/orgs/{org}/curriculum")
public class CurriculumController {

  @Autowired private CurriculumService curriculumService;

  @Autowired private AuthService authService;

  @GetMapping
  public ResponseEntity<List<EduBoard>> getBoardsHierarchy(final @PathVariable("org") String org) {
    return ResponseEntity.ok()
        .cacheControl(CacheControl.maxAge(CacheConstants.MEDIUM))
        .body(curriculumService.getBoardsHierarchy(org));
  }

  @GetMapping("/{content_type}")
  public ResponseEntity<List<ContentProvider>> getContentProviders(
      @PathVariable String org, @PathVariable("content_type") String contentType) {

    return ResponseEntity.ok()
        .cacheControl(CacheControl.maxAge(CacheConstants.MEDIUM))
        .body(curriculumService.getContentProviders(org, contentType));
  }

  @GetMapping("/{provider_slug}/{content_type}")
  public ResponseEntity<List<EduBoard>> getProvidersHierarchy(
      @PathVariable String org,
      @PathVariable("provider_slug") String providerSlug,
      @PathVariable("content_type") String contentType) {

    return ResponseEntity.ok()
        .cacheControl(CacheControl.maxAge(CacheConstants.MEDIUM))
        .body(curriculumService.getProvidersHierarchy(org, providerSlug, contentType));
  }
}
