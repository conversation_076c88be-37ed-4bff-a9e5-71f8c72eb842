package com.wexl.retail.test.school.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TestDefinitionMetadata {
  private List<ChapterMetadata> chapters;
  private List<SubTopicMetadata> subtopics;
  private VideoExplanationMetadata explanationVideo;

  @JsonProperty("asset_slug")
  private String assetSlug;

  @Builder
  @Data
  public static class VideoExplanationMetadata {
    private String altVimeoLink;
    private String videoLink;
  }
}
