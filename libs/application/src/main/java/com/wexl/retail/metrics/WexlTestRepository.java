package com.wexl.retail.metrics;

import com.wexl.retail.student.exam.Exam;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface WexlTestRepository extends JpaRepository<Exam, Long> {

  @Query(
      value =
          """
                      select concat(u.first_name,' ',u.last_name) as Name,
                                   e.start_time as Date, e.subject_name as Subject,e.chapter_name as Chapter,
                                   e.no_of_questions as NoOfQuestion,
                                   e.marks_scored as MarksScored,
                                   e.total_marks as TotalMarks,
                                   (e.marks_scored*100/e.total_marks) as Percentage,
                                   se.grade_name as GradeName from exams e
                                   inner join sections se on se.id=e.section_id
                                   inner join students ss on ss.id=e.student_id
                                   inner join users u on u.id=ss.user_id
                                   where e.exam_type=:examType
                                   and to_char(e.start_time , 'YYYY-MM-DD') between :fromDate and :toDate
                                   and e.end_time is not null
                                   and (cast((:subjectSlugs) as varChar) is null or e.subject_slug in (:subjectSlugs))
                                   and (cast((:gradeSlugs) as varChar) is null or se.grade_slug in (:gradeSlugs))
                                   and u.organization=:orgSlug
                                   group by e.chapter_name,u.first_name,u.last_name,e.start_time,e.subject_name,e.chapter_name,e.no_of_questions,
                                   e.marks_scored,e.total_marks,se.grade_name
                           \s""",
      nativeQuery = true)
  List<WexlTestData> getWexlTestsReport(
      String orgSlug,
      String fromDate,
      String toDate,
      List<String> subjectSlugs,
      List<String> gradeSlugs,
      long examType);
}
