package com.wexl.retail.announcement.controller;

import static com.wexl.retail.util.Constants.NOTIFICATION_MESSAGE;

import com.wexl.retail.announcement.AnnouncementService;
import com.wexl.retail.announcement.dto.AnnouncementRequest;
import com.wexl.retail.announcement.dto.AnnouncementType;
import com.wexl.retail.announcement.dto.AssigneeMode;
import com.wexl.retail.commons.security.annotation.IsTeacher;
import com.wexl.retail.model.Student;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.services.StudentService;
import com.wexl.retail.util.NotificationUtils;
import com.wexl.retail.util.ValidationUtils;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgId}/sections/{sectionId}/announcements")
public class AppAnnouncementController {

  private final List<AnnouncementService> announcementServices;
  private final NotificationUtils notificationUtils;
  private final StudentRepository studentRepository;
  private final StudentService studentService;
  private final ValidationUtils validationUtils;

  @PostMapping
  @IsTeacher
  public void getSectionActivities(
      @PathVariable("orgId") String orgSlug,
      @PathVariable("sectionId") String sectionUuid,
      @RequestBody AnnouncementRequest announcementRequest) {
    announcementServices.getFirst().createAnnouncement(announcementRequest, sectionUuid);
    if (!announcementRequest.getMaterial().getAnnouncementType().equals(AnnouncementType.POST)) {
      sendNotification(announcementRequest, orgSlug, sectionUuid);
    }
  }

  private void sendNotification(
      AnnouncementRequest announcementRequest, String orgSlug, String sectionUuid) {
    var message =
        NOTIFICATION_MESSAGE.formatted(announcementRequest.getMaterial().getAnnouncementType());
    var students = getStudentIds(announcementRequest, sectionUuid);
    notificationUtils.sendNotification(message, students, orgSlug, null);
  }

  private List<Student> getStudentIds(AnnouncementRequest announcementRequest, String sectionUuid) {
    if (announcementRequest.getAssigneeMode().equals(AssigneeMode.ALL_STUDENTS.toString())) {
      var section = validationUtils.findSectionByUuid(sectionUuid);
      return studentRepository.getStudentsBySectionAndDeletedAtIsNull(section);
    }
    return studentRepository.getStudentsByUserAuthId(
        announcementRequest.getIndividualStudentOptions().getStudentAuthIds());
  }
}
