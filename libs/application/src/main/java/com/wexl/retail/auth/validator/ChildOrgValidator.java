package com.wexl.retail.auth.validator;

import static com.wexl.retail.auth.AuthUtil.isTeacher;

import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.teacher.orgs.TeacherOrgsRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class ChildOrgValidator implements RoleValidator {

  private final TeacherOrgsRepository teacherOrgsRepository;
  private final UserRepository userRepository;

  public boolean isValidRole(User user, String childOrgSlug) {
    User teacherUser = userRepository.getUserByAuthUserId(user.getAuthUserId());
    Teacher teacher = teacherUser.getTeacherInfo();
    return isChildOrg(teacher.getId(), childOrgSlug) && isTeacher(user);
  }

  private boolean isChildOrg(long teacherId, String childOrgSlug) {
    List<String> childOrgs = teacherOrgsRepository.getChildOrgsByTeacherId(teacherId);
    return childOrgs.parallelStream().anyMatch(childOrg -> childOrg.equals(childOrgSlug));
  }
}
