package com.wexl.retail.persons.controller;

import com.wexl.retail.persons.dto.PersonsDto;
import com.wexl.retail.persons.service.PersonService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@Slf4j
public class PersonController {

  private final PersonService personService;

  @PostMapping(value = "orgs/{orgSlug}/persons")
  public void createPerson(
      @RequestBody PersonsDto.PersonsRequest request, @PathVariable String orgSlug) {

    personService.createPerson(request, orgSlug);
  }

  @GetMapping("orgs/{orgSlug}/persons")
  public List<PersonsDto.PersonsResponse.Persons> getAllPersons(@PathVariable String orgSlug) {
    return personService.getAllPersonsOfOrg(orgSlug);
  }

  @GetMapping("orgs/{orgSlug}/persons/{personId}")
  public PersonsDto.PersonsResponse.PersonDetails getPersonDetails(
      @PathVariable Long personId, @PathVariable String orgSlug) {

    return personService.getPersonDetails(orgSlug, personId);
  }

  @DeleteMapping("orgs/{orgSlug}/persons/{personId}")
  @ResponseStatus(HttpStatus.ACCEPTED)
  public void deletePerson(@PathVariable String orgSlug, @PathVariable Long personId) {

    personService.deletePerson(orgSlug, personId);
  }

  @PutMapping("orgs/{orgSlug}/persons/{personId}")
  @ResponseStatus(HttpStatus.OK)
  public void editPerson(
      @PathVariable Long personId,
      @PathVariable String orgSlug,
      @RequestBody PersonsDto.EditPersonsRequest request) {

    personService.editPerson(orgSlug, request, personId);
  }
}
