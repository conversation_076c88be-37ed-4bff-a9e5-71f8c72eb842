package com.wexl.retail.organization.admin.teacher;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import java.util.List;
import lombok.Builder;

public record TeacherDto() {

  @Builder
  public record CountryResponse(String code, String name, Long id) {}

  public record UpdateExternalRefRequest(
      @NotBlank String email,
      @NotBlank @JsonProperty("org_slug") String orgSlug,
      @NotBlank @JsonProperty("external_ref") String externalRef) {}

  @Builder
  public record TeacherResponseWithMetrics(
      @JsonProperty("email") String email,
      @JsonProperty("first_name") String firstName,
      @JsonProperty("last_name") String lastName,
      @JsonProperty("mobile_number") String mobileNumber,
      @JsonProperty("org_admin") boolean orgAdmin,
      @JsonProperty("last_login_time") Long lastLoginTime,
      @JsonProperty("subject_preferences") List<String> subjectPreferences,
      @JsonProperty("boards") List<Board> boards) {
    public record Board(
        @JsonProperty("board_name") String boardName, @JsonProperty("grades") List<Grade> grades) {}

    public record Grade(
        @JsonProperty("grade_slug") String gradeSlug,
        @JsonProperty("sections") List<SectionWithSubjects> sections) {}

    public record SectionWithSubjects(
        @JsonProperty("section_name") String sectionName,
        @JsonProperty("subjects") List<String> subjects) {}
  }
}
