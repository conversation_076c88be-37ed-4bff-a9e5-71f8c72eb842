package com.wexl.retail.subjects.repository;

import com.wexl.retail.subjects.model.SubjectsMetaData;
import com.wexl.retail.subjects.model.SubjectsMetadataStudents;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface SubjectsMetadataStudentsRepository
    extends JpaRepository<SubjectsMetadataStudents, Long> {

  List<SubjectsMetadataStudents> findByStudentId(Long studentId);

  List<SubjectsMetadataStudents> findByStudentIdAndSubjectsMetaData(
      Long studentId, SubjectsMetaData subjectsMetaData);

  @Query(
      value =
          """
          select sms.* from subject_metadata_students sms
          join students s on s.id = sms.student_id
          join users u on u.id = s.user_id
          where sms.subject_metadata_id =:subjectMetaDataId
          and (CAST(:sectionId AS VARCHAR) IS NULL OR s.section_id = :sectionId) and s.deleted_at  is null and u.deleted_at  is null""",
      nativeQuery = true)
  List<SubjectsMetadataStudents> getSubjectMetaDataStudents(Long subjectMetaDataId, Long sectionId);
}
