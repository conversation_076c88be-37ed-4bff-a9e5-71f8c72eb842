package com.wexl.retail.pdf.viewer.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DrawingPositionRequest {
  private long id;
  private long oldId;
  private double origX;
  private double origY;
  private double lastX;
  private double lastY;

  @JsonProperty("x")
  private double cordX;

  @JsonProperty("y")
  private double cordY;
}
