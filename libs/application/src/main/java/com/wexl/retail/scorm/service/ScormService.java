package com.wexl.retail.scorm.service;

import static java.lang.String.format;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.coursecontent.domain.ScormDefinition;
import com.wexl.retail.coursecontent.repository.ScormDefintionRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.scorm.domain.ScormInst;
import com.wexl.retail.scorm.domain.ScormInstAttribute;
import com.wexl.retail.scorm.repository.ScormInstAttributeRepository;
import com.wexl.retail.scorm.repository.ScormInstRepository;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ScormService {

  @Value("${app.scorm.playerLaunchUrl:https://blah}")
  private String playerLaunchUrl;

  // NOSONAR
  //
  //  @Value("${app.scorm.cfKeyPairId}")
  //  private String cfKeyPairId;
  //
  //  @Value("classpath:private_key.der")
  //  private Resource privateKeyFilePath;
  //
  //  @Value("${app.scorm.playerUrl}")
  //  private String scormPlayerUrl;
  // NOSONAR

  private final ScormDefintionRepository scormDefintionRepository;
  private final UserRepository userRepository;
  private final StudentRepository studentRepository;
  private final ScormInstRepository scormInstRepository;
  private final AuthService authService;

  private final ScormInstAttributeRepository scormInstAttributeRepository;

  private static final String COURSES = "courses";
  private static final String STUDENT_ID = "cmi.core.student_id";
  private static final String SCORM_DEFINITION_ID = "cmi.core.scorm_definition_id";
  private static final String LESSON_STATUS = "cmi.core.lesson_status";
  private static final Integer MAX_WIDTH = 1500;

  public void commitCourse(Map<String, Object> request) {
    var scormInst = saveScormInstData(request);
    saveScormInstAttributeData(request, scormInst);
  }

  public ScormInst saveScormInstData(Map<String, Object> request) {

    final var studentUser =
        userRepository.findById(Long.parseLong((String) request.get(STUDENT_ID)));
    if (studentUser.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidUser");
    }

    Optional<ScormDefinition> scormDefinition =
        scormDefintionRepository.findById(Long.valueOf((String) request.get(SCORM_DEFINITION_ID)));

    if (scormDefinition.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidScromDefId");
    }
    var scormInstData =
        scormInstRepository.findByScormDefinitionAndUser(scormDefinition.get(), studentUser.get());

    if (scormInstData == null) {
      ScormInst data =
          ScormInst.builder()
              .scormDefinition(scormDefinition.get())
              .user(studentUser.get())
              .lessonStatus((String) request.get(LESSON_STATUS))
              .build();
      return scormInstRepository.save(data);
    } else {
      scormInstData.setLessonStatus((String) request.get(LESSON_STATUS));
      return scormInstRepository.save(scormInstData);
    }
  }

  public void saveScormInstAttributeData(Map<String, Object> map, ScormInst scormInst) {
    var scormAttribute = scormInstAttributeRepository.findAllByScormInst(scormInst);
    List<ScormInstAttribute> scormInstAttributes = new ArrayList<>();
    if (scormAttribute == null) {
      for (Map.Entry<String, Object> entry : map.entrySet()) {
        ScormInstAttribute sa = new ScormInstAttribute();
        sa.setScormInst(scormInst);
        sa.setName(entry.getKey());
        sa.setValue((String) getScormAttributeValue(entry.getValue()));
        scormInstAttributes.add(sa);
      }
    } else {
      for (Map.Entry<String, Object> entry : map.entrySet()) {
        var scormInstAttributeData =
            scormAttribute.stream().filter(x -> x.getName().equals(entry.getKey())).findFirst();

        if (scormInstAttributeData.isEmpty()) {
          ScormInstAttribute sa = new ScormInstAttribute();
          sa.setScormInst(scormInst);
          sa.setName(entry.getKey());
          sa.setValue((String) getScormAttributeValue(entry.getValue()));
          scormInstAttributes.add(sa);
        } else {
          var scormData = scormInstAttributeData.get();
          scormData.setValue((String) getScormAttributeValue(entry.getValue()));
          scormInstAttributes.add(scormData);
        }
      }
    }
    scormInstAttributeRepository.saveAll(scormInstAttributes);
  }

  public Object getScormAttributeValue(Object inputValue) {
    return (inputValue != null && inputValue.toString().length() > MAX_WIDTH)
        ? inputValue.toString().substring(0, MAX_WIDTH)
        : inputValue;
  }

  public String createPlayerLaunchUrl(String authCode, String idResource) {
    String result = null;
    var scormDefinition = scormDefintionRepository.findById(Long.valueOf(idResource));
    if (scormDefinition.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidDefId");
    }
    var response = authService.retrieveDetailsFromScormToken(authCode);
    final var studentUser = userRepository.getUserByAuthUserId(response.id());
    var scormInst =
        scormInstRepository.findByScormDefinitionAndUser(scormDefinition.get(), studentUser);

    var launchUrl =
        format(
            "%s/%s/%s/%s/%s",
            playerLaunchUrl,
            COURSES,
            response.org(),
            scormDefinition.get().getUnzippedLink(),
            scormDefinition.get().getMainFile());
    if (scormInst == null) {
      result =
          """
              window.dcd_player.scoload({"success":true,"auth_code":"%s","launch_url":"%s","title":"%s","initialize":{"cmi.core.scorm_definition_id":"%s","cmi.core.lesson_status":"incomplete","cmi.core.student_id":"%s","cmi.core.student_name":"%s","cmi.core.credit":"credit","cmi.core.lesson_mode":"normal","cmi.core.entry":"resume","cmi.core.total_time":"0000:12:59.00","cmi.launch_data":null,"cmi.student_data.mastery_score":"80","cmi.student_data.max_time_allowed":"","cmi.student_data.time_limit_action":"","cmi.student_preference.language":"en","cmi.core.session_time":"0000:00:00.00","cmi.suspend_data":"NoIgEghgTgJiA0IDeAdEAbCA7A5mgXGgKZZoC+CIwAjPGgGwDM9AHAEZExswAMjAxgE40AXQA+wcZMoB6fhAAuRHAHsoATwC0AZyLoi\\/BQEsVWBDUYAmeD3jBbDm09u1gbhyPi1bNG5+qe7n5eTvZenjyBYZEhPt7+UR6xdvF+UdHOwd521rQALPCMWYmZSdnARQXZlgmBHnWlma5BjjHltNZFMTEtzv6h1i6FESWt\\/T5d8LkJdhlloUOdtbNzfcm+BbZFNeGzrWvl2yGby26r8xPwm1O7AXuNbaGTVVOnYw37js1z4yk3baMsqEOsEej91rZpsFeo8fItTjCDqEXltXrtERc7EVqlc3mk7J8mitCbCCVcbtiZhigXCbgVKeF0mMITc7ncYb9fIMRvdMcBcqDAaSucUmQ9OUchnkqecaX8Cp1RWT6sqHiEziTOYrbFUZZr1vl\\/sNGcTxesBTrjezZcLDaidgEPiJnfAkCAjFglFBtMZjBB0NoQPgQNRqDxwxHI1HozHY3HI5QoERtCoAK5QfjJoOSRAQQxGABuRAAypmsNATABJODBkBkERAA===","cmi.core.exit":"suspend"}})
              """;
      return String.format(
          result,
          authCode,
          launchUrl,
          scormDefinition.get().getTitle(),
          scormDefinition.get().getId(),
          studentUser.getId(),
          studentUser.getFirstName());
    } else {
      var scormAttributes = scormInstAttributeRepository.findAllByScormInst(scormInst);

      if (!scormAttributes.isEmpty()) {
        JSONObject jsonObject = new JSONObject();
        for (var a : scormAttributes) {
          jsonObject.put(a.getName(), a.getValue());
        }

        result =
            """
                window.dcd_player.scoload({"success":true,"auth_code":"%s","launch_url":"%s","title":"%s","initialize":%s})
                """;
        return String.format(
            result, authCode, launchUrl, scormDefinition.get().getTitle(), jsonObject);
      }
    }
    return launchUrl;
  }

  public String createScormPlayerUrl() {
    // TODO: This will be implemented later as there are no users using this feature.
    return "";
  }

  //  public List<ResponseCookie> addCloudFrontCookiesToResponse() throws Exception {
  //  Bharat - it depends on the code from -> net.java.dev.jets3t:jets3t
  //    Date oneDayFromTodayDate = getTomorrowDate();
  //    String policy =
  //        CloudFrontService.buildPolicyForSignedUrl(null, oneDayFromTodayDate, "0.0.0.0/0", null);
  //
  //    byte[] signatureBytes =
  //        EncryptionUtil.signWithRsaSha1(loadPrivateKey(),
  // policy.getBytes(StandardCharsets.UTF_8));
  //    String urlSafePolicy = makeStringUrlSafe(policy);
  //    String urlSafeSignature = makeBytesUrlSafe(signatureBytes);
  //
  //    log.debug("Policy: {}", urlSafePolicy);
  //    log.debug("Signature: {}", urlSafeSignature);
  //    log.debug("Key-Pair-Id: {}", cfKeyPairId);
  //
  //    return Arrays.asList(
  //        createCookie("CloudFront-Signature", urlSafeSignature),
  //        createCookie("CloudFront-Policy", urlSafePolicy),
  //        createCookie("CloudFront-Key-Pair-Id", cfKeyPairId));
  //  }
  //
  //  private Date getTomorrowDate() {
  //    final LocalDateTime oneDayFromToday = LocalDateTime.now().plus(1, ChronoUnit.DAYS);
  //    return Date.from(oneDayFromToday.atZone(ZoneId.systemDefault()).toInstant());
  //  }
  //
  //  private ResponseCookie createCookie(String key, String value) {
  //    return ResponseCookie.from(key, value)
  //        .httpOnly(true)
  //        .secure(true)
  //        .path("/courses")
  //        .maxAge(24 * 60 * 60)
  //        .build();
  //  }
  //
  //  private String makeStringUrlSafe(String str) {
  //    return ServiceUtils.toBase64(str.getBytes(StandardCharsets.UTF_8))
  //        .replace('+', '-')
  //        .replace('=', '_')
  //        .replace('/', '~');
  //  }
  //
  //  private String makeBytesUrlSafe(byte[] bytes) {
  //    return ServiceUtils.toBase64(bytes).replace('+', '-').replace('=', '_').replace('/', '~');
  //  }
  //
  //  public String createScormPlayerUrl(String scormDefId) throws Exception {
  //    var scormDefData = scormDefintionRepository.findById(Long.valueOf(scormDefId));
  //    if (scormDefData.isEmpty()) {
  //      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidScromDefId");
  //    }
  //    var scormData = scormDefData.get();
  //    var authToken = authService.buildScormAuthToken();
  //    final String formattedUrl =
  //        String.format(
  //            scormPlayerUrl,
  //            scormData.getVersion(),
  //            scormData.getId(),
  //            trimWhiteSpaces(scormData.getTitle()),
  //            authToken);
  //    Date oneDayFromTodayDate = getTomorrowDate();
  //
  //    return CloudFrontService.signUrlCanned(
  //        formattedUrl, // Resource URL or Path
  //        cfKeyPairId, // Certificate identifier,
  //        loadPrivateKey(), // DER Private key data
  //        oneDayFromTodayDate);
  //  }

  //  private String trimWhiteSpaces(String title) {
  //    if (StringUtils.isBlank(title)) {
  //      return "";
  //    }
  //    return title.replace(" ", "");
  //  }
  //
  //  private byte[] loadPrivateKey() {
  //    try (InputStream inputStream = getClass().getResourceAsStream("/private_key.der")) {
  //      assert inputStream != null;
  //      return ServiceUtils.readInputStreamToBytes(inputStream);
  //    } catch (Exception ex) {
  //      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.UnableToConfigure", ex);
  //    }
  //  }
}
