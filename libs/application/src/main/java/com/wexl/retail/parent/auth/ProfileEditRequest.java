package com.wexl.retail.parent.auth;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class ProfileEditRequest {

  private String firstName;
  private String lastName;
  private String parentFirstName;
  private String parentLastName;
  private String parentEmail;
  private String parentMobileNumber;
  private String parentPassword;
  private String captchaCode;
  private Boolean termsAndConditions;
}
