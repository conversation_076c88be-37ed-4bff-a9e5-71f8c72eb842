package com.wexl.retail.student.exam.revision.repository;

import com.wexl.retail.model.Student;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.revision.domain.ExamRevision;
import com.wexl.retail.student.exam.revision.dto.RevisionQuestionsCountQueryResult;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface ExamRevisionRepository extends JpaRepository<ExamRevision, Long> {

  void deleteByExam(Exam exam);

  List<ExamRevision> findByStudentAndQuestionUuid(Student student, String questionUuid);

  @Query(
      value =
          """
          select er.question_uuid from exam_revision er\s
          inner join exams e on e.id = er.exam_id
          where er.student_id = :studentId
          and e.chapter_slug = :chapterSlug
          and  er.exam_revision_status = :revisionStatus\
          """,
      nativeQuery = true)
  List<String> findQuestionsByStudentAndChapter(
      long studentId, String chapterSlug, String revisionStatus);

  @Query(
      value =
          """
          select count(er.question_uuid), e.chapter_slug as chapter
          from exam_revision er
          inner join exams e on e.id = er.exam_id
          where er.student_id = :studentId
          and e.subject_slug = :subjectSlug
          and  er.exam_revision_status = :revisionStatus
          group by e.chapter_slug
          order by e.chapter_slug\
          """,
      nativeQuery = true)
  List<RevisionQuestionsCountQueryResult> findQuestionsCountByChapter(
      long studentId, String subjectSlug, String revisionStatus);
}
