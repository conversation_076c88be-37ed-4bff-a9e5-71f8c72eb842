package com.wexl.retail.zoom.controller;

import com.wexl.retail.zoom.dto.ParticipantEventResponse;
import com.wexl.retail.zoom.service.ZoomEventsService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@ConditionalOnProperty(value = "app.featureFlags.zoomEvents", havingValue = "true")
@RestController
@RequiredArgsConstructor
public class ZoomEventsController {

  private final ZoomEventsService zoomEventsService;

  // NOSONAR
  /*
  private final SqsOrgConfig sqsOrgConfig;
  @SqsListener(
      value = "${app.sqs.zoomEventsUrl}",
      deletionPolicy = SqsMessageDeletionPolicy.ON_SUCCESS)
  public void receiveMessage(SqsMessageHeaders messageHeaders, String message) {
    var sqsZoomOrgConfig =
        sqsOrgConfig.getOrgConfig().stream()
            .filter(orgConfig -> orgConfig.getOrganization().equals(messageHeaders.get("org")))
            .findFirst();
    if (Objects.nonNull(message)
        && sqsZoomOrgConfig.isPresent()
        && sqsZoomOrgConfig.get().getAuthorization().equals(messageHeaders.get("authorization"))) {
      zoomEventsService.processZoomEvents(message);
    }
  }*/
  // NOSONAR

  @GetMapping("/orgs/{orgSlug}/teachers/{teacherAuthId}/student:attendance")
  public List<ParticipantEventResponse> getStudentAttendanceInfo(
      @RequestParam("external_event_id") String externalEventId,
      @RequestParam("auth_user_id") String authUserId) {
    return zoomEventsService.fetchUserAttendance(externalEventId, authUserId);
  }
}
