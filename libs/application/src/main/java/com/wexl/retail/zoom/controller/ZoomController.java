package com.wexl.retail.zoom.controller;

import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.commons.security.annotation.IsTeacherOrStudent;
import com.wexl.retail.zoom.dto.MobileZoomResponse;
import com.wexl.retail.zoom.dto.ZoomRequest;
import com.wexl.retail.zoom.dto.ZoomResponse;
import com.wexl.retail.zoom.service.ZoomService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequestMapping("/orgs/{orgId}/zoom-meetings")
public class ZoomController {

  @Autowired private ZoomService zoomService;

  @IsTeacherOrStudent
  @PostMapping
  public ZoomResponse startZoomMeeting(
      @PathVariable String orgId, @RequestBody @Valid ZoomRequest request) {
    return zoomService.startZoom(orgId, request);
  }

  @IsStudent
  @PostMapping("/sdk")
  public MobileZoomResponse startMobileZoomMeeting(
      @PathVariable String orgId, @RequestBody @Valid ZoomRequest request) {
    return zoomService.startMobileZoom(orgId, request);
  }
}
