package com.wexl.retail.mlp.repository;

import com.wexl.retail.mlp.dto.KmSummary;
import com.wexl.retail.mlp.model.MlpInst;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface KnowledgeMeterRepository extends JpaRepository<MlpInst, Long> {

  @Query(
      value =
          """
          with Summary as(with Grades as (with Section As(with CTE as(select s.name as section,m.subject_name as SubjectName,m.subject_slug as SubjectSlug,\
          m.chapter_name as ChapterName,m.subtopic_name as SubtopicName,m.grade_slug as Grade,m.created_at,COALESCE((attendance_percentage),0) as AttendancePercentage,
          Row_Number() Over(Partition by m.grade_slug,s.name,m.subject_slug,m.subject_name,m.chapter_name,m.subtopic_name order by m.created_at DESC) As rowNumber
          from mlp m join sections s on m.section_id = s.id where org_slug =:orgSlug and m.grade_slug in (:gradeSlug)\s
          group by SubjectSlug,m.subject_name,ChapterName,SubtopicName,s.name,Grade,m.created_at,AttendancePercentage)
          select Grade,section,SubjectName,SubjectSlug,ChapterName,SubtopicName,AttendancePercentage from CTE where rowNumber = 1)
          select Grade,section,SubjectSlug,COALESCE(sum(AttendancePercentage)/count(AttendancePercentage),0) as AttendancePercentage from Section\s
          group by section,SubjectSlug,Grade)
          select Grade,section,COALESCE(sum(AttendancePercentage)/count(AttendancePercentage),0) as AttendancePercentage from Grades
          group by Grade,section)
          select Grade as GradeSlug,COALESCE(sum(AttendancePercentage)/count(AttendancePercentage),0) as AttendancePercentage from Summary
          group by Grade\
          """,
      nativeQuery = true)
  List<KmSummary> getKmSummaryAttendance(String orgSlug, List<String> gradeSlug);

  @Query(
      value =
          """
          with Summary as(with Grades as (with Section As(with CTE as(select s.name as section,m.subject_name as SubjectName,m.subject_slug as SubjectSlug,\
          m.chapter_name as ChapterName,m.subtopic_name as SubtopicName,m.grade_slug as Grade,m.created_at,
          COALESCE((knowledge_percentage),0) as KnowledgePercentage,Row_Number() Over(Partition by m.grade_slug,s.name,m.subject_slug,\
          m.subject_name,m.chapter_name,m.subtopic_name order by m.created_at DESC) As rowNumber from mlp m join sections s on m.section_id = s.id\s
          where org_slug =:orgSlug and m.grade_slug in (:gradeSlug) and m.subtopic_name <> ''
          group by SubjectSlug,m.subject_name,ChapterName,SubtopicName,s.name,Grade,m.created_at,knowledge_percentage)
          select Grade,section,SubjectName,SubjectSlug,ChapterName,SubtopicName,KnowledgePercentage from CTE where rowNumber = 1)\
          select Grade,section,SubjectSlug,COALESCE(sum(knowledgePercentage)/count(knowledgePercentage),0) as knowledgePercentage,count(knowledgePercentage) \
          from Section group by section,SubjectSlug,Grade)
           select Grade,section,COALESCE(sum(knowledgePercentage)/count(knowledgePercentage),0) as knowledgePercentage from Grades group by Grade,section)
           select Grade as GradeSlug,COALESCE(sum(knowledgePercentage)/count(knowledgePercentage),0) as KnowledgePercentage from Summary group by Grade\
          """,
      nativeQuery = true)
  List<KmSummary> getKmSummaryKnowledge(String orgSlug, List<String> gradeSlug);

  @Query(
      value =
          """
          with Grades as (with sections As(with CTE as(select s.name as section,m.subject_slug as SubjectSlug,m.subject_name as SubjectName,\
          m.chapter_name as ChapterName,m.subtopic_name as SubtopicName,attendance_percentage as AttendancePercentage,
          Row_Number() Over(Partition by s.name,m.subject_name,m.chapter_name,m.subtopic_name order by m.created_at DESC) As RowNumber
          from mlp m join sections s on m.section_id = s.id where org_slug =:orgSlug and m.grade_slug in (:gradeSlug)
          group by section,SubjectName,SubjectSlug,ChapterName,SubtopicName,s.name,m.created_at,AttendancePercentage)
          select section,SubjectName,SubjectSlug,ChapterName,SubtopicName,AttendancePercentage from CTE where RowNumber = 1)
          select section,SubjectSlug,SubjectName,ChapterName,SubtopicName,COALESCE(sum(AttendancePercentage)/count(AttendancePercentage),0) as AttendancePercentage \
          from sections group by section,SubjectSlug,SubjectName,ChapterName,SubtopicName)
          select section as Name,SubjectName,SubjectSlug,COALESCE(sum(AttendancePercentage)/count(AttendancePercentage),0) as AttendancePercentage from Grades
          group by Name,SubjectName,SubjectSlug\
          """,
      nativeQuery = true)
  List<KmSummary> getKmGradesAttendance(String orgSlug, String gradeSlug);

  @Query(
      value =
          """
          with Grades as (with sections As(with CTE as(select s.name as section,m.subject_slug as SubjectSlug,m.subject_name as SubjectName,\
          m.chapter_name as ChapterName,m.subtopic_name as SubtopicName,knowledge_percentage as KnowledgePercentage,
          Row_Number() Over(Partition by s.name,m.subject_name,m.chapter_name,m.subtopic_name order by m.created_at DESC) As RowNumber
          from mlp m join sections s on m.section_id = s.id where org_slug =:orgSlug and m.grade_slug in (:gradeSlug)  and m.subtopic_name <> '' \
          group by section,SubjectName,SubjectSlug,ChapterName,SubtopicName,s.name,m.created_at,knowledge_percentage)
           select section,SubjectName,SubjectSlug,ChapterName,SubtopicName,KnowledgePercentage from CTE where RowNumber = 1)
          select section,SubjectSlug,SubjectName,ChapterName,SubtopicName,
          COALESCE(sum(knowledgePercentage)/count(knowledgePercentage),0) as knowledgePercentage,count(knowledgePercentage) from sections
          group by section,SubjectSlug,SubjectName,ChapterName,SubtopicName)
          select section as Name,SubjectName,SubjectSlug,COALESCE(sum(knowledgePercentage)/count(knowledgePercentage),0) as KnowledgePercentage from Grades
          group by Name,SubjectName,SubjectSlug\
          """,
      nativeQuery = true)
  List<KmSummary> getKmGradesKnowledge(String orgSlug, String gradeSlug);

  @Query(
      value =
          """
          With CTE as(select m.subject_name as SubjectName,m.subject_slug as SubjectSlug,m.chapter_name as ChapterName,m.subtopic_name as SubtopicName,m.created_at,
          COALESCE(m.attendance_percentage,0) as AttendancePercentage,COALESCE(m.knowledge_percentage,0) as KnowledgePercentage,
          Row_Number() Over(Partition by m.subject_name,m.chapter_name,m.subtopic_name order by m.created_at DESC) As rowNumber
          from mlp m join sections s on m.section_id = s.id where org_slug =:orgSlug and cast(s.uuid as varchar) in (:sectionUuid)
          group by SubjectName,SubjectSlug,ChapterName,SubtopicName,m.created_at,AttendancePercentage,KnowledgePercentage)
          select SubjectName,SubjectSlug,ChapterName,SubtopicName,AttendancePercentage,KnowledgePercentage from CTE where rowNumber = 1\
          """,
      nativeQuery = true)
  List<KmSummary> getSectionSummaryAttendance(String orgSlug, String sectionUuid);
}
