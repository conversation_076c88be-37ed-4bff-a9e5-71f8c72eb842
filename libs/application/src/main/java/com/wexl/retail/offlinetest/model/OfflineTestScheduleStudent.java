package com.wexl.retail.offlinetest.model;

import com.wexl.retail.model.Model;
import com.wexl.retail.term.model.TermAssessmentCategory;
import jakarta.persistence.*;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "offline_test_schedule_student")
public class OfflineTestScheduleStudent extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "student_id")
  private Long studentId;

  @Column(name = "org_slug")
  private String orgSlug;

  @Column(name = "is_attended")
  private Boolean isAttended;

  @Column(name = "marks", scale = 2)
  private BigDecimal marks;

  @Column(name = "marks_1", scale = 2)
  private BigDecimal marks1;

  @Column(name = "marks_2")
  private BigDecimal marks2;

  @Column(name = "marks_3")
  private BigDecimal marks3;

  @Column(name = "remarks")
  private String remarks;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "offline_test_schedule_id")
  private OfflineTestSchedule offlineTestScheduleDetails;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "term_assessment_category_id")
  private TermAssessmentCategory termAssessmentCategory;
}
