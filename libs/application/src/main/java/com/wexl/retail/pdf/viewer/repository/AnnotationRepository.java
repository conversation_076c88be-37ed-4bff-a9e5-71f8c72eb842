package com.wexl.retail.pdf.viewer.repository;

import com.wexl.retail.pdf.viewer.domain.Annotation;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface AnnotationRepository extends JpaRepository<Annotation, Long> {

  @Query(value = "select ann  from Annotation ann where ann.docId=:docId")
  List<Annotation> getAnnotationsByDocId(@Param("docId") String docId);

  @Query(value = "select nextval('public.\"annotation_id_seq\"')", nativeQuery = true)
  long getNextValOfAnnotationIdSeq();
}
