package com.wexl.retail.team.event;

import com.wexl.retail.courses.teams.event.TeamCourseScheduleEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
public class TeamCourseScheduleEventPublisher {

  @Autowired private ApplicationEventPublisher applicationEventPublisher;

  public void publishOnboardedCourseScheduleEvent(final TeamCourseScheduleMetaData metaData) {
    TeamCourseScheduleEvent teamCourseScheduleEvent = new TeamCourseScheduleEvent(metaData);
    applicationEventPublisher.publishEvent(teamCourseScheduleEvent);
  }
}
