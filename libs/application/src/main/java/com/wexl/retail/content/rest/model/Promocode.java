package com.wexl.retail.content.rest.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Promocode {
  @JsonProperty("Code")
  private String code;

  @JsonProperty("MaxUsers")
  private int maxUsers;

  @JsonProperty("Status")
  private boolean status;

  @JsonProperty("SubscriptionFeeReduction")
  private int subscriptionFeeReduction;

  @JsonProperty("PointsValueReduction")
  private int pointsValueReduction;

  @JsonProperty("FreePoints")
  private int freePoints;

  private String promocodeType;

  @JsonProperty("PromoCodeType")
  private void unpackNameFromNestedObject(Map<String, String> promocodeTypeMap) {
    promocodeType = promocodeTypeMap.get("Type");
  }
}
