package com.wexl.retail.msg91.service;

import static java.lang.String.format;

import com.wexl.retail.msg91.dto.Msg91Dto.Recipient;
import com.wexl.retail.msg91.dto.Msg91Dto.TxtLocalResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

@Service
@RequiredArgsConstructor
@Slf4j
public class TextLocalSmsService implements SmsService {

  private final RestTemplate restTemplate;
  private static final String AUTH_KEY = "9qodAtzjeoQ-8up585baaH31jM6xYsLBn6GxJD0prb";

  @Override
  public void sendBulkMessage(String templateId, List<Recipient> recipients) {
    var bulkSmsUrl = "https://api.textlocal.in/send/?apikey=%s&numbers=%s&sender=DPSNAC&message=%s";
    var finalList = filterInvalidPhoneNumbers(recipients);
    if (finalList.isEmpty()) {
      return;
    }

    for (Recipient recipient : finalList) {
      String message =
          URLEncoder.encode(placeholders(templateId, recipient), StandardCharsets.UTF_8);
      String numbers = recipient.mobiles();

      final String url = format(bulkSmsUrl, AUTH_KEY, numbers, message);

      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

      MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
      map.add("apikey", AUTH_KEY);
      map.add("numbers", numbers);
      map.add("sender", "DPSNAC");
      map.add("message", message);
      map.add("custom", "b0b97894-7ecc-4ec9-be0d-7ff6e8fa4369");
      //      map.add("receipt_url", "https://webhook.site/fa8eb81e-9795-4b02-8973-6f640a6f32ed");

      HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(map, headers);

      ResponseEntity<TxtLocalResponse> response =
          restTemplate.exchange(url, HttpMethod.POST, entity, TxtLocalResponse.class);
      if (response.getBody() == null) {
        log.info("Response from txtlocal {}", response);
        return;
      }

      log.info("Status: {}", response.getBody().status());
    }
  }

  private String placeholders(String template, Recipient recipient) {
    if (recipient.name() != null) {
      template = template.replace("${studentName}", recipient.name());
    }
    if (recipient.date() != null) {
      template = template.replace("${attendanceDate}", recipient.date());
    }
    if (recipient.orgname() != null) {
      template = template.replace("${branchName}", recipient.orgname());
    }
    return template;
  }
}
