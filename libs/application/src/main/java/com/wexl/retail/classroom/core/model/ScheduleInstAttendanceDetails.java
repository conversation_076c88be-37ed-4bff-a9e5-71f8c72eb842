package com.wexl.retail.classroom.core.model;

import com.wexl.retail.classroom.core.dto.ScheduleInstAttendanceStatus;
import com.wexl.retail.model.Model;
import com.wexl.retail.model.Student;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "schedule_inst_attendance_details")
public class ScheduleInstAttendanceDetails extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Enumerated(EnumType.STRING)
  private ScheduleInstAttendanceStatus attendanceStatus;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "student_id")
  private Student student;

  @ManyToOne
  @JoinColumn(name = "schedule_inst_attendance_id")
  private ScheduleInstAttendance scheduleInstAttendance;

  @Column(name = "org_slug")
  private String orgSlug;
}
