package com.wexl.retail.coursecontent.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.sql.Timestamp;
import java.util.Date;
import lombok.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

@Data
@Getter
@Setter
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "scorm_definition", schema = "public")
public class ScormDefinition extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  private String version;

  @Column(name = "main_file")
  private String mainFile;

  private String title;
  private String link;
  private String unzippedLink;
  @CreatedDate private Timestamp createdAt;
  @LastModifiedDate private Timestamp updatedAt;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
  private Date deletedAt;
}
