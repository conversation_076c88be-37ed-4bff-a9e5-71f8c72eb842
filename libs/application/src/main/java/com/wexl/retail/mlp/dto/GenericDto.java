package com.wexl.retail.mlp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@RequiredArgsConstructor
public class GenericDto {

  private String name;
  private String slug;
  private Double average;
  private Double attendance;

  @JsonProperty("exam_records")
  private List<ExamRecords> examRecords;
}
