package com.wexl.retail.student.answer;

import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.PracticeExamInfo;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

public interface StudentAnswerRepository extends JpaRepository<ExamAnswer, Long> {

  @Modifying
  @Transactional
  @Query("update ExamAnswer s set s.active=?2 where s.id=?1")
  void updateActive(long id, boolean active);

  @Query("select s from ExamAnswer s left join fetch s.exam where s.id=?1")
  Optional<ExamAnswer> findById(long id);

  @Query(
      value = "select s from ExamAnswer s left join fetch s.exam",
      countQuery = "select count(s) from ExamAnswer s")
  Page<ExamAnswer> findAllStudents(Pageable pageable);

  @Query("select s from ExamAnswer s left join fetch s.exam e where e.id=?1")
  List<ExamAnswer> findByExamId(long examId);

  @Query(
      value =
          """
          select sum(marks_scored) as marksScored,
                 sum(marks_per_question) as totalMarks,
                 count(case when is_correct = true then 1 end) as correctAnswersCount
          from exam_answers where exam_id=:examId\
          """,
      nativeQuery = true)
  PracticeExamInfo getPracticeExamInfo(long examId);

  @Query("select sum(s.marksScoredPerQuestion) from ExamAnswer s where s.exam.id=?1")
  Float countMarksScoredForExam(long examId);

  @Query(
      """
      SELECT CASE WHEN COUNT(s) > 0 THEN TRUE ELSE FALSE END FROM ExamAnswer s LEFT \
      JOIN s.exam e WHERE e.id=?1 AND s.type='subjective'\
      """)
  boolean isSubjectiveQuestionExists(long examId);

  @Query(countQuery = "select count(s) from ExamAnswer s left join fetch s.exam e where e.id=?1")
  int countByExamId(long examId);

  Optional<ExamAnswer> findByExamAndQuestionId(Exam exam, Long questionId);
}
