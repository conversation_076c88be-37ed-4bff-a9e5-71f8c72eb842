package com.wexl.retail.generic;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.ResourceUtils;
import com.wexl.retail.util.StrapiService;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class GenericService {

  private final JdbcTemplate jdbcTemplate;
  private final AuthService authService;
  private final StrapiService strapiService;
  private final LineItems lineItems;

  @Value("classpath:kts-resources.json")
  private Resource resource;

  @Value("classpath:stc-resources.json")
  private Resource stcResource;

  public GenericResponse fetchData(GenericRequest genericRequest) {
    List<String> allParams = genericRequest.getParams();
    allParams.add(String.valueOf(authService.getUserDetails().getId()));
    var sqlQueryResponse = getSqlQueryFromStrapi(genericRequest.getKey());
    var query =
        String.format(
            sqlQueryResponse.getQuery(),
            genericRequest.getParams().toArray(new Object[genericRequest.getParams().size()]));
    return executeQuery(query);
  }

  public GenericResponse fetchDataById(String key) {
    var sqlQueryResponse = getSqlQueryFromStrapi(key);
    return executeQuery(
        String.format(sqlQueryResponse.getQuery(), authService.getUserDetails().getId()));
  }

  private GenericResponse executeQuery(String queryToExecute) {
    List<Map<String, Object>> queryResult = jdbcTemplate.queryForList(queryToExecute);
    return GenericResponse.builder().data(queryResult).build();
  }

  private SqlQueryResponse getSqlQueryFromStrapi(String key) {
    return strapiService.getSqlQueryUsingKey(key);
  }

  public LineItems getResources(String orgId) {
    if (orgId.equals("mah177")) {
      return lineItems;
    }
    try {
      var objectMapper = new ObjectMapper();

      if (orgId.equals("stc572533")) {
        return objectMapper.readValue(ResourceUtils.asString(stcResource), LineItems.class);
      }
      return objectMapper.readValue(ResourceUtils.asString(resource), LineItems.class);
    } catch (Exception ex) {
      log.error("Unable to process the resource [kts-resources.json] from the classpath", ex);
    }
    throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidRequest");
  }
}
