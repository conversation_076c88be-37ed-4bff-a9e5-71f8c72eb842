package com.wexl.retail.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "student_registration_codes")
public class StudentRegistrationCode {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  @NotNull private String orgSlug;

  @NotNull private String code;

  @Column(name = "start_date")
  private LocalDateTime startDate;

  @Column(name = "expiry_date")
  private LocalDateTime expiryDate;

  private Boolean isUsed;
}
