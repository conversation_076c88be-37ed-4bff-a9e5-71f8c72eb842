package com.wexl.retail.courses.module.service;

import static java.lang.String.format;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.courses.definition.model.CourseDefinition;
import com.wexl.retail.courses.definition.service.CourseDefinitionService;
import com.wexl.retail.courses.module.dto.CourseModuleRequest;
import com.wexl.retail.courses.module.dto.CourseModuleResponse;
import com.wexl.retail.courses.module.dto.ModuleReorderRequest;
import com.wexl.retail.courses.module.model.CourseModule;
import com.wexl.retail.courses.module.repository.CourseModuleRepository;
import java.util.Objects;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CourseModuleService {

  private final AuthService authService;
  private final DateTimeUtil dateTimeUtil;
  private final CourseModuleRepository courseModuleRepository;
  private final CourseDefinitionService courseDefinitionService;

  public CourseModuleService(
      AuthService authService,
      DateTimeUtil dateTimeUtil,
      CourseModuleRepository courseModuleRepository,
      @Lazy CourseDefinitionService courseDefinitionService) {
    this.authService = authService;
    this.dateTimeUtil = dateTimeUtil;
    this.courseModuleRepository = courseModuleRepository;
    this.courseDefinitionService = courseDefinitionService;
  }

  @SneakyThrows
  public CourseModuleResponse createCourseModule(CourseModuleRequest request, long courseDefId) {

    var courseDefinition = courseDefinitionService.findCourseDefinitionById(courseDefId);
    courseDefinitionService.checkIfCurrentUserIsHavingPermission(
        courseDefinition.getOwner().getAuthUserId());

    var nextSeqNum = courseModuleRepository.getNextSeqNumInModule(courseDefId);
    var courseModule = buildCourseModule(request, courseDefinition);
    courseModule.setSequenceNumber(nextSeqNum);
    return saveCourseModule(courseModule);
  }

  private CourseModuleResponse saveCourseModule(CourseModule courseModule) {
    try {
      return buildCourseModuleResponse(courseModuleRepository.save(courseModule));
    } catch (DataIntegrityViolationException e) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          format("%s sequence number already exists", courseModule.getSequenceNumber()));
    }
  }

  @SneakyThrows
  public CourseModule findCourseModuleById(long moduleId) {
    return courseModuleRepository
        .findById(moduleId)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST,
                    "Course Module Not Found for %s".formatted(moduleId)));
  }

  public CourseModuleResponse updateCourseModuleById(CourseModuleRequest request, long moduleId) {
    var courseModule = findCourseModuleById(moduleId);
    courseDefinitionService.checkIfCurrentUserIsHavingPermission(
        courseModule.getCourseDefinition().getOwner().getAuthUserId());

    courseModule.setName(request.getName());
    return saveCourseModule(courseModule);
  }

  public CourseModuleResponse getCourseModuleById(long moduleId) {
    var courseModule = findCourseModuleById(moduleId);
    return buildCourseModuleResponse(courseModule);
  }

  public CourseModuleResponse buildCourseModuleResponse(CourseModule courseModule) {

    return CourseModuleResponse.builder()
        .id(courseModule.getId())
        .name(courseModule.getName())
        .sequenceNumber(courseModule.getSequenceNumber())
        .courseDefinitionId(courseModule.getCourseDefinition().getId())
        .publishedAt(
            Objects.nonNull(courseModule.getPublishedAt())
                ? dateTimeUtil.convertIso8601ToEpoch(
                    courseModule.getPublishedAt().toLocalDateTime())
                : null)
        .build();
  }

  CourseModule buildCourseModule(CourseModuleRequest request, CourseDefinition courseDefinition) {
    return CourseModule.builder()
        .courseDefinition(courseDefinition)
        .name(request.getName())
        .orgSlug(authService.getUserDetails().getOrganization())
        .build();
  }

  public void reorderModules(ModuleReorderRequest request, long courseDefId) {

    var courseModules = courseModuleRepository.getAllModulesAssociatedToCourse(courseDefId);
    if (!Objects.equals(request.getModuleIds().size(), courseModules.size())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Modules count is different");
    }

    for (int i = 0; i < request.getModuleIds().size(); i++) {
      for (var courseModule : courseModules) {
        if (Objects.equals(courseModule.getId(), request.getModuleIds().get(i))) {
          courseModule.setSequenceNumber(i + 1);
        }
      }
    }

    courseModuleRepository.saveAll(courseModules);
  }
}
