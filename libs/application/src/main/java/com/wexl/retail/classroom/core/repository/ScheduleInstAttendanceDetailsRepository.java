package com.wexl.retail.classroom.core.repository;

import com.wexl.retail.classroom.core.model.ScheduleInstAttendance;
import com.wexl.retail.classroom.core.model.ScheduleInstAttendanceDetails;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface ScheduleInstAttendanceDetailsRepository
    extends JpaRepository<ScheduleInstAttendanceDetails, Long> {
  @Query(
      value =
          """
                  select siad.* from schedule_inst_attendance sia
                  join schedule_inst_attendance_details siad on siad.schedule_inst_attendance_id = sia.id
                  join classroom_schedule_inst csi on csi.id = sia.classroom_schedule_inst_id
                  join classroom_schedules cs  on cs.id = csi.classroom_schedule_id
                  join classrooms c  on c.id  = cs.classroom_id
                  where   csi.start_time BETWEEN :fDate and :tDate
                  and siad.student_id =:studentId
                                    """,
      nativeQuery = true)
  List<ScheduleInstAttendanceDetails> findStudentAttendanceByDate(
      Long studentId, LocalDateTime fDate, LocalDateTime tDate);

  List<ScheduleInstAttendanceDetails> findAllByScheduleInstAttendanceIn(
      List<ScheduleInstAttendance> scheduleInstAttendanceIds);

  @Query(
      value =
          """
                          select siad.* from schedule_inst_attendance sia
                                                 join schedule_inst_attendance_details siad on siad.schedule_inst_attendance_id = sia.id
                                                 join classroom_schedule_inst csi on csi.id = sia.classroom_schedule_inst_id
                                                  join classroom_schedules cs  on cs.id = csi.classroom_schedule_id
                                                 left join tasks t on t.classroom_schedule_inst_id = csi.id
                                                 left join task_inst ti on t.id =  ti.task_id
                                                 where  ti.id =:taskId and siad.student_id =:studentId
                                            """,
      nativeQuery = true)
  List<ScheduleInstAttendanceDetails> getAttendanceByTaskIdAndStudentId(
      Long taskId, Long studentId);

  @Query(
      value =
          """
          select siad.* from schedule_inst_attendance_details siad
             join schedule_inst_attendance sia on sia.id = siad.schedule_inst_attendance_id
             where  sia.id in (:attendanceIds) and  siad.attendance_status  = :attendanceStatus
             and  siad.deleted_at is null""",
      nativeQuery = true)
  List<ScheduleInstAttendanceDetails> getByAttendanceInstsAndStatus(
      List<Long> attendanceIds, String attendanceStatus);
}
