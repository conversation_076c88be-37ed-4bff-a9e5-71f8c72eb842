package com.wexl.retail.section.publisher;

import com.wexl.retail.model.Student;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
public class AssociateStudentSectionEventPublisher {

  @Autowired private ApplicationEventPublisher applicationEventPublisher;

  public void publishEvent(final Student student) {
    var associateStudentSectionEvent = new AssociateStudentSectionEvent(student);
    applicationEventPublisher.publishEvent(associateStudentSectionEvent);
  }
}
