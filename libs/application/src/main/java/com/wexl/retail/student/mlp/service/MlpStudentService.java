package com.wexl.retail.student.mlp.service;

import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.student.mlp.dto.*;
import com.wexl.retail.student.mlp.dto.MlpSummary;
import com.wexl.retail.student.mlp.dto.StudentsMlpOverview;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class MlpStudentService {

  private final SectionService sectionService;
  private final StudentRepository studentRepository;

  @Autowired private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

  @Value("${app.sql.getSectionMlpsDetails}")
  private String getSectionMlpsDetailsSql;

  @Value("${app.sql.getMlpReportsByStudentId}")
  private String getMlpReportsByStudentIdSql;

  private static final String[] months = {
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December"
  };
  private static final String NOT_ATTENDED = "-";

  public MlpSummary getSectionMlpSummary(
      String orgSlug, String sectionUuid, String subject, String monthName) {

    Section section = sectionService.findByUuid(sectionUuid);
    int month = Arrays.asList(months).indexOf(monthName);

    MlpSummary mlpSummary = new MlpSummary();
    mlpSummary.setOrganization(orgSlug);
    mlpSummary.setGrade(section.getGradeName());
    mlpSummary.setSection(section.getName());
    mlpSummary.setMonth(monthName);
    mlpSummary.setSubject(subject);
    mlpSummary.setTotalStudents(studentRepository.getStudentsBySection(section).size());

    List<MlpDetails> mlpDetails = getSectionMlpRecords(section.getId(), subject, month + 1);
    mlpSummary.setMlpDetails(mlpDetails);
    mlpSummary.setTotalMlpsAssigned(mlpDetails.size());

    return mlpSummary;
  }

  private List<MlpDetails> getSectionMlpRecords(Long sectionId, String subject, Integer month) {
    MapSqlParameterSource parameterSource = new MapSqlParameterSource();
    parameterSource.addValue("sectionId", sectionId);
    parameterSource.addValue("subjectSlug", subject);
    parameterSource.addValue("month", month);

    return namedParameterJdbcTemplate.query(
        getSectionMlpsDetailsSql, parameterSource, new BeanPropertyRowMapper<>(MlpDetails.class));
  }

  public StudentsMlpOverview getStudentsMlpOverview(
      String sectionUuid, String subject, String monthName) {

    Section section = sectionService.findByUuid(sectionUuid);
    int month = Arrays.asList(months).indexOf(monthName);

    List<Student> students = studentRepository.getStudentsBySection(section);
    List<Long> studentIds = students.stream().map(Student::getId).collect(Collectors.toList());
    List<MlpDetails> mlpDetails = getSectionMlpRecords(section.getId(), subject, month + 1);

    if (mlpDetails.isEmpty()) {
      return new StudentsMlpOverview(new ArrayList<>());
    }

    List<Long> mlpIds = mlpDetails.stream().map(MlpDetails::getMlpId).collect(Collectors.toList());

    List<StudentDetailsResponse> sectionMlpReports = getMlpReportsByStudentId(studentIds, mlpIds);

    List<StudentDetails> studentDetailsList = new ArrayList<>();

    students.forEach(
        student -> {
          StudentDetails studentDetails = new StudentDetails();
          User studentUser = student.getUserInfo();
          studentDetails.setFullName(studentUser.getFirstName() + " " + studentUser.getLastName());
          studentDetails.setUserName(studentUser.getUserName());
          studentDetails.setStudentId(student.getId());

          List<StudentDetailsResponse> studentMlpReports =
              sectionMlpReports.stream()
                  .filter(sectionMlp -> sectionMlp.getStudentId().equals(student.getId()))
                  .collect(Collectors.toList());

          List<StudentMlpReport> mlpReports = buildMlpReports(studentMlpReports, mlpDetails);
          studentDetails.setMlpReports(mlpReports);
          studentDetails.setMonthlyAttendance(countStudentAttendance(mlpReports));
          studentDetailsList.add(studentDetails);
        });
    List<StudentDetails> sortedStudentDetailsList =
        studentDetailsList.stream()
            .sorted(Comparator.comparing(StudentDetails::getMonthlyAttendance).reversed())
            .collect(Collectors.toList());
    return StudentsMlpOverview.builder().studentDetails(sortedStudentDetailsList).build();
  }

  private List<StudentMlpReport> buildMlpReports(
      List<StudentDetailsResponse> studentMlpResponses, List<MlpDetails> totalMlps) {

    List<StudentMlpReport> studentMlpReports = new ArrayList<>();

    if (studentMlpResponses.isEmpty()) {
      return defaultMlpReports(totalMlps);
    }

    List<Long> unAssignedMlps = new ArrayList<>();
    List<Long> assignedMlps =
        studentMlpResponses.stream()
            .map(StudentDetailsResponse::getMlpId)
            .collect(Collectors.toList());

    for (var mlp : totalMlps) {
      if (!assignedMlps.contains(mlp.getMlpId())) {
        unAssignedMlps.add(mlp.getMlpId());
      }
    }

    if (!unAssignedMlps.isEmpty()) {
      List<MlpDetails> unAssignedMlpDetails = buildUnAssignedMlpDetails(unAssignedMlps, totalMlps);
      studentMlpReports.addAll(defaultMlpReports(unAssignedMlpDetails));
    }

    studentMlpResponses.forEach(
        mlp -> {
          StudentMlpReport studentMlpReport = new StudentMlpReport();
          studentMlpReport.setMlpId(mlp.getMlpId());
          studentMlpReport.setTitle(mlp.getTitle());
          if (Boolean.TRUE.equals(mlp.getStatus())) {
            studentMlpReport.setStatus(mlp.getPercentageScored().toString());
          } else {
            studentMlpReport.setStatus(NOT_ATTENDED);
          }
          studentMlpReports.add(studentMlpReport);
        });
    return studentMlpReports;
  }

  private List<MlpDetails> buildUnAssignedMlpDetails(
      List<Long> unAssignedMlps, List<MlpDetails> allMlpDetails) {
    List<MlpDetails> unassignedMlpDetails = new ArrayList<>();

    unAssignedMlps.forEach(
        unAssignedMlp ->
            unassignedMlpDetails.addAll(
                allMlpDetails.stream()
                    .filter(mlpDetails -> mlpDetails.getMlpId().equals(unAssignedMlp))
                    .collect(Collectors.toList())));
    return unassignedMlpDetails;
  }

  private Integer countStudentAttendance(List<StudentMlpReport> mlpReports) {
    int studentAttendance = 0;
    for (var mlp : mlpReports) {
      if (!mlp.getStatus().equals(NOT_ATTENDED)) {
        studentAttendance++;
      }
    }
    return studentAttendance;
  }

  private List<StudentDetailsResponse> getMlpReportsByStudentId(
      List<Long> studentIds, List<Long> mlpIds) {
    MapSqlParameterSource parameterSource = new MapSqlParameterSource();
    parameterSource.addValue("studentIds", studentIds);
    parameterSource.addValue("mlpIds", mlpIds);

    return namedParameterJdbcTemplate.query(
        getMlpReportsByStudentIdSql,
        parameterSource,
        new BeanPropertyRowMapper<>(StudentDetailsResponse.class));
  }

  private List<StudentMlpReport> defaultMlpReports(List<MlpDetails> mlpDetails) {
    List<StudentMlpReport> studentMlpReports = new ArrayList<>();
    mlpDetails.forEach(
        mlp -> {
          StudentMlpReport studentMlpReport = new StudentMlpReport();
          studentMlpReport.setStatus(NOT_ATTENDED);
          studentMlpReport.setTitle(mlp.getTitle());
          studentMlpReport.setMlpId(mlp.getMlpId());
          studentMlpReports.add(studentMlpReport);
        });
    return studentMlpReports;
  }
}
