package com.wexl.retail.test.schedule.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record ScheduleTestDto() {

  @Builder
  public record GroupScheduleTestRequestBulk(
      int duration,
      long testDefinitionId,
      long startDate,
      long endDate,
      List<String> board,
      List<String> grades,
      List<String> childOrgs) {}

  @Builder
  public record OnlineTestAndOfflineTestCounts(
      @JsonProperty("online_test_count") Long onlineTestCount,
      @JsonProperty("offline_test_count") Long offlineTestCount,
      @JsonProperty("grade") String grade,
      @JsonProperty("grade_name") String gradeName) {}

  @Builder
  public record OnlineTestCountsByGrade(
      String name, Long attemptedCount, Long notAttemptedCount, Long testScheduleId) {}
}
