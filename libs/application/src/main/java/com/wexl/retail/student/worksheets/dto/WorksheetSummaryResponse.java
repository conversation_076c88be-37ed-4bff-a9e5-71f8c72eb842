package com.wexl.retail.student.worksheets.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonInclude(Include.NON_EMPTY)
@JsonIgnoreProperties(ignoreUnknown = true)
public interface WorksheetSummaryResponse {

  @JsonProperty("chapter_slug")
  String getChapterSlug();

  @JsonProperty("worksheet_count")
  int getWorksheetCount();

  @JsonProperty("no_of_worksheets_completed")
  int getNoOfWorksheetsCompleted();
}
