package com.wexl.retail.scorm.repository;

import com.wexl.retail.coursecontent.domain.ScormDefinition;
import com.wexl.retail.model.User;
import com.wexl.retail.scorm.domain.ScormInst;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ScormInstRepository extends JpaRepository<ScormInst, Long> {

  ScormInst findByScormDefinitionAndUser(ScormDefinition scormDefinition, User user);
}
