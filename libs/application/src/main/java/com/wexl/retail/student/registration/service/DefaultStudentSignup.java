package com.wexl.retail.student.registration.service;

import com.wexl.retail.student.registration.dto.StudentRegistrationRequest;
import java.security.SecureRandom;

public interface DefaultStudentSignup {

  String createStudent(String orgSlug, StudentRegistrationRequest studentRegistrationRequest);

  default String generatePassword() {
    SecureRandom random = new SecureRandom();
    var otp = random.nextInt(12345678, 99999999);
    return String.valueOf(otp);
  }

  default String generateRandomCharacters(int length) {
    SecureRandom random = new SecureRandom();
    String characters = "abcdefghijklmnopqrstuvwxyz";
    StringBuilder results = new StringBuilder();

    for (int i = 0; i < length; i++) {
      results.append(characters.charAt(random.nextInt(characters.length())));
    }
    return results.toString();
  }
}
