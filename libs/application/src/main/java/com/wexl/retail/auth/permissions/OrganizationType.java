package com.wexl.retail.auth.permissions;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum OrganizationType {
  RETAIL("retail"),
  SCHOOL_B2C("school-b2c"),
  SCHOOL_DIRECT("school-direct"),
  TUITION_B2C("tuition-b2c"),
  TUITION_DIRECT("tuition-direct");
  private final String value;

  public static OrganizationType fromValue(String value) {
    if (value == null || "".equals(value)) {
      throw new IllegalArgumentException("Value cannot be null or empty!");
    }

    for (OrganizationType enumEntry : OrganizationType.values()) {
      if (enumEntry.toString().equals(value.toLowerCase())) {
        return enumEntry;
      }
    }

    throw new IllegalArgumentException("Do not understand the Org Type " + value);
  }

  @Override
  public String toString() {
    return this.value;
  }
}
