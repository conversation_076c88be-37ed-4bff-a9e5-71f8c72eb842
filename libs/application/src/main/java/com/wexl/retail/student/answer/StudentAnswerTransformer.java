package com.wexl.retail.student.answer;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.Question;
import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.liveworksheet.dto.WorkSheetQuestionType;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.ExamService;
import com.wexl.retail.test.school.domain.TestDefinitionSection;
import com.wexl.retail.test.school.domain.TestQuestion;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class StudentAnswerTransformer {

  private final ExamService examService;
  private final AuthService authService;
  private final ContentService contentService;

  /**
   * Map @{@link StudentAnswerRequest} to @{@link ExamAnswer}.
   *
   * @param studentAnswerRequest @{@link StudentAnswerRequest}
   * @return List<@ StudentAnswer>
   */
  public List<ExamAnswer> mapAnswerRequestToStudentAnswer(
      StudentAnswerRequest studentAnswerRequest, String bearerToken) {
    var exam = examService.findById(studentAnswerRequest.getExamId());

    Map<String, String> subtopicMap = getSubtopicMapByExam(exam, studentAnswerRequest, bearerToken);

    return studentAnswerRequest.getExamQuestions().stream()
        .map(
            examQuestion -> {
              var examAnswer = new ExamAnswer();
              examAnswer.setQuestionId(examQuestion.getQuestionId());
              examAnswer.setQuestionUuid(examQuestion.getQuestionUuid());
              examAnswer.setSelectedOption(examQuestion.getSelectedAnswer());
              examAnswer.setAnswer(examQuestion.getAnswer());
              examAnswer.setActive(true);
              examAnswer.setExam(exam);
              examAnswer.setExamReference(exam.getId());
              examAnswer.setIsMobile(authService.isUserLoginByMobile());
              examAnswer.setType(examQuestion.getType());
              examAnswer.setSubtopicSlug(subtopicMap.get(examQuestion.getQuestionUuid()));
              examAnswer.setAnswerType(
                  Objects.nonNull(examQuestion.getAnswerType())
                      ? WorkSheetQuestionType.valueOf(examQuestion.getAnswerType()).name()
                      : null);
              examAnswer.setMarksScoredPerQuestion(examQuestion.getMarksScored());
              examAnswer.setExamAnswerAttachments(new ArrayList<>());
              examQuestion
                  .getAttachments()
                  .forEach(
                      attachmentPath ->
                          examAnswer
                              .getExamAnswerAttachments()
                              .add(ExamAnswerAttachment.builder().path(attachmentPath).build()));
              return examAnswer;
            })
        .toList();
  }

  public Map<String, String> getSubtopicMapByExam(
      Exam exam, StudentAnswerRequest studentAnswerRequest, String bearerToken) {
    if (Objects.nonNull(exam.getTestDefinition())
        && !exam.getTestDefinition().getTestDefinitionSections().isEmpty()) {
      List<TestQuestion> testQuestions =
          exam.getTestDefinition().getTestDefinitionSections().stream()
              .map(TestDefinitionSection::getTestQuestions)
              .flatMap(Collection::stream)
              .toList();
      return testQuestions.stream()
          .collect(Collectors.toMap(TestQuestion::getQuestionUuid, TestQuestion::getSubtopicSlug));
    }

    Map<String, String> resultMap = new HashMap<>();
    var isSubTopicPresent =
        studentAnswerRequest.getExamQuestions().stream()
            .map(ExamQuestion::getQuestionDetails)
            .filter(Objects::nonNull)
            .map(Question::getSubtopicSlug)
            .allMatch(Objects::nonNull);
    studentAnswerRequest
        .getExamQuestions()
        .forEach(
            eq -> {
              if (isSubTopicPresent) {
                resultMap.put(eq.getQuestionUuid(), eq.getQuestionDetails().getSubtopicSlug());
              } else {
                Question question =
                    contentService.getQuestionByUuid(
                        bearerToken, QuestionType.valueOf(eq.getType()), eq.getQuestionUuid());
                resultMap.put(eq.getQuestionUuid(), question.getSubtopicSlug());
              }
            });
    return resultMap;
  }

  /**
   * Map @{@link ExamAnswer} to @{@link StudentAnswerResponse}.
   *
   * @param examAnswer @{@link ExamAnswer}
   * @return @{@link StudentAnswerResponse}
   */
  public StudentAnswerResponse mapStudentAnswerToAnswerResponse(ExamAnswer examAnswer) {
    return StudentAnswerResponse.builder()
        .examDetails(
            ExamDetails.builder()
                .examId(examAnswer.getExamReference())
                .noOfQuestions(
                    examService.findById(examAnswer.getExamReference()).getNoOfQuestions())
                .build())
        .build();
  }
}
