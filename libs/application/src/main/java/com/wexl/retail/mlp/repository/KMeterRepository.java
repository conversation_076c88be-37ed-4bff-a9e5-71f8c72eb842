package com.wexl.retail.mlp.repository;

import com.wexl.retail.mlp.dto.KmSummary;
import com.wexl.retail.mlp.model.MlpInst;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface KMeterRepository extends JpaRepository<MlpInst, Long> {

  @Query(
      value =
          """
          with Summary as(with Grades as (with Section As(select s.name as section,m.subject_slug as SubjectSlug,m.chapter_name as ChapterName,m.subtopic_name as SubtopicName
          ,COALESCE(sum(m.attendance_percentage)/count(m.attendance_percentage),0) as AttendancePercentage ,m.grade_slug as Grade,
           COALESCE(sum(knowledge_percentage)/count(knowledge_percentage),0) as KnowledgePercentage\s
           from mlp m join sections s on m.section_id = s.id\s
           where org_slug =:orgSlug and board_slug =:board and m.grade_slug in (:gradeSlug)
           group by SubjectSlug,ChapterName,SubtopicName,s.name,Grade)
          select Grade,section,SubjectSlug,COALESCE(sum(attendancePercentage)/count(attendancePercentage),0) as attendancePercentage,count(AttendancePercentage),
          COALESCE(sum(knowledgePercentage)/count(knowledgePercentage),0) as knowledgePercentage,count(knowledgePercentage) from Section\s
          group by section,SubjectSlug,Grade)
          select Grade,section,COALESCE(sum(attendancePercentage)/count(attendancePercentage),0)as attendancePercentage,
          COALESCE(sum(knowledgePercentage)/count(knowledgePercentage),0) as knowledgePercentage from Grades
          group by Grade,section)
          select Grade as GradeSlug,COALESCE(sum(attendancePercentage)/count(attendancePercentage),0) as AttendancePercentage,
          COALESCE(sum(knowledgePercentage)/count(knowledgePercentage),0) as KnowledgePercentage from Summary
          group by Grade\
          """,
      nativeQuery = true)
  List<KmSummary> getAttendanceSummary(String orgSlug, List<String> gradeSlug, String board);

  @Query(
      value =
          """
              with Grades as (with sections As(select s.name as section,m.subject_slug as SubjectSlug,m.subject_name as SubjectName,m.chapter_name as ChapterName,m.subtopic_name as SubtopicName,
               COALESCE(sum(m.attendance_percentage)/count(m.attendance_percentage),0) as AttendancePercentage ,
               COALESCE(sum(knowledge_percentage)/count(knowledge_percentage),0) as KnowledgePercentage
               from mlp m join sections s on m.section_id = s.id where s.organization =:orgSlug and s.grade_slug in (:gradeSlug)
               and s.status ='ACTIVE' and (CAST(:boardSlug AS VARCHAR) IS NULL OR s.board_slug = :boardSlug)
                group by SubjectName,SubjectSlug,ChapterName,SubtopicName,s.name)
                select section,SubjectSlug,SubjectName,COALESCE(sum(attendancePercentage)/count(attendancePercentage),0) as attendancePercentage,count(AttendancePercentage),
              COALESCE(sum(knowledgePercentage)/count(knowledgePercentage),0) as knowledgePercentage,count(knowledgePercentage) from sections
              group by section,SubjectSlug,SubjectName)
              select section as Name,SubjectName,SubjectSlug,COALESCE(sum(attendancePercentage)/count(attendancePercentage),0) as AttendancePercentage,
              COALESCE(sum(knowledgePercentage)/count(knowledgePercentage),0) as KnowledgePercentage from Grades
              group by Name,SubjectName,SubjectSlug""",
      nativeQuery = true)
  List<KmSummary> getKmGrades(String orgSlug, String boardSlug, String gradeSlug);

  @Query(
      value =
          """
          select m.subject_name as SubjectName,m.subject_slug as SubjectSlug,m.chapter_name as ChapterName,m.subtopic_name as SubtopicName\
          ,COALESCE(sum(m.attendance_percentage)/count(m.attendance_percentage),0) as AttendancePercentage ,COALESCE(sum(knowledge_percentage)/count(knowledge_percentage),0) as KnowledgePercentage \
           from mlp m join sections s on m.section_id = s.id\s
          where org_slug = :orgSlug and cast(s.uuid as varchar) in (:sectionUuid)
          group by SubjectName,SubjectSlug,ChapterName,SubtopicName\
          """,
      nativeQuery = true)
  List<KmSummary> getKmSectionSummary(String orgSlug, String sectionUuid);

  @Query(
      value =
          """
          With Summary as(with Subject as (with chapter as (select COALESCE(mi.attendance_percentage,0) as AttendancePercentage,m.subject_name as SubjectName,m.subject_slug as SubjectSlug
          ,m.chapter_name as ChapterName,m.chapter_slug as ChapterSlug,m.subtopic_name as SubtopicName,m.subtopic_slug as SubtopicSlug  from mlp_inst mi join mlp m on mi.mlp_id = m.id\s
          where mi.student_id =:studentId and m.subject_slug in (:subjectSlugs))
          select  SubjectName,SubjectSlug,ChapterName,ChapterSlug,SubtopicName,SubtopicSlug ,(sum(AttendancePercentage)/count(AttendancePercentage)) as AttendancePercentage from chapter
          group by ChapterName,SubtopicName,ChapterSlug,SubjectName,SubtopicSlug,SubjectSlug)
          select  SubjectName,SubjectSlug,ChapterName,ChapterSlug,(sum(AttendancePercentage)/count(AttendancePercentage)) as AttendancePercentage from Subject
          group by SubjectName,SubjectSlug,ChapterName,ChapterSlug)
          select SubjectSlug,SubjectName,(sum(AttendancePercentage)/count(AttendancePercentage)) as AttendancePercentage from Summary \s
          group by SubjectName,SubjectSlug\
          """,
      nativeQuery = true)
  List<KmSummary> getSubjectWiseAttendancePercentage(Long studentId, List<String> subjectSlugs);

  @Query(
      value =
          """
          With Summary as(with Subject as(with chapter as (select COALESCE(mi.knowledge_percentage,0) as KnowledgePercentage,m.chapter_name as ChapterName,m.chapter_slug as ChapterSlug,m.subject_name as SubjectName,m.subject_slug as SubjectSlug
          ,m.subtopic_name as SubtopicName from mlp_inst mi join mlp m on mi.mlp_id = m.id\s
          where mi.student_id =:studentId and m.subject_slug in (:subjectSlugs) and m.question_count <> 0)
          select  SubjectName,SubjectSlug,ChapterName,ChapterSlug,SubtopicName, (sum(KnowledgePercentage)/count(KnowledgePercentage)) as KnowledgePercentage from chapter
          group by SubjectName,SubjectSlug,ChapterName,SubtopicName,ChapterSlug)
          select  SubjectName,SubjectSlug,ChapterName,ChapterSlug,(sum(KnowledgePercentage)/count(KnowledgePercentage)) as KnowledgePercentage from Subject
          group by SubjectName,SubjectSlug,ChapterName,ChapterSlug)
          select SubjectSlug,SubjectName,(sum(KnowledgePercentage)/count(KnowledgePercentage)) as KnowledgePercentage from Summary \s
          group by SubjectName,SubjectSlug\
          """,
      nativeQuery = true)
  List<KmSummary> getSubjectWiseKnowledgePercentage(Long studentId, List<String> subjectSlugs);

  @Query(
      value =
          """
                      with CTE as (select COALESCE(mi.attendance_percentage,0) as AttendancePercentage,m.subject_name as SubjectName,m.chapter_name as ChapterName,m.chapter_slug as ChapterSlug,
                          m.subtopic_name as SubtopicName,m.subtopic_slug as SubtopicSlug  from mlp_inst mi join mlp m on mi.mlp_id = m.id
                          where mi.student_id = :studentId and m.chapter_slug in (:chapterSlug) and m.subtopic_slug is not null)
                          select  SubjectName,ChapterName,ChapterSlug,SubtopicName,SubtopicSlug , (sum(AttendancePercentage)/count(AttendancePercentage)) as AttendancePercentage from CTE
                          group by ChapterName,SubtopicName,ChapterSlug,SubjectName,SubtopicSlug""",
      nativeQuery = true)
  List<KmSummary> getSubTopicWiseAttendancePercentage(Long studentId, List<String> chapterSlug);

  @Query(
      value =
          """
          with CTE as (select COALESCE(mi.knowledge_percentage,0) as KnowledgePercentage,m.chapter_name as ChapterName,m.chapter_slug as ChapterSlug,
          			 m.subtopic_name as SubtopicName from mlp_inst mi join mlp m on mi.mlp_id = m.id\s
           where mi.student_id = :studentId and m.chapter_slug in (:chapterSlug) and m.question_count <> 0)
          \s
           select  ChapterName,ChapterSlug,SubtopicName, (sum(KnowledgePercentage)/count(KnowledgePercentage)) as KnowledgePercentage from CTE
           group by ChapterName,SubtopicName,ChapterSlug\
          """,
      nativeQuery = true)
  List<KmSummary> getSubTopicWiseKnowledgePercentage(Long studentId, List<String> chapterSlug);

  @Query(
      value =
          """
          with Subject as (with chapter as (select COALESCE(mi.attendance_percentage,0) as AttendancePercentage,m.subject_name as SubjectName,\
          m.chapter_name as ChapterName,m.chapter_slug as ChapterSlug,m.subtopic_name as SubtopicName,m.subtopic_slug as SubtopicSlug  \
          from mlp_inst mi join mlp m on mi.mlp_id = m.id where mi.student_id = :studentId and m.subject_slug in (:subjectSlug))
          select SubjectName,ChapterName,ChapterSlug,SubtopicName,SubtopicSlug,(sum(AttendancePercentage)/count(AttendancePercentage)) as AttendancePercentage \
          from chapter group by ChapterName,SubtopicName,ChapterSlug,SubjectName,SubtopicSlug)
          select SubjectName,ChapterName,ChapterSlug,(sum(AttendancePercentage)/count(AttendancePercentage)) as AttendancePercentage from Subject
          group by SubjectName,ChapterName,ChapterSlug\
          """,
      nativeQuery = true)
  List<KmSummary> getChapterWiseAttendancePercentage(long studentId, String subjectSlug);

  @Query(
      value =
          """
          with Subject as(with chapter as (select COALESCE(mi.knowledge_percentage,0) as KnowledgePercentage,m.chapter_name as ChapterName,m.chapter_slug as ChapterSlug,
          m.subtopic_name as SubtopicName from mlp_inst mi join mlp m on mi.mlp_id = m.id\s
          where mi.student_id = :studentId and m.subject_slug in (:subjectSlug) and m.question_count <> 0)
          select  ChapterName,ChapterSlug,SubtopicName,(sum(KnowledgePercentage)/count(KnowledgePercentage)) as KnowledgePercentage from chapter
          group by ChapterName,SubtopicName,ChapterSlug)
          select ChapterName,ChapterSlug,(sum(KnowledgePercentage)/count(KnowledgePercentage)) as KnowledgePercentage from Subject
          group by ChapterName,ChapterSlug\
          """,
      nativeQuery = true)
  List<KmSummary> getChapterWiseKnowledgePercentage(long studentId, String subjectSlug);
}
