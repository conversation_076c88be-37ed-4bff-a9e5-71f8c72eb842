package com.wexl.retail.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.auth.Permission;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class OrgSettings {

  private List<EduBoard> boards;

  @JsonProperty("content_providers")
  private List<ContentProvider> contentProviders;

  @JsonProperty("content_recipients")
  private List<ContentProvider> contentRecipients;

  @JsonProperty("user_permissions")
  private List<Permission> userPermissions;

  @JsonProperty("ui_config")
  private UiConfig appConfig;
}
