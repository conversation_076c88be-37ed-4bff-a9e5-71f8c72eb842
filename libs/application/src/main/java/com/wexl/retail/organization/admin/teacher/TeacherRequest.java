package com.wexl.retail.organization.admin.teacher;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.globalprofile.model.RoleTemplate;
import com.wexl.retail.model.TeacherMetadata;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@AllArgsConstructor
@Builder
@RequiredArgsConstructor
public class TeacherRequest {
  @NotBlank private String email;

  private String password;
  @NotBlank private String firstName;
  private String lastName;
  @NotBlank private String mobileNumber;
  private String roleType;

  private boolean orgAdmin;

  private TeacherMetadata teacherMetadata;

  @JsonProperty("country_code")
  private String countryCode;

  @JsonProperty("external_ref")
  private String externalRef;

  @NotNull
  @JsonProperty("role_template")
  private RoleTemplate roleTemplate;
}
