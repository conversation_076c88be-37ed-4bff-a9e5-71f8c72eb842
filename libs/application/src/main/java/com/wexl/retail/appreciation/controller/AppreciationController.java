package com.wexl.retail.appreciation.controller;

import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.commons.security.annotation.IsTeacherOrStudent;
import com.wexl.retail.notification.service.EventNotificationService;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.dto.NotificationDto.NotificationRequest;
import com.wexl.retail.notifications.dto.NotificationDto.StudentNotificationResponse;
import com.wexl.retail.notifications.dto.NotificationDto.TeacherNotificationResponse;
import com.wexl.retail.notifications.service.NotificationsService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}")
public class AppreciationController {

  private static final String APPRECIATION_TITLE = "Congratulations!! \uD83D\uDC4F\uD83D\uDC4F";

  private final NotificationsService notificationService;
  private final EventNotificationService eventNotificationService;

  @IsOrgAdminOrTeacher
  @PostMapping("/teachers/{teacherAuthUserId}/appreciations")
  @ResponseStatus(HttpStatus.CREATED)
  public void createAppreciation(
      @RequestBody NotificationDto.NotificationRequest notificationRequest,
      @PathVariable String orgSlug,
      @PathVariable("teacherAuthUserId") String teacherAuthId) {

    final NotificationRequest updatedNotificationRequest =
        transformNotification(notificationRequest);

    notificationService.createNotificationByTeacher(
        orgSlug, updatedNotificationRequest, teacherAuthId, false);
  }

  private NotificationRequest transformNotification(NotificationRequest notificationRequest) {
    return NotificationRequest.builder()
        .title(APPRECIATION_TITLE)
        .message(notificationRequest.message())
        .notificationType(notificationRequest.notificationType())
        .studentIds(notificationRequest.studentIds())
        .classroomIds(notificationRequest.classroomIds())
        .sectionUuids(notificationRequest.sectionUuids())
        .attachment(notificationRequest.attachment())
        .gradeSlug(notificationRequest.gradeSlug())
        .messageTemplateId(notificationRequest.messageTemplateId())
        .notificationType(notificationRequest.notificationType())
        .notificationType(notificationRequest.notificationType())
        .build();
  }

  @IsOrgAdminOrTeacher
  @PostMapping("/teachers/{teacherAuthUserId}/appreciations/{id}")
  @ResponseStatus(HttpStatus.ACCEPTED)
  public void editAppreciation(
      @RequestBody NotificationDto.NotificationRequest notificationRequest,
      @PathVariable String orgSlug,
      @PathVariable("teacherAuthUserId") String teacherAuthId,
      @PathVariable("id") Long notificationId) {
    final NotificationRequest updatedNotificationRequest =
        transformNotification(notificationRequest);

    notificationService.editNotificationByTeacher(
        orgSlug, updatedNotificationRequest, teacherAuthId, notificationId);
  }

  @IsOrgAdminOrTeacher
  @DeleteMapping("/teachers/{teacherAuthUserId}/appreciations/{id}")
  @ResponseStatus(HttpStatus.ACCEPTED)
  public void deleteAppreciation(
      @PathVariable String orgSlug,
      @PathVariable("teacherAuthUserId") String teacherAuthId,
      @PathVariable Long id) {
    notificationService.deleteNotification(orgSlug, teacherAuthId, id);
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/teachers/{teacherAuthUserId}/appreciations")
  public List<NotificationDto.TeacherNotificationResponse> getTeacherNotifications(
      @PathVariable String orgSlug,
      @PathVariable("teacherAuthUserId") String teacherAuthId,
      @RequestParam(required = false, defaultValue = "100") int limit) {
    final List<TeacherNotificationResponse> teacherNotifications =
        notificationService.getTeacherAppreciationNotifications(
            orgSlug, teacherAuthId, limit, APPRECIATION_TITLE);
    return teacherNotifications.stream().distinct().toList();
  }

  @IsTeacherOrStudent
  @GetMapping("/students/{studentAuthId}/appreciations")
  public List<StudentNotificationResponse> getStudentNotification(
      @PathVariable String orgSlug,
      @PathVariable String studentAuthId,
      @RequestParam(name = "from_date", required = false) Long fromDate,
      @RequestParam(value = "academic_year", required = false) String academicYear,
      @RequestParam(value = "limit", required = false) Long limit) {
    final List<StudentNotificationResponse> studentNotifications =
        notificationService.getStudentNotificationsfromTeacher(
            orgSlug, fromDate, studentAuthId, APPRECIATION_TITLE, academicYear, limit);
    return studentNotifications.stream().distinct().toList();
  }
}
