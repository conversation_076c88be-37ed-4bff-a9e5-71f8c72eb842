package com.wexl.retail.mobile.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.Data;

@Data
@Entity
@Table(name = "mobile_config_keys")
public class MobileConfigKeys extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  @Column(name = "name")
  private String name;

  private Boolean status;

  private String description;
}
