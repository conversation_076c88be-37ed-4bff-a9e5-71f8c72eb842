package com.wexl.retail.student.subject.profiles.service;

import com.wexl.retail.curriculum.service.CurriculumService;
import com.wexl.retail.model.EduBoard;
import com.wexl.retail.model.Grade;
import com.wexl.retail.model.Subject;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.student.auth.StudentAuthService;
import com.wexl.retail.student.subject.profiles.domain.SubjectProfileDetails;
import com.wexl.retail.student.subject.profiles.domain.SubjectProfiles;
import com.wexl.retail.student.subject.profiles.dto.SubjectProfileRequest;
import com.wexl.retail.student.subject.profiles.dto.SubjectsRequest;
import com.wexl.retail.student.subject.profiles.repository.StudentSubjectProfilesRepository;
import com.wexl.retail.student.subject.profiles.repository.SubjectProfilesRepository;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class SubjectProfileMigrationService {
  private final CurriculumService curriculumService;
  private final StudentAuthService studentAuthService;
  private final SubjectProfilesService subjectProfilesService;
  private final SubjectProfilesRepository subjectProfilesRepository;

  private final StudentRepository studentRepository;
  private final StudentSubjectProfilesRepository studentSubjectProfilesRepository;
  private final OrganizationRepository organizationRepository;

  public void createStandardSubjectProfiles(String orgSlug) {
    var org = organizationRepository.findBySlug(orgSlug);
    List<EduBoard> curriculumResponse = curriculumService.getBoardsHierarchy(orgSlug);
    List<SubjectProfileRequest> subjectProfileRequestList = new ArrayList<>();
    curriculumResponse.forEach(
        board -> {
          List<Grade> grades = board.getGrades();
          grades.forEach(
              grade -> {
                List<Subject> subjects = grade.getSubjects();
                List<SubjectsRequest> subjectsRequest = new ArrayList<>();
                subjects.forEach(
                    subject ->
                        subjectsRequest.add(
                            SubjectsRequest.builder()
                                .boardName(board.getName())
                                .boardSlug(board.getSlug())
                                .gradeName(grade.getName())
                                .gradeSlug(grade.getSlug())
                                .subjectSlug(subject.getSlug())
                                .subjectName(subject.getName())
                                .orgSlug(orgSlug)
                                .displayName(subject.getName())
                                .build()));
                SubjectProfileRequest subjectProfileRequest =
                    SubjectProfileRequest.builder()
                        .name(board.getName() + " " + grade.getName() + " Profile")
                        .subjectsRequest(subjectsRequest)
                        .build();
                subjectProfileRequestList.add(subjectProfileRequest);
              });
        });
    List<SubjectProfiles> subjectProfiles =
        subjectProfilesRepository.findAllByOrgAndStatusOrderByCreatedAtDesc(org, true);
    if (subjectProfiles.isEmpty()) {
      subjectProfileRequestList.forEach(
          subjectProfileRequest ->
              subjectProfilesService.createSubjectProfile(orgSlug, subjectProfileRequest));
    } else {
      updateSubjectProfile(subjectProfileRequestList, subjectProfiles, orgSlug);
    }
  }

  private void updateSubjectProfile(
      List<SubjectProfileRequest> subjectProfileRequestList,
      List<SubjectProfiles> subjectProfiles,
      String orgSlug) {
    Map<String, SubjectProfiles> subjectProfilesMap =
        subjectProfiles.stream().collect(Collectors.toMap(SubjectProfiles::getName, sp -> sp));

    subjectProfileRequestList.forEach(
        subjectProfileRequest -> {
          var subjectProfile = subjectProfilesMap.get(subjectProfileRequest.getName());
          if (Objects.isNull(subjectProfile)) {
            subjectProfilesService.createSubjectProfile(orgSlug, subjectProfileRequest);
          } else {
            subjectProfileRequest
                .getSubjectsRequest()
                .removeAll(
                    getMappedSubjectsBySubjectProfile(
                        subjectProfileRequest, subjectProfile, orgSlug));

            if (Objects.nonNull(subjectProfileRequest.getSubjectsRequest())) {
              subjectProfilesService.addNewSubjectToProfile(
                  orgSlug, subjectProfileRequest, subjectProfile.getId());
            }
          }
        });
  }

  private List<SubjectsRequest> getMappedSubjectsBySubjectProfile(
      SubjectProfileRequest subjectProfileRequest, SubjectProfiles subjectProfile, String orgSlug) {

    Set<String> subjectRequestSlugs =
        subjectProfileRequest.getSubjectsRequest().stream()
            .map(SubjectsRequest::getSubjectSlug)
            .collect(Collectors.toSet());

    List<SubjectProfileDetails> filteredDetails =
        subjectProfile.getSubjectProfileDetails().stream()
            .filter(details -> !subjectRequestSlugs.contains(details.getSubjectSlug()))
            .toList();

    if (!filteredDetails.isEmpty()) {
      filteredDetails.forEach(
          fd ->
              subjectProfilesService.deleteASubjectFromProfile(
                  orgSlug, subjectProfile.getId(), fd.getId()));
    }

    List<String> alreadyMappedSubjects =
        subjectProfile.getSubjectProfileDetails().stream()
            .map(SubjectProfileDetails::getSubjectSlug)
            .toList();

    return subjectProfileRequest.getSubjectsRequest().stream()
        .filter(subjectsRequest -> alreadyMappedSubjects.contains(subjectsRequest.getSubjectSlug()))
        .toList();
  }

  public List<Long> validateStudentSubjectProfiles(List<String> orgs) {
    List<Long> students = studentRepository.getAllStudentsOfOrgs(orgs);
    if (students.isEmpty()) {
      new ArrayList<>();
    }
    List<Long> mappedStudents = new ArrayList<>();
    if (!students.isEmpty()) {
      mappedStudents = studentSubjectProfilesRepository.getStudentsWithSubjectProfiles(students);
    }
    List<Long> unMappedStudents = new ArrayList<>();
    if (students.size() != mappedStudents.size()) {
      for (var element : students) {
        if (!mappedStudents.contains(element)) unMappedStudents.add(element);
      }
    }
    return unMappedStudents;
  }
}
