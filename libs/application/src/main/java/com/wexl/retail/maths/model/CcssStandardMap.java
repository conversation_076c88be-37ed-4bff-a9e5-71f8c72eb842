package com.wexl.retail.maths.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Entity
@Data
@Table(name = "ccss_standard_mapping")
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
public class CcssStandardMap extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "source_standard_id")
  private Long sourceStandard;

  @Column(name = "target_standard_id")
  private Long targetStandard;

  @Enumerated(EnumType.STRING)
  private CcssStandardRelationship relationship;
}
