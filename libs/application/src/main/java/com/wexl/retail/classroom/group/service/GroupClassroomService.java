package com.wexl.retail.classroom.group.service;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.classroom.core.dto.ClassroomRequest;
import com.wexl.retail.classroom.core.dto.ClassroomScheduleInstResponse;
import com.wexl.retail.classroom.core.dto.ClassroomScheduleRequest;
import com.wexl.retail.classroom.core.model.Classroom;
import com.wexl.retail.classroom.core.model.ClassroomSchedule;
import com.wexl.retail.classroom.core.model.ClassroomScheduleInst;
import com.wexl.retail.classroom.core.repository.ClassroomRepository;
import com.wexl.retail.classroom.core.repository.ClassroomScheduleInstRepository;
import com.wexl.retail.classroom.core.repository.ClassroomScheduleRepository;
import com.wexl.retail.classroom.core.service.ClassroomScheduleInstService;
import com.wexl.retail.classroom.core.service.ClassroomScheduleService;
import com.wexl.retail.classroom.core.service.ClassroomService;
import com.wexl.retail.classroom.group.dto.GroupClassroomDto;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.organization.admin.teacher.TeacherResponse;
import com.wexl.retail.organization.admin.teacher.TeacherService;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.util.Constants;
import java.time.LocalTime;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class GroupClassroomService {

  private final ClassroomService classroomService;
  private final ClassroomScheduleService classroomScheduleService;
  private final ClassroomRepository classroomRepository;
  private final StudentRepository studentRepository;
  private final TeacherService teacherService;
  private final AuthService authService;
  private final UserService userService;
  private final DateTimeUtil dateTimeUtil;
  private final ClassroomScheduleInstRepository classroomScheduleInstRepository;
  private final ClassroomScheduleInstService classroomScheduleInstService;

  private final ClassroomScheduleRepository classroomScheduleRepository;

  public void createGroupClassroom(GroupClassroomDto.GroupClassroomRequest groupClassroomRequest) {
    try {
      Classroom parentClassroom = createAndGetParentClassroom(groupClassroomRequest);
      List<Classroom> classrooms =
          groupClassroomRequest.orgClassroomRequests().stream()
              .map(
                  request -> {
                    classroomService.verifyClassRoomByName(
                        groupClassroomRequest.classroomRequest().getName(), request.orgSlug());
                    return validateAndBuildClassroom(
                        request,
                        groupClassroomRequest.classroomRequest(),
                        new Classroom(),
                        parentClassroom);
                  })
              .toList();
      classroomRepository.saveAll(classrooms);
    } catch (Exception exception) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, exception.getMessage(), exception);
    }
  }

  private Classroom createAndGetParentClassroom(
      GroupClassroomDto.GroupClassroomRequest groupClassroomRequest) {
    classroomService.verifyClassRoomByName(
        groupClassroomRequest.classroomRequest().getName(), Constants.WEXL_INTERNAL);
    groupClassroomRequest
        .classroomRequest()
        .setTeacherIds(List.of(authService.getTeacherDetails().getTeacherInfo().getId()));
    groupClassroomRequest
        .classroomRequest()
        .setTeacherIds(groupClassroomRequest.classroomRequest().getTeacherIds());
    List<String> gradeSlugs =
        groupClassroomRequest.orgClassroomRequests().stream()
            .map(GroupClassroomDto.OrgClassroomRequest::gradeSlug)
            .toList();
    List<Long> internalStudentIds =
        studentRepository.findByGradeAndOrg(gradeSlugs, Constants.WEXL_INTERNAL).stream()
            .map(Student::getId)
            .toList();
    groupClassroomRequest.classroomRequest().setStudentIds(internalStudentIds);
    return classroomRepository.save(
        classroomService.buildClassroom(
            new Classroom(),
            groupClassroomRequest.classroomRequest(),
            Constants.WEXL_INTERNAL,
            null));
  }

  private Classroom validateAndBuildClassroom(
      GroupClassroomDto.OrgClassroomRequest orgClassroomRequest,
      ClassroomRequest classroomRequest,
      Classroom classroom,
      Classroom parentClassroom) {
    var students =
        studentRepository.findByGradeAndOrg(
            Collections.singletonList(orgClassroomRequest.gradeSlug()),
            orgClassroomRequest.orgSlug());
    var teacherIds =
        teacherService.getTeachers(orgClassroomRequest.orgSlug()).stream()
            .filter(TeacherResponse::isOrgAdmin)
            .map(TeacherResponse::getTeacherId)
            .toList();
    classroomRequest.setTeacherIds(teacherIds);
    if (!students.isEmpty()) {
      classroomRequest.setStudentIds(students.stream().map(Student::getId).toList());
    }
    return classroomService.buildClassroom(
        classroom, classroomRequest, orgClassroomRequest.orgSlug(), parentClassroom);
  }

  public void scheduleGroupClassroom(
      List<ClassroomScheduleRequest> classroomScheduleRequests, long classroomId) {
    try {
      List<Classroom> classrooms = new ArrayList<>();
      var parentClassroom =
          classroomService.getClassroomByIdAndOrgSlug(classroomId, Constants.WEXL_INTERNAL);
      classrooms.add(parentClassroom);
      classrooms.addAll(classroomService.getGroupClassRoomsByParent(parentClassroom));

      List<ClassroomSchedule> classroomSchedules = new ArrayList<>();
      classrooms.forEach(
          classroom ->
              classroomSchedules.addAll(
                  new ArrayList<>(
                      classroomScheduleRequests.stream()
                          .map(
                              request ->
                                  classroomScheduleService.scheduleClassroom(
                                      classroom, request, classroom.getOrgSlug()))
                          .toList())));
      classroomScheduleRepository.saveAll(classroomSchedules);
    } catch (Exception exception) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, exception.getMessage(), exception);
    }
  }

  public List<GroupClassroomDto.GroupClassroomsResponse> getGroupClassrooms() {
    return buildGroupClassroomsResponses(
        classroomRepository.getGroupClassrooms(Constants.WEXL_INTERNAL));
  }

  private List<GroupClassroomDto.GroupClassroomsResponse> buildGroupClassroomsResponses(
      List<Classroom> groupClassrooms) {
    var internalClassrooms =
        groupClassrooms.stream()
            .filter(classroom -> classroom.getOrgSlug().equals(Constants.WEXL_INTERNAL))
            .sorted(Comparator.comparing(Classroom::getCreatedAt).reversed())
            .toList();
    return internalClassrooms.stream()
        .map(
            classroom ->
                GroupClassroomDto.GroupClassroomsResponse.builder()
                    .id(classroom.getId())
                    .name(classroom.getName())
                    .orgs(getClassroomOrganizations(classroom, groupClassrooms))
                    .teachers(
                        userService.getFullNamesByUsers(
                            classroom.getTeachers().stream().map(Teacher::getUserInfo).toList()))
                    .studentCount(
                        (long) getGroupClassroomStudents(classroom, groupClassrooms).size())
                    .scheduleCount(
                        (long) getGroupClassroomSchedules(groupClassrooms, classroom).size())
                    .build())
        .toList();
  }

  private List<ClassroomSchedule> getGroupClassroomSchedules(
      List<Classroom> groupClassrooms, Classroom classroom) {
    var classrooms =
        groupClassrooms.stream()
            .filter(
                groupClassroom ->
                    Objects.nonNull(groupClassroom.getParent())
                        && groupClassroom.getParent().getId().equals(classroom.getId()))
            .toList();
    if (classrooms.isEmpty()) {
      return Collections.emptyList();
    }
    return classrooms.getFirst().getSchedules();
  }

  private List<Student> getGroupClassroomStudents(
      Classroom classroom, List<Classroom> groupClassrooms) {
    if (groupClassrooms.isEmpty()) {
      return new ArrayList<>();
    }
    return new ArrayList<>(
        groupClassrooms.stream()
            .filter(
                cr ->
                    Objects.nonNull(cr.getParent())
                        && cr.getParent().getId().equals(classroom.getId()))
            .map(Classroom::getStudents)
            .flatMap(Collection::stream)
            .toList());
  }

  private List<String> getClassroomOrganizations(
      Classroom classroom, List<Classroom> groupClassrooms) {
    return new ArrayList<>(
        groupClassrooms.stream()
            .filter(
                cr ->
                    Objects.nonNull(cr.getParent())
                        && cr.getParent().getId().equals(classroom.getId()))
            .map(Classroom::getOrganization)
            .map(Organization::getName)
            .toList());
  }

  public GroupClassroomDto.GroupClassroomDetails getGroupClassroomDetails(Long classroomId) {
    var classroom =
        classroomService.getClassroomByIdAndOrgSlug(classroomId, Constants.WEXL_INTERNAL);
    List<Classroom> groupClassRooms = classroomService.getGroupClassRoomsByParent(classroom);
    return GroupClassroomDto.GroupClassroomDetails.builder()
        .classroomName(classroom.getName())
        .schedules(
            classroomScheduleService.buildScheduleResponses(
                groupClassRooms.getFirst().getSchedules()))
        .teachers(classroomService.getTeacherResponseByTeachers(classroom.getTeachers()))
        .students(
            classroomService.getStudentResponsesByStudents(
                getGroupClassroomStudents(classroom, groupClassRooms)))
        .build();
  }

  public List<ClassroomScheduleInstResponse> getGroupClassroomSchedules(
      Long fromDateInEpoch, Long toDateInEpoch) {
    var fromDate = dateTimeUtil.convertEpochToIso8601(fromDateInEpoch);
    var toDate = dateTimeUtil.convertEpochToIso8601(toDateInEpoch);
    List<ClassroomScheduleInst> classroomScheduleInsts =
        classroomScheduleInstRepository.getGroupClassroomScheduleInstsByParentOrg(
            Constants.WEXL_INTERNAL, fromDate.with(LocalTime.MIN), toDate.with(LocalTime.MAX));
    return classroomScheduleInstService.sortedList(
        classroomScheduleInstService.buildScheduleInstResponses(classroomScheduleInsts));
  }

  public void deleteGroupClassroomDetails(Long classroomId) {
    List<Classroom> classroomList = new ArrayList<>();
    Classroom parentClassroom =
        classroomService.getClassroomByIdAndOrgSlug(classroomId, Constants.WEXL_INTERNAL);
    classroomList.add(parentClassroom);
    var groupClassrooms = classroomService.getGroupClassRoomsByParent(parentClassroom);
    classroomList.addAll(groupClassrooms);
    classroomRepository.saveAll(classroomService.softDeleteClassrooms(classroomList));
  }

  public void editGroupClassroom(
      Long classroomId, GroupClassroomDto.GroupClassroomRequest groupClassroomRequest) {

    Classroom parentClassroom =
        classroomService.getClassroomByIdAndOrgSlug(classroomId, Constants.WEXL_INTERNAL);
    var groupClassrooms =
        new ArrayList<>(classroomService.getGroupClassRoomsByParent(parentClassroom));
    groupClassrooms.add(parentClassroom);

    classroomRepository.saveAll(
        updateClassrooms(groupClassrooms, groupClassroomRequest, parentClassroom));
    var classrooms =
        groupClassroomRequest.orgClassroomRequests().stream()
            .map(request -> buildClassroom(request, groupClassroomRequest, parentClassroom))
            .toList();
    classroomRepository.saveAll(new ArrayList<>(classrooms));
  }

  private List<Classroom> updateClassrooms(
      List<Classroom> groupClassrooms,
      GroupClassroomDto.GroupClassroomRequest groupClassroomRequest,
      Classroom parentClassroom) {
    return new ArrayList<>(
        groupClassrooms.stream()
            .map(
                classroom ->
                    classroomService.buildClassroom(
                        classroom,
                        buildClassroomRequest(classroom, groupClassroomRequest.classroomRequest()),
                        classroom.getOrgSlug(),
                        !Constants.WEXL_INTERNAL.equals(classroom.getOrgSlug())
                            ? parentClassroom
                            : null))
            .toList());
  }

  private Classroom buildClassroom(
      GroupClassroomDto.OrgClassroomRequest request,
      GroupClassroomDto.GroupClassroomRequest groupClassroomRequest,
      Classroom parentClassroom) {

    var optionalClassroom =
        classroomRepository.findByNameAndOrgSlug(
            groupClassroomRequest.classroomRequest().getName(), request.orgSlug());
    if (optionalClassroom.isPresent()) {
      return validateAndBuildClassroom(
          request,
          groupClassroomRequest.classroomRequest(),
          optionalClassroom.get(),
          parentClassroom);
    }
    classroomService.verifyClassRoomByName(
        groupClassroomRequest.classroomRequest().getName(), request.orgSlug());
    return validateAndBuildClassroom(
        request, groupClassroomRequest.classroomRequest(), new Classroom(), parentClassroom);
  }

  private ClassroomRequest buildClassroomRequest(
      Classroom classroom, ClassroomRequest classroomRequest) {
    if (classroom.getOrgSlug().equals(Constants.WEXL_INTERNAL)) {
      return ClassroomRequest.builder()
          .name(classroomRequest.getName())
          .studentIds(classroomRequest.getStudentIds())
          .teacherIds(classroomRequest.getTeacherIds())
          .build();
    }
    return ClassroomRequest.builder()
        .name(classroomRequest.getName())
        .studentIds(classroom.getStudents().stream().map(Student::getId).toList())
        .teacherIds(classroom.getTeachers().stream().map(Teacher::getId).toList())
        .build();
  }
}
