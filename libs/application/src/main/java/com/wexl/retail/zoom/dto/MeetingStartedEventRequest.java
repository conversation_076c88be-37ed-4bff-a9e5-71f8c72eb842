package com.wexl.retail.zoom.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingStartedEventRequest extends ZoomMeetingEventRequest {

  @JsonProperty("payload")
  private MeetingStartedEventPayload payload;

  @Data
  public static class MeetingStartedEventPayload {

    @JsonProperty("object")
    private ZoomEventObject object;

    @JsonProperty("account_id")
    private String accountId;
  }
}
