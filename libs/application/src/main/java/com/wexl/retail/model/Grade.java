package com.wexl.retail.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wexl.retail.curriculum.SectionDto;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Grade {

  private int id;
  private String slug;
  private String name;
  private Integer orderId;
  private List<Subject> subjects;
  private List<SectionDto> sections;
}
