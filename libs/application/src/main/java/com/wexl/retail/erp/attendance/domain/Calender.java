package com.wexl.retail.erp.attendance.domain;

import com.wexl.retail.model.Model;
import com.wexl.retail.organization.model.Organization;
import jakarta.persistence.*;
import lombok.Data;

@Data
@Entity
@Table(name = "calender")
public class Calender extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  @Column(name = "academic_year_slug")
  private String academicYearSlug;

  @ManyToOne
  @JoinColumn(name = "org_id")
  private Organization org;
}
