package com.wexl.retail.metrics.handler;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PersonDataCounts extends AbstractMetricHandler implements MetricHandler {
  @Override
  public String name() {
    return "person-data-counts";
  }

  @Override
  public List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    String orgSlug =
        Optional.ofNullable(genericMetricRequest.getInput().get(ORG_KEY))
            .map(String::valueOf)
            .orElse(org);
    String studentAuthUserId =
        Optional.ofNullable(genericMetricRequest.getInput().get(AUTHUSERID))
            .map(String::valueOf)
            .orElse(org);
    if (!orgSlug.equals(org)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "orgSlug is wrong");
    } else {
      return teamService.getPersonDataCount(orgSlug, studentAuthUserId);
    }
  }
}
