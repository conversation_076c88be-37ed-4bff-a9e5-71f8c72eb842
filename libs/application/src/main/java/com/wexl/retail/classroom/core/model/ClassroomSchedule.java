package com.wexl.retail.classroom.core.model;

import com.wexl.retail.meetingroom.domain.MeetingRoom;
import com.wexl.retail.model.Model;
import com.wexl.retail.organization.model.Organization;
import jakarta.persistence.*;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.OffsetTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@Table(name = "classroom_schedules")
@AllArgsConstructor
public class ClassroomSchedule extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String title;

  @ManyToOne
  @JoinColumn(name = "classroom_id")
  private Classroom classroom;

  @OneToMany(orphanRemoval = true, cascade = CascadeType.ALL)
  @JoinColumn(name = "classroom_schedule_inst_id")
  private List<ClassroomScheduleInst> classroomScheduleInsts;

  @ManyToOne
  @JoinColumn(name = "meeting_room_id")
  private MeetingRoom meetingRoom;

  @Column(name = "start_date")
  private LocalDateTime startDate;

  @Column(name = "expiry_date")
  private LocalDateTime endDate;

  @Column(name = "meeting_start_time", columnDefinition = "TIME WITH TIME ZONE")
  private OffsetTime meetingStartTime;

  @Column(name = "meeting_end_time", columnDefinition = "TIME WITH TIME ZONE")
  private OffsetTime meetingEndTime;

  @Enumerated(EnumType.STRING)
  private DayOfWeek dayOfWeek;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "org_id")
  private Organization organization;

  @JoinColumn(name = "org_slug")
  private String orgSlug;
}
