package com.wexl.retail.classroom.core.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class TaskInstResponse {

  @JsonProperty("task_inst_id")
  private Long taskInstId;

  @JsonProperty("student_id")
  private Long studentId;

  @JsonProperty("student_name")
  private String studentName;

  @JsonProperty("class_name")
  private String classroomName;

  @JsonProperty("task_type")
  private String taskType;

  @JsonProperty("grade_name")
  private String gradeName;

  @JsonProperty("subject_name")
  private String subject;

  @JsonProperty("chapter_name")
  private String chapter;

  private String status;

  @JsonProperty("scheduled_date")
  private Long scheduledDate;

  @JsonProperty("exam_id")
  private Long examId;

  @JsonProperty("activity_name")
  private String activityName;

  @JsonProperty("subtopic_name")
  private String subtopicName;

  @JsonProperty("subtopic_slug")
  private String subtopicSlug;

  @JsonProperty("board_name")
  private String boardName;

  @JsonProperty("board_slug")
  private String boardSlug;
}
