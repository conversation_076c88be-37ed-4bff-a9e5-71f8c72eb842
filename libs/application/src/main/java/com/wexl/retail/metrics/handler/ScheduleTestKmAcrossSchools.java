package com.wexl.retail.metrics.handler;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ScheduleTestKmAcrossSchools extends AbstractMetricHandler {
  @Override
  public String name() {
    return "across-orgs-knowledge-meter";
  }

  @Override
  protected List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    String board =
        Optional.ofNullable(genericMetricRequest.getInput().get(BOARD))
            .map(String.class::cast)
            .orElse(null);

    List<String> gradeList =
        Optional.ofNullable(genericMetricRequest.getInput().get(GRADE))
            .map(List.class::cast)
            .orElse(Collections.emptyList());
    List<String> subjectList =
        Optional.ofNullable(genericMetricRequest.getInput().get(SUBJECT))
            .map(List.class::cast)
            .orElse(Collections.emptyList());
    return dacKmService.getAllOrganizationKnowledgeMeter(org, board, gradeList, subjectList);
  }
}
