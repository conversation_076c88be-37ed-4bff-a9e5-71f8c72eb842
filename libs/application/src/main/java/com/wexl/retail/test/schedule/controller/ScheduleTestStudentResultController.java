package com.wexl.retail.test.schedule.controller;

import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.test.school.dto.QuestionDto.TestSectionDetails;
import com.wexl.retail.v2.service.ScheduleTestStudentService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@Controller
@RequiredArgsConstructor
public class ScheduleTestStudentResultController {

  private final ScheduleTestStudentService scheduleTestStudentService;
  private final DateTimeUtil dateTimeUtil;

  public static final String DATE_FORMAT = "MMM dd";

  @GetMapping(value = "/public/test-results/{resultKey}", produces = MediaType.TEXT_HTML_VALUE)
  public String getStudentResult(Model model, @PathVariable String resultKey) {
    TestSectionDetails testSectionDetails = scheduleTestStudentService.getStudentResult(resultKey);
    model.addAttribute("testName", testSectionDetails.testName());
    model.addAttribute(
        "scheduleDate",
        dateTimeUtil.updateDateFormat(testSectionDetails.scheduleDate(), DATE_FORMAT));
    model.addAttribute("studentName", testSectionDetails.studentName());
    model.addAttribute("results", testSectionDetails.sectionWiseMarks());
    return "student-results";
  }
}
