package com.wexl.retail.dac.knowledgemeter.model;

import com.wexl.retail.model.Model;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.hibernate.annotations.Type;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "test_schedule_kms")
public class TestScheduleKm extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "org_slug", nullable = false)
  private String orgSlug;

  @Column(name = "subtopic_slug")
  private String subTopicSlug;

  @Column(name = "student_id")
  private Long studentId;

  @Column(name = "average_marks")
  private Float averageMarks;

  @Column(name = "total_marks")
  private Long totalMarks;

  @Type(JsonType.class)
  @Column(name = "test_schedule_ids", columnDefinition = "jsonb")
  private List<Long> testScheduleId;

  @Column(name = "test_schedule_count")
  private Long testScheduleCount;

  @Column(name = "test_schedule_sum")
  private Long testScheduleSum;
}
