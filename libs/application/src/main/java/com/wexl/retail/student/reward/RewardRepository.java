package com.wexl.retail.student.reward;

import java.util.List;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface RewardRepository extends JpaRepository<RewardTransaction, Long> {

  @Query("select r from RewardTransaction r left join fetch r.user u where u.id=?1")
  List<RewardTransaction> findByUserIdLastTen(long userId, PageRequest pageRequest);
}
