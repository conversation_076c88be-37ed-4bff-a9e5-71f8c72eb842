package com.wexl.retail.courses.enrollment.controller;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.courses.definition.dto.CourseDefinitionResponse;
import com.wexl.retail.courses.definition.service.CourseDefinitionService;
import com.wexl.retail.courses.enrollment.dto.*;
import com.wexl.retail.courses.enrollment.service.CourseScheduleInstService;
import com.wexl.retail.courses.step.dto.StepCompletionRequest;
import com.wexl.retail.student.exam.ExamResponse;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}")
public class CourseScheduleInstController {

  private final CourseScheduleInstService courseScheduleInstService;
  private final CourseDefinitionService courseDefinitionService;

  @PostMapping("/courses/{courseDefId}/enrollments")
  public void enrollStudentsToCourse(
      @PathVariable long courseDefId,
      @RequestBody CourseEnrollmentRequest courseEnrollmentRequest) {
    try {
      courseScheduleInstService.createCourseAndEnrollStudents(courseEnrollmentRequest, courseDefId);
    } catch (Exception exception) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, exception.getMessage(), exception);
    }
  }

  @GetMapping("/enrollments")
  public List<CourseScheduleDto.ScheduleResponse> getAllScheduledCourses(
      @PathVariable String orgSlug) {
    try {
      return courseScheduleInstService.getAllScheduledCourses(orgSlug);
    } catch (Exception exception) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, exception.getMessage(), exception);
    }
  }

  @GetMapping("/course-definitions/{courseDefId}/enrollments")
  public List<CourseEnrollmentResponse> getAllStudentsEnrolledInCourse(
      @PathVariable long courseDefId) {
    try {
      return courseScheduleInstService.getAllStudentsEnrolledInCourse(courseDefId);
    } catch (Exception exception) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, exception.getMessage(), exception);
    }
  }

  @PutMapping("/courses/{courseDefId}/enrollments/{courseId}")
  public void updateCourseEnrollment(
      @PathVariable long courseId, @RequestBody CourseEnrollmentRequest courseEnrollmentRequest) {
    try {
      courseScheduleInstService.updateCourseScheduleInst(courseEnrollmentRequest, courseId);
    } catch (Exception exception) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, exception.getMessage(), exception);
    }
  }

  @GetMapping("/students/{studentId}/courses")
  public List<CourseScheduleResponse> getStudentEnrolledCourses(
      @RequestParam(value = "courseCategoryId", required = false) Integer courseCategory) {
    try {
      return courseScheduleInstService.getEnrolledCourses(courseCategory);
    } catch (Exception exception) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, exception.getMessage(), exception);
    }
  }

  @GetMapping("/students/{studentId}/courses/{courseDefId}/enrollments/{courseId}")
  public CourseDefinitionResponse getStudentCourseProgress(
      @PathVariable("studentId") String studentAuthId,
      @PathVariable long courseDefId,
      @PathVariable("courseId") long courseScheduleId) {
    try {
      return courseScheduleInstService.getStudentCourseProgress(
          courseDefId, courseScheduleId, studentAuthId);
    } catch (Exception exception) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, exception.getMessage(), exception);
    }
  }

  @GetMapping("/teachers/{teacherId}/courses")
  public List<CourseDefinitionResponse> getCoursesCreatedByTeacher() {
    try {
      return courseDefinitionService.getCoursesCreatedByTeacher();
    } catch (Exception exception) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, exception.getMessage(), exception);
    }
  }

  @PutMapping("/students/{studentId}/course:complete")
  public void updateCourseCompletionStatus(
      @RequestBody CourseCompletionRequest courseCompletionRequest) {
    try {
      courseScheduleInstService.updateCourseCompletionStatus(courseCompletionRequest);
    } catch (Exception exception) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, exception.getMessage(), exception);
    }
  }

  @PutMapping("/students/{studentId}/step:complete")
  public void updateStepCompletionStatus(@RequestBody StepCompletionRequest stepCompletionRequest) {
    try {
      courseScheduleInstService.updateStepCompletionStatus(stepCompletionRequest);
    } catch (Exception exception) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, exception.getMessage(), exception);
    }
  }

  @PostMapping("/courses/{courseId}/team-enrollment")
  public void enrollCourseIndividualGroup(
      @PathVariable long courseId,
      @PathVariable String orgSlug,
      @Valid @RequestBody CourseEnrollmentPersonRequest courseEnrollmentPersonRequest) {
    try {
      courseScheduleInstService.enrollTeamToCourses(
          courseEnrollmentPersonRequest, courseId, orgSlug);
    } catch (Exception exception) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, exception.getMessage(), exception);
    }
  }

  @IsStudent
  @PostMapping("/students/{studentId}/course-schedule-insts/{courseScheduleItemInstId}/exams")
  public ExamResponse startCourseExam(@PathVariable long courseScheduleItemInstId) {
    return courseScheduleInstService.startCourseExam(courseScheduleItemInstId);
  }
}
