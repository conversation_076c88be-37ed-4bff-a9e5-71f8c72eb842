package com.wexl.retail.content.rest.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Organization {
  private int id;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("Slug")
  private String slug;

  @JsonProperty("Type")
  private String type;

  @JsonProperty("OrgType")
  private String orgType;

  @JsonProperty("OrganizationConfig")
  private String organizationConfig;

  @JsonProperty("Status")
  private String status;
}
