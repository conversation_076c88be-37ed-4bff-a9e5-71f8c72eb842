package com.wexl.retail.test.school.domain;

public enum TestType {
  ASSIGNMENT("ASSIGNMENT"),
  WORKSHEET("WORKSHEET"),
  SCHOOL_TEST("SCHOOL_TEST"),
  SCHEDULED_TEST("SCHEDULED_TEST"),
  MOCK_TEST("MOCK_TEST"),
  LIVE_WORKSHEET("LIVE_WORKSHEET");

  private String value;

  TestType(String value) {
    this.value = value;
  }

  @Override
  public String toString() {
    return this.value;
  }

  public static TestType fromValue(String value) {
    if (value == null || "".equals(value)) {
      throw new IllegalArgumentException("Value cannot be null or empty!");
    }

    for (TestType enumEntry : TestType.values()) {
      if (enumEntry.toString().equals(value)) {
        return enumEntry;
      }
    }

    throw new IllegalArgumentException("Cannot create enum from " + value + " value!");
  }
}
