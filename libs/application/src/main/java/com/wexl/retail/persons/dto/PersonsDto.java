package com.wexl.retail.persons.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import org.jetbrains.annotations.NotNull;

public record PersonsDto() {

  public record PersonsRequest(
      @JsonProperty("first_name") @NotNull String firstName,
      @JsonProperty("last_name") @NotNull String lastName,
      @JsonProperty("user_name") @NotNull String userName,
      @NotNull String email,
      @NotNull String password,
      @JsonProperty("cr_student_authId") String crStudentUserName,
      @JsonProperty("country_code") String countryCode,
      @JsonProperty("mobile_number") String mobileNumber,
      String gender,
      String country,
      String address,
      String state,
      @JsonProperty("zip_code") String zipCode,
      String designation) {}

  public record EditPersonsRequest(
      @JsonProperty("first_name") @NotNull String firstName,
      @JsonProperty("last_name") @NotNull String lastName,
      @JsonProperty("cr_student_authId") String crStudentUserName,
      @JsonProperty("country_code") String countryCode,
      @JsonProperty("mobile_number") String mobileNumber,
      String gender,
      String country,
      String address,
      String state,
      @JsonProperty("zip_code") String zipCode,
      String designation) {}

  public record PersonsResponse() {

    @Builder
    public record Persons(
        String firstName,
        String lastName,
        Long noOfTeams,
        String reportingManager,
        String studentAuthId,
        Long studentId) {}

    public interface PersonsQueryResult {
      String getFirstName();

      String getLastName();

      String getReportingManager();

      Integer getNoOfTeams();

      Long getStudentId();
    }

    @Builder
    public record PersonDetails(
        String firstName,
        String lastName,
        String userName,
        String email,
        String reportingManager,
        String reportingManagerAuthId,
        String mobileNumber,
        String gender,
        String country,
        String address,
        String state,
        String zipCode,
        String designation) {}
  }
}
