package com.wexl.retail.util;

import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service
public class ReCaptchaService {
  private final RestTemplate restTemplate = new RestTemplate();

  @Value("${reCaptcha.key}")
  private String reCaptchaKey;

  @Value("${reCaptcha.secret}")
  private String reCaptchaSecret;

  @Value("${reCaptcha.api}")
  private String reCaptchaApi;

  @Value("${spring.profiles.active:default}")
  private String envProfile;

  public boolean verify(String token) {
    log.debug("validating recaptcha token : " + token);
    var returnVal = false;
    try {

      if (List.of(Constants.DEV_PROFILE, Constants.LOCAL_PROFILE).contains(envProfile)) {
        return true;
      }

      String urlPart = "?secret=" + reCaptchaSecret + "&response=" + token;
      ResponseEntity<Map> response =
          restTemplate.exchange(reCaptchaApi + urlPart, HttpMethod.POST, null, Map.class);
      @SuppressWarnings("unchecked")
      Map<String, Object> apiResponse = response.getBody();
      if (apiResponse != null) {
        returnVal = (boolean) apiResponse.get("success");
      }
    } catch (Exception e) {
      log.debug("Error while recaptcha validation: " + e.getMessage());
    }
    return returnVal;
  }
}
