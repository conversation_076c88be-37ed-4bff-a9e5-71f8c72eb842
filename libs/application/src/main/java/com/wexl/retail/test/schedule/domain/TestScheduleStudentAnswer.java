package com.wexl.retail.test.schedule.domain;

import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.model.Model;
import com.wexl.retail.test.schedule.dto.StudentTestAttemptStatus;
import com.wexl.retail.test.school.domain.TestCategory;
import com.wexl.retail.test.school.dto.PbqDto;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import net.minidev.json.annotate.JsonIgnore;
import org.hibernate.annotations.Type;

@Data
@Builder
@Entity
@Table(name = "test_schedule_student_answers")
@RequiredArgsConstructor
@AllArgsConstructor
public class TestScheduleStudentAnswer extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  @Column(name = "id", nullable = false)
  private UUID uuid;

  private String tssUuid;

  private String questionUuid;

  private Integer mcqSelectedAnswer;

  @JsonIgnore
  @Type(JsonType.class)
  @Column(name = "msq_selected_answer", columnDefinition = "jsonb")
  private List<Long> msqSelectedAnswer;

  private Boolean yesNoSelectedAnswer;

  private Float natSelectedAnswer;

  private String fbqSelectedAnswer;

  @Column(columnDefinition = "TEXT")
  private String subjectiveWrittenAnswer;

  private Integer amcqSelectedAnswer;
  private String spchSelectedAnswer;

  private String userName;

  @Enumerated(EnumType.STRING)
  private QuestionType questionType;

  private LocalDateTime submittedTime;

  @Enumerated(EnumType.STRING)
  private StudentTestAttemptStatus attemptStatus;

  private Long testQuestionId;

  @com.fasterxml.jackson.annotation.JsonIgnore
  @Type(JsonType.class)
  @Column(name = "pbq_answers", columnDefinition = "jsonb")
  private PbqDto.Data pbqAnswers;

  @Column(columnDefinition = "VARCHAR(10000)")
  private String ddfbqAttemptedAnswer;

  @Column(name = "section_id")
  private Long sectionId;

  private TestCategory category;

  private Long speechTaskId;
}
