package com.wexl.retail.courses.enrollment.repository;

import com.wexl.retail.courses.enrollment.dto.StudentEnrollmentDetail;
import com.wexl.retail.courses.enrollment.dto.TeacherEnrollmentDetail;
import com.wexl.retail.courses.enrollment.model.CourseEnrollmentSchedule;
import com.wexl.retail.courses.enrollment.model.CourseSchedule;
import com.wexl.retail.courses.enrollment.model.CourseScheduleInst;
import com.wexl.retail.model.User;
import jakarta.transaction.Transactional;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CourseScheduleInstRepository extends JpaRepository<CourseScheduleInst, Long> {

  List<CourseScheduleInst> findByOrgSlugAndStudentId(String orgSlug, User studentId);

  @Transactional
  @Modifying
  @Query(
      value =
          """
             update course_schedule_inst
             set status = :status
             where student_id = :studentId and course_schedule_id = :courseScheduleId""",
      nativeQuery = true)
  int updateCourseCompletionStatus(long studentId, long courseScheduleId, String status);

  @Query(
      value =
          """
                  with student_course_enrollments as (select count(ce.id) as enrollments, ce.course_schedule_id,ce.student_id
                  from course_schedule_inst ce group by ce.course_schedule_id,ce.student_id)
                  select c.course_definition_id as courseDefinitionId, COALESCE(Sum(sce.enrollments),0) as enrollments, cd.name as name,
                  SUM(COALESCE(cd.duration,0)) as duration from course_schedule c inner join student_course_enrollments sce on sce.course_schedule_id = c.id
                  inner join course_definition cd on cd.id = c.course_definition_id
                  join students s on s.user_id = sce.student_id left join teams_students ts on ts.students_id = s.id
                  left join teams t on t.id = ts.team_id  where cd.org_slug = :orgSlug group by c.course_definition_id,cd.name,cd.duration\s
                  order by c.course_definition_id desc
                   """,
      nativeQuery = true)
  List<CourseEnrollmentSchedule> getAllScheduledCourses(String orgSlug);

  @Query(
      value =
          """
              select c.start_date as startDate, c.end_date as endDate,cd.course_category_id as categoryId,cc.name as categoryName, c.course_definition_id as
              courseDefinitionId, cd.name as name,
              cd.published_at as publishedAt, cd.version as version,
              c.id as id, ce.status as status,cd.image_path as thumbNail
              from course_schedule c
              inner join course_schedule_inst ce on ce.course_schedule_id = c.id and ce.student_id = :studentId
              inner join course_definition cd on cd.id = c.course_definition_id
               inner join course_categories cc on cd.course_category_id = cc.id
              order by c.start_date desc""",
      nativeQuery = true)
  List<CourseEnrollmentSchedule> getStudentEnrolledCourses(long studentId);

  @Query(
      value =
          """
                   select concat(u.first_name, ' ', u.last_name) as fullName,ts.team_id as teamId,t.name as teamName,ce.student_id as studentId,u.auth_user_id as studentAuthId,
                                    c.id as courseScheduleId,ce.course_schedule_inst_date as enrollmentDate,ce.status as status,COALESCE((cd.duration),0) as duration
                                    from course_schedule_inst ce join course_schedule c on ce.course_schedule_id = c.id inner join users u on ce.student_id = u.id
                                    join students s on s.user_id = ce.student_id left join teams_students ts on ts.students_id = s.id
                                    left join teams t on t.id = ts.team_id join course_definition cd on cd.id = c.course_definition_id
                  where c.course_definition_id = :courseDefinitionId order by ce.course_schedule_inst_date desc
                  """,
      nativeQuery = true)
  List<StudentEnrollmentDetail> getAllStudentsEnrolledInCourse(long courseDefinitionId);

  @Query(
      value =
          "select * from course_schedule_inst where student_id = :studentId and course_schedule_id = :courseScheduleId",
      nativeQuery = true)
  CourseScheduleInst getStudentEnrolledCourseById(long studentId, long courseScheduleId);

  List<CourseScheduleInst> findByCourseScheduleIn(List<CourseSchedule> courseSchedules);

  @Query(
      value =
          """
                     select concat(u.first_name, ' ', u.last_name) as fullName,ce.student_id as teacherId,u.auth_user_id as teacherAuthId,
                     c.id as courseScheduleId,ce.course_schedule_inst_date as enrollmentDate,ce.status as status
                     from course_schedule_inst ce join course_schedule c on ce.course_schedule_id = c.id inner join users u on ce.student_id = u.id
                     where c.course_definition_id = :courseDefinitionId order by ce.course_schedule_inst_date desc
                 """,
      nativeQuery = true)
  List<TeacherEnrollmentDetail> getAllTeachersEnrolledInCourse(long courseDefinitionId);
}
