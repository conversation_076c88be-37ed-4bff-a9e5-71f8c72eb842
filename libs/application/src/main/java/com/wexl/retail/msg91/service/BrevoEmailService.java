package com.wexl.retail.msg91.service;

import com.wexl.retail.msg91.dto.Msg91Dto;
import com.wexl.retail.util.Constants;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service
@RequiredArgsConstructor
public class BrevoEmailService implements EmailService {

  private final RestTemplate restTemplate;

  private final Msg91EmailService msg91EmailService;

  private static final String emailUrl = "https://api.brevo.com/v3/smtp/email";

  public void sendEmail(String template, List<Msg91Dto.EmailTo> emails) {
    if (emails.isEmpty()) {
      return;
    }
    var request =
        Msg91Dto.BrevoEmailRequest.builder()
            .templateId(Long.parseLong(template))
            .sender(Msg91Dto.Sender.builder().id(1).name("DPS Admin").build());

    msg91EmailService
        .filterValidEmails(emails)
        .forEach(
            recipient -> {
              try {
                var recipientReq = request.to(List.of(recipient)).build();
                ResponseEntity<Msg91Dto.Error> response =
                    restTemplate.exchange(
                        emailUrl,
                        HttpMethod.POST,
                        getRequestEntity(recipientReq),
                        Msg91Dto.Error.class);

                if (!response.getStatusCode().is2xxSuccessful()
                    || Objects.isNull(response.getBody())) {
                  log.error("Error while sending email through Brevo email {}", response.getBody());
                } else {
                  log.info("Email sent successfully through  Brevo email");
                }
              } catch (Exception e) {
                log.error("Error while sending emails : " + e.getMessage(), e);
              }
            });
  }

  private HttpEntity<Msg91Dto.BrevoEmailRequest> getRequestEntity(
      Msg91Dto.BrevoEmailRequest request) {
    var headers = new HttpHeaders();
    headers.add("api-key", Constants.API_KEY);
    return new HttpEntity<>(request, headers);
  }
}
