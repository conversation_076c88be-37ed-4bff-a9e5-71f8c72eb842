package com.wexl.retail.test.assignment.dto;

import lombok.Builder;

public record Assignment() {

  @Builder
  public record AssignmentResponse(
      Long studentId,
      Long examID,
      Long testDefinitionId,
      String name,
      String subjectName,
      String subjectSlug,
      Long assignmentDate,
      String chapterName,
      String chapterSlug,
      String subtopicName,
      String subtopicSlug,
      String status,
      Float marksScored,
      Float totalMarks,
      String activityType,
      Long lastUpdated) {}
}
