package com.wexl.retail.v2.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record StudentResultDto() {

  @Builder
  public record StudentReportResponse(
      String name,
      String grade,
      String subject,
      String admissionNumber,
      String testName,
      String totalNumberOfQuestions,
      String totalMarks,
      String totalAiAnalysisMarks,
      List<StudentAnswerResult> answers) {}

  @Builder
  public record StudentAnswerResult(
      String questionNumber,
      String answerWrittenByStudent,
      String aiAnalysisPerformed,
      String totalMarksEachQuestion,
      String marksAttained) {}

  @Builder
  public record StudentAnalysisReportResponseList(
      @JsonProperty("student_id") Long studentId, List<StudentAnalysisReportResponse> data) {}

  @Builder
  public record StudentAnalysisReportResponse(
      @JsonProperty("sc_id") Long scId,
      @JsonProperty("scd_id") Long scdId,
      @JsonProperty("sca_id") Long scaId,
      @JsonProperty("exam_id") Long examId,
      @JsonProperty("question_uuid") String questionUuid,
      @JsonProperty("annotated_answer") String answer,
      @JsonProperty("marks") float marks,
      @JsonProperty("ai_summary") String aiSummary,
      StudentAnalysisUnit grammar,
      StudentAnalysisUnit spelling,
      StudentAnalysisUnit punctuation,
      StudentAnalysisStatsUnit stats,
      List<StudentAnalysisKeyword> keywords) {}

  @Builder
  public record StudentAnalysisUnit(int total, List<StudentAnalysisUnitGrammar> errors) {}

  @Builder
  public record StudentAnalysisUnitGrammar(String bad, String better, String description) {}

  @Builder
  public record StudentAnalysisStatsUnit(
      @JsonProperty("readability_score") String readabilityScore,
      int words,
      @JsonProperty("text_length") int textLength,
      @JsonProperty("clear_text_length") int clearTextLength) {}

  @Builder
  public record StudentAnalysisKeyword(String word, boolean available) {}
}
