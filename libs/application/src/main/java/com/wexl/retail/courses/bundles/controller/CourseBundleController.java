package com.wexl.retail.courses.bundles.controller;

import com.wexl.retail.courses.bundles.dto.CourseBundleDto;
import com.wexl.retail.courses.bundles.service.CourseBundleService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
public class CourseBundleController {
  private final CourseBundleService courseBundleService;

  @PostMapping("orgs/{orgSlug}/course-bundles")
  public void createCourseBundle(
      @PathVariable String orgSlug, @RequestBody CourseBundleDto.Request request) {
    courseBundleService.createCourseBundle(orgSlug, request);
  }

  @GetMapping("orgs/{orgSlug}/course-bundles")
  public List<CourseBundleDto.Response> getCourseBundle(@PathVariable String orgSlug) {
    return courseBundleService.getCourseBundlesByOrg(orgSlug);
  }

  @GetMapping("orgs/{orgSlug}/course-bundles/{courseBundleId}")
  public CourseBundleDto.Response getCourseBundleById(@PathVariable Long courseBundleId) {
    return courseBundleService.getCourseBundleById(courseBundleId);
  }

  @PostMapping("orgs/{orgSlug}/course-bundles/{courseBundleId}")
  public void addCoursesToBundle(
      @PathVariable Long courseBundleId, @RequestBody CourseBundleDto.Request request) {
    courseBundleService.addCoursesToBundle(courseBundleId, request);
  }

  @DeleteMapping("orgs/{orgSlug}/course-bundles/{courseBundleId}/course-definition/{definitionId}")
  public void deleteCourse(@PathVariable Long courseBundleId, @PathVariable Long definitionId) {
    courseBundleService.deleteCourse(courseBundleId, definitionId);
  }

  @DeleteMapping("orgs/{orgSlug}/course-bundles/{courseBundleId}")
  public void deleteCourseBundle(@PathVariable Long courseBundleId) {
    courseBundleService.deleteCourseBundle(courseBundleId);
  }

  @PostMapping("orgs/{orgSlug}/course-bundles/{courseBundleId}/thumbnail:delete")
  public void deleteThumbNail(@PathVariable Long courseBundleId) {
    courseBundleService.deleteThumbNail(courseBundleId);
  }
}
