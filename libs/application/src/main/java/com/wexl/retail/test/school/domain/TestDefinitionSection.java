package com.wexl.retail.test.school.domain;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "test_definition_sections")
public class TestDefinitionSection {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String name;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "test_definition_id")
  private TestDefinition testDefinition;

  @Column(name = "seq_num")
  private Long sequenceNumber;

  @Column(name = "no_of_questions")
  private Long noOfQuestions;

  @OneToMany(fetch = FetchType.LAZY, mappedBy = "testDefinitionSection", cascade = CascadeType.ALL)
  private List<TestQuestion> testQuestions;
}
