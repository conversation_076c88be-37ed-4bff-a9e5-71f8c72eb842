package com.wexl.retail.mobile.controller;

import com.wexl.retail.mobile.dto.MobileConfigDto;
import com.wexl.retail.mobile.service.MobileService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/public/mobile-config")
@RequiredArgsConstructor
public class MobileController {

  private final MobileService mobileService;

  @GetMapping()
  public List<MobileConfigDto.PackageResponse> getPackages() {
    return mobileService.getPackages();
  }

  @GetMapping("/{packageName}")
  public MobileConfigDto.PackageResponse getKeyValuesByName(@PathVariable String packageName) {
    return mobileService.getKeyValuesById(packageName);
  }
}
