package com.wexl.retail.term.repository;

import com.wexl.retail.term.model.TermAssessmentGrade;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface TermAssessmentGradeRepository extends JpaRepository<TermAssessmentGrade, Long> {
  List<TermAssessmentGrade> findAllByGradeSlugAndBoardSlug(String gradeSlug, String boardSlug);
}
