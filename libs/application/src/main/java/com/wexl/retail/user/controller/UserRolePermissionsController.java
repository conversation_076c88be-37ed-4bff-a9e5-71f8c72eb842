package com.wexl.retail.user.controller;

import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import com.wexl.retail.model.UserRole;
import com.wexl.retail.user.dto.PermissionsResponse;
import com.wexl.retail.user.dto.RolePermissionsRequest;
import com.wexl.retail.user.dto.RoleResponse;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@IsOrgAdmin
@RestController
@RequiredArgsConstructor
public class UserRolePermissionsController {

  private final UserRoleHelper userRoleHelper;

  @GetMapping("orgs/{orgSlug}/permissions")
  public List<PermissionsResponse> getAllPermissions(@RequestParam String category) {
    return new ArrayList<>();
  }

  @GetMapping("orgs/{orgSlug}/roles")
  public List<RoleResponse> getAllRoles(@PathVariable String orgSlug) {
    return new ArrayList<>();
  }

  @GetMapping("orgs/{orgSlug}/roles/{role}/permissions")
  public List<PermissionsResponse> getRoleSpecificPermissions(
      @PathVariable String orgSlug, @PathVariable UserRole role) {
    return userRoleHelper.getPermissionsByOrgAndRole(orgSlug, role);
  }

  @PostMapping("orgs/{orgSlug}/roles/{role}/permissions")
  public ResponseEntity<List<PermissionsResponse>> editPermissions(
      @PathVariable String orgSlug,
      @PathVariable String role,
      @RequestBody RolePermissionsRequest rolePermissionsRequest) {

    return ResponseEntity.ok().body(new ArrayList<>());
  }
}
