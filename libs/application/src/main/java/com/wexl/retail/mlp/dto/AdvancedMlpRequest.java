package com.wexl.retail.mlp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AdvancedMlpRequest extends MlpRequest {

  @JsonProperty("question_uuids")
  private List<String> questionUuids;

  @JsonProperty("section_uuids")
  private List<String> sectionUuids;

  @JsonProperty("student_ids")
  private List<Long> studentIds;
}
