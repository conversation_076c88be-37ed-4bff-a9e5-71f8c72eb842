package com.wexl.retail.zoom.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ZoomAttendanceRequest {

  @JsonProperty("external_event_id")
  private String externalEventId;

  @JsonProperty("schedule_start_time")
  private Timestamp scheduleStartTime;
}
