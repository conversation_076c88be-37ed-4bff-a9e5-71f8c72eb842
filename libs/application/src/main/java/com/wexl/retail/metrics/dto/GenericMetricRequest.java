package com.wexl.retail.metrics.dto;

import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
public class GenericMetricRequest {

  private String metricName;
  private Integer timePeriod;
  private Long fromDate;
  private String year;
  private Map<String, Object> input;
}
