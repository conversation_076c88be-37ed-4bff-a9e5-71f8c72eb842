package com.wexl.retail.student.adaptivelearning.dto;

import java.util.List;
import lombok.Builder;

public class AdaptiveLearningDto {

  @Builder
  public record AdaptiveLearningPromptRequest(
      String board,
      String grade,
      String subject,
      String subtopic,
      List<AdaptiveLearningMcq> mcqs) {}

  @Builder
  public record AdaptiveLearningMcq(
      String question,
      String option1,
      String option2,
      String option3,
      String option4,
      Integer correctAnswer,
      Integer selectedAnswer,
      String complexity) {}

  public record AdaptiveLearningAiResponse(
      String summary, String analysis, String references, String feedback, List<Mcq> mcqs) {}

  @Builder
  public record AdaptiveLearningData(List<String> questionUuids) {}

  @Builder
  public record Mcq(
      String question,
      String option1,
      String option2,
      String option3,
      String option4,
      Long answer,
      Integer marks,
      String explanation) {}
}
