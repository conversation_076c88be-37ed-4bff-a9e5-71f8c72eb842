package com.wexl.retail.mlp.controller;

import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.mlp.dto.*;
import com.wexl.retail.mlp.service.KMeterService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}/")
@RequiredArgsConstructor
public class KMeterController {

  private final KMeterService kMeterService;

  @PostMapping("/knowledge-summary")
  public ResponseEntity<KMSummaryResponse> getKmSummary(
      @PathVariable String orgSlug,
      @RequestBody KMBoardRequest request,
      @RequestParam String board) {

    return ResponseEntity.ok()
        .body(kMeterService.getKmSummary(request.getGrades(), orgSlug, board));
  }

  @PostMapping("/knowledge-detail")
  public List<KMSummaryResponse> getKmGradesSummary(
      @PathVariable String orgSlug,
      @RequestParam(required = false) String board,
      @RequestParam String grades) {
    return kMeterService.getKmGrades(orgSlug, board, grades);
  }

  @PostMapping("/sections/{sectionUuid}/knowledge-chapter-detail")
  ResponseEntity<List<SubjectDetailResponse>> getKmSectionSummary(
      @PathVariable String orgSlug, @PathVariable String sectionUuid) {

    return ResponseEntity.ok(kMeterService.getKmSectionSummary(orgSlug, sectionUuid));
  }

  @PostMapping("/students/{authUserId}/knowledge-summary")
  ResponseEntity<KMSummaryResponse> getKmSummaryByStudent(
      @PathVariable String orgSlug,
      @PathVariable String authUserId,
      @RequestBody KMBoardRequest request) {

    return ResponseEntity.ok(
        kMeterService.getSubjectWisePerformanceOfStudent(authUserId, request.getSubjects()));
  }

  @PostMapping("/students/{authUserId}/knowledge-subject-details")
  public ResponseEntity<SubjectSummaryBuilder> getKmSubjectByStudent(
      @PathVariable String orgSlug,
      @PathVariable String authUserId,
      @RequestBody KMBoardRequest request) {

    return ResponseEntity.ok(
        kMeterService.getChapterWisePerformanceByStudent(authUserId, request.getData()));
  }

  @PostMapping("/students/{authUserId}/knowledge-chapter-detail")
  ResponseEntity<ChapterSummaryDto> getKmChapterDetails(
      @PathVariable String authUserId, @RequestBody KMBoardRequest request) {
    return ResponseEntity.ok(
        kMeterService.getSubtopicWisePerformanceOfStudent(authUserId, request.getData()));
  }

  @IsStudent
  @GetMapping("/students/{authUserId}/knowledge-meter")
  public StudentKMeterDto.StudentKMeterResponse getStudentKMeter(
      @PathVariable("authUserId") String studentId,
      @RequestParam(value = "exam_type") Long examType,
      @RequestParam(value = "subject") String subjectSlug) {

    return kMeterService.getStudentKMeter(studentId, examType, subjectSlug);
  }
}
