package com.wexl.retail.qrcode.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QrCodeDetails {

  private String uuid;
  private String grade;
  private String board;
  private String organization;
  private QrCodeStatus status;
  private String boardName;
  private String gradeName;
  private String publisherName;
}
