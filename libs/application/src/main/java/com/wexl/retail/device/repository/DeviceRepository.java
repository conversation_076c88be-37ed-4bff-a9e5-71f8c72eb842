package com.wexl.retail.device.repository;

import com.wexl.retail.device.model.Device;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface DeviceRepository extends JpaRepository<Device, Long> {
  Optional<Device> findDeviceByUserDetailsId(Long id);

  @Query(
      value =
          """
                  select u.id,u.user_name as userName,u.first_name as firstName,
                                                    u.last_name as lastName,se.grade_name as grade,
                                                    u.deleted_at,d.app_version as appVersion,
                                                    se.name as section,u.organization as organization,
                                                    case when d.app_version is null then 'false'
                                                    else 'true' End as installedMobileApp
                                                    from students s
                                                    left join users u on u.id = s.user_id\s
                                                    left join device_info d on u.id = d.user_id
                                                    left join sections se on se.id=s.section_id
                                                    where u.organization = :orgSlug
                                                    and u.deleted_at is null""",
      nativeQuery = true)
  List<DeviceDataInfoResponse> deviceInfoDetails(String orgSlug);

  @Query(
      value =
          """
                                  select DISTINCT u.id,u.user_name as userName,case when u.last_login is null then 'false'
                                                                    else 'true' End as installedMobileApp from login_history lh
                                  inner join users u on u.id = lh.user_id
                                  inner join students s on s.user_id = u.id
                                  where u.organization in (:orgSlug) and lh.login_device = 'MOBILE' and lh.login_method = 'USERNAME_PASSWORD'
                          """,
      nativeQuery = true)
  List<DeviceDataInfoResponse> deviceInfoData(String orgSlug);

  @Query(value = "select d.appPackageName from Device d where d.userDetails.authUserId = ?1")
  Optional<String> getDeviceAppPackageNameByAuthUsername(String username);
}
