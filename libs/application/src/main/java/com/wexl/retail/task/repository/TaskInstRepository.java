package com.wexl.retail.task.repository;

import com.wexl.retail.model.Student;
import com.wexl.retail.task.domain.Task;
import com.wexl.retail.task.domain.TaskInst;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface TaskInstRepository extends JpaRepository<TaskInst, Long> {
  List<TaskInst> findAllByStudentIdOrderByCompletionStatus(Long studentId, Sort sort);

  @Query(
      value =
          """
                          select ti.* from task_inst ti
                          inner join tasks t on  t.id =  ti.task_id
                            inner join classroom_schedule_inst csi on csi.id = t.classroom_schedule_inst_id
                             where student_id = :studentId and to_char(csi.start_time ,'yyyy-MM-dd') = :date and ti.deleted_at is null
                             order by ti.created_at DESC limit :limit""",
      nativeQuery = true)
  List<TaskInst> getStudentActivitiesByDate(long studentId, String date, int limit);

  Optional<TaskInst> findByIdAndDeletedAtIsNull(long taskInstId);

  @Query(
      value =
          """
                          select ti.*
                          from tasks t join task_inst ti on t.id = ti.task_id where ti.student_id=:studentId and t.due_date >=:fDate and t.due_date<=:tDate
                          and ti.completion_status in (:status)
                          """,
      nativeQuery = true)
  List<TaskInst> getStudentActivityByDates(
      List<String> status, Long studentId, LocalDateTime fDate, LocalDateTime tDate);

  @Query(
      value =
          """
                              select ti.* from tasks t
                              join task_inst ti on t.id = ti.task_id
                              inner join classroom_schedule_inst csi on csi.id = t.classroom_schedule_inst_id
                              where ti.student_id=:studentId and csi.start_time  BETWEEN :fDate and :tDate
                              and ti.deleted_at is null and task_type in ('REVISION','ASSIGNMENT','TEST','PRACTICE')
                           order by CASE WHEN t.subject_slug = 'mathematics' THEN 1
                            WHEN t.subject_slug = 'science' THEN 2
                            WHEN t.subject_slug = 'social' THEN 3
                            WHEN t.subject_slug = 'english' THEN 4
                            END  ASC, CASE WHEN t.task_type = 'REVISION' THEN 1
                            WHEN t.task_type = 'ASSIGNMENT' THEN 2
                            WHEN t.task_type = 'PRACTICE' THEN 3
                            WHEN t.task_type = 'TEST' THEN 4
                          END  ASC
                              """,
      nativeQuery = true)
  List<TaskInst> getStudentActivitiesByTaskType(
      Long studentId, LocalDateTime fDate, LocalDateTime tDate);

  @Query(
      value =
          """
                          select ti.* from tasks t
                                inner join task_inst ti on ti.task_id = t.id
                                inner join classroom_schedule_inst csi  on csi.id = t.classroom_schedule_inst_id
                                where t.org_slug= :orgSlug
                                and (cast((:studentId) as varChar) is null or ti.student_id =(:studentId))
                                and (cast((:subject) as varChar) is null or t.subject_slug =(:subject))
                                 and (cast((:chapter) as varChar) is null or t.chapter_slug  =(:chapter))
                                 and (cast((:subTopic) as varChar) is null or t.subtopic_slug  =(:subTopic))
                                and  csi.start_time  >:startTime and csi.start_time <:endTime
                                 and  (cast((:taskStatus) as varChar) is null or ti.completion_status =(:taskStatus))
                                 and ti.deleted_at is null order by csi.start_time desc limit :limit""",
      nativeQuery = true)
  List<TaskInst> getPendingCorrections(
      String orgSlug,
      LocalDateTime startTime,
      LocalDateTime endTime,
      Long studentId,
      String subject,
      String chapter,
      String subTopic,
      String taskStatus,
      Integer limit);

  @Query(
      value =
          """
                          select ti.* from tasks t
                                  inner join task_inst ti on ti.task_id = t.id
                                  inner join classroom_schedule_inst csi  on csi.id = t.classroom_schedule_inst_id
                                  inner  join classroom_schedules cs on cs.id = csi.classroom_schedule_id
                                  inner  join classroom_teachers ct on ct.classroom_id  = cs.classroom_id
                                  and (cast((:teacherId) as varChar) is null or ct.teacher_id  =(:teacherId))
                                  where  (cast((:studentId) as varChar) is null or ti.student_id =(:studentId))
                                  and (cast((:subject) as varChar) is null or t.subject_slug =(:subject))
                                   and (cast((:chapter) as varChar) is null or t.chapter_slug  =(:chapter))
                                   and (cast((:subTopic) as varChar) is null or t.subtopic_slug  =(:subTopic))
                                  and  csi.start_time  >:startTime and csi.start_time <:endTime
                                  and  (cast((:taskStatus) as varChar) is null or ti.completion_status =(:taskStatus))
                                    and ti.deleted_at is null  order by csi.start_time desc limit :limit""",
      nativeQuery = true)
  List<TaskInst> getTeacherPendingCorrections(
      long teacherId,
      LocalDateTime startTime,
      LocalDateTime endTime,
      Long studentId,
      String subject,
      String chapter,
      String subTopic,
      String taskStatus,
      Integer limit);

  @Query(
      value =
          """
                          select ti.* from tasks t
                                  inner join task_inst ti on ti.task_id = t.id
                                  inner join classroom_schedule_inst csi  on csi.id = t.classroom_schedule_inst_id
                                  inner  join classroom_schedules cs on cs.id = csi.classroom_schedule_id
                                  inner  join classroom_teachers ct on ct.classroom_id  = cs.classroom_id
                                  where  (cast((:studentId) as varChar) is null or ti.student_id in(:studentId))
                                  and (cast((:subject) as varChar) is null or t.subject_slug =(:subject))
                                   and (cast((:chapter) as varChar) is null or t.chapter_slug  =(:chapter))
                                   and (cast((:subTopic) as varChar) is null or t.subtopic_slug  =(:subTopic))
                                  and  csi.start_time  >:startTime and csi.start_time <:endTime
                                  and  (cast((:taskStatus) as varChar) is null or ti.completion_status =(:taskStatus))
                                    and ti.deleted_at is null  order by csi.start_time desc limit :limit""",
      nativeQuery = true)
  List<TaskInst> getStudentsTasksInsts(
      LocalDateTime startTime,
      LocalDateTime endTime,
      List<Long> studentId,
      String subject,
      String chapter,
      String subTopic,
      String taskStatus,
      Integer limit);

  @Query(
      value =
          """
                                          select ti.* from task_inst ti join tasks t
                                          join classroom_schedule_inst csi on csi.id = t.classroom_schedule_inst_id
                                          on ti.task_id = t.id where student_id = :studentId
                                          and t.task_type not in ('REVISION')
                                          and ti.completion_status not in ('DRAFT')
                                          order by CASE WHEN ti.completion_status  = 'NOT_STARTED' THEN 1
                                                                                         WHEN ti.completion_status = 'PENDING' THEN 2
                                                                                         WHEN ti.completion_status = 'COMPLETED' THEN 3
                                                                                       END asc,csi.start_time desc
                          """,
      nativeQuery = true)
  List<TaskInst> getStudentActivityByCompletionStatus(Long studentId);

  @Query(
      value =
          """
                          select ti.* from  task_inst ti
                          inner join tasks t on t.id = ti.task_id
                          where t.classroom_schedule_inst_id = :scheduleInstId""",
      nativeQuery = true)
  List<TaskInst> getTaskInstsByScheduleInst(long scheduleInstId);

  Boolean existsByStudentAndTask(Student student, Task task);

  List<TaskInst> findAllByStudentId(Long studentId);

  @Query(
      value =
          """
                          select * from task_inst ti  where task_id in(:taskIds)
                          """,
      nativeQuery = true)
  List<TaskInst> getTaskInstByTaskId(List<Long> taskIds);

  @Query(
      value =
          """
                          select ti.* from task_inst ti
                          join students s on s.id = ti.student_id
                          join sections s2 on s2.id = s.section_id
                          where task_id  in (:taskIds) and s2.uuid in (:sectionUuids)""",
      nativeQuery = true)
  List<TaskInst> getTaskInstByTaskIdAndSections(List<Long> taskIds, List<UUID> sectionUuids);

  Optional<TaskInst> findByStudentAndTaskAndDeletedAtIsNull(Student student, Task task);
}
