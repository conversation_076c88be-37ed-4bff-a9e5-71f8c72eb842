package com.wexl.retail.pdf.viewer.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Entity
@Accessors(chain = true)
@Table(name = "pdf_bookmarks")
public class Bookmark implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Column(name = "id", updatable = false)
  private Long id;

  @Column(name = "page_index", nullable = false)
  private Long pageIndex;

  @Column(name = "label", nullable = false)
  private String label;

  @Column(name = "zoom", nullable = false)
  private Double zoom;

  @Column(name = "pagex", nullable = false)
  private Double pagex;

  @Column(name = "pagey", nullable = false)
  private Double pagey;

  @Column(name = "page_width", nullable = false)
  private Long pageWidth;

  @Column(name = "page_height", nullable = false)
  private Long pageHeight;

  @Column(name = "doc_id", nullable = false)
  private String docId;

  @Column(name = "parent_id", nullable = false)
  private Long parentId;

  @Column(name = "hierarchy", nullable = false)
  private Long hierarchy;

  @Column(name = "created_by", nullable = false)
  private String createdBy;

  @Column(nullable = false)
  private Timestamp createdAt;

  @Column(nullable = false)
  private Timestamp updatedAt;
}
