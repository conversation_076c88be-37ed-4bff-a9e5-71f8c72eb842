package com.wexl.retail.student.exam.competitive.processor;

import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.student.exam.competitive.dto.CompetitiveExamsDto;
import com.wexl.retail.test.school.domain.TestCategory;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.domain.TestDefinitionSection;
import com.wexl.retail.test.school.domain.TestQuestion;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class StandardProcessor extends AbstractCompetitiveExamProcessor {

  @Override
  public boolean supports(TestCategory testCategory) {
    return TestCategory.STANDARD.equals(testCategory);
  }

  @Override
  public int getTotalMarks() {
    return -1;
  }

  protected List<TestDefinitionSection> buildTestDefinitionSections(
      TestDefinition testDefinition, List<CompetitiveExamsDto.AnswerKeys> answerKeys) {
    List<TestDefinitionSection> sections = new ArrayList<>();
    testDefinition.setTotalMarks(answerKeys.size());
    testDefinition.setNoOfQuestions(answerKeys.size());
    sections.add(
        buildSections(
            testDefinition,
            "Section A",
            (long) answerKeys.size(),
            QuestionType.MCQ,
            answerKeys,
            1L));
    return sections;
  }

  @Override
  protected List<TestQuestion> buildTestQuestions(
      TestDefinitionSection testDefinitionSection,
      String sectionName,
      Long questionCount,
      QuestionType type,
      List<CompetitiveExamsDto.AnswerKeys> answerKeys) {
    List<TestQuestion> questions = new ArrayList<>();
    for (int i = 0; i < questionCount; i++) {
      if (answerKeys.get(i) != null) {
        String answer = answerKeys.get(i).answer();
        Long questionUuid = answerKeys.get(i).questionNumber();
        long mcqAnswer = 0L;
        if (answer != null && !answer.isEmpty()) {
          mcqAnswer = Long.parseLong(answer);
        }
        questions.add(
            TestQuestion.builder()
                .testDefinitionSection(testDefinitionSection)
                .mcqAnswer(mcqAnswer)
                .negativeMarks(0)
                .questionUuid(generateMcqQuestionUuid(mcqAnswer, questionUuid))
                .type(QuestionType.MCQ.name())
                .marks(1)
                .build());
      }
    }

    return questions;
  }
}
