package com.wexl.retail.teacher.preferences;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.domain.TeacherSubjects;
import com.wexl.retail.section.repository.TeacherSubjectsRepository;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.section.service.TeacherSubjectsService;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Data
@Builder
@Service
public class TeacherPreferences {

  private final TeacherSubjectsService teacherSubjectsService;
  private final AuthService authService;
  private final SectionService sectionService;
  private final UserRepository userRepository;
  private final TeacherSubjectsRepository teacherSubjectsRepository;

  public TeacherPreferencesResponse getTeacherPreferences() {
    User teacher = authService.getTeacherDetails();

    List<TeacherSubjects> teacherSubjects =
        teacherSubjectsRepository.findByTeacher(teacher.getAuthUserId());

    Set<String> mappedSubjectsSlugs =
        teacherSubjects.stream().map(TeacherSubjects::getSubject).collect(Collectors.toSet());

    List<Long> mappedSections = teacherSubjects.stream().map(s -> s.getSection().getId()).toList();

    if (!mappedSections.isEmpty() && !mappedSubjectsSlugs.isEmpty()) {
      return TeacherPreferencesResponse.builder()
          .sections(mappedSections)
          .subjects(mappedSubjectsSlugs)
          .build();
    }

    Set<String> subjectPreferences =
        new HashSet<>(teacher.getTeacherInfo().getMetadata().getSubjects());
    User teacherUser = userRepository.getUserByAuthUserId(teacher.getAuthUserId());
    Set<Section> sectionPreferences = new HashSet<>(teacherUser.getTeacherInfo().getSections());

    return TeacherPreferencesResponse.builder()
        .sections(sectionPreferences.stream().map(Section::getId).toList())
        .subjects(subjectPreferences)
        .build();
  }
}
