package com.wexl.retail.content.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class SubTopicResponse {
  private Integer id;
  private String name;
  private Boolean status;
  private String slug;

  @JsonProperty("chapter_id")
  private Integer chapterId;

  @JsonProperty("chapter_slug")
  private String chapterSlug;

  @JsonProperty("grade_id")
  private Integer gradeId;

  @JsonProperty("grade_slug")
  private String gradeSlug;

  @JsonProperty("subject_id")
  private Integer subjectId;

  @JsonProperty("subject_slug")
  private String subjectSlug;

  @JsonProperty("board_id")
  private Integer boardId;

  @JsonProperty("board_slug")
  private String boardSlug;

  private Boolean premium;
  private Integer reference;

  @JsonProperty("chapter_name")
  private String chapterName;

  @JsonProperty("grade_name")
  private String gradeName;

  @JsonProperty("subject_name")
  private String subjectName;
}
