package com.wexl.retail.courses.definition.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.courses.definition.model.CourseDefinitionStatus;
import jakarta.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CourseDefinitionRequest {

  private String name;
  private String description;
  private CourseDefinitionStatus status = CourseDefinitionStatus.DRAFT;

  @JsonProperty("is_private")
  private boolean isPrivate = Boolean.FALSE;

  @JsonIgnore
  @JsonProperty("org_associations")
  private List<String> orgAssociations = Collections.emptyList();

  @JsonProperty("course_category_id")
  @NotNull
  private Long courseCategoryId;

  @JsonProperty("image_reference")
  private String imageReference;

  @JsonProperty("image_extension")
  private String imageExtension;
}
