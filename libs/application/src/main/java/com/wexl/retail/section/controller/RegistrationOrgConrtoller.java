package com.wexl.retail.section.controller;

import com.wexl.retail.content.ContentService;
import com.wexl.retail.model.Grade;
import com.wexl.retail.section.service.SectionService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/public/wexl-internal")
public class RegistrationOrgConrtoller {

  private final SectionService sectionService;
  private final ContentService contentService;

  @GetMapping("/orgs/{orgSlug}/grades")
  public List<Grade> getGradesForOrg(@PathVariable String orgSlug) {
    return sectionService.getGradesByOrgSlug(orgSlug);
  }
}
