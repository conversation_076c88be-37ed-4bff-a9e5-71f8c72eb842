package com.wexl.retail.pdf.viewer.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.sql.Timestamp;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AnnotationRequest {
  private long id;
  private long oldId;
  private double origX;
  private double origY;
  private double origW;
  private double origH;
  private String icon;
  private String text;
  private String font;
  private String color;
  private long fontSize;
  private long audio;
  private boolean audioAvailable;
  private String docId;
  private boolean dummy;
  private boolean hidden;
  private long lineWidth;
  private double opacity;
  private long pageIndex;
  private long pageWidth;
  private long pageHeight;
  private String iconSrc;
  private boolean moving;
  private boolean clicked;
  private String modified;
  private boolean selected;
  private boolean readOnly;
  private String oldModified;
  private double circleLastX;
  private double circleLastY;
  private double circleStartX;
  private double circleStartY;
  private boolean selectable;
  private boolean hasDimension;
  private String formFieldName;
  private String formFieldValue;
  private String backgroundColor;
  private boolean readOnlyComment;

  private String calibrationLabel;
  private double calibrationValue;

  @JsonProperty("calibrationMeasurementType")
  private long calibrationMeasurementTypeId;

  @JsonProperty("x")
  private double cordX;

  @JsonProperty("y")
  private double cordY;

  @JsonProperty("w")
  private double cordW;

  @JsonProperty("h")
  private double cordH;

  @JsonProperty("lineStyle")
  private long lineStyleId;

  @JsonProperty("dateCreated")
  private Timestamp createdAt;

  @JsonProperty("dateModified")
  private Timestamp updatedAt;

  @JsonProperty("annotationType")
  private long annotationTypeId;

  @JsonProperty("measurementType")
  private long measurementTypeId;

  private List<CommentRequest> comments;
  private List<SelectionHandle> selectionHandles;
  private List<DrawingPositionRequest> drawingPositions;
  private List<HighlightTextRectRequest> highlightTextRects;
}
