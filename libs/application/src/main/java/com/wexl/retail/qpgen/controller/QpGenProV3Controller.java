package com.wexl.retail.qpgen.controller;

import com.wexl.retail.qpgen.dto.QPGenProV2Dto;
import com.wexl.retail.qpgen.service.QpGenProV3Service;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}/qp-gen-pro-v3")
@RequiredArgsConstructor
public class QpGenProV3Controller {

  private final QpGenProV3Service qpGenProV3Service;

  @PostMapping("/question-summary")
  public List<QPGenProV2Dto.QuestionSummaryResponse> getQuestionSummary(
      @PathVariable String orgSlug, @RequestBody QPGenProV2Dto.QuestionSummaryRequest request) {
    return qpGenProV3Service.getQuestionSummary(orgSlug, request);
  }

  @PostMapping("/chapters/{chapterSlug}")
  public List<QPGenProV2Dto.SectionsResponse> getChapterSummary(
      @PathVariable("chapterSlug") String chapterSlug,
      @PathVariable String orgSlug,
      @RequestBody QPGenProV2Dto.QuestionSummaryRequest request) {
    return qpGenProV3Service.getChapterSummary(orgSlug, request, chapterSlug);
  }
}
