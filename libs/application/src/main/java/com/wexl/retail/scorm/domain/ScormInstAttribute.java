package com.wexl.retail.scorm.domain;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.*;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "scorm_inst_attributes", schema = "public")
public class ScormInstAttribute extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  @ManyToOne
  @JoinColumn(name = "scorm_inst_id")
  private ScormInst scormInst;

  private String name;
  private String value;
}
