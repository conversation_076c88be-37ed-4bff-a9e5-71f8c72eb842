package com.wexl.retail.teacher.training.controller;

import java.io.IOException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/teacher-training")
public class TeacherTrainingController {

  private final SimpleDataControllerHelper simpleDataControllerHelper;

  @GetMapping("/live-classes")
  public ResponseEntity<String> getTeacherLiveClassSchedules() throws IOException {
    return ResponseEntity.ok()
        .body(simpleDataControllerHelper.getStringResponseEntity("live-classes.json"));
  }
}
