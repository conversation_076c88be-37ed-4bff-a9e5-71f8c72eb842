package com.wexl.retail.mlp.dto;

import java.sql.Timestamp;

public interface StudentResponseMlpQueryResult {

  Long getId();

  String getTitle();

  String getExamRef();

  Integer getQuestionCount();

  String getSynopsisSlug();

  String getVideoSlug();

  String getAltVideoSlug();

  String getTeacherName();

  String getSubtopicName();

  String getSynopsisName();

  String getGradeName();

  String getChapterName();

  String getSubtopicSlug();

  String getChapterSlug();

  String getSubjectSlug();

  String getSubjectName();

  String getSectionName();

  String getPracticeStatus();

  String getSynopsisStatus();

  String getVideoStatus();

  Long getExamId();

  String getQuestionsAssigneeMode();

  Timestamp getCreatedAt();

  String getVideoSource();

  String getComment();

  String getShaLink();
}
