package com.wexl.retail.student.auth;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.storage.S3FileUploadResult;
import com.wexl.retail.parent.auth.ProfileEditRequest;
import com.wexl.retail.student.profile.AnonymousProfileImageRequest;
import com.wexl.retail.student.profile.AnonymousProfileImageResponse;
import com.wexl.retail.student.profile.PasswordResetRequest;
import com.wexl.retail.student.profile.ProfileService;
import com.wexl.retail.student.profile.StudentProfileResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@AllArgsConstructor
public class ProfileController {

  private final ProfileService profileService;

  @GetMapping("orgs/{orgSlug}/users/{authUserId}")
  public StudentProfileResponse getStudentProfile(
      @PathVariable String orgSlug, @PathVariable String authUserId) {
    try {
      return profileService.getStudentProfileDetails(authUserId);
    } catch (Exception e) {
      throw new ApiException(
          InternalErrorCodes.NO_RECORD_FOUND,
          "Unable to Fetch StudentProfile with username [" + authUserId + "]",
          e);
    }
  }

  @PostMapping("orgs/{orgSlug}/users/{authUserId}")
  public S3FileUploadResult uploadProfileImage(
      @PathVariable String orgSlug,
      @PathVariable String authUserId,
      @RequestBody StudentProfileImageRequest profileImageRequest) {
    try {
      return profileService.uploadProfileImage(
          orgSlug,
          profileImageRequest.getImageName(),
          profileImageRequest.getImageType(),
          authUserId);
    } catch (ApiException e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Unable to upload image", e);
    }
  }

  @PostMapping("orgs/{orgSlug}/users/{authUserId}/parent")
  public void editParentAccount(
      @PathVariable String orgSlug,
      @PathVariable String authUserId,
      @RequestBody ProfileEditRequest profileEditRequest) {
    profileService.editStudentProfile(authUserId, profileEditRequest);
  }

  @PostMapping("orgs/{orgSlug}/users/{authUserId}/password:reset")
  @ResponseStatus(HttpStatus.ACCEPTED)
  public void resetPassword(
      @PathVariable String authUserId, @RequestBody PasswordResetRequest passwordResetRequest) {
    if (passwordResetRequest.getNewPassword() == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "New password cannot be null");
    }
    profileService.resetPassword(authUserId, passwordResetRequest.getNewPassword());
  }

  @PostMapping("/public/orgs/{orgSlug}/anonymous-profile-image")
  public AnonymousProfileImageResponse getAnonymousProfileImagePath(
      @PathVariable String orgSlug,
      @RequestBody @Valid @NotNull AnonymousProfileImageRequest request) {
    return profileService.createProfileImageUploadPathForSignup(orgSlug, request);
  }
}
