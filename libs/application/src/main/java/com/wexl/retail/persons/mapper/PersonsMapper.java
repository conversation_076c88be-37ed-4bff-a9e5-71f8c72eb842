package com.wexl.retail.persons.mapper;

import com.wexl.retail.persons.dto.PersonsDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface PersonsMapper {

  PersonsMapper mapper = Mappers.getMapper(PersonsMapper.class);

  @Mapping(target = "firstName", source = "queryResult.firstName")
  @Mapping(target = "lastName", source = "queryResult.lastName")
  @Mapping(target = "noOfTeams", source = "queryResult.noOfTeams")
  @Mapping(target = "reportingManager", source = "queryResult.reportingManager")
  @Mapping(target = "studentId", source = "queryResult.studentId")
  PersonsDto.PersonsResponse.Persons mapQueryResultsToPersons(
      PersonsDto.PersonsResponse.PersonsQueryResult queryResult);
}
