package com.wexl.retail.mlp.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SubjectDetailResponse {

  @JsonProperty("subject_name")
  private String name;

  @JsonProperty("subject_average")
  private Double percentage;

  @JsonProperty("subject_slug")
  private String slug;

  @JsonProperty("subject_attendance_average")
  private Double attendancePercentage;

  @JsonProperty("chapters")
  private List<ChapterSummaryDto> chapterSummaryList;
}
