package com.wexl.retail.elp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;

public record DashBoardResponse() {

  @Builder
  public record DashBoardDetails(
      @JsonProperty("total_elp_count") Long totalElpCount,
      @JsonProperty("elp_attempted_count") Long elpAttemptedCount,
      @JsonProperty("total_my_tests") Long totalMyTestCount,
      @JsonProperty("my_test_attempted_count") Long myTestAttemptedCount,
      Long percentage) {}
}
