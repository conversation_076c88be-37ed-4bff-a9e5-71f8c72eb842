package com.wexl.retail.student.exam.revision.domain;

import com.wexl.retail.mlp.model.Mlp;
import com.wexl.retail.model.Model;
import com.wexl.retail.model.Student;
import com.wexl.retail.student.exam.Exam;
import jakarta.persistence.*;
import lombok.*;

@Getter
@Setter
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "exam_revision")
public class ExamRevision extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "student_id")
  private Student student;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "exam_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
  private Exam exam;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "revision_exam_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
  private Exam revisionExam;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "mlp_id")
  private Mlp mlp;

  @Column(name = "question_uuid")
  private String questionUuid;

  @Column(name = "exam_revision_status")
  @Enumerated(EnumType.STRING)
  private ExamRevisionStatus examRevisionStatus;
}
