package com.wexl.retail.student.exam.competitive.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.test.school.domain.TestCategory;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Builder;

public record CompetitiveExamsDto() {

  @Builder
  public record TeacherResponse(
      @JsonProperty("test_definition_id") Long testDefinitionId,
      @JsonProperty("test_schedule_id") Long testScheduleId,
      String title,
      TestCategory testCategory,
      @JsonProperty("test_schedule_date") Long date,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("section_name") String sectionName,
      @JsonProperty("section_uuid") String sectionUuid) {}

  @Builder
  public record Request(
      String title,
      @JsonProperty("question_paper_ref") String questionPaperRef,
      @JsonProperty("answers_paper_ref") String answersPaperRef,
      @JsonProperty("category") TestCategory testCategory,
      List<String> sections,
      @JsonProperty("answer_keys") List<AnswerKeys> answerKeys) {}

  public record UploadRequest(@NotNull String name, @NotNull String extension) {}

  public record AnswerKeys(@JsonProperty("question_number") Long questionNumber, String answer) {}
}
