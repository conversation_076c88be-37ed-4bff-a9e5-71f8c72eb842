package com.wexl.retail.qpgen.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "qp_gen_internal_choice")
public class InternalChoice extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String questionUuid;

  private String chapterSlug;
  private String subtopicSlug;

  @JsonProperty("subject_slug")
  private String subjectSlug;

  @Column(columnDefinition = "VARCHAR(100) default 'mcq'")
  private String type;

  @Column(columnDefinition = "INTEGER default 1")
  private Integer marks;

  @Column(columnDefinition = "FLOAT default 0")
  @JsonProperty("negative_marks")
  private float negativeMarks;

  @JsonProperty("mcq_answer")
  private Long mcqAnswer;

  private String subjectiveAnswer;

  private Long testSectionId;

  private String testQuestionUUid;

  private Long testQuestionId;
}
