package com.wexl.retail.mobile.controller;

import com.wexl.retail.mobile.dto.CountryCodeDto;
import com.wexl.retail.mobile.service.CountryCodeService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgId}/country-codes")
@RequiredArgsConstructor
public class CountryCodeController {

  private final CountryCodeService countryCodeService;

  @GetMapping()
  public List<CountryCodeDto.Response> getCountryCodes() {
    return countryCodeService.getCountryCodes();
  }
}
