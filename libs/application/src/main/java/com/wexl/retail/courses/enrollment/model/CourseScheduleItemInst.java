package com.wexl.retail.courses.enrollment.model;

import com.wexl.retail.courses.step.model.CourseItem;
import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Entity
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(
    name = "course_schedule_item_inst",
    uniqueConstraints = {
      @UniqueConstraint(columnNames = {"course_schedule_inst_id", "course_item_id"})
    })
public class CourseScheduleItemInst extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  @ManyToOne private CourseScheduleInst courseScheduleInst;

  @ManyToOne private CourseItem courseItem;

  @Column(name = "org_slug")
  private String orgSlug;

  @Enumerated(EnumType.STRING)
  private CourseScheduleItemInstStatus status;
}
