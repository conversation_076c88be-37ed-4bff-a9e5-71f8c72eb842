package com.wexl.retail.department.controller;

import com.wexl.retail.department.dto.DepartmentDto;
import com.wexl.retail.department.service.DepartmentService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}/departments")
@RequiredArgsConstructor
public class DepartmentController {
  private final DepartmentService departmentService;

  @PostMapping
  public void createDepartment(
      @PathVariable String orgSlug, @RequestBody DepartmentDto.Department request) {
    departmentService.createDepartment(orgSlug, request);
  }

  @GetMapping
  public DepartmentDto.DepartmentResponse getDepartments(@PathVariable String orgSlug) {
    return departmentService.getDepartments(orgSlug);
  }

  @GetMapping("/{departmentId}")
  public DepartmentDto.Department getDepartment(@PathVariable Long departmentId) {
    return departmentService.getDepartmentById(departmentId);
  }

  @PutMapping("/{departmentId}")
  public void updateDepartment(
      @PathVariable Long departmentId, @RequestBody DepartmentDto.Department request) {
    departmentService.updateDepartment(departmentId, request);
  }

  @DeleteMapping("/{departmentId}")
  public void deleteDepartment(@PathVariable Long departmentId) {
    departmentService.deleteDepartment(departmentId);
  }
}
