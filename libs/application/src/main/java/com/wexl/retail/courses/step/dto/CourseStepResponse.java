package com.wexl.retail.courses.step.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.courses.step.model.CourseItemAttributesDto;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_EMPTY)
public class CourseStepResponse {

  private String title;

  @JsonProperty("exam_id")
  private List<Long> examId;

  private Long id;

  @JsonProperty("seq_num")
  private Integer sequenceNumber;

  @JsonProperty("item_type")
  private String itemType;

  @JsonProperty("published_at")
  private Long publishedAt;

  @JsonProperty("course_module_id")
  private Long courseModuleId;

  @JsonProperty("course_page_id")
  private Long coursePageId;

  @JsonProperty("test_definition_id")
  private Long testDefinitionId;

  @JsonProperty("asset_slug")
  private String assetSlug;

  @JsonProperty("file_id")
  private Long fileId;

  @JsonProperty("status")
  private String status;

  @JsonProperty("course_schedule_item_inst_id")
  private Long courseScheduleItemInstId;

  private CourseItemAttributesDto.Attributes attributes;

  @JsonProperty("corrected")
  private Boolean corrected;

  @JsonProperty("course_step_km")
  private Double courseStepKM;
}
