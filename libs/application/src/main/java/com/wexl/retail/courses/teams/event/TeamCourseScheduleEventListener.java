package com.wexl.retail.courses.teams.event;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.courses.teams.service.TeamCoursesService;
import com.wexl.retail.team.event.TeamCourseScheduleMetaData;
import lombok.AllArgsConstructor;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class TeamCourseScheduleEventListener
    implements ApplicationListener<TeamCourseScheduleEvent> {

  private final TeamCoursesService teamCoursesService;

  @Override
  public void onApplicationEvent(TeamCourseScheduleEvent event) {
    Object source = event.getSource();
    if (source instanceof TeamCourseScheduleMetaData metaData) {
      try {
        teamCoursesService.scheduleCourseToNewlyOnboardedTeam(metaData);
      } catch (Exception e) {
        throw new ApiException(
            InternalErrorCodes.SERVER_ERROR,
            "Failed to schedule courses for the new persons of this team",
            e);
      }
    }
  }
}
