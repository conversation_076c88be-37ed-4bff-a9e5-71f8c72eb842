package com.wexl.retail.products.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record CourseDto() {
  public record CourseRequest(
      @JsonProperty("course_def_ids") List<Long> courseRequest,
      @JsonProperty("course_bundle_ids") List<Long> courseBundleIds) {}

  @Builder
  public record CourseEnrollResponse(
      @JsonProperty("course_def_id") Long courseDefId, @JsonProperty("course_id") Long courseId) {}
}
