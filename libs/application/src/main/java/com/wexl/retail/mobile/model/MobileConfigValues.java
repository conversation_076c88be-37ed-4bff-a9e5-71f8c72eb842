package com.wexl.retail.mobile.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.Data;

@Data
@Entity
@Table(name = "mobile_config_values")
public class MobileConfigValues extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "mobile_config_id")
  private MobileConfig mobileConfig;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "mobile_config_key_id")
  private MobileConfigKeys mobileConfigKey;

  @Column(name = "name")
  private String name;

  private Boolean status;
}
