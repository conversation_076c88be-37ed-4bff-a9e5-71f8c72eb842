package com.wexl.retail.content.model;

import com.wexl.retail.model.UiConfig;
import com.wexl.retail.organization.dto.Curriculum;
import com.wexl.retail.organization.dto.OrgMetaData;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class OrganizationRequest {

  @NotNull private String name;
  @NotNull private String type;
  @NotNull private String slug;
  @NotNull String orgTypeSlug;
  private String preferredBoardSlug;
  private Curriculum curriculum;
  private OrgMetaData orgMetaData;
  private String parentOrgSlug;
  private String abbreviation;
  private UiConfig uiConfig;
}
