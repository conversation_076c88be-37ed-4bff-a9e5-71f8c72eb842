package com.wexl.retail.pdf.viewer.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Entity
@Accessors(chain = true)
@Table(name = "pdf_highlight_text_rects")
public class HighlightTextRect implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Column(name = "id", updatable = false)
  private long id;

  @Column(name = "annotation_id", nullable = false)
  private Long annotationId;

  @Column(name = "coordinate", nullable = false)
  private String coordinate;

  @Column(name = "dom_rotate_angle", nullable = false)
  private Long domRotateAngle;
}
