package com.wexl.retail.metrics.handler;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.joda.time.DateTime;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SyllabusTrackingReport extends AbstractMetricHandler implements MetricHandler {
  @Override
  public String name() {
    return "syllabus-tracking-report";
  }

  @Override
  public List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    String sectionSlug =
        Optional.ofNullable(genericMetricRequest.getInput().get(SECTIONS))
            .map(String.class::cast)
            .orElse(null);
    String boardSlug =
        Optional.ofNullable(genericMetricRequest.getInput().get(BOARD))
            .map(String.class::cast)
            .orElse(null);
    return syllabusTrackingService.getSyllabusDetails(
        DateTime.now().getMillis(), org, sectionSlug, boardSlug);
  }
}
