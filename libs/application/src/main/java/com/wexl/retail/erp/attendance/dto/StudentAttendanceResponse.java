package com.wexl.retail.erp.attendance.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.calenderevent.dto.CalenderEventDto;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class StudentAttendanceResponse {

  @JsonProperty("section_attendance_details_id")
  private Long id;

  @JsonProperty("name_of_student")
  private String name;

  @JsonProperty("grade")
  private String grade;

  @JsonProperty("section_id")
  private String section;

  @JsonProperty("status")
  private String status;

  @JsonProperty("auth_user_id")
  private String authUserId;

  @JsonProperty("user_id")
  private Long userId;

  @JsonProperty("present_days")
  private Integer presentDays;

  @JsonProperty("total_days_in_month")
  private Integer totalDaysInMonth;

  @JsonProperty("calender_response")
  private List<CalenderEventDto.CalenderResponse> response;
}
