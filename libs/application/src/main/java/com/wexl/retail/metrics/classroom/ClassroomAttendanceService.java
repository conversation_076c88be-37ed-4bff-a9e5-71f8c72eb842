package com.wexl.retail.metrics.classroom;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.AuthUtil;
import com.wexl.retail.classroom.core.dto.ClassRoomDto;
import com.wexl.retail.classroom.core.dto.ScheduleInstAttendanceStatus;
import com.wexl.retail.classroom.core.model.*;
import com.wexl.retail.classroom.core.repository.*;
import com.wexl.retail.classroom.core.service.ScheduleInstAttendanceService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.dto.StudentClassReportInterface;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.notification.service.EventNotificationService;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.dto.NotificationType;
import com.wexl.retail.notifications.service.NotificationsService;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.telegram.service.UserService;
import java.time.*;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ClassroomAttendanceService {
  private final DateTimeUtil dateTimeUtil;
  private final ClassroomRepository classroomRepository;
  private final UserService userService;

  private final UserRepository userRepository;
  private final ScheduleInstAttendanceRepository scheduleInstAttendanceRepository;
  private final ScheduleInstAttendanceDetailsRepository scheduleInstAttendanceDetailsRepository;
  private final ClassroomScheduleInstRepository classroomScheduleInstRepository;
  private final AuthService authService;
  private final ClassroomScheduleRepository classroomScheduleRepository;
  private final NotificationsService notificationsService;
  private final EventNotificationService eventNotificationService;
  private final ScheduleInstAttendanceService scheduleInstAttendanceService;
  private final StudentRepository studentRepository;

  public List<GenericMetricResponse> getClassRoomReportDetailsBySlug(
      String orgSlug,
      Long fromDateInEpoch,
      Long toDateInEpoch,
      String attendanceStatus,
      String classRoomName) {
    LocalDateTime fromDate = dateTimeUtil.convertEpochToIso8601Legacy(fromDateInEpoch);
    LocalDateTime toDate = dateTimeUtil.convertEpochToIso8601Legacy(toDateInEpoch);
    var scheduleInstAttendances =
        scheduleInstAttendanceRepository.getScheduleInstAttendanceByDate(
            orgSlug, fromDate.with(LocalTime.MIN), toDate.with(LocalTime.MAX), classRoomName);
    List<ScheduleInstAttendanceDetails> scheduleInstAttendanceDetails =
        scheduleInstAttendanceDetailsRepository.findAllByScheduleInstAttendanceIn(
            scheduleInstAttendances);
    if (Objects.nonNull(attendanceStatus)) {
      return scheduleInstAttendanceDetails.stream()
          .filter(
              siad ->
                  siad.getAttendanceStatus()
                      .equals(ScheduleInstAttendanceStatus.fromValue(attendanceStatus)))
          .map(siad -> GenericMetricResponse.builder().data(mapClassRoomAttendance(siad)).build())
          .toList();
    }
    return scheduleInstAttendanceDetails.stream()
        .map(siad -> GenericMetricResponse.builder().data(mapClassRoomAttendance(siad)).build())
        .toList();
  }

  Map<String, Object> mapClassRoomAttendance(
      ScheduleInstAttendanceDetails scheduleInstAttendanceDetails) {
    Classroom classroom =
        scheduleInstAttendanceDetails
            .getScheduleInstAttendance()
            .getClassroomScheduleInst()
            .getClassroomSchedule()
            .getClassroom();
    var student = getStudentByValidateStudentPromotion(scheduleInstAttendanceDetails.getStudent());
    Map<String, Object> map = new HashMap<>();
    map.put("class_Name", classroom.getName());
    map.put("attendance_status", scheduleInstAttendanceDetails.getAttendanceStatus());
    map.put("id", classroom.getId());
    map.put(
        "meeting_end_time",
        convertIso8601ToEpoch(
            scheduleInstAttendanceDetails
                .getScheduleInstAttendance()
                .getClassroomScheduleInst()
                .getEndTime()));
    map.put(
        "meeting_start_time",
        convertIso8601ToEpoch(
            scheduleInstAttendanceDetails
                .getScheduleInstAttendance()
                .getClassroomScheduleInst()
                .getStartTime()));
    map.put(
        "class_room_status",
        scheduleInstAttendanceDetails
            .getScheduleInstAttendance()
            .getClassroomScheduleInst()
            .getStatus());
    map.put("student_name", userService.getNameByUserInfo(student.getUserInfo()));
    map.put("last_Login", student.getUserInfo().getLastLogin());
    map.put(
        "teacher_name",
        userService.getFullNamesByUsers(
            classroom.getTeachers().stream().map(Teacher::getUserInfo).toList()));
    map.put("user_name", student.getUserInfo().getUserName());
    map.put("org_slug", classroom.getOrgSlug());
    map.put(
        "start_date",
        convertIso8601ToEpoch(
            scheduleInstAttendanceDetails
                .getScheduleInstAttendance()
                .getClassroomScheduleInst()
                .getStartTime()));
    map.put(
        "attendance_updated_at",
        convertIso8601ToEpoch(scheduleInstAttendanceDetails.getUpdatedAt().toLocalDateTime()));
    return map;
  }

  private Student getStudentByValidateStudentPromotion(Student student) {
    if (Objects.nonNull(student.getUserInfo())) {
      return student;
    }
    var promotedStudent =
        studentRepository
            .findByPrevStudentId(student.getId())
            .orElseThrow(
                () ->
                    new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.StudentNotFound"));
    return getStudentByValidateStudentPromotion(promotedStudent);
  }

  public List<GenericMetricResponse> getHostJoinedReport(
      String orgSlug, Long fromDateInEpoch, Long toDateInEpoch) {
    var user = authService.getTeacherDetails();
    if (Objects.isNull(fromDateInEpoch) || Objects.isNull(toDateInEpoch)) {
      return buildReport(
          classroomScheduleInstRepository.findByOrgSlugAndDeletedAtIsNullAndStartTimeBetween(
              orgSlug,
              LocalDateTime.now().minusDays(7).with(LocalTime.MIN),
              LocalDateTime.now().with(LocalTime.now())));
    }
    LocalDateTime fromDate = dateTimeUtil.convertEpochToIso8601(fromDateInEpoch);
    LocalDateTime toDate = dateTimeUtil.convertEpochToIso8601(toDateInEpoch);

    if (!AuthUtil.isOrgAdmin(user)) {
      return buildTeacherReport(
          classroomScheduleRepository.getTeacherScheduleInsts(
              orgSlug,
              user.getTeacherInfo().getId(),
              fromDate.toLocalDate().toString(),
              toDate.toLocalDate().toString()));
    }
    return buildReport(
        classroomScheduleInstRepository.findByOrgSlugAndDeletedAtIsNullAndStartTimeBetween(
            orgSlug, fromDate.with(LocalTime.MIN), toDate.with(LocalTime.MAX)));
  }

  private List<GenericMetricResponse> buildTeacherReport(
      List<ClassroomScheduleInstResults> teacherScheduleInsts) {
    if (teacherScheduleInsts.isEmpty()) {
      return Collections.emptyList();
    }
    return teacherScheduleInsts.stream()
        .map(inst -> GenericMetricResponse.builder().data(mapTeacherAttendanceReport(inst)).build())
        .toList();
  }

  private Map<String, Object> mapTeacherAttendanceReport(ClassroomScheduleInstResults inst) {
    Map<String, Object> reportMap = new HashMap<>();
    reportMap.put("classroom_id", inst.getClassroomId());
    reportMap.put("schedule_inst_id", inst.getScheduleInstId());
    reportMap.put("classroom_name", inst.getClassroomName());
    reportMap.put("day_of_week", inst.getDayOfWeek());
    reportMap.put("status", inst.getStatus());
    reportMap.put("title", inst.getTitle());
    reportMap.put(
        "tutor_name",
        getTutorNamesByIds(
            ClassRoomDto.Tutors.builder()
                .tutorIds(List.of(authService.getTeacherDetails().getId()))
                .build()));
    reportMap.put("schedule_start_time", convertIso8601ToEpoch(inst.getStartTime()));
    reportMap.put("schedule_end_time", convertIso8601ToEpoch(inst.getEndTime()));
    reportMap.put(
        "joined_time",
        Objects.nonNull(inst.getHostJoinTime())
            ? convertIso8601ToEpoch(inst.getHostJoinTime())
            : null);
    return reportMap;
  }

  private List<GenericMetricResponse> buildReport(
      List<ClassroomScheduleInst> classroomScheduleInsts) {
    if (classroomScheduleInsts.isEmpty()) {
      return Collections.emptyList();
    }
    return sortInstsByHostJoinTime(classroomScheduleInsts).stream()
        .map(inst -> GenericMetricResponse.builder().data(mapAdminAttendanceReport(inst)).build())
        .toList();
  }

  private List<ClassroomScheduleInst> sortInstsByHostJoinTime(
      List<ClassroomScheduleInst> classroomScheduleInsts) {
    var joinedInsts =
        classroomScheduleInsts.stream()
            .filter(inst -> Objects.nonNull(inst.getHostJoinTime()))
            .toList();
    var unJoinedInsts =
        classroomScheduleInsts.stream()
            .filter(inst -> Objects.isNull(inst.getHostJoinTime()))
            .sorted(Comparator.comparing(ClassroomScheduleInst::getStartTime).reversed())
            .toList();
    var sortedInsts =
        new ArrayList<>(
            joinedInsts.stream()
                .sorted(Comparator.comparing(ClassroomScheduleInst::getHostJoinTime).reversed())
                .toList());
    sortedInsts.addAll(new ArrayList<>(unJoinedInsts));
    return sortedInsts;
  }

  private Map<String, Object> mapAdminAttendanceReport(ClassroomScheduleInst inst) {
    Map<String, Object> reportMap = new HashMap<>();
    reportMap.put("classroom_id", inst.getClassroomSchedule().getClassroom().getId());
    reportMap.put("schedule_inst_id", inst.getId());
    reportMap.put("classroom_name", inst.getClassroomSchedule().getClassroom().getName());
    reportMap.put("day_of_week", inst.getDayOfWeek());
    reportMap.put("tutor_name", getTutorNamesByIds(inst.getTutors()));
    reportMap.put("status", inst.getStatus());
    reportMap.put("title", inst.getTitle());
    reportMap.put("schedule_start_time", convertIso8601ToEpoch(inst.getStartTime()));
    reportMap.put("schedule_end_time", convertIso8601ToEpoch(inst.getEndTime()));
    reportMap.put(
        "joined_time",
        Objects.nonNull(inst.getHostJoinTime())
            ? convertIso8601ToEpoch(inst.getHostJoinTime().toLocalDateTime())
            : null);
    return reportMap;
  }

  private List<String> getTutorNamesByIds(ClassRoomDto.Tutors tutors) {
    return Objects.nonNull(tutors) && !tutors.tutorIds().isEmpty()
        ? userService.getFullNamesByUsers(userRepository.findAllById(tutors.tutorIds()))
        : Collections.emptyList();
  }

  public void sendNotificationToClassAbsentees(String orgSlug, boolean isLongAbsentees) {
    List<Student> absentees = new ArrayList<>();
    NotificationDto.NotificationRequest notificationRequest;
    if (isLongAbsentees) {
      absentees.addAll(scheduleInstAttendanceService.getLongAbsentees(orgSlug));
      var studentIds = absentees.stream().map(Student::getId).collect(Collectors.toSet());
      notificationRequest = buildNotificationRequest(studentIds, true);
    } else {
      absentees.addAll(scheduleInstAttendanceService.getAbsentees(orgSlug, 1));
      var studentIds = absentees.stream().map(Student::getId).collect(Collectors.toSet());
      notificationRequest = buildNotificationRequest(studentIds, false);
    }
    if (absentees.isEmpty()) {
      log.info("There are no absentees to send notifications");
      return;
    }
    notificationsService.createNotificationByTeacher(
        orgSlug, notificationRequest, "3809eb52-e13e-464b-b396-861939961dcc", false);
  }

  private NotificationDto.NotificationRequest buildNotificationRequest(
      Set<Long> absenteesIds, boolean isLongAbsent) {
    if (absenteesIds.isEmpty()) {
      return null;
    }
    var msg =
        isLongAbsent
            ? "You have been absent from your tuition class for 3 days. If the absence is going to be longer, please let the relationship manager know when you will be joining again."
            : "You missed your tuition class today. We look forward to you joining next class.";
    return NotificationDto.NotificationRequest.builder()
        .title(isLongAbsent ? "Long Absent Notice" : "Absent Notice ")
        .message(msg)
        .notificationType(NotificationType.INDIVIDUAL)
        .studentIds(new ArrayList<>(absenteesIds))
        .build();
  }

  public List<GenericMetricResponse> getAttendanceDetails(
      String orgSlug, Long fromDate, Long endDate, List<String> classroomId) {
    List<GenericMetricResponse> genericMetricResponses = new ArrayList<>();
    List<Long> ids = new ArrayList<>();
    for (String id : classroomId) {
      ids.add(Long.parseLong(id));
    }
    List<Classroom> classrooms =
        classroomRepository.findByIdInAndOrgSlugAndDeletedAtIsNull(ids, orgSlug);
    if (classrooms != null && !classrooms.isEmpty()) {
      for (Classroom classroom : classrooms) {
        var fDate = dateTimeUtil.convertEpochToIso8601(fromDate);
        var eDate = dateTimeUtil.convertEpochToIso8601(endDate);

        List<StudentClassReportInterface> studentAttendanceDetails =
            studentAttendanceDetails(classroom, fDate, eDate);

        for (StudentClassReportInterface studentReports : studentAttendanceDetails) {
          Map<String, Object> data = new HashMap<>();
          Map<String, Object> summary = new HashMap<>();
          if (classroom.getStudents().isEmpty()) {
            return Collections.emptyList();
          }
          summary.put("classroom_id", classroom.getId());
          summary.put("classroom_name", classroom.getName());
          data.put("student_name", studentReports.getStudentName());
          data.put("no.of_classes_present", studentReports.getPresentCount());
          data.put("no.of_classes_absent", studentReports.getAbsentCount());
          data.put("no_of_classes_not_marked", studentReports.getNotMarked());
          data.put("total_classes", studentReports.getTotalCount());
          genericMetricResponses.add(
              GenericMetricResponse.builder().data(data).summary(summary).build());
        }
      }
    }
    return genericMetricResponses;
  }

  private List<StudentClassReportInterface> studentAttendanceDetails(
      Classroom classroom, LocalDateTime fromDate, LocalDateTime endDate) {
    return classroomRepository.getStudentData(
        classroom.getOrgSlug(),
        classroom.getId(),
        fromDate.with(LocalTime.MIN),
        endDate.with(LocalTime.MAX));
  }
}
