package com.wexl.retail.courses.definition.model;

import com.wexl.retail.courses.categories.model.CourseCategory;
import com.wexl.retail.model.Model;
import com.wexl.retail.model.User;
import jakarta.persistence.*;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Entity
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@Table(name = "course_definition")
public class CourseDefinition extends Model {

  @Id
  @GeneratedValue(
      strategy = GenerationType.SEQUENCE,
      generator = "course-definition-sequence-generator")
  @SequenceGenerator(
      name = "course-definition-sequence-generator",
      sequenceName = "course_definition_seq",
      allocationSize = 1)
  private long id;

  private String name;

  @Column(columnDefinition = "TEXT")
  private String description;

  @Enumerated(EnumType.STRING)
  private CourseDefinitionStatus status;

  @ManyToOne private User owner;

  private int version;

  @Column(name = "org_slug")
  private String orgSlug;

  @Column(name = "is_private")
  private boolean isPrivate;

  @Column(name = "published_at")
  private Timestamp publishedAt;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "course_category_id")
  private CourseCategory courseCategory;

  private Long duration;

  @Column(name = "image_path")
  private String imagePath;
}
