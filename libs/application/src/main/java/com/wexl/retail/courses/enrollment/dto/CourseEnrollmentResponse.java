package com.wexl.retail.courses.enrollment.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_EMPTY)
public class CourseEnrollmentResponse {

  @JsonProperty("enrollment_date")
  private Long enrollmentDate;

  @JsonProperty("full_name")
  private String fullName;

  @JsonProperty("student_id")
  private Long studentId;

  @JsonProperty("status")
  private String status;

  @JsonProperty("team_id")
  private Long teamId;

  @JsonProperty("team_name")
  private String teamName;

  @JsonProperty("duration")
  private Long duration;

  @JsonProperty("student_authid")
  private String studentAuthId;

  @JsonProperty("course_scheduleid")
  private Long courseScheduleId;
}
