package com.wexl.retail.metrics.handler;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AllStudentTestReport extends AbstractMetricHandler {
  @Override
  public String name() {
    return "all-student-test-report";
  }

  @Override
  protected List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {

    Long testScheduleId =
        Long.valueOf(genericMetricRequest.getInput().get(TEST_SCHEDULE_ID).toString());
    return leaderBoardReportService.leaderBoardReportDetails(testScheduleId);
  }
}
