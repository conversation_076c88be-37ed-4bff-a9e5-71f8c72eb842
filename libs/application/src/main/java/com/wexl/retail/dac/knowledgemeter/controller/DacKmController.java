package com.wexl.retail.dac.knowledgemeter.controller;

import static com.wexl.retail.util.Constants.AUTHORIZATION_HEADER;

import com.wexl.retail.dac.knowledgemeter.dto.DacKmDto;
import com.wexl.retail.dac.knowledgemeter.service.DacKmService;
import com.wexl.retail.mlp.dto.KMBoardRequest;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}")
@RequiredArgsConstructor
public class DacKmController {
  private final DacKmService dacKmService;

  @PostMapping("/dac-kms")
  public DacKmDto.DacKmResponse getSummaryByBoard(
      @PathVariable String orgSlug,
      @RequestParam("board") String boardSlug,
      @RequestParam(value = "from_date", required = false) Long fromDate,
      @RequestParam(value = "to_date", required = false) Long toDate,
      @RequestBody KMBoardRequest request) {
    return dacKmService.getSummaryByBoard(
        orgSlug, boardSlug, fromDate, toDate, request.getGrades());
  }

  @PostMapping("/dac-kms-details")
  public List<DacKmDto.SectionResponse> getSummaryByGrade(
      @PathVariable String orgSlug,
      @RequestParam("board") String boardSlug,
      @RequestParam("grade") String gradeSlug,
      @RequestParam(value = "from_date", required = false) Long fromDate,
      @RequestParam(value = "to_date", required = false) Long toDate,
      @RequestBody DacKmDto.Data subjects) {
    return dacKmService.getSummaryByBoardAndGrade(
        orgSlug, boardSlug, gradeSlug, subjects, fromDate, toDate);
  }

  @PostMapping("/dac-kms-grade")
  public DacKmDto.gradeResponse getKmByGrade(
      @PathVariable String orgSlug,
      @RequestParam("board") String boardSlug,
      @RequestParam("grade") String gradeSlug,
      @RequestParam(value = "from_date", required = false) Long fromDate,
      @RequestParam(value = "to_date", required = false) Long toDate,
      @RequestBody DacKmDto.Data subjects) {
    return dacKmService.getKmByGrade(orgSlug, boardSlug, gradeSlug, subjects, fromDate, toDate);
  }

  @PostMapping("/sections/{sectionUuid}/dac-kms")
  public List<DacKmDto.SubjectResponse> getSummeryBySection(
      @PathVariable String orgSlug,
      @RequestParam String board,
      @PathVariable String sectionUuid,
      @RequestParam(value = "from_date", required = false) Long fromDate,
      @RequestParam(value = "to_date", required = false) Long toDate,
      @RequestBody DacKmDto.Data subjects) {
    return dacKmService.getSummaryBySection(
        orgSlug, board, sectionUuid, subjects, fromDate, toDate);
  }

  @ResponseStatus(HttpStatus.CREATED)
  @PostMapping("/test-schedules-km-migrations")
  public void testKnowledgeMigrate(
      @RequestBody DacKmDto.TestKmMigrationRequest request,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {
    dacKmService.migrateTestScheduleKnowledgeBySchedule(request, bearerToken);
  }

  @PostMapping("/dac-km-summary")
  public List<DacKmDto.BoardSummary> getOverallSummary(
      @PathVariable String orgSlug,
      @RequestParam(value = "from_date", required = false) Long fromDate,
      @RequestParam(value = "to_date", required = false) Long toDate) {
    return dacKmService.dacKmSummary(orgSlug, fromDate, toDate);
  }

  @PostMapping("/dac-km-summary:upload")
  public void uploadKmSummary(
      @RequestParam("org_slug") String orgSlug,
      @RequestParam(value = "from_date", required = false) Long fromDate,
      @RequestParam(value = "to_date", required = false) Long toDate) {
    dacKmService.uploadKmSummary(orgSlug, fromDate, toDate);
  }

  @GetMapping("/dac-km-summary")
  public List<DacKmDto.BoardSummary> getDacKmSummary(@PathVariable String orgSlug) {
    return dacKmService.getDacKmSummary(orgSlug);
  }
}
