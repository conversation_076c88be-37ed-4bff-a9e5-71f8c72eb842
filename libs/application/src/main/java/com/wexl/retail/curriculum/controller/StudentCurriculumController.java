package com.wexl.retail.curriculum.controller;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.caching.CacheConstants;
import com.wexl.retail.curriculum.service.StudentCurriculumService;
import com.wexl.retail.model.EduBoard;
import com.wexl.retail.model.User;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.CacheControl;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class StudentCurriculumController {

  @Autowired private StudentCurriculumService studentCurriculumService;

  @Autowired private AuthService authService;

  @GetMapping("/orgs/{org}/students/{userName}/curriculum")
  public ResponseEntity<List<EduBoard>> getStudentCurriculum(@PathVariable String userName) {
    User user = authService.getUserByAuthUserId(userName);
    return ResponseEntity.ok()
        .cacheControl(CacheControl.maxAge(CacheConstants.MEDIUM))
        .body(studentCurriculumService.getStudentCurriculum(user));
  }
}
