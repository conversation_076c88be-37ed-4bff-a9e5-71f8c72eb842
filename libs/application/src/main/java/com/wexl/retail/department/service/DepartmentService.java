package com.wexl.retail.department.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.department.dto.DepartmentDto;
import com.wexl.retail.department.model.Department;
import com.wexl.retail.department.repository.DepartmentRepository;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class DepartmentService {

  private final DepartmentRepository departmentRepository;

  public void createDepartment(String orgSlug, DepartmentDto.Department request) {
    var department = new Department();
    department.setName(request.name());
    department.setStatus(request.status());
    department.setOrgSlug(orgSlug);
    departmentRepository.save(department);
  }

  public DepartmentDto.DepartmentResponse getDepartments(String orgSlug) {
    var departments = departmentRepository.findAllByOrgSlug(orgSlug);
    List<DepartmentDto.Department> departmentList = new ArrayList<>();
    departments.forEach(department -> departmentList.add(buildDepartMent(department)));
    return DepartmentDto.DepartmentResponse.builder().departments(departmentList).build();
  }

  private DepartmentDto.Department buildDepartMent(Department department) {
    return DepartmentDto.Department.builder()
        .id(department.getId())
        .name(department.getName())
        .status(department.getStatus())
        .orgSlug(department.getOrgSlug())
        .build();
  }

  public void updateDepartment(Long departmentId, DepartmentDto.Department request) {
    var department = validateDepartment(departmentId);
    department.setStatus(request.status());
    department.setName(request.name());
    departmentRepository.save(department);
  }

  public void deleteDepartment(Long departmentId) {
    departmentRepository.deleteById(departmentId);
  }

  public DepartmentDto.Department getDepartmentById(Long departmentId) {
    var department = validateDepartment(departmentId);
    return buildDepartMent(department);
  }

  public Department validateDepartment(Long departmentId) {
    return departmentRepository
        .findById(departmentId)
        .orElseThrow(
            () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.DepartmentNotFound"));
  }

  public Department getDepartmentByNameAndOrgSlug(String orgSlug, String departmentName) {
    return departmentRepository
        .findByNameAndOrgSlug(departmentName, orgSlug)
        .orElseThrow(
            () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.DepartmentNotFound"));
  }
}
