package com.wexl.retail.courses.step.model;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum CourseItemType {
  SCHOOL_TEST("SCHOOL_TEST"),
  MOCK_TEST("MOCK_TEST"),
  ASSIGNMENT("ASSIGNMENT"),
  PAGE("PAGE"),
  VIDEO("VIDEO"),
  FILE("FILE"),
  ASSET("ASSET"),
  SCORM("SCORM"),

  CONCEPT_VIDEOS("CONCEPT_VIDEOS");

  private final String value;

  public static CourseItemType fromValue(String value) {
    if (value == null || "".equals(value)) {
      throw new IllegalArgumentException("Value cannot be null or empty!");
    }

    for (var enumEntry : CourseItemType.values()) {
      if (enumEntry.toString().equalsIgnoreCase(value)) {
        return enumEntry;
      }
    }

    throw new IllegalArgumentException("Do not understand the Type " + value);
  }

  @Override
  public String toString() {
    return this.value;
  }
}
