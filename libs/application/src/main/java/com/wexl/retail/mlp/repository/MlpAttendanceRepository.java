package com.wexl.retail.mlp.repository;

import com.wexl.retail.mlp.dto.*;
import com.wexl.retail.mlp.model.MlpAttendance;
import java.time.LocalDate;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface MlpAttendanceRepository extends JpaRepository<MlpAttendance, Long> {

  @Query(
      value =
          "select id from mlp_attendance where student_id = :studentId and to_char(mlp_date,'YYYY-MM-DD') = :mlpDate",
      nativeQuery = true)
  List<Long> findAttendanceByStudentIdAndMlpDate(long studentId, String mlpDate);

  @Query(
      value =
          """
                      with CTE as (select m.id,m.grade_slug as GradeSlug,to_char(m.created_at,'YYYY-MM-DD') as Created<PERSON>,m.created_at as CreatedAt ,\s
                      count(distinct m.id) as mlpCount,m.grade_name as GradeName,mi.student_id,
                       count(case when (exam_id is not null or synopsis_status = 'COMPLETED' or video_status = 'COMPLETED') then 1 end)
                       from mlp m
                                 join mlp_inst mi on m.id  = mi.mlp_id where mlp_id in(:mlpIds)
                                        group by m.id,m.grade_slug,mi.student_id
                                        order by m.grade_slug,CreatedAt,Createddt )
                      select distinct c.student_id as StudentId,cast(c.CreatedAt as varchar)  as MlpDate from CTE c
                      left join mlp_attendance ma on c.Createddt = to_char(ma.mlp_date,'YYYY-MM-DD') where Count > 0
                                     """,
      nativeQuery = true)
  List<MlpAttendanceResult> findAttendanceByMlpIds(Long mlpIds);

  @Query(
      value =
          """
          select count(*) as AttemptedCount,cast(to_char(mlp_date,'YYYY-MM-DD')  as varchar ) as mlpDate \
          from mlp_attendance ma left join mlp_inst mi on ma.mlp_id = mi.id join students s on s.id = ma.student_id \
          join sections se on se.id = s.section_id where (to_char(mlp_date,'YYYY-MM-DD') >= :fromDate and to_char(mlp_date,'YYYY-MM-DD') <= :toDate)  and se.organization in (:orgSlugs) \
          group by MlpDate\
          """,
      nativeQuery = true)
  List<MlpAttendanceResult> getMlpAttendanceByDay(
      List<String> orgSlugs, LocalDate fromDate, LocalDate toDate);

  @Query(
      value =
          """
                  select count(*) as AttemptedCount from mlp_attendance ma
                        left join mlp_inst mi on ma.mlp_id = mi.id
                        join students s on s.id = ma.student_id
                        join sections se on se.id = s.section_id
                        where  se.organization in (:orgSlugs)
                  """,
      nativeQuery = true)
  Integer getMlpAttendance(List<String> orgSlugs);

  @Query(
      value =
          """
                          select se.grade_slug as gradeSlug,
                           count(case when cast(to_char(ma.mlp_date,'YYYY-MM-DD') as varchar) = :day1 then 1 end) as mlpAttemptedDay1,
                           count(case when cast(to_char(ma.mlp_date,'YYYY-MM-DD') as varchar) = :day2 then 1 end) as mlpAttemptedDay2,
                           count(case when cast(to_char(ma.mlp_date,'YYYY-MM-DD') as varchar) = :day3 then 1 end) as mlpAttemptedDay3,
                           count(case when cast(to_char(ma.mlp_date,'YYYY-MM-DD') as varchar) = :day4 then 1 end) as mlpAttemptedDay4,
                           count(case when cast(to_char(ma.mlp_date,'YYYY-MM-DD') as varchar) = :day5 then 1 end) as mlpAttemptedDay5
                           from mlp_attendance ma left join mlp_inst mi on ma.mlp_id = mi.id
                           join students s on s.id = ma.student_id
                           join sections se on se.id = s.section_id
                           where se.grade_slug in (:gradeSlugs)
                           and ((:orgSlugs) is null or se.organization in (:orgSlugs))
                           group by se.grade_slug
                           order by gradeSlug desc""",
      nativeQuery = true)
  List<MlpAttendancePercentage> getMlpAttendancePercentage(
      List<String> orgSlugs,
      List<String> gradeSlugs,
      String day1,
      String day2,
      String day3,
      String day4,
      String day5);

  @Query(
      value =
          """
                select se.grade_slug as gradeSlug, count(*) as totalStudents,
               count(case when to_char(u.last_login,'YYYY-MM-DD') <= :date then 1 end) as loginCountDay1
               from users u
               left join students s on s.user_id=u.id
               left join sections se on s.section_id = se.id
               where se.grade_slug in (:gradeSlugs)
               and ((:orgSlugs) is null or se.organization in (:orgSlugs))
               and s.deleted_at is null
               and u.deleted_at is null
               group by se.grade_slug""",
      nativeQuery = true)
  List<MlpAttendancePercentageQueryResults> getMlpStudentDetailByGrade(
      List<String> orgSlugs, List<String> gradeSlugs, LocalDate date);

  @Query(
      value =
          """
                                  select se.organization as organizationSlug,
                                   o."name" as orgName,
                                   count(case when cast(to_char(ma.mlp_date,'YYYY-MM-DD') as varchar) = :day1 then 1 end) as mlpAttemptedDay1,
                                   count(case when cast(to_char(ma.mlp_date,'YYYY-MM-DD') as varchar) = :day2 then 1 end) as mlpAttemptedDay2,
                                   count(case when cast(to_char(ma.mlp_date,'YYYY-MM-DD') as varchar) = :day3 then 1 end) as mlpAttemptedDay3,
                                   count(case when cast(to_char(ma.mlp_date,'YYYY-MM-DD') as varchar) = :day4 then 1 end) as mlpAttemptedDay4,
                                   count(case when cast(to_char(ma.mlp_date,'YYYY-MM-DD') as varchar) = :day5 then 1 end) as mlpAttemptedDay5
                                   from mlp_attendance ma left join mlp_inst mi on ma.mlp_id = mi.id
                                   join students s on s.id = ma.student_id
                                   join sections se on se.id = s.section_id
                                   join orgs o on o.slug in (se.organization)
                                   where se.organization in (:orgSlugs)
                                   group by organizationSlug,o."name" """,
      nativeQuery = true)
  List<MlpAttendancePercentage> getMlpAttendancePercentageOrgWise(
      List<String> orgSlugs, String day1, String day2, String day3, String day4, String day5);

  @Query(
      value =
          """
                                       select se.organization as organizationSlug,o."name" as orgName,
                                       count(*) as totalStudents,count(case when to_char(u.last_login,'YYYY-MM-DD') <= :date then 1 end) as loginCountDay1
                                       from students s
                                       join sections se on s.section_id = se.id\s
                                       join orgs o on o.slug in (se.organization)
                                       join users u on s.user_id = u.id\s
                                       where se.organization in(:orgSlugs)
                                       group by organizationSlug,o."name" """,
      nativeQuery = true)
  List<MlpAttendancePercentageQueryResults> getMlpStudentDetailChildOrgWise(
      List<String> orgSlugs, LocalDate date);
}
