package com.wexl.retail.task.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.classroom.core.model.ClassroomScheduleInst;
import com.wexl.retail.mlp.model.QuestionsAssigneeMode;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class TaskRequest {

  @NotNull private String name;

  private String description;

  @JsonProperty("task_type")
  private String taskType;

  @JsonProperty("due_date")
  private long dueDate;

  @NotNull
  @JsonProperty("classroom_id")
  private Long classroomId;

  @JsonProperty("student_ids")
  private List<Long> studentIds;

  @JsonProperty("classroom_schedule_inst")
  private ClassroomScheduleInst classroomScheduleInst;

  @JsonProperty("chapter_detail")
  private TaskDetailRequest chapterDetail;

  @JsonProperty("subject_detail")
  private TaskDetailRequest subjectDetail;

  @JsonProperty("subtopic_detail")
  private TaskDetailRequest subtopicDetail;

  @NotNull
  @JsonProperty("grade_id")
  private Integer gradeId;

  @JsonProperty("question_count")
  private Integer questionCount;

  @JsonProperty("questions_assignee_mode")
  private QuestionsAssigneeMode questionsAssigneeMode;

  @JsonProperty("video_details")
  private TaskDetailRequest videoDetails;

  @JsonProperty("synopsis_details")
  private TaskDetailRequest synopsisDetails;

  @JsonProperty("question_uuids")
  private List<String> questionUuids;

  @JsonProperty("assignment_id")
  private List<Long> assignmentIds;

  @JsonProperty("board_slug")
  private String boardSlug;

  @JsonProperty("board_name")
  private String boardName;

  @JsonProperty("grade_slug")
  private String gradeSlug;

  @JsonProperty("grade_name")
  private String gradeName;

  @JsonProperty("school_test_id")
  private List<Long> schoolTestIds;
}
