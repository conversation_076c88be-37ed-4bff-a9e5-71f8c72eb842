package com.wexl.retail.scorm.domain;

import com.wexl.retail.coursecontent.domain.ScormDefinition;
import com.wexl.retail.model.Model;
import com.wexl.retail.model.User;
import jakarta.persistence.*;
import lombok.*;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "scorm_inst", schema = "public")
public class ScormInst extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  @ManyToOne
  @JoinColumn(name = "scorm_definition_id")
  private ScormDefinition scormDefinition;

  @ManyToOne
  @JoinColumn(name = "user_id")
  private User user;

  private String lessonStatus;
}
