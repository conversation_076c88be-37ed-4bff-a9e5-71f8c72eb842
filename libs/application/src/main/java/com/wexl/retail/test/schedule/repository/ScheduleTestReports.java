package com.wexl.retail.test.schedule.repository;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
public class ScheduleTestReports {
  private String chapter;
  private String teacherName;
  private String testName;
  private String grade;
  private String sections;
  private String subject;
  private String scheduledDate;
  private Integer attempted;
  private Integer notAttempted;
  private Integer assigned;
  private Double totalMarks;
  private Double highestMarksScored;
  private Double leastMarksScored;
}
