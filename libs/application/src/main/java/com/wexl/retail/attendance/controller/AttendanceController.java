package com.wexl.retail.attendance.controller;

import com.wexl.retail.attendance.dto.AttendanceProfile;
import com.wexl.retail.attendance.dto.AttendanceRequest;
import com.wexl.retail.attendance.dto.AttendanceResponse;
import com.wexl.retail.attendance.dto.AttendanceSummary;
import com.wexl.retail.attendance.service.AttendanceService;
import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgId}")
public class AttendanceController {

  private final AttendanceService attendanceService;

  @PostMapping("/students/{studentId}/attendance")
  public void addAttendance(@RequestBody AttendanceRequest attendanceRequest) {
    this.attendanceService.addAttendanceForStudent(attendanceRequest);
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/teachers/{teacherId}/attendance")
  public List<AttendanceResponse> getAttendanceOfAllTeacherSections() {
    return this.attendanceService.getAttendanceOfAllTeacherSections();
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/teachers/{teacherId}/attendance/{id}")
  public AttendanceSummary getAttendanceDetail(@PathVariable("id") long attendanceId) {
    return attendanceService.getAttendedStudents(attendanceId);
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/students/{studentAuthId}/attendance-summary")
  public AttendanceProfile.AttendanceDetails getStudentAttendanceSummary(
      @RequestParam Long fromDate,
      @PathVariable String studentAuthId,
      @RequestParam(value = "academic_year", required = false) String academicYear) {
    return attendanceService.getStudentAttendanceSummary(fromDate, studentAuthId, academicYear);
  }
}
