package com.wexl.retail.metrics.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GenericMetricResponse {
  private Long date;
  private Integer count;
  private Integer studentMlpCount;
  private Map<String, Object> summary;
  private Map<String, Object> data;
}
