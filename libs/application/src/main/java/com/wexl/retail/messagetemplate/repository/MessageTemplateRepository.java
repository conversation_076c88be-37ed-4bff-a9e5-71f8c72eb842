package com.wexl.retail.messagetemplate.repository;

import com.wexl.retail.messagetemplate.category.model.MessageTemplateCategory;
import com.wexl.retail.messagetemplate.dto.MessageType;
import com.wexl.retail.messagetemplate.model.MessageTemplate;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface MessageTemplateRepository extends JpaRepository<MessageTemplate, Long> {

  List<MessageTemplate> findAllByOrgSlugOrderByUpdatedAtDesc(String orgSlug);

  List<MessageTemplate> findAllByOrgSlugAndTypeOrderByUpdatedAtDesc(
      String orgSlug, MessageType messageType);

  Optional<MessageTemplate> findByOrgSlugAndId(String orgSlug, Long templateId);

  List<MessageTemplate> findByMessageTemplateCategory(
      MessageTemplateCategory messageTemplateCategory);

  Optional<MessageTemplate> findBySmsDltTemplateId(String smsDltTemplateId);

  Optional<MessageTemplate> findBySmsDltTemplateIdAndOrgSlug(
      String smsDltTemplateId, String orgSlug);
}
