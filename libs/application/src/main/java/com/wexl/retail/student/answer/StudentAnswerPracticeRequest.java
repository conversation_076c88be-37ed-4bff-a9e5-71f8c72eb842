package com.wexl.retail.student.answer;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StudentAnswerPracticeRequest {
  private long questionId;
  private String questionUuid;
  private int selectedAnswer;
  private long examId;
  private String type;
  private String answer;

  private String permKey;

  public String getType() {
    if (Objects.isNull(type)) {
      return "mcq";
    }
    return type;
  }
}
