package com.wexl.retail.metrics;

import com.wexl.retail.curriculum.service.OrgSettingsService;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.dto.StudentsStatusResponse;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.section.service.SectionService;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class StudentsActivityReportService {

  @Autowired OrgSettingsService orgSettingsService;

  @Autowired SectionRepository sectionRepository;

  @Autowired SectionService sectionService;

  public List<GenericMetricResponse> getStudentActivityMetrics(String orgSlug) {
    List<GenericMetricResponse> genericMetricResponses = new ArrayList<>();
    var organization = orgSettingsService.validateOrganizaiton(orgSlug);
    List<StudentsStatusResponse> studentsResponse =
        sectionRepository.getAllGradeStudentsOfOrganization(orgSlug);
    DecimalFormat decimalFormat = new DecimalFormat("0.00");
    studentsResponse.forEach(
        studentsStatusResponse -> {
          if (studentsStatusResponse.getGradeSlug() != null) {
            genericMetricResponses.add(buildMetricResponse(studentsStatusResponse, decimalFormat));
          }
        });
    return genericMetricResponses;
  }

  private GenericMetricResponse buildMetricResponse(
      StudentsStatusResponse studentsStatusResponse, DecimalFormat decimalFormat) {
    Map<String, Object> map = new HashMap<>();
    map.put("grade_slug", studentsStatusResponse.getGradeSlug());
    map.put("grade_name", studentsStatusResponse.getGradeSlug());
    map.put(
        "active_users_percentage",
        String.valueOf(
            decimalFormat.format(
                ((double) studentsStatusResponse.getActiveUsersCount()
                        / studentsStatusResponse.getTotalUsersCount())
                    * 100)));
    map.put(
        "inactive_users_percentage",
        String.valueOf(
            decimalFormat.format(
                ((double) studentsStatusResponse.getInactiveUsersCount()
                        / studentsStatusResponse.getTotalUsersCount())
                    * 100)));
    return GenericMetricResponse.builder().data(map).build();
  }
}
