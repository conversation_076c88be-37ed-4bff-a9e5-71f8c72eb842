package com.wexl.retail.util;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.ecommerce.CommerceStore;
import com.wexl.retail.products.dto.StoreConfiguration;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class StoreUtils {
  private final StoreConfiguration storeConfiguration;

  public CommerceStore getStoreDetails(String orgSlug) {
    var store =
        storeConfiguration.getStores().stream()
            .filter(s -> orgSlug.equals(s.getSlug()))
            .findFirst();

    if (store.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.Store", new String[] {orgSlug});
    }
    return transformToCommerceStore(store.get());
  }

  private CommerceStore transformToCommerceStore(StoreConfiguration.Store storeDetails) {
    return CommerceStore.builder()
        .productId(storeDetails.getProductId())
        .token(storeDetails.getToken())
        .slug(storeDetails.getSlug())
        .name(storeDetails.getName())
        .type(storeDetails.getType())
        .androidAppUrl(storeDetails.getAndroidAppUrl())
        .webUrl(storeDetails.getWebUrl())
        .build();
  }
}
