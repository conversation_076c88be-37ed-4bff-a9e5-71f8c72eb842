package com.wexl.retail.schoolknot.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.model.Gender;
import lombok.Builder;

public record SchoolKnotDto() {
  @Builder
  public record schoolKnotStudentRequest(
      String email,
      @JsonProperty("org_slug") String orgSlug,
      @JsonProperty("first_name") String firstName,
      @JsonProperty("last_name") String lastName,
      @JsonProperty("grade_slug") String gradeSlug,
      Gender gender,
      String password,
      @JsonProperty("admission_number") String admissionNumber,
      @JsonProperty("roll_number") String rollNumber,
      @JsonProperty("section_uuid") String sectionUuid,
      String userName,
      @JsonProperty("mobile_number") String mobileNumber) {}

  @Builder
  public record schoolKnotTeacherRequest(
      String email,
      @JsonProperty("org_slug") String orgSlug,
      @JsonProperty("first_name") String firstName,
      @JsonProperty("last_name") String lastName,
      String password,
      @JsonProperty("mobile_number") String mobileNumber) {}

  @Builder
  public record schoolKnotSectionRequest(
      @JsonProperty("org_slug") String orgSlug,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("section_name") String sectionName) {}
}
