package com.wexl.retail.qrcode.domain;

import com.wexl.retail.model.Model;
import com.wexl.retail.model.Student;
import com.wexl.retail.qrcode.dto.QrCodeStatus;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "qr_code", schema = "public", indexes = @Index(columnList = "uuid"))
public class QrCode extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "uuid")
  private String uuid;

  @Column(name = "org_slug")
  private String orgSlug;

  @Column(name = "grade_slug")
  private String gradeSlug;

  @Column(name = "board_slug")
  private String boardSlug;

  @Column(name = "status")
  @Enumerated(EnumType.STRING)
  private QrCodeStatus status;

  @Column(name = "qr_code")
  private String qrCodeLabel;

  @Column(name = "s3_file_path")
  private String s3FilePath;

  @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, mappedBy = "qrCode")
  private List<Student> students;
}
