package com.wexl.retail.test.school.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.model.Model;
import com.wexl.retail.test.school.dto.PbqDto;
import com.wexl.retail.test.school.dto.QuestionDto;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

@EqualsAndHashCode(callSuper = true)
@Entity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "test_questions")
public class TestQuestion extends Model {
  @Id
  @GeneratedValue(
      strategy = GenerationType.SEQUENCE,
      generator = "test_questions-sequence-generator")
  @SequenceGenerator(
      name = "test_questions-sequence-generator",
      sequenceName = "test_questions_seq",
      allocationSize = 1)
  private long id;

  private String questionUuid;
  private String chapterSlug;
  private String chapterName;
  private String complexity;
  private String category;
  private String subtopicSlug;

  @JsonProperty("subject_slug")
  private String subjectSlug;

  @Column(columnDefinition = "VARCHAR(100) default 'mcq'")
  private String type;

  @Column(columnDefinition = "INTEGER default 1")
  private Integer marks;

  @Column(columnDefinition = "FLOAT default 0")
  @JsonProperty("negative_marks")
  private float negativeMarks;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "test_definition_section_id")
  private TestDefinitionSection testDefinitionSection;

  @JsonProperty("mcq_answer")
  private Long mcqAnswer;

  @JsonProperty("amcq_answer")
  private Integer amcqAnswer;

  private String spchAnswer;

  private Float natAnswer;

  private String fbqAnswer;

  private String subjectiveAnswer;

  private Boolean yesNo;

  @JsonIgnore
  @Type(JsonType.class)
  @Column(name = "msq_answer", columnDefinition = "jsonb")
  private List<Long> msqAnswer;

  @JsonIgnore
  @Type(JsonType.class)
  @Column(name = "ddfbq_answer", columnDefinition = "jsonb")
  private QuestionDto.DdFbq ddFbqAnswer;

  @JsonIgnore
  @Type(JsonType.class)
  @Column(name = "pbq_answers", columnDefinition = "jsonb")
  private PbqDto.Data pbqAnswers;

  @JsonProperty("question_tags")
  private String questionTags;
}
