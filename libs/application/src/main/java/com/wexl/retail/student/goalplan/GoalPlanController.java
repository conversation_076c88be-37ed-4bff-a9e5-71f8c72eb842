package com.wexl.retail.student.goalplan;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/student/goal_plan")
@RequiredArgsConstructor
public class GoalPlanController {

  @GetMapping
  public GoalPlanResponse findForStudent() {
    return null;
  }
}
