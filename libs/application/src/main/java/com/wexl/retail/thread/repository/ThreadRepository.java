package com.wexl.retail.thread.repository;

import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.thread.model.Thread;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface ThreadRepository extends JpaRepository<Thread, Long> {
  List<Thread> findAllByCreatedBy(Student student);

  @Query(
      value = "select  count(*)  from thread_reply tr where thread_id  = :threadId",
      nativeQuery = true)
  Long replyCountByThreadId(Long threadId);

  List<Thread> findAllByOrgSlug(String orgSlug);

  @Query(
      value =
          """
                  select t.* from threads t
                  join students s on s.id = t.created_by
                  join sections sec on sec.id = s.section_id
                  where org_slug = :orgSlug and sec.grade_slug = :gradeSlug and t.deleted_at is null
                  """,
      nativeQuery = true)
  List<Thread> getAllByOrgSlugAndGradeSlug(String orgSlug, String gradeSlug);

  Optional<Thread> findByIdAndCreatedBy(Long threadId, Student studentAuthId);

  List<Thread> findAllByAssignedTo(Teacher teacherId);
}
