package com.wexl.retail.student.registration.controller;

import com.wexl.retail.student.registration.dto.StudentRegistrationRequest;
import com.wexl.retail.student.registration.service.StudentRegistrationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping
public class StudentRegistrationController {
  private final StudentRegistrationService studentRegistrationService;

  @ResponseStatus(HttpStatus.CREATED)
  @PostMapping("/public/org/{orgSlug}/students")
  public String registerStudent(
      @PathVariable String orgSlug,
      @RequestBody StudentRegistrationRequest studentRegistrationRequest) {
    return studentRegistrationService.createStudent(orgSlug, studentRegistrationRequest);
  }
}
