package com.wexl.retail.test.schedule.service;

import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.schedule.dto.SimpleScheduleTestResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ScheduleTestTransformer {
  @Autowired DateTimeUtil dateTimeUtil;

  public SimpleScheduleTestResponse mapTestScheduleToScheduleTestResponse(
      ScheduleTest scheduleTest) {
    return SimpleScheduleTestResponse.builder()
        .id(scheduleTest.getId())
        .status(scheduleTest.getStatus())
        .duration(scheduleTest.getDuration())
        .endDate(DateTimeUtil.convertIso8601ToEpoch(scheduleTest.getEndDate()))
        .startDate(DateTimeUtil.convertIso8601ToEpoch(scheduleTest.getStartDate()))
        .build();
  }
}
