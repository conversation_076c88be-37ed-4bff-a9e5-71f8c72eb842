package com.wexl.retail.student.exam;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.courses.enrollment.model.CourseScheduleItemInst;
import com.wexl.retail.model.Student;
import com.wexl.retail.services.StudentService;
import com.wexl.retail.task.domain.TaskType;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.school.domain.TestType;
import com.wexl.retail.util.Constants;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ExamFactory {

  private final StudentService studentService;

  public Exam createPracticeTest(String ref, Student student) {
    Student verifiedStudent = studentService.findStudentById(student.getId());
    if (verifiedStudent == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.StudentNotFound");
    }
    return new Exam(Constants.PRACTICE_EXAM, ref, verifiedStudent);
  }

  public Exam createTest(String ref) {
    return new Exam(Constants.TEST_EXAM, ref, studentService.fetchStudentUsingAuthToken());
  }

  public Exam createScheduledTest() {
    return new Exam(
        Constants.SCHOOL_SCHEDULED_TEST_EXAM, studentService.fetchStudentUsingAuthToken());
  }

  public Exam createScheduledTest(ScheduleTest scheduleTest) {
    var isAssignment = TestType.ASSIGNMENT.equals(scheduleTest.getTestDefinition().getType());

    if (isAssignment) {
      return createAssignment();
    } else if (TestType.LIVE_WORKSHEET.equals(scheduleTest.getTestDefinition().getType())) {
      return createLiveWorksheet();
    }

    return createScheduledTest();
  }

  public Exam createLiveWorksheet() {
    return new Exam(Constants.LIVE_WORKSHEET, studentService.fetchStudentUsingAuthToken());
  }

  public Exam createSchoolSurpriseTest(String ref) {
    return new Exam(
        Constants.SCHOOL_SURPRISE_TEST_EXAM, ref, studentService.fetchStudentUsingAuthToken());
  }

  public Exam createWorksheetTest() {
    return new Exam(Constants.WORKSHEET, studentService.fetchStudentUsingAuthToken());
  }

  public Exam createAssignment() {
    return new Exam(Constants.ASSIGNMENT_EXAM, studentService.fetchStudentUsingAuthToken());
  }

  public Exam createRevision(String ref) {
    return new Exam(Constants.REVISION_EXAM, ref, studentService.fetchStudentUsingAuthToken());
  }

  public Exam createTaskPracticeExam(Long taskId) {
    return new Exam(Constants.PRACTICE_EXAM, taskId, studentService.fetchStudentUsingAuthToken());
  }

  public Exam createAssignmentTask(Long taskId, String taskType) {
    if (taskType.equals(TaskType.SCHOOLTEST.name())) {
      return new Exam(
          Constants.SCHOOL_SCHEDULED_TEST_EXAM,
          taskId,
          studentService.fetchStudentUsingAuthToken());
    }
    return new Exam(Constants.ASSIGNMENT_EXAM, taskId, studentService.fetchStudentUsingAuthToken());
  }

  public Exam createAssignmentTaskForCourse(CourseScheduleItemInst courseScheduleItemInst) {
    return new Exam(
        Constants.COURSE_ASSIGNMENT,
        courseScheduleItemInst,
        studentService.fetchStudentUsingAuthToken());
  }

  public Exam createSchoolTaskForCourse(CourseScheduleItemInst courseScheduleItemInst) {
    return new Exam(
        Constants.COURSE_TEST, courseScheduleItemInst, studentService.fetchStudentUsingAuthToken());
  }

  public Exam createMockTaskForCourse(CourseScheduleItemInst courseScheduleItemInst) {
    return new Exam(
        Constants.COURSE_MOCK_TEST,
        courseScheduleItemInst,
        studentService.fetchStudentUsingAuthToken());
  }

  public Exam createTaskTestExam(Long task) {
    return new Exam(Constants.TEST_EXAM, task, studentService.fetchStudentUsingAuthToken());
  }

  public Exam createElpExam(Long task) {
    return new Exam(Constants.ELP_EXAM, task, studentService.fetchStudentUsingAuthToken());
  }

  public Exam createBetExam(Long lessonInstId) {
    return new Exam(Constants.BET_EXAM, studentService.fetchStudentUsingAuthToken(), lessonInstId);
  }

  public Exam createEbcExam(Long lessonInstId) {
    return new Exam(Constants.EBC_EXAM, studentService.fetchStudentUsingAuthToken(), lessonInstId);
  }
}
