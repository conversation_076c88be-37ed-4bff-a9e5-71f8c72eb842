package com.wexl.retail.section.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.section.domain.SectionStatus;
import com.wexl.retail.section.dto.ConnectedTeacher;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SectionCreateRequest {

  @NotBlank private String name;
  @NotNull private SectionStatus status;
  private String remarks;
  private List<ConnectedTeacher> teachers;

  @JsonProperty("grade")
  @NotBlank
  private String gradeSlug;

  @JsonProperty("board")
  @NotBlank
  private String boardSlug;
}
