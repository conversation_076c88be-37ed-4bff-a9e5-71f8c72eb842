package com.wexl.retail.section.repository;

import com.wexl.retail.model.Teacher;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.domain.TeacherSection;
import java.sql.Timestamp;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface TeacherSectionRepository extends JpaRepository<TeacherSection, Long> {

  @Query("SELECT ts FROM TeacherSection ts WHERE ts.teacher=:teacher AND ts.section=:section")
  TeacherSection findByTeacherAndSection(Teacher teacher, Section section);

  @Transactional
  @Modifying
  @Query(
      value =
          """
          UPDATE teacher_sections SET deleted_at=NULL, updated_at=:updatedAt\
           WHERE section_id=:sectionId AND teacher_id=:teacherId\
          """,
      nativeQuery = true)
  int addSectionIfRemovedAlready(long sectionId, long teacherId, Timestamp updatedAt);

  @Query(
      value =
          """
          select ts.* from teacher_sections ts
          inner join sections s  on s.id = ts.section_id
          where ts.teacher_id = :teacherId  and ts.deleted_at is null and s.deleted_at is null""",
      nativeQuery = true)
  List<TeacherSection> getTeacherSectionsByTeacherAndDeletedAtIsNull(long teacherId);

  List<TeacherSection> findAllBySection(Section section);
}
