package com.wexl.retail.model;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Data
@Table(name = "addresses")
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
public class Addresses extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "addresses-sequence-generator")
  @SequenceGenerator(
      name = "addresses-sequence-generator",
      sequenceName = "addresses_seq",
      allocationSize = 1)
  private long id;

  private String line1;
  private String line2;
  private String city;
  private String state;
  private String country;
  private String zipCode;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "createdBy")
  @Getter(AccessLevel.PRIVATE)
  private User user;
}
