package com.wexl.retail.mlp.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MlpLearningAnalyticsByStudent {
  private String fullName;
  private String userName;
  private String sectionName;
  private List<String> subjects;
  private int totalMlpCount;
  private int attemptedMlpCount;
  private Double attendancePercentage;
  private Double knowledgePercentage;
}
