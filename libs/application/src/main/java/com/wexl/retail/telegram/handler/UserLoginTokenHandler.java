package com.wexl.retail.telegram.handler;

import static com.wexl.retail.telegram.util.Constants.BASE_URL;
import static com.wexl.retail.telegram.util.Constants.TOKEN_CODE;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.AuthUtil;
import com.wexl.retail.model.LoginMethod;
import com.wexl.retail.model.User;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UserLoginTokenHandler extends UserBotHandler {

  @Autowired AuthService authService;

  @Override
  protected void handleInternal(User user, Map<String, Object> result) {
    if (AuthUtil.isStudent(user)) {
      String url =
          BASE_URL.formatted(
              "student",
              authService.generateStudentAccessToken(false, user, LoginMethod.SYSTEM_CREATED));
      result.put(TOKEN_CODE, url);
    } else if (AuthUtil.isTeacher(user)) {
      String url =
          BASE_URL.formatted(
              "teacher",
              authService.generateTeacherAccessToken(false, user, LoginMethod.SYSTEM_CREATED));
      result.put(TOKEN_CODE, url);
    } else {
      result.put("Error", "Invalid Role");
    }
  }

  @Override
  public String getCommandName() {
    return "/token";
  }

  @Override
  public String getHelpText() {
    return "/token [user-name]";
  }
}
