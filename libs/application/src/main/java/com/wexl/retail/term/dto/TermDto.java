package com.wexl.retail.term.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record TermDto() {
  @Builder
  public record TermDetails(
      @JsonProperty("id") Long termId,
      @JsonProperty("name") String name,
      @JsonProperty("slug") String slug,
      @JsonProperty("term_assessments") List<TermAssessmentDetails> termAssessments) {}

  @Builder
  public record TermAssessmentDetails(
      @JsonProperty("id") Long assessmentId,
      @JsonProperty("name") String assessmentName,
      @JsonProperty("slug") String assessmentSlug,
      @JsonProperty("seq_no") Integer seqNo) {}

  @Builder
  public record TermAssessmentCategoryDetails(
      @JsonProperty("id") Long id,
      @JsonProperty("category_name") String categoryName,
      @JsonProperty("seq_no") Long seqNo,
      @JsonProperty("assessment_name") String assessmentName,
      @JsonProperty("term_name") String termName,
      @JsonProperty("is_selected") Boolean isSelected) {}

  @Builder
  public record TermAssessmentCategoryRequest(
      @JsonProperty("category_name") String categoryName, @JsonProperty("seq_no") Long seqNo) {}
}
