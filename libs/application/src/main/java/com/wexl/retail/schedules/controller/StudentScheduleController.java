package com.wexl.retail.schedules.controller;

import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.schedules.dto.ScheduleResponse;
import com.wexl.retail.schedules.service.ScheduleService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@IsStudent
@RestController
@RequestMapping("/orgs/{org}/students/{studentId}/schedules")
public class StudentScheduleController {

  @Autowired private ScheduleService scheduleService;

  @GetMapping
  public List<ScheduleResponse> getSectionSchedules(
      @PathVariable("org") String organization, @RequestParam String section) {
    return scheduleService.getSectionSchedules(organization, section);
  }
}
