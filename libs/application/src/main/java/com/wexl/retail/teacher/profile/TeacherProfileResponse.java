package com.wexl.retail.teacher.profile;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.model.TeacherMetadata;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class TeacherProfileResponse {

  @JsonProperty("profile_image_url")
  private String profileImageUrl;

  TeacherPersonalDetails teacherPersonalDetails;
  ContactDetails contactDetails;
  InstituteDetails instituteDetails;
  BankDetails bankDetails;
  ProfessionalDetails professionalDetails;
  TeacherMetadata metadata;
}
