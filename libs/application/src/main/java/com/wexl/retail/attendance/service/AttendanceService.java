package com.wexl.retail.attendance.service;

import com.wexl.retail.attendance.domain.Attendance;
import com.wexl.retail.attendance.domain.AttendanceMetadata;
import com.wexl.retail.attendance.domain.UserMetadata;
import com.wexl.retail.attendance.dto.*;
import com.wexl.retail.attendance.repository.AttendanceRepository;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.erp.attendance.dto.AddAttendanceRequest;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.mlp.service.MlpService;
import com.wexl.retail.model.StudentAudit;
import com.wexl.retail.repository.StudentAuditRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.schedules.domain.SectionSchedule;
import com.wexl.retail.schedules.repository.SectionScheduleRepository;
import com.wexl.retail.schedules.service.ScheduleService;
import com.wexl.retail.section.dto.ConnectedStudent;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.util.StrapiService;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AttendanceService {

  private final AuthService authService;
  private final DateTimeUtil dateTimeUtil;
  private final StrapiService strapiService;
  private final AttendanceRepository attendanceRepository;
  private final SectionScheduleRepository sectionScheduleRepository;
  private final ScheduleService scheduleService;
  private final SectionService sectionService;
  private final ContentService contentService;
  private final StudentAuditRepository studentAuditRepository;
  private final TeacherRepository teacherRepository;
  private final MlpService mlpService;
  private final UserService userService;
  private final ReportCardService reportCardService;
  private final UserRepository userRepository;
  private final StudentRepository studentRepository;

  public void addAttendanceForStudent(AttendanceRequest attendanceRequest) {
    var attendanceById =
        attendanceRepository.getAttendanceByMeetingId(
            attendanceRequest.getMeetingId(),
            dateTimeUtil.getUpdatedTimestamp(attendanceRequest.getMeetingStartTime()),
            dateTimeUtil.getUpdatedTimestamp(attendanceRequest.getMeetingEndTime()));
    if (attendanceById.isEmpty()) {
      createAttendanceRecordAndInsertStudent(attendanceRequest);
    } else {
      attendanceRequest.setAttendanceId(attendanceById.get().getId());
      insertStudenInfoToAttendanceRecord(attendanceRequest);
    }
  }

  private void insertStudenInfoToAttendanceRecord(AttendanceRequest attendanceRequest) {
    var studentJson = new JSONObject();
    studentJson.put("id", attendanceRequest.getId());
    studentJson.put("firstName", attendanceRequest.getFirstName());
    studentJson.put("lastName", attendanceRequest.getLastName());
    studentJson.put("meetingJoinTime", System.currentTimeMillis());
    attendanceRepository.insertStudenInfoToAttendanceRecord(
        attendanceRequest.getId(), attendanceRequest.getAttendanceId(), studentJson.toString());
  }

  private void createAttendanceRecordAndInsertStudent(AttendanceRequest attendanceRequest) {
    var metadata = new AttendanceMetadata();
    var userMetada = new UserMetadata();
    BeanUtils.copyProperties(attendanceRequest, userMetada);
    userMetada.setMeetingJoinTime(System.currentTimeMillis());
    metadata.setStudents(Collections.singletonList(userMetada));

    var attendance = buildAttendance(attendanceRequest);
    attendance.setMetadata(metadata);
    this.attendanceRepository.save(attendance);
  }

  private Attendance buildAttendance(AttendanceRequest attendanceRequest) {
    var attendance = new Attendance();
    BeanUtils.copyProperties(attendanceRequest, attendance);
    attendance.setMeetingStartTime(
        dateTimeUtil.getUpdatedTimestamp(attendanceRequest.getMeetingStartTime()));
    attendance.setMeetingEndTime(
        dateTimeUtil.getUpdatedTimestamp(attendanceRequest.getMeetingEndTime()));
    attendance.setSectionSchedule(
        checkIfSectionScheduleIsPresent(attendanceRequest.getMeetingId()));
    return attendance;
  }

  private SectionSchedule checkIfSectionScheduleIsPresent(long meetingId) {
    return sectionScheduleRepository
        .findById(meetingId)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST,
                    "error.SectionScheduleFind.MeetingID",
                    new String[] {Long.toString(meetingId)}));
  }

  public List<AttendanceResponse> getAttendanceOfAllTeacherSections() {
    var teacherId = authService.getTeacherDetails().getTeacherInfo().getId();
    var allowedDaysBefore = LocalDateTime.now().minusDays(15);

    return attendanceRepository
        .getAttendanceBySections(teacherId, Timestamp.valueOf(allowedDaysBefore))
        .stream()
        .map(
            attendance -> {
              var meeting = attendance.getSectionSchedule();
              var section = meeting.getSection();
              var gradeId = section.getGradeId();

              return AttendanceResponse.builder()
                  .meetingName(meeting.getName())
                  .meetingId(meeting.getId())
                  .attendanceId(attendance.getId())
                  .meetingStartTime(
                      DateTimeUtil.convertIso8601ToEpoch(
                          attendance.getMeetingStartTime().toLocalDateTime()))
                  .grade(contentService.getGradeById(gradeId).getSlug())
                  .sectionName(section.getName())
                  .sectionUuid(section.getUuid().toString())
                  .studentCount(attendance.getMetadata().getStudents().size())
                  .zoomMeetingNumber(scheduleService.deriveZoomMeetingFromUrl(meeting.getUrl()))
                  .build();
            })
        .toList();
  }

  public AttendanceSummary getAttendedStudents(long attendanceId) {
    var attendance = checkForAttendanceRecord(attendanceId);
    var section = attendance.getSectionSchedule().getSection();
    var studentsInSection = sectionService.getAllConnectedStudents(section.getUuid().toString());
    var attendedStudents = attendance.getMetadata().getStudents();

    return getAttendanceSummary(
        attendance, getStudentAttendedResponses(studentsInSection, attendedStudents));
  }

  private List<StudentAttendedResponse> getStudentAttendedResponses(
      Set<ConnectedStudent> studentsInSection, List<UserMetadata> attendedStudents) {

    return studentsInSection.stream()
        .map(
            student -> {
              final Optional<UserMetadata> optionalStudentMetadata =
                  checkForAttendedStudent(attendedStudents, student);

              var studentAttendedResponse = new StudentAttendedResponse();
              studentAttendedResponse.setStudentId(student.getUserId());
              studentAttendedResponse.setFirstName(student.getFirstName());
              studentAttendedResponse.setLastName(student.getLastName());
              studentAttendedResponse.setUserName(student.getAuthUserId());
              studentAttendedResponse.setIsPresent(optionalStudentMetadata.isPresent());
              optionalStudentMetadata.ifPresent(
                  userMetadata ->
                      studentAttendedResponse.setMeetingJoinTime(
                          userMetadata.getMeetingJoinTime()));
              return studentAttendedResponse;
            })
        .toList();
  }

  private Attendance checkForAttendanceRecord(long attendanceId) {
    return attendanceRepository
        .findById(attendanceId)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST,
                    "error.AttendanceRecordFind.AttendnaceId",
                    new String[] {Long.toString(attendanceId)}));
  }

  private AttendanceSummary getAttendanceSummary(
      Attendance attendanceById, List<StudentAttendedResponse> studentAttendedResponses) {

    var attendanceResponse = new AttendanceSummary();
    attendanceResponse.setMeetingSummary(getMeetingSummary(attendanceById));
    attendanceResponse.setStudentAttendedResponses(studentAttendedResponses);
    return attendanceResponse;
  }

  private Optional<UserMetadata> checkForAttendedStudent(
      List<UserMetadata> attendedStudents, ConnectedStudent user) {
    return attendedStudents.stream()
        .filter(attendedStudent -> attendedStudent.getId() == user.getUserId())
        .findFirst();
  }

  private MeetingSummary getMeetingSummary(Attendance attendance) {
    var meetingSummary = new MeetingSummary();
    var schedule = attendance.getSectionSchedule();
    meetingSummary.setScheduleName(schedule.getName());
    meetingSummary.setMeetType(schedule.getMeetingType().toString());
    meetingSummary.setStartTime(attendance.getMeetingStartTime().getTime());
    meetingSummary.setEndTime(attendance.getMeetingEndTime().getTime());
    return meetingSummary;
  }

  public void saveAudit(AddAttendanceRequest attendanceRequest) {

    try {
      var teacherUser =
          Objects.nonNull(attendanceRequest.getTeacher())
              ? mlpService.validateTeacher(attendanceRequest.getTeacher()).getUserInfo()
              : authService.getTeacherDetails();

      List<StudentAudit> studentAuditList = new ArrayList<>();
      attendanceRequest
          .getStudents()
          .forEach(
              student -> {
                StudentAudit studentAudit = new StudentAudit();
                studentAudit.setStudentId(student.getStudent());
                studentAudit.setTeacherId(teacherUser.getId());
                studentAudit.setTeacherName(userService.getNameByUserInfo(teacherUser));
                studentAudit.setAction(student.getAttendanceStatus());
                studentAuditList.add(studentAudit);
              });
      studentAuditRepository.saveAll(studentAuditList);
    } catch (Exception e) {
      log.error("Error occurred while saving Audit: {}", e.getMessage(), e);
    }
  }

  public AttendanceProfile.AttendanceDetails getStudentAttendanceSummary(
      Long fromDate, String authUserId, String academicYear) {
    var user = userRepository.findByAuthUserId(authUserId).orElseThrow();
    Long fDate = Long.valueOf(dateTimeUtil.convertEpocToIntegerFormat(Long.valueOf(fromDate)));
    var student = user.getStudentInfo();
    var orgSlug = user.getOrganization();
    if (!student.getAcademicYearSlug().equals(academicYear) && student.getPrevStudentId() != null) {
      student = studentRepository.findById(student.getPrevStudentId()).orElseThrow();
    }
    var totalDays = reportCardService.getTotalDays(student, fDate, orgSlug);
    var presentDays = reportCardService.getPresentDays(student, fDate, orgSlug);
    return AttendanceProfile.AttendanceDetails.builder()
        .totalDays(totalDays)
        .presentDays(presentDays)
        .build();
  }
}
