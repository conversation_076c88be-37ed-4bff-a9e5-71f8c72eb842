package com.wexl.retail.courses.bundles.service;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.courses.bundles.dto.CourseBundleDto;
import com.wexl.retail.courses.bundles.model.CourseBundles;
import com.wexl.retail.courses.bundles.repository.CourseBundleRepository;
import com.wexl.retail.courses.definition.model.CourseDefinition;
import com.wexl.retail.courses.definition.repository.CourseDefinitionRepository;
import com.wexl.retail.courses.enrollment.repository.CourseScheduleRepository;
import com.wexl.retail.courses.enrollment.service.CourseScheduleInstService;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.util.ValidationUtils;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CourseBundleService {

  private final CourseBundleRepository courseBundleRepository;
  private final CourseDefinitionRepository courseDefinitionRepository;
  private final ValidationUtils validationUtils;
  private final StorageService storageService;
  private final CourseScheduleRepository courseScheduleRepository;
  private final CourseScheduleInstService courseScheduleInstService;
  private final AuthService authService;

  public void createCourseBundle(String orgSlug, CourseBundleDto.Request request) {
    validationUtils.isOrgValid(orgSlug);
    var courseDefinition = getCourseDefinitions(request.courseDefinitionIds());
    courseBundleRepository.save(
        buildCourseBundle(new CourseBundles(), request, courseDefinition, orgSlug));
  }

  private CourseBundles buildCourseBundle(
      CourseBundles courseBundles,
      CourseBundleDto.Request request,
      List<CourseDefinition> courseDefinition,
      String orgSlug) {
    courseBundles.setName(request.name());
    courseBundles.setCourseDefinitions(courseDefinition);
    courseBundles.setOrgSlug(orgSlug);
    courseBundles.setThumbnail(request.thumbnail());
    return courseBundles;
  }

  private List<CourseDefinition> getCourseDefinitions(List<Long> courseDefinitionIds) {
    var courseDefinition = courseDefinitionRepository.findAllById(courseDefinitionIds);
    if (courseDefinition.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "Invalid CourseDefId " + courseDefinition);
    }
    return courseDefinition;
  }

  public List<CourseBundleDto.Response> getCourseBundlesByOrg(String orgSlug) {
    validationUtils.isOrgValid(orgSlug);
    var courseBundles = courseBundleRepository.findAllByOrgSlugAndDeletedAtIsNull(orgSlug);
    List<CourseBundleDto.Response> response = new ArrayList<>();
    courseBundles.forEach(
        bundle ->
            response.add(
                CourseBundleDto.Response.builder()
                    .name(bundle.getName())
                    .thumbNail(
                        bundle.getThumbnail() == null
                            ? bundle.getThumbnail()
                            : storageService.generatePreSignedUrlForFetch(bundle.getThumbnail()))
                    .id(bundle.getId())
                    .courseDefinitions(buildCourseDefinitions(bundle.getCourseDefinitions()))
                    .build()));
    return response;
  }

  private List<CourseBundleDto.CourseDefinition> buildCourseDefinitions(
      List<CourseDefinition> courseDefinitions) {
    List<CourseBundleDto.CourseDefinition> courseDefinitionList = new ArrayList<>();
    courseDefinitions.forEach(
        courseDef ->
            courseDefinitionList.add(
                CourseBundleDto.CourseDefinition.builder()
                    .name(courseDef.getName())
                    .id(courseDef.getId())
                    .courseKm(buildCourseKm(courseDef))
                    .thumbNail(
                        courseDef.getImagePath() == null
                            ? courseDef.getImagePath()
                            : storageService.generatePreSignedUrlForFetch(courseDef.getImagePath()))
                    .build()));
    courseDefinitionList.sort(Comparator.comparing(CourseBundleDto.CourseDefinition::name));
    return courseDefinitionList;
  }

  public Double buildCourseKm(CourseDefinition courseDef) {
    var courseSchedules = courseScheduleRepository.findByCourseDefinition(courseDef);
    var user = authService.getUserDetails();
    if (courseSchedules.isEmpty()) {
      return 0.0;
    }
    var courseModules =
        courseScheduleInstService.getStudentCourseProgress(
            courseDef.getId(), courseSchedules.getFirst().getId(), user.getAuthUserId());
    return courseModules.getCourseKm();
  }

  public CourseBundleDto.Response getCourseBundleById(Long courseBundleId) {
    var courseBundle = getCourseBundle(courseBundleId);
    var courseDefinitions = buildCourseDefinitions(courseBundle.getCourseDefinitions());
    return CourseBundleDto.Response.builder()
        .name(courseBundle.getName())
        .thumbNail(
            (courseBundle.getThumbnail() == null || courseBundle.getThumbnail().isEmpty())
                ? null
                : storageService.generatePreSignedUrlForFetch(courseBundle.getThumbnail()))
        .id(courseBundle.getId())
        .courseDefinitions(courseDefinitions)
        .courseBundleKm(buildCourseBundleKm(courseDefinitions))
        .build();
  }

  private Double buildCourseBundleKm(List<CourseBundleDto.CourseDefinition> courseDefinitions) {
    double km;
    var sum =
        courseDefinitions.stream().mapToDouble(CourseBundleDto.CourseDefinition::courseKm).sum();
    long count = courseDefinitions.stream().filter(s -> s.courseKm() != 0).count();
    km = sum == 0 ? 0.0 : Math.round(sum / count);
    return km;
  }

  private CourseBundles getCourseBundle(Long courseBundleId) {
    var courseBundle = courseBundleRepository.findById(courseBundleId);
    if (courseBundle.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "Invalid courseBundleId " + courseBundleId);
    }
    return courseBundle.get();
  }

  public void addCoursesToBundle(Long courseBundleId, CourseBundleDto.Request request) {
    var courseBundle = getCourseBundle(courseBundleId);
    var courseDefinition = getCourseDefinitions(request.courseDefinitionIds());
    courseBundleRepository.save(buildCourse(courseBundle, request, courseDefinition));
  }

  private CourseBundles buildCourse(
      CourseBundles courseBundle,
      CourseBundleDto.Request request,
      List<CourseDefinition> courseDefinition) {
    courseBundle.setCourseDefinitions(courseDefinition);
    courseBundle.setName(request.name());
    if (!StringUtils.isEmpty(request.thumbnail())) {
      courseBundle.setThumbnail(request.thumbnail());
    }
    return courseBundle;
  }

  public void deleteCourse(Long courseBundleId, Long definitionId) {
    var courseBundle = getCourseBundle(courseBundleId);
    List<Long> definitionsList = new ArrayList<>();
    definitionsList.add(definitionId);
    var courseDefinition = getCourseDefinitions(definitionsList);
    courseBundle.getCourseDefinitions().removeAll(courseDefinition);
    courseBundleRepository.save(courseBundle);
  }

  public void deleteCourseBundle(Long courseBundleId) {
    var courseBundle = getCourseBundle(courseBundleId);
    courseBundle.setDeletedAt(new Date());
    courseBundleRepository.save(courseBundle);
  }

  public void deleteThumbNail(Long courseBundleId) {
    var courseBundle = getCourseBundle(courseBundleId);
    courseBundle.setThumbnail(null);
    courseBundleRepository.save(courseBundle);
  }
}
