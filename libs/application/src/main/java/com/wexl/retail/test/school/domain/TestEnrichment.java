package com.wexl.retail.test.school.domain;

import com.wexl.retail.ai.dto.ExamAnalysis;
import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.model.Model;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.hibernate.annotations.Type;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "test_enrichment")
public class TestEnrichment extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(columnDefinition = "TEXT")
  private String summary;

  private String questionUuid;

  @Column(columnDefinition = "TEXT")
  private String concept;

  @Column(columnDefinition = "TEXT")
  private String reference;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb")
  private ExamAnalysis.TestEnrichData content;

  @Enumerated(EnumType.STRING)
  private QuestionType questionType;

  private String subtopicSlug;
}
