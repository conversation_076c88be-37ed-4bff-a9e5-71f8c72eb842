package com.wexl.retail.test.schedule.controller;

import static com.wexl.retail.util.Constants.AUTHORIZATION_HEADER;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.commons.security.annotation.IsTeacher;
import com.wexl.retail.metrics.dto.QuestionsAnalytics;
import com.wexl.retail.model.GenericResponse;
import com.wexl.retail.notification.service.EventNotificationService;
import com.wexl.retail.test.schedule.dto.*;
import com.wexl.retail.test.schedule.service.ScheduleTestService;
import com.wexl.retail.test.school.domain.TestType;
import com.wexl.retail.test.school.service.TestDefinitionService;
import com.wexl.retail.util.ValidationUtils;
import com.wexl.retail.v2.service.ScheduleTestStudentService;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.web.bind.annotation.*;

@IsTeacher
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgId}/teachers/{teacherId}")
public class ScheduleTestController {

  private final ScheduleTestService scheduleTestService;
  private final TestDefinitionService testDefinitionService;
  private final EventNotificationService eventNotificationService;
  private final ScheduleTestStudentService scheduleTestStudentService;
  private final ValidationUtils validationUtils;

  @PostMapping("/test-schedules")
  public SimpleScheduleTestResponse scheduleTest(
      final @PathVariable("orgId") String orgSlug,
      @Valid @RequestBody SimpleScheduleTestRequest scheduleTestRequest) {
    try {
      scheduleTestRequest.setOrgSlug(orgSlug);
      var scheduleTest = scheduleTestService.scheduleTest(scheduleTestRequest);
      eventNotificationService.triggerScheduleTestPushNotification(scheduleTest.getId());
      return scheduleTest;
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @PostMapping("/group-test-schedules")
  public List<SimpleScheduleTestResponse> groupScheduleTest(
      final @PathVariable("orgId") String parentOrg,
      @Valid @RequestBody GroupScheduleTestRequest groupScheduleTestRequest) {

    return scheduleTestService.groupScheduleTest(groupScheduleTestRequest, parentOrg);
  }

  @PostMapping("/group-test-schedules:bulk")
  public List<SimpleScheduleTestResponse> groupScheduleTestByGradeList(
      final @PathVariable("orgId") String parentOrg,
      @Valid @RequestBody
          ScheduleTestDto.GroupScheduleTestRequestBulk groupScheduleTestRequestBulk) {

    return scheduleTestService.groupScheduleTestByGradeList(
        groupScheduleTestRequestBulk, parentOrg);
  }

  @PutMapping("/test-schedules/{id}")
  public SimpleScheduleTestResponse editScheduleTest(
      final @PathVariable("orgId") String orgSlug,
      @Valid @PathVariable("id") long testScheduleId,
      @Valid @RequestBody SimpleScheduleTestRequest scheduleTestRequest) {

    scheduleTestRequest.setOrgSlug(orgSlug);
    return scheduleTestService.editScheduleTest(testScheduleId, scheduleTestRequest);
  }

  @GetMapping("/test-schedules")
  public List<ScheduleTestResponse> getScheduledTests(
      @PathVariable("orgId") String orgSlug,
      @RequestParam Optional<String> subject,
      @RequestParam Optional<String> grade,
      @RequestParam Optional<String> board,
      @RequestParam Optional<String> type,
      @RequestParam(value = "child_org", required = false) String childOrg,
      @RequestParam Optional<TestType> testType) {

    String testOrgSlug = Optional.ofNullable(childOrg).orElse(orgSlug);

    var getScheduledTestsRequest =
        GetScheduledTestsRequest.builder()
            .board(board.orElse(Strings.EMPTY))
            .grade(grade.orElse(null))
            .subject(subject.orElse(Strings.EMPTY))
            .orgSlug(testOrgSlug)
            .type(type.orElse(Strings.EMPTY))
            .testType(testType.orElse(null))
            .build();

    return scheduleTestService.getScheduledTests(getScheduledTestsRequest);
  }

  @GetMapping("/test-schedules/{id}")
  public ScheduleTestResponse getScheduledTest(@Valid @PathVariable("id") long testScheduleId) {

    return scheduleTestService.getScheduledTest(testScheduleId);
  }

  @DeleteMapping("/test-schedules/{id}")
  public GenericResponse deleteTestSchedule(@Valid @PathVariable("id") long testScheduleId) {

    return scheduleTestService.deleteScheduleTestById(testScheduleId);
  }

  @GetMapping("/test-schedules/{id}/questions-analysis")
  public QuestionsAnalytics getQuestionAnalyticsOfScheduledTest(
      @PathVariable("teacherId") String authUserid,
      @PathVariable("id") long scheduleId,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {
    return testDefinitionService.getQuestionsAnalyticsById(authUserid, scheduleId, bearerToken);
  }

  @IsTeacher
  @PostMapping("/test-schedules/{id}/complete-exam")
  public void endTest(@PathVariable("id") long scheduleId) {
    testDefinitionService.endTest(scheduleId);
  }

  @IsOrgAdminOrTeacher
  @PostMapping("/test-schedules/{id}/notifications")
  public void sendNotification(
      @PathVariable("teacherId") String teacherAuthId, @PathVariable("id") long scheduleId) {
    scheduleTestStudentService.sendStudentResultsNotification(scheduleId, teacherAuthId);
  }

  @IsOrgAdminOrTeacher
  @GetMapping(value = "/test-schedules/{id}/exam-results")
  public ExamResultDto.Response getScheduleTestResult(
      @PathVariable("id") long scheduleId,
      @RequestParam(name = "regenerate", defaultValue = "false") boolean regenerate) {
    return scheduleTestStudentService.generateStudentsScheduleTestResult(scheduleId, regenerate);
  }

  @IsOrgAdminOrTeacher
  @GetMapping(value = "/exam-results/{examId}")
  public ExamResultDto.Response getExamResult(@PathVariable long examId) {
    return scheduleTestStudentService.generateStudentTestResult(
        validationUtils.findByExamId(examId));
  }

  @IsOrgAdminOrTeacher
  @DeleteMapping("/test-schedules/{id}/students/{studentAuthUserId}")
  public void deleteExam(
      @PathVariable("id") long scheduleId,
      @PathVariable("studentAuthUserId") String studentAuthUserId) {
    scheduleTestStudentService.deleteExam(scheduleId, studentAuthUserId);
  }

  @GetMapping("/test-schedules/reports")
  public List<TestScheduleReportResponse.Response> testScheduleReports(
      @RequestParam List<String> orgSlugs,
      @RequestParam List<String> board,
      @RequestParam List<String> grades,
      @RequestParam List<String> sectionUuids,
      @RequestParam String fromDate,
      @RequestParam String toDate) {
    return scheduleTestService.getTestScheduleReports(
        orgSlugs, board, grades, sectionUuids, fromDate, toDate);
  }
}
