package com.wexl.retail.mlp.model;

import com.wexl.retail.model.Model;
import com.wexl.retail.model.Student;
import jakarta.persistence.*;
import java.sql.Timestamp;
import java.util.Date;
import lombok.*;
import org.springframework.data.annotation.CreatedDate;

@Data
@Getter
@Setter
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "mlp_attendance", schema = "public")
public class MlpAttendance extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "student_id")
  private Student student;

  @ManyToOne
  @JoinColumn(name = "mlp_id")
  private Mlp mlp;

  private Date mlpDate;

  @CreatedDate private Timestamp createdAt;
}
