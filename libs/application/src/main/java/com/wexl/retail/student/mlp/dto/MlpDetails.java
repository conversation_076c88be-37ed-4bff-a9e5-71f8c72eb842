package com.wexl.retail.student.mlp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
public class MlpDetails {

  private String title;

  @JsonProperty("mlp_id")
  private Long mlpId;

  @JsonProperty("section_attendance")
  private int sectionAttendance;
}
