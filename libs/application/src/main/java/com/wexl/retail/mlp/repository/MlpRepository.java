package com.wexl.retail.mlp.repository;

import com.wexl.retail.metrics.dto.QuestionDetails;
import com.wexl.retail.mlp.dto.*;
import com.wexl.retail.mlp.model.Mlp;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.organization.dto.MetricsCountByOrg;
import com.wexl.retail.organization.dto.MlpStudentActivity;
import com.wexl.retail.organization.dto.MlpTeacherActivity;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.user.dto.BasicUserInfo;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface MlpRepository extends CrudRepository<Mlp, Long> {
  Optional<Mlp> getFirstByExamRef(String examRef);

  @Query(
      value =
          """
          select * \
          from mlp \
          where org_slug =:orgId and subject_slug in (:subjects) and \
           section_id in (:sections) order by created_at desc limit :limit \
          """,
      nativeQuery = true)
  List<Mlp> getMlpsByTeacherPreferences(
      String orgId, Set<String> subjects, List<Long> sections, int limit);

  @Query(
      value =
          """
          select * from mlp where org_slug =:orgId and grade_slug = :gradeSlug \
          and section_id =:sectionId and to_char(created_at , 'YYYY-MM-DD') = :date order by created_at desc\
          """,
      nativeQuery = true)
  List<Mlp> getSectionMlps(String orgId, String gradeSlug, Long sectionId, String date);

  @Query(
      value =
          """
             select m.teacher_id as teacherId,concat(u.first_name,' ',u.last_name) as name,
  m.grade_name as gradeName, m.grade_slug as gradeSlug,s."name" as sectionName,
  COALESCE(sum(m.attendance_percentage)/count(*),0) as averageAttendance,
  count(*) as mlpCount from mlp m
  inner join teacher_details td on m.teacher_id = td.id
  inner join users u on td.user_id = u.id
  inner join sections s on s.id = m.section_id
  where to_char(m.created_at , 'YYYY-MM-DD') > :fromDate and org_slug =:orgSlug
  and  (cast((:gradeSlug) as varChar) is null or m.grade_slug in (:gradeSlug))
  and s.deleted_at is null
  group by m.teacher_id, u.first_name,u.last_name,m.grade_name,m.grade_slug,s."name"
  order by 4 desc""",
      nativeQuery = true)
  List<MlpTeacherMetrics> getMlpTeacherMetrics(String orgSlug, String fromDate, String gradeSlug);

  @Query(
      value =
          """
          select m.grade_name as name, count(*) as mlpCount \
          from mlp m \
          where to_char(m.created_at , 'YYYY-MM-DD') > :fromDate and org_slug =:orgSlug \
          group by m.grade_name \
          order by 2 desc\
          """,
      nativeQuery = true)
  List<MlpGradeMetrics> getMlpGradeMetrics(String orgSlug, String fromDate);

  @Query(
      value =
          """
          select distinct to_char(created_at , 'YYYY-MM-DD') from mlp\
           where org_slug=:orgId and grade_slug=:gradeSlug and section_id=:sectionId\
           order by to_char(created_at , 'YYYY-MM-DD') desc\
           limit 90\
          """,
      nativeQuery = true)
  List<String> getActivityDatesForSection(String orgId, String gradeSlug, Long sectionId);

  Optional<Mlp> findFirstByExamRef(String examRef);

  @Query(value = "select count(*) from mlp where org_slug = :orgSlug", nativeQuery = true)
  Integer mlpCountByOrg(String orgSlug);

  @Query(
      value =
          """
          select extract(epoch from date(created_at)) * 1000 as examDate, count(id) as teacherActivity from mlp\s
          where extract(epoch from date(created_at)) > :fromTime
          and extract(epoch from date(created_at)) < :toTime
          and org_slug = :orgSlug and subtopic_slug is not null \
          group by date(created_at)\
          """,
      nativeQuery = true)
  List<MlpTeacherActivity> getMlpTeacherActivity(String orgSlug, long fromTime, long toTime);

  @Query(
      value =
          """
          with mlp_ids as(select id,extract(epoch from date(created_at)) * 1000 as examDate from mlp
          where extract(epoch from date(created_at)) > :fromTime
          and extract(epoch from date(created_at)) < :toTime and org_slug = :orgSlug)
           select count(distinct minst.mlp_id) as studentActivity, m.examDate from mlp_ids m\s
          left join mlp_inst minst on m.id = minst.mlp_id and minst.exam_id is not null\s
          group by m.examDate\
          """,
      nativeQuery = true)
  List<MlpStudentActivity> getMlpStudentActivity(String orgSlug, long fromTime, long toTime);

  @Query(
      value =
          """
                  select m.* from mlp m
                  join sections sec on m.section_id = sec.id
                  where org_slug =:orgId and sec.deleted_at is null and
                  to_char(m.created_at , 'YYYY-MM-DD') > :fromDate order by m.created_at desc limit :limit
          """,
      nativeQuery = true)
  List<Mlp> getAllMlps(String orgId, String fromDate, int limit);

  @Query(
      value =
          """
          select m.grade_name as name, count(*) as mlpCount \
          from mlp m \
          where to_char(m.created_at , 'YYYY-MM-DD') > :fromDate and org_slug in (:orgSlugs) \
          group by m.grade_name \
          order by 2 desc\
          """,
      nativeQuery = true)
  List<MlpGradeMetrics> getChildInstitutionGradeMetrics(List<String> orgSlugs, String fromDate);

  @Query(
      value =
          """
                      select o.slug as slug, o.name as name, count(m.id) as mlpCount,
                                    round( coalesce(CAST(avg(m.attendance_percentage) as numeric),0), 2) as attendancePercentage from orgs o
                                    left join mlp m on m.org_slug = o.slug and  to_char(m.created_at , 'YYYY-MM-DD') > :fromDate
                                    where o.slug in (:orgSlugs)
                                    group by o.slug, o.name order by attendancePercentage desc""",
      nativeQuery = true)
  List<MlpGradeMetrics> getChildInstitutionMetrics(List<String> orgSlugs, String fromDate);

  @Query(
      value =
          """
          select m.subject_name as name, count(*) as mlpCount \
          from mlp m \
          where to_char(m.created_at , 'YYYY-MM-DD') > :fromDate and org_slug in (:orgSlugs) \
          group by m.subject_name \
          order by 2 desc\
          """,
      nativeQuery = true)
  List<MlpGradeMetrics> getChildInstitutionSubjectMetrics(List<String> orgSlugs, String fromDate);

  @Query(value = " select count(*) from mlp where org_slug in (:orgSlugs)", nativeQuery = true)
  Integer getMlpCount(List<String> orgSlugs);

  @Query(
      value =
          """
          select o.slug as orgSlug ,count(m.id) as count from orgs o
               left join mlp m on m.org_slug = o.slug  where o.slug in (:orgSlugs)
               group by orgSlug\
          """,
      nativeQuery = true)
  List<MetricsCountByOrg> getMlpCountByorgs(List<String> orgSlugs);

  @Query(
      value =
          """
          select count(*) from mlp_inst mi\s
          inner join mlp m on m.id = mi.mlp_id\s
          where m.org_slug in (:orgSlugs) and mi.attendance_percentage is not null\
          """,
      nativeQuery = true)
  Integer getStudentMlpAttemptedCount(List<String> orgSlugs);

  @Query(
      value =
          """
          select count(*) from mlp where org_slug in (:orgSlugs) and
          to_char(created_at , 'YYYY-MM-DD') = :date\
          """,
      nativeQuery = true)
  Integer getMlpsCreatedYesterday(List<String> orgSlugs, String date);

  @Query(
      value =
          """
          with studentMlpCount as (select to_char(m.created_at , 'YYYY-MM-DD') as date, count(mi.attendance_percentage) as studentMlpCount\s
           from mlp m
           left join mlp_inst mi on mi.mlp_id = m.id and mi.attendance_percentage is not null
           where m.org_slug in (:orgSlugs) and  to_char(m.created_at , 'YYYY-MM-DD') between :fromDate and :toDate
           group by to_char(m.created_at , 'YYYY-MM-DD'))
           select to_char(m.created_at , 'YYYY-MM-DD') as date, count(m.id) as mlpCount, s.studentMlpCount
           from mlp m left join studentMlpCount s on s.date = to_char(m.created_at , 'YYYY-MM-DD')\s
           where m.org_slug in (:orgSlugs) and  to_char(m.created_at , 'YYYY-MM-DD') between :fromDate and :toDate
           group by to_char(m.created_at , 'YYYY-MM-DD'), s.studentMlpCount\
          """,
      nativeQuery = true)
  List<MlpAnalytics> fetchMlpWeeklyCount(
      List<String> orgSlugs, LocalDate fromDate, LocalDate toDate);

  @Query(
      value =
          """
          select date(d) as date, count(distinct m.*) as mlpCount, count(mi.attendance_percentage) as studentMlpCount\
                      from generate_series(current_date - interval '5 day',
                      current_date - interval '1 day', '1 day') d
                      left join mlp m on date(to_char(m.created_at,'YYYY-MM-DD')) = d and org_slug in(:orgSlugs)\
          left join mlp_inst mi on mi.mlp_id = m.id and mi.attendance_percentage is not null
                      group by date order by date desc\
          """,
      nativeQuery = true)
  List<MlpAnalytics> getMlpCountDateWise(List<String> orgSlugs);

  @Query(
      value =
          """
                      with studentMlpCount as (select cast(to_char(m.created_at,'MM') as int) as date, count(mi.attendance_percentage) as studentMlpCount from mlp m
                      left join mlp_inst mi on mi.mlp_id = m.id and mi.attendance_percentage is not null
                      where org_slug in (:orgSlugs) and  to_char(m.created_at , 'YYYY-MM-DD') between :fromDate and :toDate
                      group by to_char(m.created_at,'MM'))
                      select cast(to_char(created_at,'MM') as int) as date, count(id) as mlpCount, s.studentMlpCount from mlp m
                      left join studentMlpCount s on s.date = cast(to_char(created_at,'MM') as int)
                      where org_slug in (:orgSlugs) and  to_char(m.created_at , 'YYYY-MM-DD') between :fromDate and :toDate
                      group by to_char(m.created_at,'MM'),s.studentMlpCount
                      order by to_char(m.created_at,'MM') desc""",
      nativeQuery = true)
  List<MlpAnalytics> getMlpCountByMonth(
      List<String> orgSlugs, LocalDate fromDate, LocalDate toDate);

  @Query(
      value =
          """
                      select concat(u.first_name,' ',u.last_name) as fullName,u.auth_user_id as userName,ss.id as studentId,s.name as sectionName,count(m.id) as totalMlpCount,
                      count(mi.attendance_percentage) as attemptedMlpCount,
                      COALESCE(sum(mi.attendance_percentage)/count(m.id),0) as attendancePercentage,
                      COALESCE(sum(mi.knowledge_percentage)/count(m.id),0) as knowledgePercentage
                      from mlp m join sections s on m.section_id = s.id
                      inner join mlp_inst mi on m.id = mi.mlp_id
                      inner join students ss on ss.id = mi.student_id  and ss.section_id = s.id
                      inner join users u on u.id = ss.user_id
                      where s.uuid in (:sectionUuid)
                      and to_char(m.created_at , 'YYYY-MM-DD') > :fromDate
                      and m.subject_slug in(:subjects) and u.deleted_at is null group by ss.id,u.first_name,u.last_name,fullName,userName,s.name
                      order by attendancePercentage DESC,knowledgePercentage DESC""",
      nativeQuery = true)
  List<StudentMlpDetails> getMlpStudentDetails(
      List<UUID> sectionUuid, List<String> subjects, LocalDate fromDate);

  @Query(
      value =
          """
          select m.* from mlp m join mlp_inst mi on mi.mlp_id = m.id where mi.exam_id is not null and mi.knowledge_percentage = 'NaN'\
          limit 200\
          """,
      nativeQuery = true)
  List<Mlp> getAllMlpsByNullAm();

  @Query(
      value =
          """
          select id from mlp where parent_mlp_id = :mlpId
          """,
      nativeQuery = true)
  List<Long> getAllChildMlpIds(Long mlpId);

  @Query(
      value =
          """
          select m.* from mlp m where m.section_id = :sectionId \
          order by created_at desc limit :limit\
          """,
      nativeQuery = true)
  List<Mlp> getAllSectionMlps(Long sectionId, int limit);

  @Query(
      value =
          """
                      select concat(u.first_name,' ',u.last_name) as teacherName,
                      m.title as mlpName,m.question_count as questionCount,
                      count(mi.student_id) as totalNoStudent,
                      count(mi.attendance_percentage) as mlpAttemptedStudentCount,
                      m.subject_name as subject,m.created_at as createdDate,
                      m.chapter_name as chapter,
                      m.exam_ref as examRef,
                      m.subtopic_name as subtopic
                      from mlp m
                      inner join teacher_details td on td.id=teacher_id
                      inner join users u on u.id=td.user_id
                      left join mlp_inst mi on mi.mlp_id=m.id
                      where m.id in (:mlpIds)
                      and  ((:orgSlug) is null or m.org_slug in (:orgSlug))
                      group by m.title,m.created_at,m.question_count,m.subject_name,m.chapter_name
                      ,m.exam_ref,m.subtopic_name,u.first_name,u.last_name
                      """,
      nativeQuery = true)
  List<MlpDetails> getMlpDetails(List<Long> mlpIds, String orgSlug);

  @Query(
      value =
          """
                      select ea.question_uuid as questionUuid,
                      sum(case when ea.is_correct = true then 0
                      when ea.is_correct = false then 1 end) as incorrectAnsweredCount
                      from exam_answers ea
                      where ea.exam_id in (select exam_id from mlp_inst mi join mlp m on m.id = mi.mlp_id
                       where mlp_id in (:mlpIds) and exam_id is not null and ((:orgSlug) is null or m.org_slug in (:orgSlug)))
                      group by ea.question_uuid
                      order by incorrectAnsweredCount desc
                      """,
      nativeQuery = true)
  List<QuestionDetails> getQuestionDetails(List<Long> mlpIds, String orgSlug);

  @Query(
      value =
          """
          select m.title as title, mi.exam.id as examId from Mlp m join MlpInst mi on m.id = mi.mlp.id where m.subtopicSlug = ?1\
           and mi.student.id = ?2 and mi.exam.id is not null\
          """)
  List<ExamRecordsQueryResult> getMlpRecordsForSubtopic(
      String subtopicSlug, Long studentId, PageRequest pageRequest);

  @Query(
      value =
          """
          with student_mlps as (
          select
          	count(mi.id)
          from
          	students s
          inner join mlp_inst mi on
          	mi.student_id = s.id
          inner join mlp m on
          	m.id = mi.mlp_id
          where
          	s.user_id = :userId
          	and to_char(m.created_at , 'YYYY-MM-DD') > :createdAt
          	and mi.exam_id is not null
          ),
          total_mlp as (
          select
          	count(*)
          from
          	mlp m
          inner join students s on
          	s.section_id = m.section_id
          	and s.id = (
          	select
          		id
          	from
          		students
          	where
          		user_id = :userId)
          	and to_char(m.created_at , 'YYYY-MM-DD') > :createdAt
          )
          select
          	sm.count = tm.count
          from
          	student_mlps sm,
          	total_mlp tm
          """,
      nativeQuery = true)
  Optional<Boolean> didAttemptAllMlpsFromDate(long userId, String createdAt);

  @Query(
      value =
          """
          select m.* from mlp m where m.org_slug in (:orgSlugs) \
          and to_char(m.created_at,'YYYY-MM-DD') >= :fromDate and to_char(m.created_at,'YYYY-MM-DD') <= :toDate order by m.created_at desc limit :limit \
          """,
      nativeQuery = true)
  List<Mlp> getMlpsByOrgSlugs(List<String> orgSlugs, String fromDate, String toDate, Integer limit);

  @Query(
      value =
          """
                  select m.* from Mlp m join sections s on s.id = m.section_id
                  where m.org_slug = :orgSlug
                  and (cast((:gradeSlug) as varChar) is null or m.grade_slug in (:gradeSlug))
                  and (cast((:subjectSlug) as varChar) is null or m.subject_slug in (:subjectSlug))
                  and to_char(m.created_at,'YYYY-MM-DD') >= :fromDate
                  and to_char(m.created_at,'YYYY-MM-DD') <= :toDate""",
      nativeQuery = true)
  List<Mlp> fetchMlpByOrgSlugAndGradeSlugAndSubjectSlugAndCreatedAtAfter(
      String orgSlug,
      String gradeSlug,
      String subjectSlug,
      String fromDate,
      String toDate,
      PageRequest pageRequest);

  @Query(
      value =
          """
                          select m.* from Mlp m join sections s on s.id = m.section_id
                          where m.org_slug = :orgSlug
                          and to_char(m.created_at,'YYYY-MM-DD') >= :fromDate
                          and to_char(m.created_at,'YYYY-MM-DD') <= :toDate""",
      nativeQuery = true)
  List<Mlp> fetchMlpByChildOrg(String orgSlug, String fromDate, String toDate);

  @Query(
      value =
          """
                          select m.* from Mlp m join sections s on s.id = m.section_id
                          where m.org_slug = :orgSlug
                          and (cast((:gradeSlug) as varChar) is null or m.grade_slug in (:gradeSlug))
                          and to_char(m.created_at,'YYYY-MM-DD') >= :fromDate
                          and to_char(m.created_at,'YYYY-MM-DD') <= :toDate""",
      nativeQuery = true)
  List<Mlp> fetchMlpByOrgSlugAndGradeSlug(
      String orgSlug, String gradeSlug, String fromDate, String toDate);

  @Query(
      value =
          """
          select concat(u.first_name,' ',u.last_name) as fullName, u.email as email, s2.name as section
          from users u
          inner join students s on s.user_id = u.id
          inner join sections s2 on s.section_id = s2.id
          where u.organization = :orgSlug
            and u.deleted_at is null
          and s.id not in(
              select mi.student_id
              from mlp m
          inner join mlp_inst mi on m.id = mi.mlp_id
              where org_slug = :orgSlug and to_char(m.created_at, 'YYYY-MM-DD') = :requiredDate
                and mi.exam_id is not null
          )\
          order by s2.name\
          """,
      nativeQuery = true)
  List<BasicUserInfo> getMlpsNotAttendedStudents(String orgSlug, LocalDate requiredDate);

  @Query(
      value =
          """

          select u.id as userId, concat(u.first_name,' ',u.last_name) as fullName, u.email as email
          from users u
          inner join teacher_details td on td.user_id = u.id
          where u.organization = :orgSlug
            and u.deleted_at is null
          and td.id not in(select td.id from mlp m
            inner join teacher_details td on td.id = m.teacher_id
            where org_slug = :orgSlug and to_char(m.created_at, 'YYYY-MM-DD') = :requiredDate)\
          order by u.first_name\
          """,
      nativeQuery = true)
  List<BasicUserInfo> getMlpsNotAssignedTeachers(String orgSlug, LocalDate requiredDate);

  @Query(
      value =
          """
                 select m.* from mlp m inner join teacher_details td on td.id = m.teacher_id
                 inner join users u on u.id = td.user_id where u.auth_user_id = :teacherUuid and
                 to_char(m.created_at , 'YYYY-MM-DD') > :fromDate and to_char(m.created_at,'YYYY-MM-DD') <= :toDate
                 and (cast((:subjectSlugs) as varChar) is null or m.subject_slug in (:subjectSlugs)) order by m.created_at desc limit :limit
                      """,
      nativeQuery = true)
  List<Mlp> getMlpsByTeacherUuidAndDateBetween(
      String teacherUuid, String fromDate, String toDate, Integer limit, List<String> subjectSlugs);

  @Query(
      value =
          """
                         select m.* from mlp m inner join teacher_details td on td.id = m.teacher_id
                         inner join users u on u.id = td.user_id where u.auth_user_id = :teacherUuid
                         and (cast((:subjectSlugs) as varChar) is null or m.subject_slug in (:subjectSlugs)) order by m.created_at desc limit :limit
                              """,
      nativeQuery = true)
  List<Mlp> getMlpsByTeacherUuid(String teacherUuid, Integer limit, List<String> subjectSlugs);

  @Query(
      value =
          """
          select m.* from mlp m where m.org_slug in (:orgSlugs) \
          and to_char(m.created_at,'YYYY-MM-DD') >= :fromDate and to_char(m.created_at,'YYYY-MM-DD') <= :toDate \
           and (cast((:subjectSlugs) as varChar) is null or m.subject_slug in (:subjectSlugs)) order by m.created_at desc limit :limit \
          """,
      nativeQuery = true)
  List<Mlp> getMlpsByOrgSlugsFromDateToDate(
      List<String> orgSlugs,
      String fromDate,
      String toDate,
      Integer limit,
      List<String> subjectSlugs);

  @Query(
      value =
          """
                          select date(d) as date,m.grade_slug as GradeSlug,m.grade_name as GradeName,COALESCE(sum(attendance_percentage)/count(attendance_percentage),0) as attendancePercentage
                          from generate_series(current_date - interval '5 day',current_date - interval '1 day', '1 day') d
                          left join mlp m on date(to_char(m.created_at,'YYYY-MM-DD')) = d and org_slug in(:orgSlugs) where m.grade_slug is not null
                          group by date,m.grade_slug,m.grade_name order by date desc
                                        """,
      nativeQuery = true)
  List<MlpAnalytics> getMlpAttendanceByDay(List<String> orgSlugs);

  @Query(
      value =
          """

                      select m.id, m.title, m.exam_ref as examRef,
                      m.question_count as questionCount,
                      m.synopsis_slug as synopsisSlug,
                      m.video_slug as videoSlug,
                      m.alt_video_slug as altVideoSlug,
                      m.sha_link as shaLink,
                      concat(u.first_name, ' ',u.last_name) as teacherName,
                      m.subtopic_name as subtopicName,
                      m.synopsis_name as synopsisName,
                      m.grade_name as gradeName,
                      m.chapter_name as chapterName,
                      m.subtopic_slug as subtopicSlug,
                      s.name as sectionName,
                      case when mi.exam_id is not null then 'PRACTICE_COMPLETED' else 'NOT_STARTED' end as practiceStatus,
                      mi.synopsis_status as synopsisStatus,
                      mi.video_status as videoStatus,
                      m.questions_assignee_mode as questionsAssigneeMode,
                      m.created_at as createdAt,
                      m.video_source as videoSource,
                      m.subject_slug as subjectSlug,
                      m.subject_name as subjectName,
                      m.comment,case when mi.exam_id is null then 0 else mi.exam_id end as examId
                      from mlp_inst mi
                      inner join mlp m on mi.mlp_id = m.id
                          inner join teacher_details td on m.teacher_id = td.id
                      inner join users u on td.user_id = u.id
                          inner join sections s on m.section_id = s.id
                      where mi.student_id = :studentId
                      and m.grade_slug = :gradeSlug
                        and to_char(m.created_at, 'YYYY-MM-DD') = :date order by m.created_at desc""",
      nativeQuery = true)
  List<StudentResponseMlpQueryResult> getStudentMlpReponseByDate(
      String date, long studentId, String gradeSlug);

  @Query(
      value =
          """

                      with allMlps as (select m.id,
                                              m.title,
                                              m.exam_ref                                                               as examRef,
                                              m.question_count                                                         as questionCount,
                                              m.synopsis_slug                                                          as synopsisSlug,
                                              m.video_slug                                                             as videoSlug,
                                              m.alt_video_slug                                                         as altVideoSlug,
                                              m.subtopic_name                                                          as subtopicName,
                                              m.synopsis_name                                                          as synopsisName,
                                              m.grade_name                                                             as gradeName,
                                              m.chapter_name                                                           as chapterName,
                                              m.subtopic_slug                                                           as subtopicSlug,
                                              case when mi.exam_id is not null then 'PRACTICE_COMPLETED' else 'NOT_STARTED' end as practiceStatus,
                                              mi.synopsis_status                                                       as synopsisStatus,
                                              mi.video_status                                                          as videoStatus,
                                              m.questions_assignee_mode                                                as questionsAssigneeMode,
                                              m.created_at                                                             as createdAt,
                                              m.video_source                                                           as videoSource,
                                              m.subject_slug                                                            as subjectSlug,
                                              m.subject_name                                                           as subjectName,
                                              m.comment,
                                              m.sha_link                                                               as shaLink,
                                              case when mi.exam_id is null then 0 else mi.exam_id end                  as examId
                                       from mlp_inst mi
                                                inner join mlp m on mi.mlp_id = m.id
                                       where mi.student_id = :id
                                       order by m.created_at desc)
                      select *
                      from allMlps
                      where (
                          ((subtopicSlug is null) or (practiceStatus = 'PRACTICE_COMPLETED'))
                          and ((videoSlug is null) or (videoStatus = 'COMPLETED'))
                          and ((synopsisSlug is null) or (synopsisStatus = 'COMPLETED'))
                          )
                      limit :limit""",
      nativeQuery = true)
  List<StudentResponseMlpQueryResult> getCompletedMlpResponses(long id, int limit);

  @Query(
      value =
          """

                      with allMlps as (select m.id,
                                              m.title,
                                              m.exam_ref                                                               as examRef,
                                              m.question_count                                                         as questionCount,
                                              m.synopsis_slug                                                          as synopsisSlug,
                                              m.video_slug                                                             as videoSlug,
                                              m.alt_video_slug                                                         as altVideoSlug,
                                              m.subtopic_name                                                          as subtopicName,
                                              m.synopsis_name                                                          as synopsisName,
                                              m.grade_name                                                             as gradeName,
                                              m.chapter_name                                                           as chapterName,
                                              m.subtopic_slug                                                           as subtopicSlug,
                                              case when mi.exam_id is not null then 'PRACTICE_COMPLETED' else 'NOT_STARTED' end as practiceStatus,
                                              mi.synopsis_status                                                       as synopsisStatus,
                                              mi.video_status                                                          as videoStatus,
                                              m.questions_assignee_mode                                                as questionsAssigneeMode,
                                              m.created_at                                                             as createdAt,
                                              m.video_source                                                           as videoSource,
                                              m.comment,
                                              m.sha_link                                                               as shaLink,
                                              m.subject_slug                                                           as subjectSlug,
                                              m.subject_name                                                           as subjectName,
                                              case when mi.exam_id is null then 0 else mi.exam_id end                  as examId
                                       from mlp_inst mi
                                                inner join mlp m on mi.mlp_id = m.id
                                                where mi.student_id = :id
                                       order by m.created_at desc)
                      select *
                      from allMlps
                      where (
                          ((subtopicSlug is not null) and (practiceStatus = 'NOT_STARTED'))
                          or ((videoSlug is not null) and (videoStatus = 'NOT_STARTED'))
                          or ((synopsisSlug is not null) and (synopsisStatus = 'NOT_STARTED'))
                          )
                      limit :limit""",
      nativeQuery = true)
  List<StudentResponseMlpQueryResult> getPendingMlpReponses(long id, int limit);

  List<Mlp> findByParentAndOrgSlug(Mlp parentMlp, String orgId);

  List<Mlp> findByOrgSlug(String orgSlug);

  @Query(
      value =
          """

                      select m.* from mlp m join sections s on s.organization= m.org_slug and s.grade_slug = m.grade_slug
                      join students st on st.class_id = s.grade_id
                      where st.id =:studentId  and m.org_slug =:orgSlug
          """,
      nativeQuery = true)
  List<Mlp> findByOrgSlugAndGrade(String orgSlug, Long studentId);

  List<Mlp> findAllByTeacherAndSectionIdInAndCreatedAtBetweenOrderByCreatedAtDesc(
      Teacher teacher, List<Long> sectionIds, Timestamp fromTime, Timestamp currentTime);

  List<Mlp> findByParentInAndDeletedAtIsNull(List<Mlp> mlps);

  List<Mlp> findAllByTeacherAndSectionIdInOrderByCreatedAtDesc(
      Teacher teacher, List<Long> sectionsByTeacher);

  List<Mlp> findAllByOrgSlugAndSectionInOrderByCreatedAtDesc(
      String orgSlug, List<Section> sections, Pageable pageable);
}
