package com.wexl.retail.mlp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public interface MlpAnalytics {

  @JsonProperty("date")
  String getDate();

  @JsonProperty("mlp_count")
  Integer getMlpCount();

  @JsonProperty("student_mlp_count")
  Integer getStudentMlpCount();

  @JsonProperty("grade_slug")
  String getGradeSlug();

  @JsonProperty("grade_name")
  String getGradeName();

  @JsonProperty("attendance_percentage")
  Double getAttendancePercentage();
}
