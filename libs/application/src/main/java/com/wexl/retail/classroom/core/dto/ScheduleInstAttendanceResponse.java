package com.wexl.retail.classroom.core.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
public class ScheduleInstAttendanceResponse {

  @Enumerated(EnumType.STRING)
  @JsonProperty("attendance_status")
  private ScheduleInstAttendanceStatus attendanceStatus;

  @JsonProperty("student_id")
  private Long student;

  @JsonProperty("full_name")
  private String fullName;
}
