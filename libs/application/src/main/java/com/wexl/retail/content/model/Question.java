package com.wexl.retail.content.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.liveworksheet.dto.WorkSheetQuestionType;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Question {

  @JsonProperty("Question")
  private String questions;

  private int marks;

  private Integer id;

  private String uuid;

  private String type;

  private String complexity;

  private String chapterSlug;

  private String subtopicSlug;

  private String subjectSlug;

  private String subjectName;

  private boolean active;
  private boolean published;
  private String organizationSlug;
  private String organization;

  private boolean hasMultipleSubTopics = false;

  @JsonProperty("question_category_id")
  private String questionCategoryId;

  @JsonProperty("Option1")
  private String option1;

  @JsonProperty("Option2")
  private String option2;

  @JsonProperty("Option3")
  private String option3;

  @JsonProperty("Option4")
  private String option4;

  @JsonProperty("Explanation")
  private String explanation;

  @JsonProperty("Answer")
  private Integer answer;

  @JsonProperty("live_worksheet_answer")
  private String liveWorksheetAnswer;

  @JsonProperty("live_worksheet_answer_type")
  private WorkSheetQuestionType liveWorksheetAnswerType;

  @JsonProperty("live_worksheet_answer_uuid")
  private String liveWorksheetAnswerUuid;

  private String permKey;
}
