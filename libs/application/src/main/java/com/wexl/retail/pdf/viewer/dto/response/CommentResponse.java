package com.wexl.retail.pdf.viewer.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.sql.Timestamp;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CommentResponse {
  private long id;
  private String username;
  private String comment;

  @JsonProperty("parent_id")
  private long parentId;

  @JsonProperty("annotation_id")
  private long annotationId;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy/MM/dd HH:mm:ss")
  @JsonProperty("date_created")
  private Timestamp createdAt;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy/MM/dd HH:mm:ss")
  @JsonProperty("date_modified")
  private Timestamp updatedAt;

  @JsonProperty("review_statuses")
  private List<CommentsReviewHistoryResponse> reviewStatuses;
}
