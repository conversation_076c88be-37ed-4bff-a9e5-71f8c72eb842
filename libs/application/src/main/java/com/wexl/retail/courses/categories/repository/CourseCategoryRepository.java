package com.wexl.retail.courses.categories.repository;

import com.wexl.retail.courses.categories.model.CourseCategory;
import com.wexl.retail.organization.model.Organization;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CourseCategoryRepository extends JpaRepository<CourseCategory, Long> {

  List<CourseCategory> findAllByOrganizationOrderByIdDesc(Organization organization);

  CourseCategory findByIdAndOrganization(Long id, Organization organization);
}
