package com.wexl.retail.teacher.orgs;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.mobile.dto.MobileConfigDto;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.dto.OrgParentResponse;
import com.wexl.retail.organization.dto.OrganizationResponse;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.UserRepository;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class TeacherOrgsService {

  private final UserRepository userRepository;
  private final TeacherOrgsRepository teacherOrgsRepository;
  private final OrganizationRepository organizationRepository;

  public void addOrgsToTeacher(
      String parentOrgSlug, String authUserId, List<String> childOrgSlugs) {
    User teacherUser = userRepository.getUserByAuthUserId(authUserId);
    Teacher teacher = teacherUser.getTeacherInfo();
    Organization parentOrg = organizationRepository.findBySlug(parentOrgSlug);

    List<TeacherOrgs> teacherOrgs = new ArrayList<>();
    if (Objects.nonNull(childOrgSlugs)) {
      childOrgSlugs.forEach(
          childOrgSlug -> {
            Organization childOrg = organizationRepository.findBySlug(childOrgSlug);
            if (isOrgAddedToTeaacher(teacher, childOrg)) {
              teacherOrgs.add(
                  TeacherOrgs.builder().org(parentOrg).childOrg(childOrg).teacher(teacher).build());
            }
          });
    }
    addOrgsToTeacher(teacherOrgs);
  }

  public boolean isOrgAddedToTeaacher(Teacher teacher, Organization childOrg) {
    final var teacherOrgs = teacherOrgsRepository.findByTeacherAndOrg(childOrg, teacher);
    return Objects.isNull(teacherOrgs);
  }

  public void addOrgsToTeacher(List<TeacherOrgs> teacherOrgs) {
    teacherOrgsRepository.saveAll(teacherOrgs);
  }

  public List<Organization> getChildOrgs(String authUserId) {
    User teacherUser = userRepository.getUserByAuthUserId(authUserId);
    Teacher teacher = teacherUser.getTeacherInfo();
    return teacher.getChildOrgs();
  }

  public List<OrganizationResponse> getChildOrganizations(String authUserId) {
    User teacherUser = userRepository.getUserByAuthUserId(authUserId);
    Teacher teacher = teacherUser.getTeacherInfo();
    var childorgs = teacher.getChildOrgs();
    return new ArrayList<>(
        childorgs.stream()
            .map(
                org -> {
                  OrganizationResponse.OrganizationResponseBuilder builder =
                      OrganizationResponse.builder().name(org.getName()).slug(org.getSlug());

                  if (org.getParent() != null) {
                    OrgParentResponse.OrgParentResponseBuilder parentBuilder =
                        OrgParentResponse.builder()
                            .name(org.getParent().getName())
                            .slug(org.getParent().getSlug())
                            .isParent(org.getIsParent())
                            .id(org.getParent().getId())
                            .abbrevation(org.getParent().getAbbreviation())
                            .metadata(org.getParent().getMetadata())
                            .curriculum(org.getParent().getCurriculum())
                            .createdAt(org.getParent().getCreatedAt());

                    if (org.getParent().getMobileConfig() != null) {
                      parentBuilder.mobileConfigPackageResponse(
                          MobileConfigDto.PackageResponse.builder()
                              .packageName(org.getParent().getMobileConfig().getPackageName())
                              .description(
                                  org.getParent().getMobileConfig().getPackageDescription())
                              .build());

                      parentBuilder.mobileConfigKeyValueResponse(
                          MobileConfigDto.KeyValueResponse.builder()
                              .status(org.getParent().getMobileConfig().getStatus())
                              .build());
                    }

                    builder.parentDetails(parentBuilder.build());
                  }

                  return builder.build();
                })
            .toList());
  }

  public void deleteTeacherOrgs(String authUserId, String childOrgId) {

    User teacherUser = userRepository.getUserByAuthUserId(authUserId);
    Teacher teacher = teacherUser.getTeacherInfo();

    Organization childOrg;
    try {
      childOrg = organizationRepository.findBySlug(childOrgId);
    } catch (ApiException e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.ChildOrgNotFound", e);
    }
    teacherOrgsRepository.deleteTeacherOrgs(teacher.getId(), childOrg.getId());
  }
}
