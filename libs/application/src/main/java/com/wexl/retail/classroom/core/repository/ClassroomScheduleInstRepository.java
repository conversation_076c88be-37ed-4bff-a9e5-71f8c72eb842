package com.wexl.retail.classroom.core.repository;

import com.wexl.retail.classroom.core.model.ClassroomScheduleInst;
import com.wexl.retail.task.dto.StudentScheduleResult;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface ClassroomScheduleInstRepository
    extends JpaRepository<ClassroomScheduleInst, Long> {

  ClassroomScheduleInst findByIdAndOrgSlug(Long id, String orgSlug);

  Optional<ClassroomScheduleInst> findById(Long id);

  List<ClassroomScheduleInst> findByOrgSlugAndDeletedAtIsNullAndStartTimeBetween(
      String orgSlug, LocalDateTime fromDate, LocalDateTime toDate);

  @Query(
      value =
          """
             select c.id as  classroomId ,c.name as classroomName , cs.id as scheduleId,  csi.id as scheduleInstId,
             csi.title as scheduleInstName,csi.start_time  as startTime,csi.end_time as endTime , csi.status as status
             ,cs.day_of_week as dayOfWeek,
             COALESCE(mr_inst.join_link, mr_sched.join_link) AS meetingLink,
             COALESCE(mr_inst.id, mr_sched.id) AS meetingId,
             COALESCE(mr_inst.display_name, mr_sched.display_name) AS displayName,
             COALESCE(mr_inst.name, mr_sched.name) AS meetingName,
             siad.attendance_status as attendanceStatus
             from classroom_schedule_inst csi
             inner join classroom_schedules cs on cs.id =  csi.classroom_schedule_id
             inner join classrooms c on c.id = cs.classroom_id
             LEFT JOIN meeting_rooms mr_inst ON mr_inst.id = csi.meeting_room_id
             LEFT JOIN meeting_rooms mr_sched ON mr_sched.id = cs.meeting_room_id
             inner join  classroom_students std on std.classroom_id = cs.classroom_id
             left join schedule_inst_attendance sia on sia.classroom_schedule_inst_id = csi.id
             left  join schedule_inst_attendance_details siad on siad.schedule_inst_attendance_id = sia.id
             and siad.student_id = std.student_id
             where cs.org_slug = :orgSlug and std.student_id = :studentId
             and to_char(csi.start_time,'yyyy-MM-dd') = :todayDate
             order by csi.start_time ASC limit :limit""",
      nativeQuery = true)
  List<StudentScheduleResult> getStudentSchedules(
      String orgSlug, String todayDate, Long studentId, Integer limit);

  List<ClassroomScheduleInst> findByStartTimeBetween(
      LocalDateTime startTime, LocalDateTime endTime);

  @Query(
      value =
          """
          select csi.* from classroom_schedule_inst csi
          inner join classroom_schedules cs  on cs.id = csi.classroom_schedule_id
          inner join classrooms c on c.id = cs.classroom_id
          where c.parent_classroom_id in (select id from classrooms  where org_slug = :orgSlug)
          and  csi.start_time  between :fromDate and :toDate""",
      nativeQuery = true)
  List<ClassroomScheduleInst> getGroupClassroomScheduleInstsByParentOrg(
      String orgSlug, LocalDateTime fromDate, LocalDateTime toDate);

  List<ClassroomScheduleInst> findAllByTutorsIsNull();

  @Query(
      value =
          """
                  select csi.* from classroom_schedule_inst csi where to_char(csi.start_time,'yyyy-MM-dd') < :date
                    and org_slug =:orgSlug order by csi.start_time desc""",
      nativeQuery = true)
  List<ClassroomScheduleInst> getInstsByDate(String date, String orgSlug);
}
