package com.wexl.retail.dac.knowledgemeter.repository;

import com.wexl.retail.dac.knowledgemeter.dto.AvgPercentageResponse;
import com.wexl.retail.dac.knowledgemeter.dto.DacKmByGrade;
import com.wexl.retail.dac.knowledgemeter.model.TestScheduleKnowledge;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@EnableJpaRepositories
@Repository
public interface DacKmRepository extends JpaRepository<TestScheduleKnowledge, Long> {

  List<TestScheduleKnowledge> findAllByOrgSlugAndSubTopicSlugIn(
      String orgSlug, List<String> subtopicList);

  @Query(
      value =
          """
                  SELECT
                      ROUND(CAST(AVG(tskd.knowledge_percentage)AS NUMERIC),2) AS avgKnowledgePercentage
                  FROM
                      test_schedule_knowledge tsk
                  JOIN
                      test_schedule_knowledge_details tskd ON tsk.id = tskd.test_schedule_knowledge_id
                  WHERE
                       tsk.org_slug = :orgSlug
                       AND tsk.board = :board  AND ((:fDate IS NULL OR to_char(tskd.created_at,'yyyy-MM-dd') >=  :fDate) AND (:tDate IS NULL OR to_char(tskd.created_at,'yyyy-MM-dd') <=  :tDate))""",
      nativeQuery = true)
  Double getOverallOrgAvg(String orgSlug, String board, String fDate, String tDate);

  @Query(
      value =
          """
                          SELECT
                              tsk.grade_slug as gradeSlug,
                              tsk.grade_name as gradeName,
                              ROUND(CAST(AVG(tskd.knowledge_percentage)AS NUMERIC),2) AS avgKnowledgePercentage
                          FROM
                              test_schedule_knowledge tsk
                          JOIN
                              test_schedule_knowledge_details tskd ON tsk.id = tskd.test_schedule_knowledge_id
                          WHERE
                               tsk.org_slug = :orgSlug
                               AND tsk.board = :boardSlug
                               AND (:gradeSlug IS NULL OR tsk.grade_slug = :gradeSlug)
                               AND ((:fDate IS NULL OR to_char(tskd.created_at,'yyyy-MM-dd') >=  :fDate)
                               AND (:tDate IS NULL OR to_char(tskd.created_at,'yyyy-MM-dd') <=  :tDate))
                            GROUP BY
                              tsk.grade_slug,
                              tsk.grade_name""",
      nativeQuery = true)
  List<AvgPercentageResponse> getOverallOrgAvgByGrade(
      String orgSlug, String boardSlug, String gradeSlug, String fDate, String tDate);

  @Query(
      value =
          """
                                SELECT
                                    ROUND(CAST(AVG(tskd.knowledge_percentage)AS NUMERIC),2) AS avgKnowledgePercentage
                                FROM
                                    test_schedule_knowledge tsk
                                JOIN
                                    test_schedule_knowledge_details tskd ON tsk.id = tskd.test_schedule_knowledge_id
                                WHERE
                                    tsk.org_slug = :orgSlug
                                    AND tsk.student_id IN (:studentIds)
                                    AND ((:fDate IS NULL OR to_char(tskd.created_at,'yyyy-MM-dd') >=  :fDate)
                               AND (:tDate IS NULL OR to_char(tskd.created_at,'yyyy-MM-dd') <=  :tDate))
                  """,
      nativeQuery = true)
  Double getOverallOrgAndGradeByAvg(
      String orgSlug, List<Long> studentIds, String fDate, String tDate);

  @Query(
      value =
          """
                                SELECT
                                    ROUND(CAST(AVG(tskd.knowledge_percentage) AS NUMERIC), 2) AS avgKnowledgePercentage
                                FROM
                                    test_schedule_knowledge tsk
                                    JOIN
                                    test_schedule_knowledge_details tskd ON tsk.id = tskd.test_schedule_knowledge_id
                                WHERE
                                    tsk.org_slug = :orgSlug
                                    AND tsk.sub_topic_slug IN (:subtopicSlugs)
                                    AND tsk.student_id IN (:studentIds)
                                    AND ((:fDate IS NULL OR to_char(tskd.created_at,'yyyy-MM-dd') >=  :fDate)
                               AND (:tDate IS NULL OR to_char(tskd.created_at,'yyyy-MM-dd') <=  :tDate))
                  """,
      nativeQuery = true)
  Double getOverallOrgAndGradeByAvgSec(
      String orgSlug,
      List<String> subtopicSlugs,
      List<Long> studentIds,
      String fDate,
      String tDate);

  @Query(
      value =
          """
        SELECT
            ROUND(CAST(AVG(tskd.knowledge_percentage) AS NUMERIC), 2) AS avgKnowledgePercentage
        FROM
            test_schedule_knowledge tsk
        JOIN
            test_schedule_knowledge_details tskd ON tsk.id = tskd.test_schedule_knowledge_id
        WHERE
            tsk.org_slug = :orgSlug
            AND tsk.chapter_slug IN (:chapterSubtopics)
            AND ((:fDate IS NULL OR to_char(tskd.created_at,'yyyy-MM-dd') >=  :fDate)
                               AND (:tDate IS NULL OR to_char(tskd.created_at,'yyyy-MM-dd') <=  :tDate));
    """,
      nativeQuery = true)
  Double getOverallOrgAvgByChapterSlug(
      @Param("orgSlug") String orgSlug,
      @Param("chapterSubtopics") List<String> chapterSubtopics,
      @Param("fDate") String fDate,
      @Param("tDate") String tDate);

  List<TestScheduleKnowledge> findAllByOrgSlugAndStudentIdIn(String orgSlug, List<Long> studentIds);

  List<TestScheduleKnowledge> findAllByOrgSlugAndSubTopicSlugInAndStudentIdIn(
      String orgSlug, List<String> subtopicSlugs, List<Long> studentIds);

  List<TestScheduleKnowledge> findAllByOrgSlugAndChapterSlugInAndStudentIdIn(
      String orgSlug, List<String> chapterSlugs, List<Long> studentIds);

  List<TestScheduleKnowledge> findByOrgSlugAndStudentIdAndSubTopicSlug(
      String orgSlug, Long studentId, String subtopicList);

  @Query(
      value =
          """
               select tsk.subject_slug as subjectSlug ,ROUND(CAST(AVG(tskd.knowledge_percentage)AS NUMERIC),2) AS avgKnowledgePercentage
                from test_schedule_knowledge tsk
                join test_schedule_knowledge_details tskd on tsk.id = tskd.test_schedule_knowledge_id
                join students s on s.id = tsk.student_id  and s.deleted_at  is null
                where tsk.org_slug  = :orgSlug AND tsk.grade_slug  = :gradeSlug
                AND tsk.subject_slug in (:subjects) and tsk.board = :boardSlug
                AND ((:fromDate IS NULL OR to_char(tskd.created_at,'yyyy-MM-dd') >=  :fromDate)
                AND (:toDate IS NULL OR to_char(tskd.created_at,'yyyy-MM-dd') <=  :toDate))
                group by tsk.subject_slug
""",
      nativeQuery = true)
  List<DacKmByGrade> getKmByGrade(
      String orgSlug,
      String boardSlug,
      String gradeSlug,
      List<String> subjects,
      String fromDate,
      String toDate);
}
