package com.wexl.retail.courses.step.repository;

import com.wexl.retail.courses.enrollment.dto.StudentStepProgress;
import com.wexl.retail.courses.step.model.CourseItem;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CourseStepRepository extends JpaRepository<CourseItem, Long> {

  @Query(
      value = "select * from course_item where course_module_id = :moduleId order by seq_num",
      nativeQuery = true)
  List<CourseItem> getAllItemsAssociatedToCourseModule(long moduleId);

  @Query(
      value =
          """
              select ci.title as title,ci.id as id, ci.seq_num as sequenceNumber, ci.item_type as itemType,csi.student_id as studentId,
                                   ci.published_at as publishedAt, ci.course_module_id as courseModuleId,
                                   ci.course_page_id as coursePageId, COALESCE(ci.test_definition_id,0) as testDefinitionId,
                                   ci.asset_slug as assetSlug, ci.file_id as fileId, csii.status as status, csii.id as courseScheduleItemInstId,
                                   (ci.attributes->>'meta_data') as attributes
                            from course_schedule_inst csi
                             inner join course_schedule_item_inst csii on csi.id = csii.course_schedule_inst_id
                             inner join course_item ci on csii.course_item_id = ci.id
                             inner join course_module cm on ci.course_module_id = cm.id
                            where csi.course_schedule_id = :courseScheduleId and csi.student_id = :userId
                            and course_module_id = :moduleId order by ci.seq_num
                            """,
      nativeQuery = true)
  List<StudentStepProgress> getStudentEnrolledStepProgress(
      long courseScheduleId, long userId, long moduleId);

  @Query(
      value =
          """
          select (count(seq_num) + 1) as seq_num \
          from course_item where course_module_id = :courseModuleId\
          """,
      nativeQuery = true)
  int getNextSeqNumInStep(long courseModuleId);

  @Query(
      value =
          """
              select csi.id from  course_item ci join course_module cm on ci.course_module_id = cm.id
              inner join course_schedule c on cm.course_definition_id = c.course_definition_id
              inner join course_schedule_inst csi on c.id = csi.course_schedule_id
              where ci.id =:courseItemId and csi.student_id =:studentId
              """,
      nativeQuery = true)
  Long findCourseEnrollmentIdBYCourseItemIdAndStudentId(long courseItemId, long studentId);
}
