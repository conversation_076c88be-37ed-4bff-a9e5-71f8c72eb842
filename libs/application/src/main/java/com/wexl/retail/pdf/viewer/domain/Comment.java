package com.wexl.retail.pdf.viewer.domain;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Entity
@Accessors(chain = true)
@Table(name = "pdf_comments")
public class Comment implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Column(name = "id", updatable = false)
  private Long id;

  @Column(name = "annotation_id", nullable = false)
  private Long annotationId;

  @Column(name = "comment", columnDefinition = "TEXT", nullable = false)
  private String comment;

  @Column(name = "parent_id", nullable = false)
  private Long parentId;

  @Column(name = "username", nullable = false)
  private String username;

  @Column(nullable = false)
  private Timestamp createdAt;

  @Column(nullable = false)
  private Timestamp updatedAt;

  @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
  @JoinColumn(name = "comment_id")
  private List<CommentsReviewHistory> reviewStatuses;
}
