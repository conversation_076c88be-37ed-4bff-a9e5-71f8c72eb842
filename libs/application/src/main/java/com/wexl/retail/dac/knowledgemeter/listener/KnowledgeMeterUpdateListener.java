package com.wexl.retail.dac.knowledgemeter.listener;

import com.wexl.retail.dac.knowledgemeter.dto.DacKmDto;
import com.wexl.retail.dac.knowledgemeter.service.DacKmService;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.publisher.ExamCompletionEvent;
import com.wexl.retail.util.Constants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Slf4j
@RequiredArgsConstructor
@Component
public class KnowledgeMeterUpdateListener implements ApplicationListener<ExamCompletionEvent> {

  private final DacKmService dacKmService;

  @Value("${app.isDacMigration.enabled:false}")
  private boolean isDacMigrationEnabled;

  @Override
  public void onApplicationEvent(ExamCompletionEvent examCompletionEvent) {
    Object source = examCompletionEvent.getSource();
    if (source instanceof Exam exam
        && isDacMigrationEnabled
        && Constants.MOCK_TEST == exam.getExamType()) {
      dacKmService.migrateKnowledge(DacKmDto.KmRequest.builder().examId(exam.getId()).build());
    }
  }
}
