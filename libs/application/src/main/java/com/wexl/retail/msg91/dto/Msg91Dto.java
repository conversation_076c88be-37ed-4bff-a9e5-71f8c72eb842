package com.wexl.retail.msg91.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;

public class Msg91Dto {
  @Builder
  public record Request(
      String sender,
      @JsonProperty("template_id") String templateId,
      @JsonProperty("recipients") List<Recipient> recipients) {}

  @Builder
  public record Recipient(
      String mobiles,
      String name,
      String teacherName,
      String sectionName,
      String password,
      String date,
      String orgname,
      String link,
      String weblink,
      String applink) {
    public Recipient(String mobiles, Recipient recipient) {
      this(
          mobiles,
          recipient.name,
          recipient.teacherName,
          recipient.sectionName,
          recipient.password,
          recipient.date,
          recipient.orgname,
          recipient.link,
          recipient.weblink,
          recipient.applink);
    }
  }

  @Data
  public static class Response {
    private String message;
    private String type;
  }

  @Data
  public static class EmailResponse {
    private String status;
    private EmailData data;
    private EmailRequest errors;
    private boolean hasError;
    private String message;
  }

  @Data
  public static class EmailData {
    @JsonProperty("unique_id")
    private String uniqueId;
  }

  @Data
  public static class EmailRequest {
    private String domain;

    @JsonProperty("template_id")
    private String templateId;

    @JsonProperty private EmailFrom from;

    @JsonProperty("recipients")
    private List<EmailRecipient> recipients;
  }

  @Data
  @Builder
  public static class EmailRecipient {
    private List<EmailTo> to;
  }

  @Data
  @Builder
  public static class EmailTo {
    private String email;
    private String name;
  }

  @Builder
  @Data
  public static class EmailFrom {
    private String email;
  }

  public record Error(String code, String message) {}

  public record TxtLocalResponse(String status, List<Error> errors) {}

  @Builder
  public record BrevoEmailRequest(Sender sender, List<EmailTo> to, long templateId) {}

  @Builder
  public record Sender(int id, String name) {}
}
