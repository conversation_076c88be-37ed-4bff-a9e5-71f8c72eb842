package com.wexl.retail.courses.module.controller;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.courses.module.dto.CourseModuleRequest;
import com.wexl.retail.courses.module.dto.CourseModuleResponse;
import com.wexl.retail.courses.module.dto.ModuleReorderRequest;
import com.wexl.retail.courses.module.service.CourseModuleService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/courses/{courseDefId}/modules")
public class CourseModuleController {

  private final CourseModuleService courseModuleService;

  @PostMapping
  public CourseModuleResponse createCourseModule(
      @PathVariable long courseDefId, @Valid @RequestBody CourseModuleRequest courseModuleRequest) {
    try {
      return courseModuleService.createCourseModule(courseModuleRequest, courseDefId);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @PutMapping("/{moduleId}")
  public CourseModuleResponse updateCourseModuleById(
      @PathVariable long moduleId, @Valid @RequestBody CourseModuleRequest courseModuleRequest) {
    try {
      return courseModuleService.updateCourseModuleById(courseModuleRequest, moduleId);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @GetMapping("/{moduleId}")
  public CourseModuleResponse getCourseModuleById(@PathVariable long moduleId) {
    try {
      return courseModuleService.getCourseModuleById(moduleId);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @PostMapping("/reorder")
  public void reorderModules(
      @PathVariable long courseDefId, @RequestBody ModuleReorderRequest moduleReorderRequest) {
    try {
      courseModuleService.reorderModules(moduleReorderRequest, courseDefId);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }
}
