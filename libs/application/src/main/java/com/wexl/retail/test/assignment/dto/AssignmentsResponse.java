package com.wexl.retail.test.assignment.dto;

import java.time.LocalDateTime;

public interface AssignmentsResponse {
  Long getStudentId();

  Long getExamId();

  Long getTestDefinitionId();

  String getName();

  String getSubjectName();

  String getSubjectSlug();

  LocalDateTime getAssignmentDate();

  String getChapterName();

  String getChapterSlug();

  String getSubtopicName();

  String getSubtopicSlug();

  String getStatus();

  Float getMarksScored();

  Float getTotalMarks();

  String getActivityType();

  LocalDateTime getLastUpdated();
}
