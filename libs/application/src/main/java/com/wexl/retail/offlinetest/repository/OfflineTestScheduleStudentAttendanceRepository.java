package com.wexl.retail.offlinetest.repository;

import com.wexl.retail.offlinetest.model.OfflineTestDefinition;
import com.wexl.retail.offlinetest.model.OfflineTestScheduleStudentAttendance;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface OfflineTestScheduleStudentAttendanceRepository
    extends JpaRepository<OfflineTestScheduleStudentAttendance, Long> {
  Long countByOfflineTestDefinition(OfflineTestDefinition testDefinition);

  OfflineTestScheduleStudentAttendance findByOfflineTestDefinitionAndStudentId(
      OfflineTestDefinition testDefinition, long id);

  @Query(
      value =
          """
                  select otssa.* from offline_test_schedule_student_attendance otssa  join offline_test_definition otd
                        on otd.id = otssa.offline_test_definition_id
                        where otssa.student_id  =:studentId and otd.assessment_id =:assessmentId order by id desc limit 1
                  """,
      nativeQuery = true)
  Optional<OfflineTestScheduleStudentAttendance> getOfflineTestStudentAttendance(
      long studentId, Long assessmentId);

  @Query(
      value =
          """
                          select otssa.* from offline_test_schedule_student_attendance otssa  join offline_test_definition otd
                                on otd.id = otssa.offline_test_definition_id
                                where otssa.student_id  =:studentId order by id desc limit 1
                          """,
      nativeQuery = true)
  Optional<OfflineTestScheduleStudentAttendance> getOfflineTestStudentAttendanceByStudentId(
      long studentId);

  @Query(
      value =
          """
                       select  otssa.* from offline_test_schedule_student_attendance otssa join offline_test_definition otd
                               on otd.id = otssa.offline_test_definition_id
                               where otssa.student_id =:studentId and otd.assessment_id =:assessmentId order by updated_at desc limit 1
                          """,
      nativeQuery = true)
  Optional<OfflineTestScheduleStudentAttendance>
      getOfflineTestStudentAttendanceByStudentAndAssessmentId(long studentId, Long assessmentId);

  @Query(
      value =
          """
                         SELECT otssa.* FROM offline_test_schedule_student otss
                          JOIN offline_test_schedule ots ON ots.id = otss.offline_test_schedule_id
                          JOIN offline_test_definition otd ON otd.id = ots.offline_test_definition_id
                          JOIN subject_metadata sm ON sm.id = ots.subject_metadata_id
                          JOIN offline_test_schedule_student_attendance otssa ON otss.student_id = otssa.student_id
                          JOIN term_assessment_categories tac ON tac.id = otd.assessment_category_id
                          JOIN term_assessments ta ON ta.id = otd.assessment_id
                          JOIN terms t ON t.id = ta.term_id AND ta.slug = :assessmentSlug
                          WHERE otss.student_id = :studentId AND otssa.remarks <> ''
                          AND (COALESCE(:term, '') = '' OR t.slug IN (:term)) LIMIT 1;

                                  """,
      nativeQuery = true)
  Optional<OfflineTestScheduleStudentAttendance> getStudentRemarksByTermAndAssessment(
      String term, String assessmentSlug, long studentId);

  @Query(
      value =
          """
                          select otssa.* from offline_test_schedule_student_attendance otssa  join offline_test_definition otd
                                                  on otd.id = otssa.offline_test_definition_id
                                                  where otssa.student_id  =:studentId and otd.assessment_id =:assessmentId order by id asc limit 1
                          """,
      nativeQuery = true)
  Optional<OfflineTestScheduleStudentAttendance> getOfflineTestStudentAttendanceAscOrder(
      long studentId, Long assessmentId);

  @Query(
      value =
          """
                                  select otssa.* from offline_test_schedule_student_attendance otssa  join offline_test_definition otd
                                                          on otd.id = otssa.offline_test_definition_id
                                                          where otssa.student_id  =:studentId and otd.term_slug =:termSlug
                                  """,
      nativeQuery = true)
  List<OfflineTestScheduleStudentAttendance> getStudentAttendanceByStudentIdAndTermSlug(
      Long studentId, String termSlug);
}
