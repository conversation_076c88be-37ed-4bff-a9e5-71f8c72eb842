package com.wexl.retail.mlp.listener;

import static com.wexl.retail.util.Constants.PRACTICE_EXAM;

import com.wexl.retail.mlp.repository.MlpRepository;
import com.wexl.retail.mlp.service.KnowledgeMeterService;
import com.wexl.retail.mlp.service.MlpService;
import com.wexl.retail.student.adaptivelearning.service.AdaptiveLearningService;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.publisher.ExamCompletionEvent;
import com.wexl.retail.task.domain.TaskStatus;
import com.wexl.retail.task.service.TaskInstService;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Slf4j
@RequiredArgsConstructor
@Component
public class ExamCompletionEventListener implements ApplicationListener<ExamCompletionEvent> {

  private final MlpService mlpService;

  private final KnowledgeMeterService knowledgeMeterService;

  private final TaskInstService taskInstService;

  private final AdaptiveLearningService adaptiveLearningService;

  private final MlpRepository mlpRepository;

  @Value("${app.adaptiveLearning.enabled:false}")
  private boolean isAdaptiveLearningEnabled;

  @Override
  public void onApplicationEvent(ExamCompletionEvent examCompletionEvent) {
    Object source = examCompletionEvent.getSource();
    if (source instanceof Exam exam && exam.getRef() != null) {
      updateMlpInstIfExists(exam);
      updateTestStatusIfExists(exam);
      createStudentKMeter(exam);
      performAdaptiveLearning(exam);
    }
  }

  private void performAdaptiveLearning(Exam exam) {
    var mlp = mlpRepository.getFirstByExamRef(exam.getRef());
    if (isAdaptiveLearningEnabled
        && mlp.isEmpty()
        && Objects.equals(PRACTICE_EXAM, exam.getExamType())) {
      adaptiveLearningService.performAdaptiveLearning(exam.getId());
    }
  }

  void updateTestStatusIfExists(Exam exam) {
    try {
      taskInstService.updateTaskStatusIfPresent(exam, TaskStatus.COMPLETED);
    } catch (Exception ex) {
      log.error("Issue while updating test status by activity for exam", ex);
    }
  }

  void updateMlpInstIfExists(Exam exam) {
    try {
      mlpService.updateMlpInstIfExists((exam));
    } catch (Exception ex) {
      log.error("Issue while updating MlpInst for exam", ex);
    }
  }

  void createStudentKMeter(Exam exam) {
    try {
      knowledgeMeterService.createStudentKMeter(exam);
    } catch (Exception ex) {
      log.error(
          "Issue while saving knowledge meter for exam ["
              + exam.getId()
              + "].  Might be a duplicate",
          ex);
    }
  }
}
