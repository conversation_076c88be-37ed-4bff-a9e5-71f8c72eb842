package com.wexl.retail.teacher.teacherpublisher;

import com.wexl.retail.calenderevent.dto.CalenderEventVisibility;
import com.wexl.retail.calenderevent.model.CalendarEvent;
import com.wexl.retail.calenderevent.model.CalendarEventUser;
import com.wexl.retail.calenderevent.repository.CalenderEventRepository;
import com.wexl.retail.model.Teacher;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Slf4j
@AllArgsConstructor
@Component
public class TeacherCreationEventListener implements ApplicationListener<TeacherCreationEvent> {
  private final CalenderEventRepository calenderEventRepository;

  @Override
  public void onApplicationEvent(TeacherCreationEvent teacherCreationEvent) {
    Object source = teacherCreationEvent.getSource();
    if (source instanceof Teacher teacher) {
      addCalendarEvents<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(teacher);
    }
  }

  public void addCalendarEventsForNewTeacher(Teacher teacher) {
    List<String> teacherSection =
        teacher.getSections().stream().map(section -> section.getUuid().toString()).toList();
    List<CalendarEvent> calendarEventList =
        calenderEventRepository.findBySectionUuidInAndOrgSlug(
            teacherSection, teacher.getUserInfo().getOrganization());
    for (CalendarEvent calendarEvent : calendarEventList) {
      if (calendarEvent.getVisibility() == CalenderEventVisibility.ALL) {
        List<CalendarEventUser> eventUsers = new ArrayList<>();
        eventUsers.add(buildCalenderEvent(teacher, calendarEvent));
        calendarEvent.setCalendarEventUsers(eventUsers);
        calenderEventRepository.save(calendarEvent);
      }
    }
  }

  public CalendarEventUser buildCalenderEvent(Teacher teacher, CalendarEvent calendarEvent) {
    return CalendarEventUser.builder()
        .userId(teacher.getUserInfo().getId())
        .isTeacher(true)
        .calendarEvent(calendarEvent)
        .build();
  }
}
