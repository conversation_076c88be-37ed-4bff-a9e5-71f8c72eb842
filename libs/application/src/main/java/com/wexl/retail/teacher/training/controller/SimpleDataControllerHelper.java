package com.wexl.retail.teacher.training.controller;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

@Service
public class SimpleDataControllerHelper {

  @Value("${app.localeFilesLocation}")
  private Resource localeFilesLocation;

  public String getStringResponseEntity(String fileName) throws IOException {
    var optionalFile = getFileInLocaleFolder(fileName);
    if (optionalFile.isPresent()) {
      return FileUtils.readFileToString(optionalFile.get(), StandardCharsets.UTF_8);
    }
    throw new ApiException(
        InternalErrorCodes.INVALID_REQUEST, "error.invalidInput", new String[] {fileName});
  }

  public Optional<File> getFileInLocaleFolder(String fileName) throws IOException {
    final File file = localeFilesLocation.getFile();
    return Arrays.stream(Objects.requireNonNull(file.listFiles()))
        .filter(f -> f.getName().contains(fileName))
        .findFirst();
  }

  public Optional<File> getFilesFromLocaleFolder(String folder, String fileName)
      throws IOException {
    final File file = new File(localeFilesLocation.getFile(), folder);
    return Arrays.stream(Objects.requireNonNull(file.listFiles()))
        .filter(f -> f.getName().contains(fileName))
        .findFirst();
  }
}
