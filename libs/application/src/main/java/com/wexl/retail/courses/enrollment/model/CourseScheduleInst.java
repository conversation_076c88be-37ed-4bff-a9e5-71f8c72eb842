package com.wexl.retail.courses.enrollment.model;

import com.wexl.retail.model.Model;
import com.wexl.retail.model.User;
import jakarta.persistence.*;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Entity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(
    name = "course_schedule_inst",
    uniqueConstraints = {@UniqueConstraint(columnNames = {"course_schedule_id", "student_id"})})
public class CourseScheduleInst extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  @ManyToOne private CourseSchedule courseSchedule;

  @ManyToOne
  @JoinColumn(name = "student_id")
  private User studentId;

  @Column(name = "org_slug")
  private String orgSlug;

  @Enumerated(EnumType.STRING)
  private CourseScheduleInstStatus status;

  @Column(name = "course_schedule_inst_date")
  private Timestamp courseScheduleInstDate;
}
