package com.wexl.retail.student.exam;

import static com.wexl.retail.util.Constants.AUTHORIZATION_HEADER;

import com.wexl.retail.ai.dto.ExamAnalysis;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.commons.security.annotation.IsTeacherOrStudent;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.SubTopicResponse;
import com.wexl.retail.student.answer.ExamDetails;
import com.wexl.retail.student.answer.StudentAnswerResponse;
import com.wexl.retail.student.exam.dto.ExamDto;
import com.wexl.retail.student.exam.publisher.ExamCompletionEventPublisher;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class ExamController {

  private final ExamService examService;
  private final AuthService authService;
  private final ContentService contentService;
  private final ExamCompletionEventPublisher examCompletionEventPublisher;

  @IsTeacherOrStudent
  @GetMapping("/orgs/{orgSlug}/students/{authUserId}/exams")
  public List<ExamDto> findExamsByStudent(
      @PathVariable String orgSlug, @PathVariable String authUserId) {
    return examService.findExamsByStudent(orgSlug, authUserId);
  }

  @IsStudent
  @GetMapping("/student/exam/{id}")
  public Exam findById(@PathVariable("id") long examId) {

    return examService.findById(examId);
  }

  @IsStudent
  @PostMapping("/student/exam/test")
  public ExamResponse createExamTest(@RequestBody ExamRequest examRequest) {
    if (authService.isUserDisabled()) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, "error.Test.Allowed");
    }

    if (examRequest.getUuid() == null) {
      examRequest.setUuid(UUID.randomUUID().toString());
    }

    return examService.createExamTest(examRequest);
  }

  @IsStudent
  @PostMapping("/student/exam/practice")
  public ExamResponse createExamPractice(
      @RequestBody ExamRequest examRequest,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {
    if (examRequest.getUuid() == null) {
      examRequest.setUuid(UUID.randomUUID().toString());
    }

    SubTopicResponse subtopic =
        contentService.getSubTopicById(
            authService.getUserDetails().getOrganization(),
            examRequest.getSubTopicId(),
            bearerToken);

    return examService.createExamPractice(
        authService.getStudentDetails().getStudentInfo(), examRequest, subtopic);
  }

  @IsStudent
  @PutMapping("/student/exam/{exam_id}")
  public ExamDetails completePracticeExam(@PathVariable("exam_id") long examId) {

    return examService.completeExam(examId);
  }

  @IsStudent
  @GetMapping("/student/exam/result/{id}")
  public StudentAnswerResponse examResult(
      @PathVariable("id") long examId, @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {

    return examService.examResultByExamId(examId, bearerToken);
  }

  @GetMapping("/student/{studentId}/screenTime")
  public List<UserUsageDetail> getStudentScreenTime(
      @PathVariable("studentId") String studentAuthId,
      @RequestParam("from_date") long fromDate,
      @RequestParam("to_date") long toDate) {

    return examService.getStudentScreenTime(studentAuthId, fromDate, toDate);
  }

  @IsStudent
  @PostMapping("/student/exam/revision")
  public ExamResponse createRevisionExam(@RequestBody ExamRequest examRequest) {

    if (examRequest.getUuid() == null) {
      examRequest.setUuid(UUID.randomUUID().toString());
    }
    return examService.createRevisionExam(examRequest);
  }

  @IsOrgAdminOrTeacher
  @PostMapping("/orgs/{orgSlug}/exam-answers/{examAnswerId}/analyze")
  public ExamAnalysis.AnswerReAnalysis answerRefresh(
      @RequestBody ExamAnalysis.AnswerReAnalysis request,
      @PathVariable String orgSlug,
      @PathVariable Long examAnswerId) {
    return examService.updateExamAnswers(request, orgSlug, examAnswerId);
  }
}
