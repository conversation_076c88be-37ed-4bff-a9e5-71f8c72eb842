package com.wexl.retail.test.competitive.validators;

import com.wexl.retail.test.schedule.domain.TestScheduleStudentAnswer;
import com.wexl.retail.test.school.domain.TestCategory;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.domain.TestDefinitionSection;
import java.util.List;

public interface TestDefinitionValidator {
  void validate(TestDefinition testDefinition);

  boolean supports(TestCategory name);

  List<TestScheduleStudentAnswer> processOptionalQuestions(
      List<TestScheduleStudentAnswer> tssa, List<TestDefinitionSection> sections);
}
