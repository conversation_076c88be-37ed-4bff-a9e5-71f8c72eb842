package com.wexl.retail.util;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import java.io.*;
import java.io.FileInputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import org.apache.commons.io.FilenameUtils;

public class ScormFileUtils {

  private ScormFileUtils() {}

  public static void unZipScormFile(String file, String destinationPath) {
    FileInputStream fis;
    FileOutputStream fos;
    try {
      fis = new FileInputStream(file);
      ZipInputStream zis = new ZipInputStream(fis);
      ZipEntry ze = zis.getNextEntry();
      byte[] buffer = new byte[1024];
      while (ze != null) {
        String fileName = ze.getName();
        File newFile = new File(destinationPath + File.separator + fileName);
        // create directories for sub directories in zip
        String canonicalDestinationPath = newFile.getParent();

        if (!canonicalDestinationPath.startsWith(destinationPath)) {
          throw new IOException("error.TargetDirectory");
        }
        new File(newFile.getParent()).mkdirs();
        fos = new FileOutputStream(newFile);
        int len;
        while ((len = zis.read(buffer)) > 0) {
          fos.write(buffer, 0, len);
        }
        fos.close();
        // close this ZipEntry
        zis.closeEntry();
        ze = zis.getNextEntry();
      }
      // close last ZipEntry
      zis.closeEntry();
      zis.close();
      fis.close();
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidFile", e);
    }
  }

  public static String createUnzipPath(String filepath) {
    String fileUnZipPath = "";
    Path path = Paths.get(filepath);
    String fileName = FilenameUtils.getBaseName(path.getFileName().toString());
    fileUnZipPath = String.valueOf(new File("/tmp/" + fileName));
    try {
      Files.createDirectories(Path.of(fileUnZipPath));
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.UnZipFolder");
    }
    return fileUnZipPath;
  }
}
