package com.wexl.retail.auth;

import com.wexl.retail.model.User;

public class AuthUtil {

  private AuthUtil() {}

  public static boolean isTeacher(User user) {
    return UserRoleHelper.get().isTeacher(user);
  }

  public static boolean isStudent(User user) {
    return UserRoleHelper.get().isStudent(user);
  }

  public static boolean isAdminStudent(User user) {
    return UserRoleHelper.get().isStudentAdmin(user);
  }

  public static boolean isOrgAdmin(User user) {
    return UserRoleHelper.get().isOrgAdmin(user);
  }
}
