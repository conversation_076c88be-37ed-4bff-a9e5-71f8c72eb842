package com.wexl.retail.test.competitive.validators;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.test.schedule.domain.TestScheduleStudentAnswer;
import com.wexl.retail.test.school.domain.TestCategory;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.domain.TestDefinitionSection;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class StandardTestDefinitionValidator implements TestDefinitionValidator {

  @Override
  public void validate(TestDefinition testDefinition) {
    if (testDefinition.getTestDefinitionSections().isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Invalid.Sections.Standard");
    }
  }

  @Override
  public boolean supports(TestCategory name) {
    return TestCategory.STANDARD.equals(name) || TestCategory.CUSTOM.equals(name);
  }

  @Override
  public List<TestScheduleStudentAnswer> processOptionalQuestions(
      List<TestScheduleStudentAnswer> tssa, List<TestDefinitionSection> sections) {
    return tssa;
  }
}
