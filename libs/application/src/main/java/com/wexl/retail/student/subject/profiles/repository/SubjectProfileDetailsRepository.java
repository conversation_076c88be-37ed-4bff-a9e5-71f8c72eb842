package com.wexl.retail.student.subject.profiles.repository;

import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.student.subject.profiles.domain.SubjectProfileDetails;
import com.wexl.retail.student.subject.profiles.domain.SubjectProfiles;
import jakarta.transaction.Transactional;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface SubjectProfileDetailsRepository
    extends JpaRepository<SubjectProfileDetails, Long> {

  List<SubjectProfileDetails> findAllBySubjectProfilesAndDeletedAtOrderByCreatedAtDesc(
      SubjectProfiles subjectProfiles, @Nullable Date date);

  Optional<SubjectProfileDetails>
      findFirstByBoardNameAndAndGradeNameAndSubjectNameAndSubjectProfilesAndSourceOrgAndDeletedAt(
          String boardName,
          String gradeName,
          String subjectName,
          SubjectProfiles subjectProfiles,
          Organization sourceOrg,
          @Nullable Date date);

  Optional<SubjectProfileDetails> findByIdAndDeletedAt(Long id, @Nullable Date date);

  @Query(
      value =
          """
                      select spd.* from student_subject_profiles  ssp
                        inner join subject_profiles_details spd on ssp.subject_profile_id = spd.subject_profile_id
                      where ssp.student_id = :studentId and spd.deleted_at is null
                  order by CASE WHEN spd.subject_slug = 'mathematics' THEN 1
                       WHEN spd.subject_slug = 'physics' THEN 2
                       WHEN spd.subject_slug = 'chemistry' THEN 3
                       WHEN spd.subject_slug = 'biology' THEN 4 end asc""",
      nativeQuery = true)
  List<SubjectProfileDetails> getSubjectProfileDetailsForStudent(Long studentId);

  @Transactional
  @Modifying
  @Query(value = "delete from SubjectProfileDetails s where s.id = ?1")
  void deleteSubjectProfileDetails(Long id);

  List<SubjectProfileDetails> findAllByOrgAndBoardSlugAndGradeSlug(
      Organization org, String boardSlug, String gradeSlug);

  @Query(
      value =
          """

                      SELECT spd.*
                      FROM public.student_subject_profiles ssp
                      inner join subject_profiles_details spd on ssp.subject_profile_id = spd.subject_profile_id
                      WHERE ssp.student_id = :studentId and ssp.deleted_at is null""",
      nativeQuery = true)
  List<SubjectProfileDetails> getSubjectProfileDetailsOfStudent(Long studentId);
}
