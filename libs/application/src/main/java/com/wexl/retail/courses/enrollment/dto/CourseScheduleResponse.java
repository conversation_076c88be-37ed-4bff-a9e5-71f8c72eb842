package com.wexl.retail.courses.enrollment.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_EMPTY)
public class CourseScheduleResponse {

  private Long id;

  @JsonProperty("published_at")
  private Long publishedAt;

  private String name;

  @JsonProperty("course_definition_id")
  private Long courseDefinitionId;

  private Integer enrollments;

  private Integer version;

  private String status;

  @JsonProperty("org_slug")
  private String orgSlug;

  @JsonProperty("start_date")
  private Long startDate;

  @JsonProperty("end_date")
  private Long endDate;

  @JsonProperty("category_id")
  private Long categoryId;

  @JsonProperty("category_name")
  private String categoryName;

  @JsonProperty("thumbnail")
  private String thumbNail;
}
