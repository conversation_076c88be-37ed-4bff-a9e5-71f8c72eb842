package com.wexl.retail.courses.enrollment.dto;

import java.sql.Timestamp;

public interface StudentStepProgress {

  String getTitle();

  Long getId();

  Integer getSequenceNumber();

  String getItemType();

  Timestamp getPublishedAt();

  Long getCourseModuleId();

  Long getCoursePageId();

  Long getTestDefinitionId();

  String getAssetSlug();

  Long getFileId();

  String getStatus();

  Long getCourseScheduleItemInstId();

  String getAttributes();

  Long getStudentId();
}
