package com.wexl.retail.metrics.reportcards.service;

import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.reportcards.dto.SubjectWiseResponse;
import com.wexl.retail.offlinetest.repository.OfflineTestDefinitionRepository;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleRepository;
import com.wexl.retail.section.repository.SectionRepository;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class SubjectWiseReportService {
  private final OfflineTestDefinitionRepository offlineTestDefinitionRepository;
  private final OfflineTestScheduleRepository offlineTestScheduleRepository;
  private final SectionRepository sectionRepository;

  public List<GenericMetricResponse> getSubjectDetail(
      String org, String board, String grade, String section, Integer scheduleId) {

    List<SubjectWiseResponse> responses =
        offlineTestScheduleRepository.getSubjectsByOrgAndBoardAndGradeAndSectionAndSchedule(
            org, board, grade, section, scheduleId);
    List<GenericMetricResponse> metricResponses = new ArrayList<>();

    for (SubjectWiseResponse response : responses) {

      GenericMetricResponse metricResponse = new GenericMetricResponse();
      var sec =
          sectionRepository.findByUuid(UUID.fromString(response.getSectionName())).orElseThrow();

      Map<String, Object> data = new HashMap<>();
      data.put("title", response.getTitle());
      data.put("id", response.getId());
      data.put("grade_name", response.getGradeName());
      data.put("section_name", sec.getName());
      data.put("subject_name", response.getSubjectName());
      data.put("total_count", response.getTotalCount());
      data.put("entered_marks_count", response.getMarksEnteredCount());
      data.put(
          "not_entered_count", response.getMarksNotEnteredCount() - response.getAbsenteesCount());
      data.put("absentees_count", response.getAbsenteesCount());

      metricResponse.setData(data);
      metricResponses.add(metricResponse);
    }

    return metricResponses;
  }
}
