package com.wexl.retail.qpgen.controller;

import static com.wexl.retail.util.Constants.AUTHORIZATION_HEADER;

import com.wexl.retail.qpgen.dto.QpGenProDto;
import com.wexl.retail.qpgen.service.QpGenProService;
import com.wexl.retail.test.school.dto.QuestionDto;
import com.wexl.retail.test.school.dto.TestDefinitionResponse;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}/qp-gen-pro")
@RequiredArgsConstructor
public class QPGenProController {

  private final QpGenProService qpGenProService;

  @ResponseStatus(HttpStatus.CREATED)
  @PostMapping()
  public QpGenProDto.Return saveQpGenPro(
      @PathVariable String orgSlug, @RequestBody QpGenProDto.Request request) {
    return qpGenProService.saveQpGenPro(orgSlug, request);
  }

  @GetMapping()
  public List<QpGenProDto.Response> getAllQpGenPros(@PathVariable String orgSlug) {
    return qpGenProService.getAllQpGenPros(orgSlug);
  }

  @GetMapping("/{qpGenProId}")
  public QpGenProDto.Response getQpGenProById(@PathVariable Long qpGenProId) {
    return qpGenProService.getQpGenProById(qpGenProId);
  }

  @PostMapping("/{qpGenProId}/change-marks")
  public void updateMarks(
      @PathVariable Long qpGenProId, @RequestBody QpGenProDto.ChangeMarks request) {
    qpGenProService.updateMarks(qpGenProId, request);
  }

  @PostMapping("/{qpGenProId}/replaceQuestion")
  public void replaceQuestion(
      @PathVariable Long qpGenProId,
      @PathVariable String orgSlug,
      @RequestBody QpGenProDto.ReplaceQuestion request) {
    qpGenProService.replaceQuestion(qpGenProId, request, orgSlug);
  }

  @PostMapping("/{qpGenProId}/review")
  public void reviewQpGenPro(
      @PathVariable Long qpGenProId, @RequestParam("teacher_auth_id") String authUserId) {
    qpGenProService.reviewQpGenPro(qpGenProId, authUserId);
  }

  @PostMapping("/{qpGenProId}/publish")
  public void publishQpGenPro(
      @PathVariable Long qpGenProId, @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {
    qpGenProService.publishQpGenPro(qpGenProId, bearerToken, true);
  }

  @PostMapping("/{qpGenProId}/unPublish")
  public void unPublishQpGenPro(
      @PathVariable Long qpGenProId, @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {
    qpGenProService.publishQpGenPro(qpGenProId, bearerToken, false);
  }

  @PostMapping("/{qpGenProId}/internal-choice")
  public void internalChoice(
      @PathVariable Long qpGenProId, @RequestBody QpGenProDto.InternalChoice request) {
    qpGenProService.internalChoice(qpGenProId, request);
  }

  @PostMapping("/{qpGenProId}/internal-choice:updateMarks")
  public void updateInternalChoiceMarks(
      @PathVariable Long qpGenProId, @RequestBody QpGenProDto.InternalChoiceChangeMarks request) {
    qpGenProService.updateInternalChoiceMarks(qpGenProId, request);
  }

  @GetMapping(
      "/{qpGenProId}/test-definitions/{testDefinitionId}/test-definition-sections/{testDefinitionSectionId}")
  public TestDefinitionResponse getQuestionsByTestDefinitionSectionId(
      @PathVariable long testDefinitionSectionId,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken,
      @PathVariable String orgSlug) {
    return qpGenProService.getQuestionsByTestDefinitionSectionId(
        testDefinitionSectionId, bearerToken, orgSlug);
  }

  @DeleteMapping("/test-definition-sections/{testDefinitionSectionId}/question/{questionUuid}")
  public void deleteSectionQuestion(
      @PathVariable long testDefinitionSectionId, @PathVariable String questionUuid) {
    qpGenProService.deleteSectionQuestion(testDefinitionSectionId, questionUuid);
  }

  @GetMapping("/test-definitions/{testDefinitionId}/full")
  public QuestionDto.QuestionResponse getQpGenQuestions(
      @PathVariable Long testDefinitionId,
      @PathVariable String orgSlug,
      @RequestParam(name = "set", required = false, defaultValue = "1") Integer questionSetNo) {
    return qpGenProService.getQpGenProQuestions(testDefinitionId, questionSetNo, orgSlug);
  }

  @PostMapping("/questions-summary")
  public QuestionDto.SectionQpGenResponse getSectionQuestionsDetails(
      @PathVariable String orgSlug, @RequestBody QuestionDto.SectionQpGenRequest chapterRequest) {
    return qpGenProService.getSectionQuestions(chapterRequest, orgSlug);
  }

  @PutMapping("/{qpGenProId}/test-definition-sections/{testDefinitionSectionId}/questions:update")
  public void updateSectionQuestions(
      @PathVariable String orgSlug,
      @PathVariable Long qpGenProId,
      @PathVariable Long testDefinitionSectionId,
      @RequestBody QpGenProDto.UpdateSectionQuestions updateRequest) {

    qpGenProService.updateSectionQuestions(
        orgSlug, qpGenProId, testDefinitionSectionId, updateRequest);
  }
}
