package com.wexl.retail.metrics.handler;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AllStudentsKMSummaryByGrade extends AbstractMetricHandler implements MetricHandler {
  @Override
  protected List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    List<String> boardList =
        Optional.ofNullable(genericMetricRequest.getInput().get(BOARD))
            .map(List.class::cast)
            .orElse(Collections.emptyList());
    List<String> gradeList =
        Optional.ofNullable(genericMetricRequest.getInput().get(GRADE))
            .map(List.class::cast)
            .orElse(Collections.emptyList());
    List<String> subjectList =
        Optional.ofNullable(genericMetricRequest.getInput().get(SUBJECT))
            .map(List.class::cast)
            .orElse(Collections.emptyList());
    return kMeterService.getAllStudentsKMSummaryByGrade(
        org, subjectList, boardList.getFirst(), gradeList.getFirst());
  }

  @Override
  public String name() {
    return "allstudents-km-summary-by-grade";
  }
}
