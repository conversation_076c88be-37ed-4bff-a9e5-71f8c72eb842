package com.wexl.retail.marks.controller;

import com.wexl.retail.teacher.training.controller.SimpleDataControllerHelper;
import java.io.IOException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/marks")
public class MarksController {
  private final SimpleDataControllerHelper simpleDataControllerHelper;

  @GetMapping
  public ResponseEntity<String> getMarksEntryResources() throws IOException {
    return ResponseEntity.ok()
        .body(simpleDataControllerHelper.getStringResponseEntity("marks-entry.json"));
  }
}
