package com.wexl.retail.monitoring.interceptor;

import com.wexl.retail.monitoring.service.EndpointService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

@Component
@RequiredArgsConstructor
public class EndpointInterceptor implements HandlerInterceptor {

  private final EndpointService endpointService;

  @Override
  public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
      throws Exception {
    //    String httpMethod = request.getMethod();
    //    String requestUri = request.getRequestURI();
    //    endpointService.saveEndpoint(httpMethod, requestUri);
    return HandlerInterceptor.super.preHandle(request, response, handler);
  }
}
