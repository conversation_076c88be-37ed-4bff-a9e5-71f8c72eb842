package com.wexl.retail.offlinetest.repository;

import com.wexl.retail.offlinetest.model.OfflineTestDefinition;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.model.TermAssessmentCategory;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface OfflineTestDefinitionRepository
    extends JpaRepository<OfflineTestDefinition, Long> {

  @Query(
      value =
          """
                        select distinct(otd.*) from offline_test_definition otd
                        join offline_test_schedule ots on otd.id = ots.offline_test_definition_id
                          join sections s on  CAST(s."uuid" AS VARCHAR) = otd.section_uuid
                          where otd.teacher_id in (:teacherId)
                          and (cast((:boardSlug) as varChar) is null or otd.board_slug in (:boardSlug))
                          and (cast((:gradeSlug) as varChar) is null or otd.grade_slug in (:gradeSlug))
                          and (cast((:sectionUuid) as varChar) is null or otd.section_uuid in (:sectionUuid))
                          and s.deleted_at is null
                          order by created_at desc""",
      nativeQuery = true)
  List<OfflineTestDefinition> getOfflineTestScheduleByTeacherId(
      List<Long> teacherId,
      List<String> boardSlug,
      List<String> gradeSlug,
      List<String> sectionUuid);

  @Query(
      value =
          """
          select distinct(otd.*) from offline_test_definition otd join offline_test_schedule ots on otd.id = ots.offline_test_definition_id
          where  (cast((:gradeSlug) as varChar) is null or otd.grade_slug in (:gradeSlug))
                          and (cast((:sectionUuid) as varChar) is null or otd.section_uuid in (:sectionUuid))
                          and (cast(:examType as bigint) is null or otd.id in (cast(:examType as bigint)))
                          and otd.org_slug = :orgSlug
          """,
      nativeQuery = true)
  List<OfflineTestDefinition> getOfflineTestScheduleByOrgSlug(
      String orgSlug, List<String> gradeSlug, List<String> sectionUuid, List<String> examType);

  @Query(
      value =
          """
                          select distinct(otd.*) from offline_test_definition otd join offline_test_schedule ots on otd.id = ots.offline_test_definition_id
                          where  (cast((:gradeSlug) as varChar) is null or otd.grade_slug in (:gradeSlug))
                                          and (cast((:sectionUuid) as varChar) is null or otd.section_uuid in (:sectionUuid))
                                          and otd.org_slug = :orgSlug and otd.title is not null
                                          and otd.academic_year_slug in (:yearSlug)
                                          order by otd.id desc
                          """,
      nativeQuery = true)
  List<OfflineTestDefinition> getOfflineTestScheduleForReportCard(
      String orgSlug, List<String> gradeSlug, List<String> sectionUuid, List<String> yearSlug);

  @Query(
      value =
          """
                                  select distinct(otd.*) from offline_test_definition otd join offline_test_schedule ots on
                                   otd.id = ots.offline_test_definition_id
                                  where  (cast((:gradeSlug) as varChar) is null or otd.grade_slug in (:gradeSlug))
                                  and (cast((:examType) as varChar) is null or otd.id in (:examType))
                                  and (cast((:sectionUuid) as varChar) is null or otd.section_uuid in (:sectionUuid))
                                  and (cast((:subjectSlug) as varChar) is null or ots.subject_slug in (:subjectSlug))
                                  and otd.org_slug = :orgSlug and otd.title is not null and ots.published_at is not null order by id desc
                                  """,
      nativeQuery = true)
  List<OfflineTestDefinition> getExamTypes(
      String orgSlug,
      List<String> gradeSlug,
      List<String> sectionUuid,
      List<String> subjectSlug,
      List<String> examType);

  @Query(
      value =
          """
                          select distinct(otd.*)  from offline_test_schedule_student otss
                          join offline_test_schedule ots on otss.offline_test_schedule_id  = ots.id
                          join offline_test_definition otd on ots.offline_test_definition_id  = otd.id
                          where student_id = :studentId and otd.org_slug = :orgSlug and otd.section_uuid =:section
                           and otd.title is not null and ots.published_at is not null
                                          """,
      nativeQuery = true)
  List<OfflineTestDefinition> getStudentExamTypes(String orgSlug, Long studentId, String section);

  @Query(
      value =
          """
                                          select distinct(otd.*)  from offline_test_schedule_student otss
                                          join offline_test_schedule ots on otss.offline_test_schedule_id  = ots.id
                                          join offline_test_definition otd on ots.offline_test_definition_id  = otd.id
                                          where student_id = :studentId and otd.org_slug = :orgSlug and otd.academic_year_slug =:academicYear
                                           and otd.title is not null and ots.published_at is not null
                                                          """,
      nativeQuery = true)
  List<OfflineTestDefinition> getStudentExamTypesByPrevStudentId(
      String orgSlug, Long studentId, String academicYear);

  @Query(
      value =
          """
                                  select distinct(otd.*)  from offline_test_schedule_student otss
                                  join offline_test_schedule ots on otss.offline_test_schedule_id  = ots.id
                                  join offline_test_definition otd on ots.offline_test_definition_id  = otd.id
                                  where student_id = :studentId and otd.org_slug = :orgSlug and otd.section_uuid =:section
                                   and otd.title is not null and (cast((:showAdmitCard) as varChar) is null or otd.show_admit_card in (:showAdmitCard))
                                                  """,
      nativeQuery = true)
  List<OfflineTestDefinition> getStudentAdmitCardExamTypes(
      String orgSlug, Long studentId, String section, Boolean showAdmitCard);

  List<OfflineTestDefinition> findAllByBoardSlugAndGradeSlugAndAcademicYearSlugAndOrgSlug(
      String boardSlug, String gradeSlug, String academicYearSlug, String orgSlug);

  List<OfflineTestDefinition> findBySectionUuidAndAssessmentSlugAndOrgSlug(
      String sectionUuid, String termSlug, String orgSlug);

  List<OfflineTestDefinition> findBySectionUuidInAndAssessmentSlug(
      List<String> sectionUuid, String assessmentSlug);

  List<OfflineTestDefinition> getAllByOrgSlug(String orgSlug);

  List<OfflineTestDefinition>
      findAllByOrgSlugAndBoardSlugAndGradeSlugAndAssessmentAndTitleAndGradeScaleSlugNotNull(
          String orgSlug,
          String boardSlug,
          String gradeSlug,
          TermAssessment assessment,
          String title);

  List<OfflineTestDefinition> findBySectionUuidAndOrgSlug(String uuid, String organization);

  List<OfflineTestDefinition> findBySectionUuidAndOrgSlugAndTermSlug(
      String uuid, String organization, String termSlug);

  List<OfflineTestDefinition> findAllByOrgSlugAndDeletedAtIsNull(String orgSlug);

  List<OfflineTestDefinition> findByOrgSlugAndSectionUuidAndAssessmentCategoryIn(
      String orgSlug, String sectionUuid, List<TermAssessmentCategory> assessmentCategory);

  Optional<OfflineTestDefinition> findByIdAndOrgSlug(Long testDefinitionId, String orgSlug);

  Optional<OfflineTestDefinition> findAllByIdAndOrgSlug(Long aLong, String orgSlug);

  Optional<OfflineTestDefinition> findByAssessmentIdAndGradeSlugAndOrgSlugAndBoardSlug(
      Long assessmentId, String gradeSlug, String orgSlug, String boardSlug);

  List<OfflineTestDefinition> findAllByTeacherId(Long teacherId);
}
