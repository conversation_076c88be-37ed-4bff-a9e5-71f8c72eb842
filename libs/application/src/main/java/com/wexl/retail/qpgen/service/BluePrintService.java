package com.wexl.retail.qpgen.service;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.curriculum.service.OrgSettingsService;
import com.wexl.retail.qpgen.dto.BluePrintDto;
import com.wexl.retail.qpgen.model.BluePrint;
import com.wexl.retail.qpgen.model.BluePrintSections;
import com.wexl.retail.qpgen.repository.BluePrintRepository;
import com.wexl.retail.qpgen.repository.BluePrintSectionRepository;
import com.wexl.retail.test.school.dto.QuestionDto;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class BluePrintService {

  private final BluePrintRepository bluePrintRepository;
  private final BluePrintSectionRepository bluePrintSectionRepository;
  private final OrgSettingsService orgSettingsService;
  private final ContentService contentService;

  public void saveBluePrint(String orgSlug, BluePrintDto.Request request) {
    BluePrint bluePrint = new BluePrint();
    bluePrint.setTitle(request.title());
    bluePrint.setTotalQuestions(request.totalQuestions());
    bluePrint.setTotalMarks(request.totalMarks());
    bluePrint.setOrgSlug(orgSlug);
    bluePrint.setBluePrintSections(buildBluePrintSections(bluePrint, request.sections()));
    bluePrintRepository.save(bluePrint);
  }

  private List<BluePrintSections> buildBluePrintSections(
      BluePrint bluePrint, List<BluePrintDto.Section> sections) {
    List<BluePrintSections> sectionList = new ArrayList<>();
    sections.forEach(
        section ->
            sectionList.add(
                BluePrintSections.builder()
                    .sectionName(section.sectionName())
                    .complexity(section.complexity())
                    .type(section.questionType())
                    .questionCount(section.questionCount())
                    .marks(section.marks())
                    .tags(section.tags())
                    .bluePrint(bluePrint)
                    .build()));
    return sectionList;
  }

  public List<BluePrintDto.Response> getAllBluePrintsByOrg(String orgSlug) {
    orgSettingsService.validateOrganizaiton(orgSlug);
    List<BluePrint> bluePrintList =
        bluePrintRepository.findAllByOrgSlugAndDeletedAtIsNullOrderByCreatedAtDesc(orgSlug);
    List<BluePrintDto.Response> responseList = new ArrayList<>();

    bluePrintList.forEach(
        bluePrint ->
            responseList.add(
                BluePrintDto.Response.builder()
                    .id(bluePrint.getId())
                    .totalQuestions(
                        Long.valueOf(
                            bluePrint.getBluePrintSections().stream()
                                .mapToLong(BluePrintSections::getQuestionCount)
                                .sum()))
                    .totalMarks(bluePrint.getTotalMarks())
                    .title(bluePrint.getTitle())
                    .createdAt(convertIso8601ToEpoch(bluePrint.getCreatedAt().toLocalDateTime()))
                    .build()));
    return responseList;
  }

  public BluePrintDto.Response getBluePrintById(String orgSlug, Long bluePrintId) {
    orgSettingsService.validateOrganizaiton(orgSlug);
    var bluePrintData = validateBluePrint(orgSlug, bluePrintId);
    return buildBluePrintResponse(bluePrintData);
  }

  private BluePrintDto.Response buildBluePrintResponse(BluePrint bluePrintData) {
    return BluePrintDto.Response.builder()
        .title(bluePrintData.getTitle())
        .id(bluePrintData.getId())
        .totalQuestions(bluePrintData.getTotalQuestions())
        .totalMarks(bluePrintData.getTotalMarks())
        .createdAt(convertIso8601ToEpoch(bluePrintData.getCreatedAt().toLocalDateTime()))
        .sections(buildBluePrintSectionsResponse(bluePrintData.getBluePrintSections()))
        .isIdUsedByQpGenPro(bluePrintData.getQpGenPros().isEmpty() ? Boolean.FALSE : Boolean.TRUE)
        .build();
  }

  public List<BluePrintDto.Section> buildBluePrintSectionsResponse(
      List<BluePrintSections> bluePrintSections) {
    List<BluePrintDto.Section> sectionList = new ArrayList<>();
    bluePrintSections.forEach(
        section ->
            sectionList.add(
                BluePrintDto.Section.builder()
                    .sectionId(section.getId())
                    .questionCount(section.getQuestionCount())
                    .questionType(section.getType())
                    .complexity(section.getComplexity())
                    .sectionName(section.getSectionName())
                    .marks(section.getMarks())
                    .tags(section.getTags())
                    .build()));
    sectionList.sort(Comparator.comparing(BluePrintDto.Section::sectionName));
    return sectionList;
  }

  public BluePrint validateBluePrint(String orgSlug, Long bluePrintId) {
    var bluePrint = bluePrintRepository.findByIdAndOrgSlug(bluePrintId, orgSlug);
    if (Objects.isNull(bluePrint)) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.BluePrint.Id",
          new String[] {bluePrintId.toString()});
    }
    return bluePrint;
  }

  public void saveBluePrintSection(String orgSlug, BluePrintDto.Section request, Long bluePrintId) {
    orgSettingsService.validateOrganizaiton(orgSlug);
    BluePrint bluePrint = validateBluePrint(orgSlug, bluePrintId);
    BluePrintSections section = buildBluePrintSection(request, bluePrint);
    bluePrintSectionRepository.save(section);
  }

  private BluePrintSections buildBluePrintSection(
      BluePrintDto.Section request, BluePrint bluePrint) {
    return BluePrintSections.builder()
        .sectionName(request.sectionName())
        .complexity(request.complexity())
        .type(request.questionType())
        .questionCount(request.questionCount())
        .tags(request.tags())
        .marks(request.marks())
        .bluePrint(bluePrint)
        .build();
  }

  public void editBluePrintSection(
      String orgSlug, BluePrintDto.Section request, Long bluePrintSectionId, Long bluePrintId) {
    orgSettingsService.validateOrganizaiton(orgSlug);
    BluePrintSections section = validateBluePrintSection(orgSlug, bluePrintId, bluePrintSectionId);
    updateBluePrintSection(section, request);
    BluePrintSections bluePrintSection = bluePrintSectionRepository.save(section);
    BluePrint bluePrint = bluePrintSection.getBluePrint();
    Long totalQuestions =
        bluePrint.getBluePrintSections().stream().mapToLong(md -> md.getQuestionCount()).sum();
    Double totalMarks =
        bluePrint.getBluePrintSections().stream()
            .mapToDouble(md -> md.getQuestionCount() * md.getMarks())
            .sum();
    bluePrint.setTotalMarks(Long.valueOf(totalMarks.longValue()));
    bluePrint.setTotalQuestions(totalQuestions);
    bluePrintRepository.save(bluePrint);
  }

  private void updateBluePrintSection(BluePrintSections section, BluePrintDto.Section request) {
    section.setSectionName(request.sectionName());
    section.setTags(request.tags());
    section.setComplexity(request.complexity());
    section.setType(request.questionType());
    section.setQuestionCount(request.questionCount());
    section.setMarks(request.marks());
  }

  private BluePrintSections validateBluePrintSection(
      String orgSlug, Long bluePrintId, Long bluePrintSectionId) {
    BluePrint bluePrint = validateBluePrint(orgSlug, bluePrintId);
    var bluePrintSection =
        bluePrint.getBluePrintSections().stream()
            .filter(x -> x.getId().equals(bluePrintSectionId))
            .findFirst();
    if (bluePrintSection.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.BluePrintSection.Id",
          new String[] {bluePrintSectionId.toString()});
    }
    return bluePrintSection.get();
  }

  public void deleteBluePrint(String orgSlug, Long bluePrintId) {
    var bluePrint = validateBluePrint(orgSlug, bluePrintId);
    if (bluePrint.getQpGenPros().isEmpty()) {
      bluePrintRepository.deleteById(bluePrintId);
    } else {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.DeleteBluePrint",
          new String[] {bluePrintId.toString()});
    }
  }

  public void deleteBluePrintSection(String orgSlug, Long bluePrintId, Long bluePrintSectionId) {
    var bluePrintSection = validateBluePrintSection(orgSlug, bluePrintId, bluePrintSectionId);
    var bluePrint = bluePrintSection.getBluePrint();
    if (bluePrint.getQpGenPros().isEmpty()) {
      bluePrint.getBluePrintSections().remove(bluePrintSection);
      bluePrintRepository.save(bluePrint);
    } else {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.DeleteBlueSection",
          new String[] {bluePrintId.toString()});
    }
  }

  public void addInstructions(
      String orgSlug,
      BluePrintDto.InstructionsRequest request,
      Long bluePrintSectionId,
      Long bluePrintId) {
    BluePrintSections section = validateBluePrintSection(orgSlug, bluePrintId, bluePrintSectionId);
    section.setInstructions(request.instructions());
    bluePrintSectionRepository.save(section);
  }

  public BluePrintDto.InstructionsRequest getInstructions(
      String orgSlug, Long bluePrintSectionId, Long bluePrintId) {
    BluePrintSections section = validateBluePrintSection(orgSlug, bluePrintId, bluePrintSectionId);
    return BluePrintDto.InstructionsRequest.builder()
        .instructions(section.getInstructions())
        .build();
  }

  public List<QuestionDto.QuestionTags> getBluePrintConfig(String orgSlug) {
    return contentService.getQuestionTags(orgSlug);
  }
}
