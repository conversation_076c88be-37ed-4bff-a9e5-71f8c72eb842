package com.wexl.retail.thread.model;

import com.wexl.retail.model.Model;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.hibernate.annotations.Type;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "threads")
public class Thread extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String title;

  private String question;

  private String orgSlug;

  @Type(JsonType.class)
  @Column(name = "tags", columnDefinition = "jsonb")
  private List<String> tags;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "teacher_id")
  private Teacher assignedTo;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "created_by")
  private Student createdBy;

  @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
  @JoinColumn(name = "thread_id")
  private List<ThreadReply> threadReplies;
}
