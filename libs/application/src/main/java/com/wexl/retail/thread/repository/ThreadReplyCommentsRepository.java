package com.wexl.retail.thread.repository;

import com.wexl.retail.model.User;
import com.wexl.retail.thread.model.ThreadReplyComments;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ThreadReplyCommentsRepository extends JpaRepository<ThreadReplyComments, Long> {
  Optional<ThreadReplyComments> findByIdAndUserInfo(Long threadReplyCommentId, User user);
}
