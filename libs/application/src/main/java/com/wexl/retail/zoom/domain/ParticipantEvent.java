package com.wexl.retail.zoom.domain;

import jakarta.persistence.*;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "event_participant")
@EqualsAndHashCode(callSuper = true)
public class ParticipantEvent extends Event {

  @Id
  @GeneratedValue
  @GenericGenerator(
      name = "event-participant-sequence-generator",
      strategy = "org.hibernate.id.enhanced.SequenceStyleGenerator",
      parameters = {
        @org.hibernate.annotations.Parameter(
            name = "sequence_name",
            value = "participant_event_id_seq"),
        @org.hibernate.annotations.Parameter(name = "initial_value", value = "2200000"),
        @org.hibernate.annotations.Parameter(name = "increment_size", value = "1")
      })
  private long id;

  @Column(name = "participant_id")
  private String participantId;

  @Column(name = "user_id")
  private String userId;

  @Column(name = "user_name")
  private String userName;

  private String email;

  @Column(name = "customer_key")
  private String customerKey;

  @Column(name = "join_time")
  private Timestamp joinTime;

  @Column(name = "leave_time")
  private Timestamp leaveTime;

  @Column(name = "event_tz")
  private String eventTz;

  @Column(name = "leave_reason")
  private String leaveReason;
}
