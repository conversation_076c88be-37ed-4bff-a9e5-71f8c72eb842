package com.wexl.retail.student.handler;

import com.wexl.retail.content.model.Grade;
import com.wexl.retail.model.Student;
import com.wexl.retail.organization.handler.EntityHandler;
import com.wexl.retail.task.domain.Task;
import com.wexl.retail.task.domain.TaskType;
import com.wexl.retail.task.repository.TaskInstRepository;
import com.wexl.retail.task.repository.TaskRepository;
import com.wexl.retail.task.service.TaskInstService;
import com.wexl.retail.util.ValidationUtils;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
@Order(100)
public class DefaultStudentHandler implements EntityHandler<Student> {
  private final TaskRepository taskRepository;
  private final TaskInstRepository taskInstRepository;
  private final TaskInstService taskInstService;
  private final ValidationUtils validationUtils;

  @Override
  public void postSave(Student student) {
    var studentGrade = validationUtils.getStudentGrade(student);
    var taskIds = getTasksIds(studentGrade, student);
    if (taskIds != null) {
      List<Student> students = new ArrayList<>();
      students.add(student);
      taskInstRepository.saveAll(taskInstService.buildTaskInsts(taskIds, students, true));
    }
  }

  private List<Long> getTasksIds(Grade studentGrade, Student student) {
    var tasks =
        taskRepository.findAllByGradeSlugAndOrgSlugAndTaskTypeAndDueDateIsNotNull(
            studentGrade.getSlug(), student.getUserInfo().getOrganization(), TaskType.ELP);
    return tasks.stream().map(Task::getId).distinct().toList();
  }
}
