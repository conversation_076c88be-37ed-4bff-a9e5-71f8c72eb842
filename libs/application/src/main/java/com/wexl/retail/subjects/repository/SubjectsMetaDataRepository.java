package com.wexl.retail.subjects.repository;

import com.wexl.retail.subjects.model.SubjectsMetaData;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface SubjectsMetaDataRepository extends JpaRepository<SubjectsMetaData, Long> {

  @Query(
      value =
          """
                             select smd.* from subject_metadata smd
                             where (cast((:type) as varChar) is null or smd.type in (:type))
                             and (cast((:boardSlug) as varChar) is null or smd.board_slug in (:boardSlug))
                            and (cast((:gradeSlug) as varChar) is null or smd.grade_slug in (:gradeSlug))
                            and (cast((:category) as varChar) is null or smd.category in (:category))
                            and org_slug = :orgSlug
                            order by created_at desc
                                                           """,
      nativeQuery = true)
  List<SubjectsMetaData> getSubjects(
      String boardSlug, String gradeSlug, String category, String type, String orgSlug);

  List<SubjectsMetaData> findByGradeSlugAndBoardSlug(String grade, String board);

  SubjectsMetaData
      findByOrgSlugAndWexlSubjectSlugAndNameAndGradeSlugAndBoardSlugAndStatusAndDeletedAtIsNull(
          String orgSlug,
          String wexlSubjectSlug,
          String name,
          String gradeSlug,
          String boardSlug,
          Boolean status);

  Optional<SubjectsMetaData> findByOrgSlugAndNameAndGradeSlugAndBoardSlug(
      String orgSlug, String subject, String grade, String board);

  List<SubjectsMetaData> findByOrgSlugAndWexlSubjectSlugInAndGradeSlugAndBoardSlug(
      String orgSlug, List<String> subjectSlugs, String gradeSlug, String boardSlug);

  List<SubjectsMetaData> findByOrgSlugAndGradeSlugAndBoardSlug(
      String orgSlug, String gradeSlug, String boardSlug);

  Optional<SubjectsMetaData> findByIdInAndOrgSlug(List<Long> ids, String orgSlug);
}
