package com.wexl.retail.erp.attendance.domain;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.Data;

@Data
@Entity
@Table(name = "calender_details")
public class CalenderDetails extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  private Integer month;
  private Integer year;
  private Integer quarter;
  private Integer dateId;
  private CalenderStatus status;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "calender_id")
  private Calender calender;
}
