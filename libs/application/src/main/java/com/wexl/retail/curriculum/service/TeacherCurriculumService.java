package com.wexl.retail.curriculum.service;

import com.wexl.retail.auth.AuthUtil;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.content.model.Entity;
import com.wexl.retail.content.model.Grade;
import com.wexl.retail.content.model.Icon;
import com.wexl.retail.curriculum.SectionDto;
import com.wexl.retail.model.EduBoard;
import com.wexl.retail.model.Subject;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.offlinetest.service.ClassTeacherSubjectHandler;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.dto.response.TeacherCurriculumResponse;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.section.repository.TeacherSubjectsRepository;
import com.wexl.retail.section.service.TeacherSubjectsService;
import com.wexl.retail.util.StrapiService;
import java.util.*;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Data
@Service
public class TeacherCurriculumService {

  private final TeacherSubjectsService teacherSubjectsService;
  private final StrapiService strapiService;
  private final UserRepository userRepository;
  private final CurriculumService curriculumService;
  private final TeacherSubjectsRepository teacherSubjectsRepository;
  private final TeacherRepository teacherRepository;
  private final List<ClassTeacherSubjectHandler> classTeacherSubjectHandlers;

  private final SectionRepository sectionRepository;

  public List<EduBoard> transformBoardHierarchy(String authUserId) {
    List<EduBoard> boardHierarchy = new ArrayList<>();
    var user = userRepository.getUserByAuthUserId(authUserId);
    if (AuthUtil.isOrgAdmin(user)) {
      return curriculumService.getBoardsHierarchy(user.getOrganization());
    }
    List<TeacherCurriculumResponse> teacherSubjects =
        getTeacherSubjects(authUserId, user.getTeacherInfo().getId(), user.getOrganization());

    Set<String> boardSlugs =
        teacherSubjects.stream()
            .map(TeacherCurriculumResponse::getBoardSlug)
            .collect(Collectors.toSet());

    final var eduBoardsMapBySlug =
        strapiService.transformStrapiEntityToMapBySlug(strapiService.getAllBoards());
    final var gradesMapBySlug =
        strapiService.transformGradeEntityToMapBySlug(strapiService.getAllGrades());
    final var subjectsMapBySlug =
        strapiService.transformStrapiEntityToMapBySlug(strapiService.getAllSubjects());

    boardSlugs.parallelStream()
        .forEach(
            boardSlug -> {
              EduBoard eduBoard = new EduBoard();
              mapEduBoard(boardSlug, eduBoard, eduBoardsMapBySlug);
              Set<Integer> mappedGradeId = getMappedGradeIds(boardSlug, teacherSubjects);
              eduBoard.setGrades(
                  mapGradeToBoard(
                      mappedGradeId,
                      boardSlug,
                      teacherSubjects,
                      gradesMapBySlug,
                      subjectsMapBySlug));
              eduBoard
                  .getGrades()
                  .forEach(
                      grade -> grade.getSubjects().sort(Comparator.comparing(Subject::getSlug)));
              boardHierarchy.add(eduBoard);
            });

    return boardHierarchy;
  }

  private List<TeacherCurriculumResponse> getTeacherSubjects(
      String authUserId, long teacherId, String orgSlug) {
    List<TeacherCurriculumResponse> teacherSubjects =
        teacherSubjectsService.getTeacherSubjects(authUserId);
    List<Section> sections = getSectionsOfClassTeacher(teacherId);
    List<TeacherCurriculumResponse> classTeacherSubjects =
        sections.stream()
            .map(section -> getClassTeacherSubjects().getClassTeacherSubjects(section))
            .flatMap(Collection::stream)
            .toList();

    teacherSubjects.addAll(classTeacherSubjects);
    return teacherSubjects.stream().distinct().toList();
  }

  private List<Section> getSectionsOfClassTeacher(Long teacherId) {
    final Optional<Teacher> byId = teacherRepository.findById(teacherId);
    if (byId.isEmpty()) {
      return new ArrayList<>();
    }
    final Teacher teacher = byId.get();
    return teacher.getSections().stream()
        .filter(
            section ->
                Objects.nonNull(section.getClassTeacher())
                    && section.getClassTeacher().getId() == teacherId)
        .toList();
  }

  public void mapEduBoard(
      String boardSlug, EduBoard eduBoard, final Map<String, Entity> entitiesMapBySlug) {
    Entity entity = entitiesMapBySlug.get(boardSlug);
    if (entity == null) {
      throw new ApiException(
          InternalErrorCodes.SERVER_ERROR, "error.BoardSlug.Exists", new String[] {boardSlug});
    }
    eduBoard.setId(entity.getId());
    eduBoard.setName(entity.getAssetName());
    eduBoard.setSlug(boardSlug);
  }

  public Set<Integer> getMappedGradeIds(
      String boardSlug, List<TeacherCurriculumResponse> teacherSubjects) {
    return teacherSubjects.stream()
        .filter(
            teacherCurriculumResponse -> teacherCurriculumResponse.getBoardSlug().equals(boardSlug))
        .map(TeacherCurriculumResponse::getGradeId)
        .collect(Collectors.toSet());
  }

  public List<com.wexl.retail.model.Grade> mapGradeToBoard(
      Set<Integer> gradeIds,
      String boardSlug,
      List<TeacherCurriculumResponse> teacherSubjects,
      final Map<String, Grade> gradeEntitiesMapById,
      final Map<String, Entity> subjectEntitiesMapBySlug) {

    List<Grade> entities = new ArrayList<>();
    gradeIds.forEach(
        gradeId ->
            entities.addAll(
                gradeEntitiesMapById.values().stream()
                    .filter(grade -> grade.getId() == gradeId)
                    .toList()));
    List<com.wexl.retail.model.Grade> grades = new ArrayList<>();
    entities.forEach(
        entity ->
            grades.add(
                com.wexl.retail.model.Grade.builder()
                    .id(entity.getId())
                    .name(entity.getName())
                    .slug(entity.getSlug())
                    .orderId(entity.getOrder())
                    .subjects(
                        mapSubjectToGradeAndBoard(
                            getMappedSubjectSlugs(boardSlug, entity.getId(), teacherSubjects),
                            subjectEntitiesMapBySlug))
                    .sections(
                        mapSectionToSubject(
                            getMappedSectionUuids(boardSlug, entity.getId(), teacherSubjects)))
                    .build()));
    grades.sort(Comparator.comparing(com.wexl.retail.model.Grade::getOrderId));
    return grades;
  }

  public List<Subject> mapSubjectToGradeAndBoard(
      Set<String> subjectSlugs, final Map<String, Entity> entitiesMapBySlug) {

    List<Entity> entities = new ArrayList<>();
    subjectSlugs.forEach(subjectSlug -> entities.add(entitiesMapBySlug.get(subjectSlug)));
    List<Subject> subjects = new ArrayList<>();
    var filteredEntities = entities.stream().filter(Objects::nonNull).toList();
    filteredEntities.forEach(
        entity ->
            subjects.add(
                Subject.builder()
                    .id(entity.getId())
                    .name(entity.getName())
                    .slug(entity.getSlug())
                    .icons(entity.getIcons().parallelStream().map(Icon::getUrl).toList())
                    .build()));
    return subjects;
  }

  public List<SectionDto> mapSectionToSubject(Set<UUID> sectionUuids) {
    List<SectionDto> sections = new ArrayList<>();

    sectionUuids.forEach(
        uuid -> {
          Optional<Section> optionalSection = sectionRepository.findByUuid(uuid);

          if (optionalSection.isPresent()) {
            var section = optionalSection.get();
            sections.add(
                SectionDto.builder()
                    .id(section.getId())
                    .name(section.getName())
                    .uuid(section.getUuid())
                    .build());
          }
        });
    return sections;
  }

  public Set<String> getMappedSubjectSlugs(
      String boardSlug, Integer gradeId, List<TeacherCurriculumResponse> teacherSubjects) {

    Set<String> subjectSlugs = new HashSet<>();
    teacherSubjects.forEach(
        teacherCurriculumResponse -> {
          if ((Objects.equals(teacherCurriculumResponse.getGradeId(), gradeId))
              && (Objects.equals(teacherCurriculumResponse.getBoardSlug(), boardSlug))) {
            subjectSlugs.add(teacherCurriculumResponse.getSubjectSlug());
          }
        });
    return subjectSlugs;
  }

  public Set<UUID> getMappedSectionUuids(
      String boardSlug, Integer gradeId, List<TeacherCurriculumResponse> teacherSubjects) {

    Set<UUID> sectionUuids = new HashSet<>();
    teacherSubjects.forEach(
        teacherCurriculumResponse -> {
          if ((Objects.equals(teacherCurriculumResponse.getGradeId(), gradeId))
              && (Objects.equals(teacherCurriculumResponse.getBoardSlug(), boardSlug))) {
            sectionUuids.add(teacherCurriculumResponse.getSectionUuid());
          }
        });
    return sectionUuids;
  }

  public ClassTeacherSubjectHandler getClassTeacherSubjects() {
    if (classTeacherSubjectHandlers.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.SERVER_ERROR,
          "error.CannotFindConfiguration",
          new String[] {"ClassTeacherSubjectHandler"});
    }
    return classTeacherSubjectHandlers.getFirst();
  }
}
