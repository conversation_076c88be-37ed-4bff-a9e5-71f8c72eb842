package com.wexl.retail.pdf.viewer.controller;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsTeacher;
import com.wexl.retail.commons.security.annotation.IsTeacherOrStudent;
import com.wexl.retail.pdf.viewer.dto.request.AnnotationRequest;
import com.wexl.retail.pdf.viewer.dto.response.PdfAnnotationResponse;
import com.wexl.retail.pdf.viewer.service.AnnotationService;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/pdf/annotations")
public class AnnotationController {

  @Autowired AnnotationService annotationService;

  @PostMapping
  @IsTeacher
  public List<AnnotationRequest> createAnnotation(
      @RequestBody List<AnnotationRequest> annotationRequestList) {
    if (!annotationRequestList.isEmpty()
        && StringUtils.isBlank(annotationRequestList.getFirst().getDocId())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.DocId.Null");
    }
    return this.annotationService.createAnnotation(annotationRequestList);
  }

  @GetMapping("/{docId}")
  @IsTeacherOrStudent
  public PdfAnnotationResponse getAnnotationsByDocId(@PathVariable String docId) {
    return this.annotationService.getAnnotationsByDocId(docId);
  }
}
