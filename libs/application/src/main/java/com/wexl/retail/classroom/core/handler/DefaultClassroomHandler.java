package com.wexl.retail.classroom.core.handler;

import com.wexl.retail.classroom.core.model.Classroom;
import com.wexl.retail.organization.handler.EntityHandler;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(100)
public class DefaultClassroomHandler implements EntityHandler<Classroom> {

  @Override
  public void postSave(Classroom entity) {
    // Add your implementation here
  }
}
