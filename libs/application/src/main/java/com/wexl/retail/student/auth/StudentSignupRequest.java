package com.wexl.retail.student.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.globalprofile.model.RoleTemplate;
import com.wexl.retail.model.Gender;
import com.wexl.retail.student.registration.dto.StudentAttributeData;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class StudentSignupRequest {
  String userName;
  String firstName;
  String lastName;
  String schoolName;
  String email;
  String rollNumber;
  String classRollNumber;
  String academicYearSlug;
  int classId;
  int boardId;
  String password;
  String parentFirstName;
  String parentLastName;
  String parentEmail;
  String parentMobileNumber;
  String parentPassword;
  String externalRef;
  String captchaCode;
  Boolean termsAndConditions;
  String mobileToken;
  Gender gender;
  String section;
  String orgSlug;
  String crStudentUserName;
  String mobileNumber;
  String countryCode;
  StudentAttributeData attributes;

  @JsonProperty("role_template")
  RoleTemplate roleTemplate;

  Boolean isFeePaid;
  Long departmentId;
}
