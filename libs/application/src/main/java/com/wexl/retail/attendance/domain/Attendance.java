package com.wexl.retail.attendance.domain;

import com.wexl.retail.model.Model;
import com.wexl.retail.schedules.domain.SectionSchedule;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "attendance")
@EqualsAndHashCode(callSuper = true)
public class Attendance extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "attendance-sequence-generator")
  @SequenceGenerator(
      name = "attendance-sequence-generator",
      sequenceName = "attendance_seq",
      allocationSize = 1)
  private long id;

  @Column(name = "to_time")
  private Timestamp meetingEndTime;

  @Column(name = "from_time")
  private Timestamp meetingStartTime;

  @Column(name = "organization")
  private String organization;

  @ManyToOne
  @JoinColumn(updatable = false)
  private SectionSchedule sectionSchedule;

  @Type(JsonType.class)
  @Column(name = "metadata", columnDefinition = "jsonb")
  private AttendanceMetadata metadata;
}
