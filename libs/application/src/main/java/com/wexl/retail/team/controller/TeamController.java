package com.wexl.retail.team.controller;

import com.wexl.retail.team.dto.TeamRequest;
import com.wexl.retail.team.dto.TeamResponse;
import com.wexl.retail.team.service.TeamService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@Slf4j
@RequiredArgsConstructor
public class TeamController {

  private final TeamService teamService;

  @PostMapping("/orgs/{orgSlug}/teams")
  @ResponseStatus(HttpStatus.OK)
  public void createTeam(@PathVariable String orgSlug, @RequestBody TeamRequest teamInfoRequest) {

    teamService.createTeam(orgSlug, teamInfoRequest);
  }

  @GetMapping("/orgs/{orgSlug}/teams")
  public List<TeamResponse> getTeamDetails(@PathVariable String orgSlug) {

    return teamService.getAllTeams(orgSlug);
  }

  @DeleteMapping("/orgs/{orgSlug}/teams/{teamId}")
  @ResponseStatus(HttpStatus.OK)
  public void deleteTeam(@PathVariable String orgSlug, @PathVariable long teamId) {

    teamService.deleteTeam(orgSlug, teamId);
  }

  @GetMapping("/orgs/{orgSlug}/teams/{teamId}")
  @ResponseStatus(HttpStatus.OK)
  public TeamResponse getTeamById(@PathVariable String orgSlug, @PathVariable long teamId) {
    TeamResponse team;

    team = teamService.getTeamDetailById(orgSlug, teamId);

    return team;
  }

  @PutMapping("/orgs/{orgSlug}/teams/{teamId}")
  public void editTeam(
      @PathVariable String orgSlug,
      @PathVariable Long teamId,
      @RequestBody TeamRequest teamRequest) {

    teamService.editTeam(orgSlug, teamId, teamRequest);
  }
}
