package com.wexl.retail.omr;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum OmrAnswers {
  a(1),
  b(2),
  c(3),
  d(4),
  x(0);

  private final int value;

  public static OmrAnswers fromValue(int value) {
    if (value == 0) {
      throw new IllegalArgumentException("error.ValueCannotBeZero");
    }

    for (OmrAnswers mcqOption : OmrAnswers.values()) {
      if (mcqOption.value == value) {
        return mcqOption;
      }
    }

    throw new IllegalArgumentException("Do not understand the Type " + value);
  }

  public Integer integer() {
    return Integer.valueOf(this.value);
  }
}
