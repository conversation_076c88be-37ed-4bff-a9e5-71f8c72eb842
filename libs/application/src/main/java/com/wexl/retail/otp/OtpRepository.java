package com.wexl.retail.otp;

import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface OtpRepository extends JpaRepository<Otp, Long> {
  @Query(
      """
      select o from Otp o left join fetch o.user u \
      where o.otp=?1 and u.id=?2 and o.deletedAt is null\
      """)
  Optional<Otp> findByOtpUserIdDeletedAtNull(String otp, long userId);

  @Query("select o from Otp o " + "where o.otp=?1 and o.target=?2 and o.deletedAt is null")
  Optional<Otp> findByOtpTargetDeletedAtNull(String otp, String target);

  Optional<Otp> findFirstById(long id);

  boolean existsByIdAndTarget(Long otpId, String mobile);
}
