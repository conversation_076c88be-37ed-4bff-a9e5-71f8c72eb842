package com.wexl.retail.products.controller;

import static com.wexl.retail.util.Constants.AUTHORIZATION_HEADER;

import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.ecommerce.ProductDto;
import com.wexl.retail.ecommerce.ProductDto.ProductResponse;
import com.wexl.retail.products.dto.CourseDto;
import com.wexl.retail.products.service.OrdersService;
import com.wexl.retail.products.service.ProductCourseEnrollmentService;
import com.wexl.retail.products.service.ProductCourseService;
import com.wexl.retail.products.service.ProductServiceV2;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/orgs/{orgSlug}")
public class ProductController {

  private final ProductServiceV2 productService;
  private final ProductCourseService productCourseService;
  private final OrdersService ordersService;
  private final ProductCourseEnrollmentService productCourseEnrollmentService;

  @GetMapping("/products")
  public List<ProductDto.ProductResponse> listProducts(
      @PathVariable String orgSlug, @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {
    return productService.getProductsForOrg(orgSlug, bearerToken);
  }

  @GetMapping("/products-banner")
  public ProductDto.Banner getProductBanners(
      @PathVariable String orgSlug, @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {
    return productService.getProductBanners(orgSlug, bearerToken);
  }

  @ResponseStatus(HttpStatus.CREATED)
  @PostMapping("/products/{product-ref}/associate-courses")
  public void associateCourseToProduct(
      @PathVariable("product-ref") String productRef,
      @PathVariable String orgSlug,
      @Valid @RequestBody CourseDto.CourseRequest courseRequest) {
    productService.associateCourseToProduct(courseRequest, productRef, orgSlug);
  }

  @GetMapping("/products/{productRef}/course-definitions")
  public ProductDto.ProductCourses getCoursesByProductId(
      @PathVariable String orgSlug,
      @PathVariable String productRef,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {
    final ProductResponse productByExtRef =
        productService.getProductByExtRef(orgSlug, productRef, bearerToken);
    return productCourseService.getCoursesByProductId(productByExtRef, orgSlug);
  }

  @PostMapping("/student/{studentAuthId}/products/{productRef}/course-definitions/{courseDefId}")
  public CourseDto.CourseEnrollResponse enrollStudentsToProductCourses(
      @PathVariable String orgSlug,
      @PathVariable String studentAuthId,
      @PathVariable Long courseDefId,
      @PathVariable String productRef) {
    // TOOD: check if the student purchased the product productRef
    return productCourseEnrollmentService.enrollStudentsToProductCourses(
        orgSlug, studentAuthId, courseDefId);
  }

  @IsStudent
  @GetMapping("/students/{studentAuthId}/orders")
  public List<ProductDto.ProductOrdersResponse> getProductByStudentId(
      @PathVariable String orgSlug,
      @PathVariable String studentAuthId,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {
    return ordersService.getProductsByStudentId(orgSlug, studentAuthId, bearerToken);
  }
}
