package com.wexl.retail.util;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;

public class CsvUtils {

  private CsvUtils() {}

  public static void generateCsv(
      String[] csvHeaders, List<List<String>> csvBody, HttpServletResponse httpServletResponse) {
    try {
      PrintWriter out = httpServletResponse.getWriter();

      try (CSVPrinter csvPrinter =
          new CSVPrinter(
              new PrintWriter(out), CSVFormat.Builder.create().setHeader(csvHeaders).build()); ) {
        for (List<String> csvRow : csvBody) {
          csvPrinter.printRecord(csvRow);
        }
        csvPrinter.flush();
      } catch (IOException e) {
        throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.CSV.generate", e);
      }
    } catch (IOException e) {
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.CouldntProcessRequest", e);
    }
  }
}
