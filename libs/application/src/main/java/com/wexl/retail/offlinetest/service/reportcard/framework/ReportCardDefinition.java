package com.wexl.retail.offlinetest.service.reportcard.framework;

import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.organization.model.Organization;
import java.util.Map;

public interface ReportCardDefinition {

  Map<String, Object> build(User user, Organization org, ReportCardDto.Request request);

  boolean supports(ReportCardTemplate reportCardTemplate);
}
