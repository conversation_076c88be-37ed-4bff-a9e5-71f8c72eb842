package com.wexl.retail.courses.step.controller;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.courses.step.dto.CoursePageRequest;
import com.wexl.retail.courses.step.dto.CoursePageResponse;
import com.wexl.retail.courses.step.service.CoursePageService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/pages")
public class CoursePageController {

  private final CoursePageService coursePageService;

  @PostMapping
  public CoursePageResponse createCoursePage(
      @Valid @RequestBody CoursePageRequest coursePageRequest) {
    try {
      return coursePageService.createCoursePage(coursePageRequest);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @PutMapping("/{pageId}")
  public CoursePageResponse updateCoursePageById(
      @PathVariable long pageId, @Valid @RequestBody CoursePageRequest coursePageRequest) {
    try {
      return coursePageService.updateCoursePageById(coursePageRequest, pageId);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @GetMapping("/{pageId}")
  public CoursePageResponse getCoursePageById(@PathVariable long pageId) {
    try {
      return coursePageService.getCoursePageById(pageId);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }
}
