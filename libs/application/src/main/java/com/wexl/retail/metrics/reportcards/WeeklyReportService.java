package com.wexl.retail.metrics.reportcards;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.dto.WeeklyMlpReport;
import com.wexl.retail.mlp.model.Mlp;
import com.wexl.retail.mlp.model.MlpInst;
import com.wexl.retail.mlp.model.MlpItemStatus;
import com.wexl.retail.mlp.repository.MlpRepository;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.domain.TeacherSection;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.section.repository.TeacherSectionRepository;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.ExamRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.schedule.domain.ScheduleTestStudent;
import com.wexl.retail.test.schedule.dto.TestStudentStatus;
import com.wexl.retail.test.schedule.repository.ScheduleTestRepository;
import com.wexl.retail.util.Constants;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class WeeklyReportService {

  private final SectionRepository sectionRepository;
  private final ScheduleTestRepository scheduleTestRepository;
  private final TeacherRepository teacherRepository;
  private final UserRepository userRepository;
  private final MlpRepository mlpRepository;
  private final UserService userService;
  private final ExamRepository examRepository;
  private final StudentRepository studentRepository;
  private final TeacherSectionRepository teacherSectionRepository;

  public List<GenericMetricResponse> getMlpWeeklyReport(
      String teacherAuthUserId, Integer timePeriod) {
    var user =
        userRepository
            .findByAuthUserId(teacherAuthUserId)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.UserNotFound"));
    List<Mlp> mlps;

    if (Objects.isNull(timePeriod)) {
      mlps = getAllMlps(teacherAuthUserId, user);
    } else {
      mlps = getMlpsByTimePeriod(teacherAuthUserId, user, timePeriod);
    }
    var sortedMlps =
        mlps.stream()
            .sorted(Comparator.comparing(Mlp::getCreatedAt).reversed())
            .distinct()
            .toList();
    var response = sortedMlps.stream().map(this::buildMlpData).toList();

    return List.of(
        GenericMetricResponse.builder()
            .summary(buildMlpSummary(mlps))
            .data(Map.of("mlp_report", response))
            .build());
  }

  public List<Mlp> getMlpsByTimePeriod(String teacherAuthUserId, User user, Integer timePeriod) {
    var teacher = getTeacherByUser(user);
    var localDateTime = LocalDateTime.now().minusWeeks(timePeriod);
    var currentDate = Timestamp.from(Instant.now());

    if (Constants.WEXL_INTERNAL.equals(user.getOrganization())) {
      return new ArrayList<>(getGroupMlps(teacherAuthUserId, localDateTime));
    }
    return mlpRepository.findAllByTeacherAndSectionIdInAndCreatedAtBetweenOrderByCreatedAtDesc(
        teacher, getSectionsByTeacher(teacher), Timestamp.valueOf(localDateTime), currentDate);
  }

  private List<Mlp> getAllMlps(String teacherAuthUserId, User user) {
    var teacher = getTeacherByUser(user);

    if (Constants.WEXL_INTERNAL.equals(user.getOrganization())) {
      return new ArrayList<>(getGroupMlps(teacherAuthUserId, null));
    }
    return mlpRepository.findAllByTeacherAndSectionIdInOrderByCreatedAtDesc(
        teacher, getSectionsByTeacher(teacher));
  }

  private List<Mlp> getGroupMlps(String teacherAuthUserId, LocalDateTime localDateTime) {
    List<Mlp> mlps;
    if (Objects.nonNull(localDateTime)) {
      mlps =
          mlpRepository.getMlpsByTeacherUuidAndDateBetween(
              teacherAuthUserId,
              localDateTime.toLocalDate().toString(),
              LocalDate.now().toString(),
              null,
              Collections.emptyList());
    } else {
      mlps = mlpRepository.getMlpsByTeacherUuid(teacherAuthUserId, null, Collections.emptyList());
    }
    return mlpRepository.findByParentInAndDeletedAtIsNull(mlps);
  }

  public Teacher getTeacherByUser(User user) {

    return teacherRepository
        .findByUserInfo(user)
        .orElseThrow(
            () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.TeacherNotFound"));
  }

  public List<Long> getSectionsByTeacher(Teacher teacher) {

    List<TeacherSection> teacherSections =
        teacherSectionRepository.getTeacherSectionsByTeacherAndDeletedAtIsNull(teacher.getId());
    return teacherSections.stream().map(TeacherSection::getSection).map(Section::getId).toList();
  }

  public List<GenericMetricResponse> getWeeklyTestReport(String teacherAuthId, Integer timePeriod) {

    var user =
        userRepository
            .findByAuthUserId(teacherAuthId)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.UserNotFound"));
    List<ScheduleTest> scheduleTests;
    if (Objects.nonNull(timePeriod)) {
      var localDate = LocalDate.now().minusWeeks(timePeriod);
      scheduleTests =
          scheduleTestRepository.getScheduleTestsByTeacherAndDate(
              user.getId(),
              localDate.toString(),
              LocalDate.now().toString(),
              user.getOrganization(),
              getSectionsByTeacher(getTeacherByUser(user)));
    } else {
      scheduleTests =
          scheduleTestRepository.getScheduleTestsByTeacher(
              user.getId(), user.getOrganization(), getSectionsByTeacher(getTeacherByUser(user)));
    }

    var summary = buildScheduleTestSummary(scheduleTests);
    return scheduleTests.stream()
        .map(
            scheduleTest ->
                GenericMetricResponse.builder()
                    .summary(summary)
                    .data(buildScheduleTestData(scheduleTest))
                    .build())
        .toList();
  }

  private Map<String, Object> buildScheduleTestData(ScheduleTest scheduleTest) {

    Map<String, Object> scheduleData = new HashMap<>();
    List<Exam> exams = examRepository.findAllByScheduleTestIn(List.of(scheduleTest));

    var studentsAttempted =
        scheduleTest.getScheduleTestStudent().stream()
            .filter(tss -> TestStudentStatus.COMPLETED.name().equals(tss.getStatus()))
            .count();

    var averageScore =
        !exams.isEmpty()
            ? exams.stream()
                .filter(exam -> Objects.nonNull(exam.getMarksScored()))
                .mapToDouble(Exam::getMarksScored)
                .average()
            : OptionalDouble.empty();

    var testCompletedStudents =
        scheduleTest.getScheduleTestStudent().stream()
            .filter(tss -> tss.getStatus().equals(TestStudentStatus.COMPLETED.name()))
            .toList();
    var metaData = scheduleTest.getTestDefinition().getMetadata();

    var student =
        studentRepository
            .findByUserInfo(scheduleTest.getScheduleTestStudent().get(0).getStudent())
            .orElseThrow(
                () ->
                    new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.StudentNotFound"));

    scheduleData.put("section_name", student.getSection().getName());
    scheduleData.put("test_name", scheduleTest.getTestDefinition().getTestName());
    scheduleData.put(
        "subtopic_name",
        metaData.getSubtopics().isEmpty() ? null : metaData.getSubtopics().get(0).getName());
    scheduleData.put(
        "chapter_name",
        metaData.getChapters().isEmpty() ? null : metaData.getChapters().get(0).getName());
    scheduleData.put("total_students", scheduleTest.getScheduleTestStudent().size());
    scheduleData.put("students_attempted", studentsAttempted);
    scheduleData.put("average_score", averageScore);
    scheduleData.put(
        "average_attempted",
        Math.round(
            ((float) testCompletedStudents.size() / scheduleTest.getScheduleTestStudent().size())
                * 100));
    return scheduleData;
  }

  private Map<String, Object> buildScheduleTestSummary(List<ScheduleTest> scheduleTests) {
    if (scheduleTests.isEmpty()) {
      return new HashMap<>();
    }
    Map<String, Object> testSummary = new HashMap<>();
    List<Exam> exams = examRepository.findAllByScheduleTestIn(scheduleTests);

    var average =
        exams.stream()
            .filter(exam -> Objects.nonNull(exam.getMarksScored()))
            .mapToDouble(Exam::getMarksScored)
            .average();

    List<ScheduleTestStudent> scheduleTestStudents =
        scheduleTests.stream()
            .map(ScheduleTest::getScheduleTestStudent)
            .flatMap(Collection::stream)
            .toList();
    var completedStudents =
        scheduleTestStudents.stream()
            .filter(tss -> TestStudentStatus.COMPLETED.name().equals(tss.getStatus()))
            .toList();

    scheduleTests.forEach(
        ts -> {
          testSummary.put("total_test_created", scheduleTests.size());
          testSummary.put("average_test_score", Math.round(average.orElse(0)));
          testSummary.put(
              "average_test_attendance",
              Math.round(((float) completedStudents.size() / scheduleTestStudents.size()) * 100));
        });
    return testSummary;
  }

  private WeeklyMlpReport.MlpResponse buildMlpData(Mlp mlp) {
    List<MlpInst> insts = mlp.getMlpInsts();

    long attemptedCount =
        insts.stream()
            .filter(
                inst ->
                    List.of(inst.getSynopsisStatus(), inst.getVideoStatus())
                            .contains(MlpItemStatus.COMPLETED)
                        || Objects.nonNull(inst.getExam()))
            .count();
    var score =
        insts.stream()
            .filter(inst -> Objects.nonNull(inst.getExam()))
            .mapToDouble(MlpInst::getKnowledgePercentage)
            .average();

    return WeeklyMlpReport.MlpResponse.builder()
        .sectionName(mlp.getSection().getName())
        .mlpName(mlp.getTitle())
        .chapterName(mlp.getChapterName())
        .subtopicName(mlp.getSubtopicName())
        .totalStudents(mlp.getMlpInsts().size())
        .studentsAttempted(attemptedCount)
        .averageScore(score.orElse(0))
        .averageAttempted(Math.round((float) attemptedCount / insts.size() * 100))
        .build();
  }

  private Map<String, Object> buildMlpSummary(List<Mlp> mlps) {
    if (mlps.isEmpty()) {
      return new HashMap<>();
    }
    Map<String, Object> summary = new HashMap<>();
    var mlpInsts = mlps.stream().map(Mlp::getMlpInsts).flatMap(Collection::stream).toList();
    OptionalDouble mlpAverage =
        mlpInsts.stream()
            .filter(inst -> Objects.nonNull(inst.getExam()))
            .mapToDouble(MlpInst::getKnowledgePercentage)
            .average();

    OptionalDouble mlpAttendance =
        mlpInsts.stream()
            .filter(inst -> Objects.nonNull(inst.getAttendancePercentage()))
            .mapToDouble(MlpInst::getAttendancePercentage)
            .average();

    var userName = userService.getNameByUserInfo(mlps.getFirst().getTeacher().getUserInfo());

    summary.put("total_mlp_created", mlps.size());
    summary.put("average_mlp_score", Math.round(mlpAverage.orElse(0) / mlps.size()));
    summary.put("average_mlp_attendance", Math.round(mlpAttendance.orElse(0) / mlps.size()));
    summary.put("teacher_name", userName);
    return summary;
  }
}
