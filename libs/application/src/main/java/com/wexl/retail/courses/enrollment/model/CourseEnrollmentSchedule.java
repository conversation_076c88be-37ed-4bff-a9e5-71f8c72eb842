package com.wexl.retail.courses.enrollment.model;

import java.sql.Timestamp;

public interface CourseEnrollmentSchedule {

  long getId();

  Timestamp getStartDate();

  Timestamp getEndDate();

  Timestamp getPublishedAt();

  String getOrgSlug();

  String getName();

  long getCourseDefinitionId();

  Integer getEnrollments();

  Integer getVersion();

  String getStatus();

  Long getCategoryId();

  String getCategoryName();

  long getDuration();

  String getThumbnail();
}
