package com.wexl.retail.student.attributes.model;

import com.wexl.retail.model.Model;
import com.wexl.retail.model.Student;
import jakarta.persistence.*;
import lombok.*;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "student_attribute_values", schema = "public")
public class StudentAttributeValueModel extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne
  @JoinColumn(name = "attribute_definition_id")
  private StudentAttributeDefinitionModel attributeDefinition;

  @ManyToOne
  @JoinColumn(name = "student_id")
  private Student student;

  private String value;
}
