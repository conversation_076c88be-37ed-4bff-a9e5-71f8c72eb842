package com.wexl.retail.courses.definition.model;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum CourseDefinitionStatus {
  DRAFT("DRAFT"),
  ACTIVE("ACTIVE"),
  ARCHIVED("ARCHIVED");

  private final String value;

  public static CourseDefinitionStatus fromValue(String value) {
    if (value == null || "".equals(value)) {
      throw new IllegalArgumentException("Value cannot be null or empty!");
    }

    for (var enumEntry : CourseDefinitionStatus.values()) {
      if (enumEntry.toString().equalsIgnoreCase(value)) {
        return enumEntry;
      }
    }

    throw new IllegalArgumentException("Do not understand the Type " + value);
  }

  @Override
  public String toString() {
    return this.value;
  }
}
