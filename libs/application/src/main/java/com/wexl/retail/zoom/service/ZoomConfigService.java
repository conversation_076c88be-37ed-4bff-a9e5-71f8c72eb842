package com.wexl.retail.zoom.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.content.model.ZoomConfig;
import com.wexl.retail.util.StrapiService;
import com.wexl.retail.zoom.config.ZoomConfiguration.ZoomCredential;
import com.wexl.retail.zoom.domain.ZoomMeetingResponse;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import javax.crypto.spec.SecretKeySpec;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service
public class ZoomConfigService {
  private static final String ZOOM_MEETING_URL_PREFIX = "https://api.zoom.us/v2/meetings/%s";
  @Autowired private StrapiService strapiService;
  @Autowired private RestTemplate restTemplate;

  @SneakyThrows
  public String retrievePassword(ZoomCredential credential, String meetingId, String organization) {
    ZoomConfig[] zoomConfigForOrganization = strapiService.getZoomConfigByMeetingId();
    if (zoomConfigForOrganization.length == 0) {
      var passcode = findPasswordFromZoom(credential, meetingId, organization);
      var organizationBySlug = strapiService.getOrganizationBySlug(organization);
      var zoomConfig = new ZoomConfig(meetingId, passcode, organizationBySlug.getId());
      return zoomConfig.getPasscode();
    }
    return zoomConfigForOrganization[0].getPasscode();
  }

  private String findPasswordFromZoom(
      ZoomCredential credential, String meetingId, String organization) {
    var jwtToken = generateZoomWebJwt(credential.getApiKey(), credential.getApiSecret());
    var headers = new HttpHeaders();
    headers.setBearerAuth(jwtToken);

    try {
      ResponseEntity<ZoomMeetingResponse> response =
          restTemplate.exchange(
              ZOOM_MEETING_URL_PREFIX.formatted(meetingId),
              HttpMethod.GET,
              new HttpEntity<>(null, headers),
              ZoomMeetingResponse.class);

      var zoomMeetingResponse = response.getBody();
      if (zoomMeetingResponse == null) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST,
            "error.Meeting.MeetingID",
            new String[] {meetingId});
      }
      return zoomMeetingResponse.getEncryptedPassword();
    } catch (Exception exception) {
      log.error(
          "Meeting with id ["
              + meetingId
              + "] doesn't exist for Organization ["
              + organization
              + "]",
          exception);
    }
    return "INVALID_PASSWORD";
  }

  private String generateZoomWebJwt(String key, String secret) {
    var jwtValidityInDays = 1;
    var tokenSignInKey =
        new SecretKeySpec(
            secret.getBytes(StandardCharsets.UTF_8), SignatureAlgorithm.HS256.getJcaName());
    var now = Instant.now();
    return Jwts.builder()
        .claim(Claims.ISSUER, key)
        .setIssuedAt(Date.from(now))
        .setExpiration(Date.from(now.plus(jwtValidityInDays, ChronoUnit.DAYS)))
        .signWith(SignatureAlgorithm.HS256, tokenSignInKey)
        .compact();
  }
}
