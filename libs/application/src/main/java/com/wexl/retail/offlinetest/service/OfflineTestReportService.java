package com.wexl.retail.offlinetest.service;

import static org.apache.xmlgraphics.util.MimeConstants.MIME_PDF;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCard;
import com.wexl.retail.offlinetest.dto.ReportCard.Marks;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.ReportCardTemplateRepository;
import com.wexl.retail.offlinetest.service.reportcard.framework.ReportCardDefinition;
import com.wexl.retail.teacher.training.controller.SimpleDataControllerHelper;
import com.wexl.retail.util.ValidationUtils;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.StringReader;
import java.util.*;
import javax.xml.transform.Result;
import javax.xml.transform.Source;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.sax.SAXResult;
import javax.xml.transform.stream.StreamSource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.fop.apps.FopFactory;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;
import org.xml.sax.SAXException;
import org.xml.sax.helpers.DefaultHandler;

@RequiredArgsConstructor
@Service
@Slf4j
public class OfflineTestReportService {

  private final SpringTemplateEngine templateEngine;
  private final SimpleDataControllerHelper simpleDataControllerHelper;
  private final ValidationUtils validationUtils;
  private final ReportCardTemplateRepository reportCardTemplateRepository;
  private static final String SINGLE_EXAM_REPORT_TEMPLATE = "report-card/single-exam.xml";
  private final List<ReportCardDefinition> reportCardDefinitions;

  public ReportCardDefinition findReportCardDefinition(ReportCardTemplate reportTemplate) {
    final Optional<ReportCardDefinition> reportCardDefinitionOptional =
        reportCardDefinitions.stream().filter(r -> r.supports(reportTemplate)).findFirst();

    if (reportCardDefinitionOptional.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.invalidInput",
          new String[] {"Report Card Template not supported"});
    }

    return reportCardDefinitionOptional.get();
  }

  public byte[] getStudentReportByOfflineTestDefinition(
      String orgSlug, Long reportCardTemplateId, ReportCardDto.Request request) {

    var reportTemplate = reportCardTemplateRepository.findById(reportCardTemplateId);
    if (reportTemplate.isEmpty()) {
      throw new ApiException(
          com.wexl.retail.commons.errorcodes.InternalErrorCodes.INVALID_REQUEST,
          "Invalid Report Card TemplateId:" + reportCardTemplateId);
    }
    var newRequest = buildNewRequest(request, reportCardTemplateId);
    Map<String, Object> model = constructReportCardModel(reportTemplate.get(), orgSlug, newRequest);
    var context = new Context(Locale.getDefault(), Map.of("model", model));
    String foTemplate =
        templateEngine.process("report-card/dps/" + reportTemplate.get().getConfig(), context);
    return generatePdf(foTemplate);
  }

  private ReportCardDto.Request buildNewRequest(
      ReportCardDto.Request request, Long reportCardTemplateId) {
    return ReportCardDto.Request.builder()
        .offlineTestDefinitionId(request.offlineTestDefinitionId())
        .termId(request.termId())
        .sectionUuid(request.sectionUuid())
        .templateId(reportCardTemplateId)
        .childOrg(request.childOrg())
        .withMarks(request.withMarks())
        .studentAuthId(request.studentAuthId())
        .build();
  }

  private Map<String, Object> constructReportCardModel(
      ReportCardTemplate reportTemplate, String orgSlug, ReportCardDto.Request request) {
    User user = validationUtils.isValidUser(request.studentAuthId());
    var org = validationUtils.isOrgValid(orgSlug);
    final ReportCardDefinition reportCardDefinition = findReportCardDefinition(reportTemplate);
    return reportCardDefinition.build(user, org, request);
  }

  public byte[] generateSingleExamReport() {
    final var model = constructModel();
    var context = new Context(Locale.getDefault(), Map.of("model", model));
    String foTemplate = templateEngine.process(SINGLE_EXAM_REPORT_TEMPLATE, context);
    return generatePdf(foTemplate);
  }

  public byte[] generatePdf(String xslFo) {
    try {
      var out = new ByteArrayOutputStream();
      var configFile = simpleDataControllerHelper.getFileInLocaleFolder("fop.xconf");
      if (configFile.isEmpty()) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST, "error.invalidInput", new String[] {"fop.xconf"});
      }

      DefaultHandler defaultHandler =
          FopFactory.newInstance(configFile.get()).newFop(MIME_PDF, out).getDefaultHandler();

      final StringReader reader = new StringReader(xslFo);
      Source src = new StreamSource(reader);
      Result res = new SAXResult(defaultHandler);

      TransformerFactory.newInstance().newTransformer().transform(src, res);

      return out.toByteArray();
    } catch (IOException | SAXException | TransformerException e) {
      throw new ApiException(
          InternalErrorCodes.SERVER_ERROR,
          "error.PdfGenerationIssue",
          new String[] {e.getMessage()},
          e);
    } catch (Exception ex) {
      if (ex instanceof ApiException) {
        throw (ApiException) ex;
      }
      throw new ApiException(
          InternalErrorCodes.SERVER_ERROR,
          "error.PdfGenerationIssue",
          new String[] {ex.getMessage()},
          ex);
    }
  }

  private Map<String, Object> constructModel() {
    ReportCard.Header header =
        ReportCard.Header.builder()
            .schoolName("Delhi Public School, Nacharam")
            .academicYear("Session: 2024-25")
            .studentName("GANDIKOTA NRIPAD SAI")
            .rollNumber("10")
            .className("IX-A")
            .mothersName("KADIYALA PADMAVATHI")
            .fathersName("GANDIKOTA SUBRAMANYA SASTRY")
            .dateOfBirth("11/01/2010")
            .admissionNumber("1150779")
            .build();

    return Map.of(
        "header",
        header,
        "pages",
        List.of(1, 2, 3),
        "marks",
        List.of(
            createMarks("1", "ENGLISH", "10", "80", "100", "A1"),
            createMarks("2", "HINDI", "15", "60", "100", "A1"),
            createMarks("3", "MATHEMATICS", "10.7", "70", "100", "A1"),
            createMarks("4", "SCIENCE", "20", "80", "100", "A1"),
            createMarks("5", "SOCIAL SCIENCE", "20", "80", "100", "A1"),
            createMarks("6", "COMPUTER SCIENCE", "20", "80", "100", "A1"),
            createMarks("7", "ART", "20", "80", "100", "A1"),
            createMarks("8", "PHYSICAL EDUCATION", "20", "80", "100", "A1"),
            createMarks("9", "MUSIC", "20", "80", "100", "A1"),
            createMarks("10", "DANCE", "20", "80", "100", "A1")),
        "coscholasticMarks",
        List.of(
            createMarks("1", "ART EDUCATION", "10", "80", "100", "B"),
            createMarks("2", "DISCIPLINE", "15", "60", "100", "A"),
            createMarks("3", "HEALTH AND PHYSICAL EDUCATION", "10.7", "70", "100", "A"),
            createMarks("4", "WORK EDUCATION", "20", "80", "100", "A")));
  }

  public Marks createMarks(
      String sno,
      String subject,
      String internalAssessment,
      String annualExam,
      String total,
      String grade) {
    return new Marks(sno, subject, internalAssessment, annualExam, total, grade);
  }
}
