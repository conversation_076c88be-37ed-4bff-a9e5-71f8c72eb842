package com.wexl.retail.test.competitive.validators;

import static com.wexl.retail.util.Constants.WEXL_INTERNAL;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.test.schedule.domain.TestScheduleStudentAnswer;
import com.wexl.retail.test.school.domain.TestCategory;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.domain.TestDefinitionSection;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class ElpTestDefinitionValidator implements TestDefinitionValidator {

  private final TestDefinitionRepository testDefinitionRepository;

  private final String[] sectionNames =
      new String[] {"Listening", "Speaking", "Reading", "Vocabulary", "Grammar"};

  @Override
  public void validate(TestDefinition testDefinition) {
    if (!WEXL_INTERNAL.equals(testDefinition.getOrganization())) {
      return;
    }

    String name = testDefinition.getTestName();
    final Optional<TestDefinition> possibleTestDefinition =
        testDefinitionRepository
            .findTop1ByTestNameAndOrganizationAndDeletedAtIsNullAndPublishedAtIsNotNull(
                name, WEXL_INTERNAL);
    if (possibleTestDefinition.isPresent()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.Invalid.ELP.TestName", new String[] {name});
    }

    if (testDefinition.getTestDefinitionSections().size() != 5) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Invalid.Sections.ELP");
    }

    List<TestDefinitionSection> sortedSections =
        testDefinition.getTestDefinitionSections().stream()
            .sorted(Comparator.comparingLong(TestDefinitionSection::getSequenceNumber))
            .toList();
    for (int i = 0; i < testDefinition.getTestDefinitionSections().size(); i++) {
      if (!sortedSections.get(i).getName().equals(sectionNames[i])) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST, "error.Invalid.SectionNames.ELP");
      }
    }
  }

  @Override
  public boolean supports(TestCategory name) {
    return TestCategory.ELP.equals(name);
  }

  @Override
  public List<TestScheduleStudentAnswer> processOptionalQuestions(
      List<TestScheduleStudentAnswer> tssa, List<TestDefinitionSection> sections) {
    return Collections.emptyList();
  }
}
