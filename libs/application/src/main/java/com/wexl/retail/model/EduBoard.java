package com.wexl.retail.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class EduBoard {

  private int id;
  private String slug;
  private String name;
  private List<Grade> grades;
  private String orgSlug;
}
