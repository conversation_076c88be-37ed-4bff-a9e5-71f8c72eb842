package com.wexl.retail.team.repository;

import com.wexl.retail.persons.dto.StudentTeamCountQueryResult;
import com.wexl.retail.team.domain.Team;
import com.wexl.retail.team.dto.TeamQueryResult;
import jakarta.transaction.Transactional;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface TeamRepository extends JpaRepository<Team, Long> {

  Team findByNameAndOrgSlug(String name, String orgSlug);

  Team findByIdAndOrgSlug(long id, String orgSlug);

  long countByOrgSlugAndStatus(String orgSlug, boolean status);

  @Query(
      value =
          """
                       select t.id as teamId,t.name as teamName,
                       count(ts.students_id) as noOfTeamMembers from teams t
                       left join teams_students ts on ts.team_id=t.id
                       where org_slug = :orgSlug and t.deleted_at is null
                       group by t.name,t.id
                          """,
      nativeQuery = true)
  List<TeamQueryResult> getTeamDetailsOrgWise(String orgSlug);

  @Transactional
  @Modifying
  @Query(value = "delete from teams_students where students_id in (:studentId)", nativeQuery = true)
  void deleteStudentFromTeam(Long studentId);

  @Transactional
  @Modifying
  @Query(value = "delete from teams_students where team_id = :teamId", nativeQuery = true)
  void deleteStudentsFromTeam(Long teamId);

  @Query(
      value = "select students_id from teams_students where team_id = :teamId",
      nativeQuery = true)
  List<Long> getStudentIdsByTeamId(Long teamId);

  @Query(value = "select course_id from course_teams where teams_id =:teamId", nativeQuery = true)
  List<Long> getCoursesForTeam(Long teamId);

  @Query(
      value =
          """
          select students_id as studentId,count(*) as studentTeamCount from teams_students where students_id in (:studentId)
          group by students_id\
          """,
      nativeQuery = true)
  List<StudentTeamCountQueryResult> getStudentNoOfTeamsCount(List<Long> studentId);
}
