package com.wexl.retail.mobile.repository;

import com.wexl.retail.mobile.model.MobileConfigValues;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface MobileConfigValuesRepository extends JpaRepository<MobileConfigValues, Long> {

  @Query(
      value =
          """
                                    select * from mobile_config_values where mobile_config_id = :packageId and mobile_config_key_id in (:mobileConfigKeys)
                                    """,
      nativeQuery = true)
  List<MobileConfigValues> findByPackageIdAndMobileConfigKeys(
      Long packageId, List<Long> mobileConfigKeys);
}
