package com.wexl.retail.courses.definition.repository;

import com.wexl.retail.courses.definition.model.CourseOrg;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CourseOrgRepository extends JpaRepository<CourseOrg, Long> {

  @Query(
      value = "select * from course_org where course_definition_id = :courseDefId",
      nativeQuery = true)
  List<CourseOrg> getAllOrgsAssociatedToCourseDef(long courseDefId);
}
