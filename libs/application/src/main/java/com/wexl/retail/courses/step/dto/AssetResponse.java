package com.wexl.retail.courses.step.dto;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.sql.Timestamp;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_EMPTY)
@Slf4j
public class AssetResponse {
  private Long id;

  private String name;
  private String synopsis;
  private String slug;

  @JsonProperty("file_type")
  private String fileType;

  @JsonProperty("file_size")
  private Float fileSize;

  @JsonProperty("sub_topics")
  private List<String> subTopics;

  private Boolean status;

  private String link;

  @JsonProperty("link_path")
  private String linkPath;

  @JsonProperty("is_published")
  private Boolean isPublished;

  @JsonProperty("teacher_name")
  private String teacherName;

  @JsonProperty("link_source")
  private String linkSource;

  @JsonIgnore Timestamp updatedAt;

  @JsonIgnore Timestamp createdAt;

  @JsonProperty("created_by_me")
  private Boolean createdByMe;

  String chapter;

  String subTopic;

  String subject;

  String grade;

  @JsonProperty("organization_name")
  String organizationName;

  @JsonProperty("org_abbreviation")
  String orgAbbreviation;

  @JsonProperty("org_slug")
  String orgSlug;

  @JsonProperty("link_type")
  String linkType;

  @JsonProperty("page_type")
  String pageType;

  @JsonProperty("is_video")
  boolean isVideo;

  @JsonProperty("alt_vimeo_link")
  String altVimeoLink;

  String sha;

  @JsonProperty("updated_at")
  public long getModifiedAt() {
    if (getUpdatedAt() != null) {
      return convertIso8601ToEpoch(getUpdatedAt().toLocalDateTime());
    }
    return 0;
  }

  @JsonProperty("created_at")
  public long getCreationTime() {
    if (getCreatedAt() != null) {
      return convertIso8601ToEpoch(getCreatedAt().toLocalDateTime());
    }
    return 0;
  }

  @JsonProperty("subtopic_slug")
  public String subtopicSlug;

  @JsonProperty("thumbnail_link")
  public String thumbnailLink;

  @JsonProperty("thumbnail_preview_url")
  public String thumbnailLPreviewUrl;

  @JsonIgnore private Integer organization;

  @JsonIgnore private String metadata;

  @JsonProperty("asset_category")
  private Long assetCategory;

  @JsonProperty("asset_type")
  private String assetType;

  @JsonProperty("asset_category_slug")
  private String assetCategorySlug;
}
