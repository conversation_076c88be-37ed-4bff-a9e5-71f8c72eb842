package com.wexl.retail.student.subject.profiles.domain;

import com.wexl.retail.model.Model;
import com.wexl.retail.organization.model.Organization;
import jakarta.persistence.*;
import lombok.*;

@Getter
@Setter
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(
    name = "subject_profiles_details",
    uniqueConstraints = {
      @UniqueConstraint(
          columnNames = {
            "subject_profile_id",
            "source_org_id",
            "board_slug",
            "subject_slug",
            "grade_slug"
          })
    })
public class SubjectProfileDetails extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "org_id")
  private Organization org;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "source_org_id")
  private Organization sourceOrg;

  @Column(name = "board_slug")
  private String boardSlug;

  @Column(name = "board_name")
  private String boardName;

  @Column(name = "subject_name")
  private String subjectName;

  @Column(name = "subject_slug")
  private String subjectSlug;

  @Column(name = "grade_name")
  private String gradeName;

  @Column(name = "grade_slug")
  private String gradeSlug;

  @Column(name = "display_name")
  private String displayName;

  @ManyToOne
  @JoinColumn(name = "subject_profile_id")
  private SubjectProfiles subjectProfiles;
}
