package com.wexl.retail.task.controller;

import static com.wexl.retail.util.Constants.AUTHORIZATION_HEADER;

import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.content.model.QuestionResponse;
import com.wexl.retail.student.exam.ExamResponse;
import com.wexl.retail.task.domain.TaskStatus;
import com.wexl.retail.task.dto.StudentTasksResponse;
import com.wexl.retail.task.dto.TaskActivityResponse;
import com.wexl.retail.task.service.TaskService;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("")
public class TaskController {

  private final TaskService taskService;

  @IsStudent
  @PostMapping("orgs/{orgSlug}/tasks/{taskId}/exams")
  public ExamResponse startTaskExam(
      @PathVariable String orgSlug, @Valid @PathVariable Long taskId) {

    return taskService.startTaskExam(orgSlug, taskId);
  }

  @IsStudent
  @PostMapping("orgs/{orgSlug}/tasks/{taskId}/taskInst/{taskInstId}/complete")
  public void completeTaskInst(@PathVariable String orgSlug, @PathVariable Long taskInstId) {

    taskService.completeTaskInst(taskInstId);
  }

  @IsStudent
  @PostMapping("orgs/{orgSlug}/tasks/student/{studentId}")
  public List<StudentTasksResponse> getStudentTasks(
      @PathVariable String orgSlug,
      @Valid @PathVariable Long studentId,
      @RequestParam(required = false) String status,
      @RequestParam(required = false, defaultValue = "100") Integer limit) {

    return taskService.getStudentTasks(studentId, status, limit);
  }

  @IsStudent
  @PostMapping("orgs/{orgSlug}/tasks/exam/{examId}/questions")
  public QuestionResponse getTaskQuestions(
      @PathVariable String orgSlug,
      @Valid @PathVariable Long examId,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {

    return taskService.getTaskQuestions(orgSlug, examId, bearerToken);
  }

  @GetMapping("orgs/{orgSlug}/tasks/students/{authUserId}/tasks:activity")
  public List<Long> getActivityDates(
      @PathVariable("authUserId") String studentId,
      @RequestParam(required = false, defaultValue = "500") int limit) {
    return taskService.getActivityDates(studentId, limit);
  }

  @GetMapping("orgs/{orgSlug}/teachers/{authUserId}/tasks")
  public List<TaskActivityResponse> getTeacherTaskActivityResponse(
      @PathVariable String orgSlug,
      @PathVariable("authUserId") String teacherAuthUserId,
      @RequestParam(required = false) Long studentId,
      @RequestParam(required = false) String subject,
      @RequestParam(required = false) String chapter,
      @RequestParam(required = false) String topic,
      @RequestParam(required = false) TaskStatus status,
      @RequestParam Long fromDateInEpoch,
      @RequestParam Long toDateInEpoch,
      @RequestParam(required = false) Boolean forDownload,
      @RequestParam(required = false, defaultValue = "500") Integer limit) {
    return taskService.getTeacherTaskActivityResponse(
        teacherAuthUserId,
        orgSlug,
        fromDateInEpoch,
        toDateInEpoch,
        studentId,
        subject,
        chapter,
        topic,
        status,
        Boolean.TRUE.equals(forDownload) ? null : limit);
  }
}
