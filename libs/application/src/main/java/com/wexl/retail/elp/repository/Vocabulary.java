package com.wexl.retail.elp.repository;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.model.Model;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Type;

@Entity
@Data
@Table(name = "vocabulary")
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
public class Vocabulary extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @JsonProperty("word")
  private String word;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb")
  private String wordData;
}
