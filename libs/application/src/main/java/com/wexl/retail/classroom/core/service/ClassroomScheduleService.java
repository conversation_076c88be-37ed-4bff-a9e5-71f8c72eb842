package com.wexl.retail.classroom.core.service;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;

import com.wexl.retail.classroom.core.dto.*;
import com.wexl.retail.classroom.core.model.Classroom;
import com.wexl.retail.classroom.core.model.ClassroomSchedule;
import com.wexl.retail.classroom.core.model.ClassroomScheduleInst;
import com.wexl.retail.classroom.core.repository.ClassroomRepository;
import com.wexl.retail.classroom.core.repository.ClassroomScheduleRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.meetingroom.domain.MeetingRoom;
import com.wexl.retail.meetingroom.repository.MeetingRoomRepository;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.util.Constants;
import jakarta.transaction.Transactional;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ClassroomScheduleService {
  private final ClassroomRepository classroomRepository;
  private final ClassroomScheduleRepository classroomScheduleRepository;
  private final MeetingRoomRepository meetingRoomRepository;
  private final DateTimeUtil dateTimeUtil;

  private final TeacherRepository teacherRepository;

  private final OrganizationRepository organizationRepository;
  private final StudentRepository studentRepository;

  public void scheduleClassroom(
      String orgSlug,
      long classroomId,
      List<ClassroomScheduleRequest> classroomScheduleRequests,
      boolean allowConflicts) {
    var classroom = classroomRepository.findByIdAndOrgSlug(classroomId, orgSlug);
    if (classroom.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.ClassRoomValidity.ClassIdAndOrg",
          new String[] {Long.toString(classroomId), orgSlug});
    }

    var busyTeachers = findBusyTeachers(orgSlug, classroom.get(), classroomScheduleRequests);
    checkTeacherAndStudentAvailability(busyTeachers, allowConflicts);
    var busyStudents = findBusyStudents(orgSlug, classroom.get(), classroomScheduleRequests);
    checkTeacherAndStudentAvailability(busyStudents, allowConflicts);

    classroomScheduleRepository.saveAll(
        new ArrayList<>(
            classroomScheduleRequests.stream()
                .map(request -> scheduleClassroom(classroom.get(), request, orgSlug))
                .toList()));
  }

  private void checkTeacherAndStudentAvailability(
      List<String> busyStudentAndTeacher, boolean allowConflicts) {
    if (!busyStudentAndTeacher.isEmpty() && !allowConflicts) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.StudentTeacher.Conflict",
          new String[] {busyStudentAndTeacher.toString()});
    }
  }

  private List<String> findBusyTeachers(
      String orgSlug,
      Classroom classroom,
      List<ClassroomScheduleRequest> classroomScheduleRequests) {
    List<ClassroomSchedule> classroomSchedule =
        classroomScheduleRepository.findByOrgSlugAndDeletedAtIsNull(orgSlug);

    var teacherIds = classroom.getTeachers().stream().map(Teacher::getId).toList();

    var activeSchedules =
        new ArrayList<>(
            classroomSchedule.stream()
                .filter(schedule -> schedule.getEndDate().isAfter(LocalDateTime.now()))
                .toList());

    List<Teacher> scheduledTeachers =
        new ArrayList<>(
            activeSchedules.stream()
                .map(ClassroomSchedule::getClassroom)
                .map(Classroom::getTeachers)
                .flatMap(List::stream)
                .toList());

    var scheduledTeacherIds =
        new ArrayList<>(scheduledTeachers.stream().map(Teacher::getId).toList());

    var filteredTeachers =
        new ArrayList<>(teacherIds.stream().filter(scheduledTeacherIds::contains).toList());

    List<ClassroomSchedule> entries;

    for (var request : classroomScheduleRequests) {
      var startTime = dateTimeUtil.convertEpochToIso8601Legacy(request.getStartTime());

      var endTime = dateTimeUtil.convertEpochToIso8601Legacy(request.getEndTime());
      entries =
          classroomScheduleRepository.checkScheduledByTime(
              startTime.toLocalTime(),
              endTime.toLocalTime(),
              request.getDayOfWeek().toString(),
              classroom.getId(),
              filteredTeachers);
      if (!entries.isEmpty()) {
        List<Teacher> conflictTeachers =
            new ArrayList<>(
                entries.stream()
                    .map(ClassroomSchedule::getClassroom)
                    .map(Classroom::getTeachers)
                    .flatMap(List::stream)
                    .toList());
        List<String> teacherNames = new ArrayList<>();
        List<Teacher> teacherDetails =
            teacherRepository.findAllById(
                new ArrayList<>(conflictTeachers.stream().map(Teacher::getId).toList()));
        teacherDetails.stream()
            .map(Teacher::getUserInfo)
            .forEach(user -> teacherNames.add(user.getFirstName() + "" + user.getLastName()));
        return teacherNames;
      }
    }

    return new ArrayList<>();
  }

  public ClassroomSchedule scheduleClassroom(
      Classroom classroom, ClassroomScheduleRequest classroomScheduleRequest, String orgSlug) {
    Optional<MeetingRoom> possibleMeetingRoom = Optional.empty();
    if (Objects.nonNull(classroomScheduleRequest.getMeetingRoomId())) {
      possibleMeetingRoom =
          meetingRoomRepository.findByIdAndOrgSlugIn(
              classroomScheduleRequest.getMeetingRoomId(),
              List.of(Constants.WEXL_INTERNAL, orgSlug));
      if (possibleMeetingRoom.isEmpty()) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST,
            "error.MeetingRoomFind.Org",
            new String[] {orgSlug});
      }
    }

    ClassroomSchedule classroomSchedule = new ClassroomSchedule();
    classroomSchedule.setClassroom(classroom);
    classroomSchedule.setTitle(classroomScheduleRequest.getTitle());
    classroomSchedule.setMeetingRoom(possibleMeetingRoom.orElse(null));
    classroomSchedule.setEndDate(
        dateTimeUtil.convertEpochToIso8601Legacy(classroomScheduleRequest.getExpiryDate()));
    classroomSchedule.setStartDate(
        dateTimeUtil.convertEpochToIso8601Legacy(classroomScheduleRequest.getStartDate()));
    classroomSchedule.setDayOfWeek(classroomScheduleRequest.getDayOfWeek());
    classroomSchedule.setMeetingStartTime(
        dateTimeUtil.convertEpochToOffsetTime(classroomScheduleRequest.getStartTime()));
    classroomSchedule.setMeetingEndTime(
        dateTimeUtil.convertEpochToOffsetTime(classroomScheduleRequest.getEndTime()));
    classroomSchedule.setClassroomScheduleInsts(
        buildClassroomScheduleInst(classroomScheduleRequest, classroomSchedule, orgSlug));
    classroomSchedule.setOrganization(organizationRepository.findBySlug(orgSlug));
    classroomSchedule.setOrgSlug(orgSlug);
    new ClassroomScheduleInst().setClassroomSchedule(classroomSchedule);
    return classroomSchedule;
  }

  private List<ClassroomScheduleInst> buildClassroomScheduleInst(
      ClassroomScheduleRequest classroomScheduleRequest,
      ClassroomSchedule classroomSchedule,
      String orgSlug) {
    final List<LocalDateTime> validDates =
        dateTimeUtil
            .getDatesBetween(classroomSchedule.getStartDate(), classroomSchedule.getEndDate())
            .stream()
            .filter(date -> date.getDayOfWeek() == classroomSchedule.getDayOfWeek())
            .toList();
    return validDates.stream()
        .map(
            date ->
                createClassroomScheduleInst(
                    classroomSchedule, classroomScheduleRequest, date, orgSlug))
        .toList();
  }

  private ClassroomScheduleInst createClassroomScheduleInst(
      ClassroomSchedule classroomSchedule,
      ClassroomScheduleRequest classroomScheduleRequest,
      LocalDateTime date,
      String orgSlug) {
    ClassroomScheduleInst classroomScheduleInst = new ClassroomScheduleInst();
    classroomScheduleInst.setClassroomSchedule(classroomSchedule);
    classroomScheduleInst.setDayOfWeek(classroomScheduleRequest.getDayOfWeek());
    classroomScheduleInst.setTitle(classroomScheduleRequest.getTitle());
    classroomScheduleInst.setOrgSlug(orgSlug);
    classroomScheduleInst.setStartTime(
        dateTimeUtil.updateToNewTime(date, classroomSchedule.getMeetingStartTime()));
    classroomScheduleInst.setEndTime(
        dateTimeUtil.updateToNewTime(date, classroomSchedule.getMeetingEndTime()));
    classroomScheduleInst.setTutors(getTutors(classroomSchedule.getClassroom().getTeachers()));
    return classroomScheduleInst;
  }

  public ClassRoomDto.Tutors getTutors(List<Teacher> teachers) {
    return ClassRoomDto.Tutors.builder()
        .tutorIds(teachers.stream().map(Teacher::getUserInfo).map(User::getId).toList())
        .build();
  }

  @Transactional
  public void editScheduleClassroom(
      String orgSlug,
      long scheduleId,
      ClassroomScheduleUpdateRequest classroomScheduleUpdateRequest) {

    var classroomSchedule = classroomScheduleRepository.findById(scheduleId).orElse(null);

    if (Objects.isNull(classroomSchedule)) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.ClassroomScheduleValidity.Org",
          new String[] {orgSlug});
    }
    if (Objects.nonNull(classroomScheduleUpdateRequest.getExpiryDate())) {
      updateScheduleClassroomByExpiryDate(
          dateTimeUtil.convertEpochToIso8601Legacy(classroomScheduleUpdateRequest.getExpiryDate()),
          classroomSchedule,
          orgSlug);
    }
    updateInstMeetingRoom(classroomScheduleUpdateRequest, classroomSchedule);
  }

  private void updateScheduleClassroomByExpiryDate(
      LocalDateTime latestDate, ClassroomSchedule classroomSchedule, String orgSlug) {
    if (classroomSchedule.getEndDate().isBefore(latestDate)) {
      scheduleClassroomInstsByDate(classroomSchedule, latestDate, orgSlug);
    } else {
      removescheduleClassroomInstsByDate(classroomSchedule, latestDate);
    }
  }

  private void updateInstMeetingRoom(
      ClassroomScheduleUpdateRequest updateRequest, ClassroomSchedule classroomSchedule) {
    var scheduleInstRequest = updateRequest.getScheduleInstRequest();
    Map<Long, ClassRoomDto.ScheduleInstRequest> requestMap;
    if (Objects.nonNull(scheduleInstRequest) && !scheduleInstRequest.isEmpty()) {
      requestMap =
          scheduleInstRequest.stream()
              .collect(
                  Collectors.toMap(
                      ClassRoomDto.ScheduleInstRequest::scheduleInstId, Function.identity()));
    } else {
      requestMap = new HashMap<>();
    }
    boolean shouldUpdateTiming;
    if (Objects.nonNull(updateRequest.getStartTime())
        && Objects.nonNull(updateRequest.getEndTime())) {
      classroomSchedule.setMeetingStartTime(
          dateTimeUtil.convertEpochToOffsetTime(updateRequest.getStartTime()));
      classroomSchedule.setMeetingEndTime(
          dateTimeUtil.convertEpochToOffsetTime(updateRequest.getEndTime()));
      classroomSchedule.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
      shouldUpdateTiming = true;
    } else {
      shouldUpdateTiming = false;
    }
    classroomSchedule.getClassroomScheduleInsts().stream()
        .filter(inst -> LocalDateTime.now().isBefore(inst.getStartTime()))
        .forEach(
            inst -> {
              if (shouldUpdateTiming) {
                inst.setStartTime(
                    dateTimeUtil.updateToNewTime(
                        inst.getStartTime(), classroomSchedule.getMeetingStartTime()));
                inst.setEndTime(
                    dateTimeUtil.updateToNewTime(
                        inst.getEndTime(), classroomSchedule.getMeetingEndTime()));
                inst.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
              }
              var instRequest = requestMap.get(inst.getId());
              if (Objects.nonNull(instRequest)) {
                inst.setMeetingRoom(
                    meetingRoomRepository.findById(instRequest.meetingRoomId()).orElse(null));
                inst.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
              }
            });
    classroomScheduleRepository.save(classroomSchedule);
  }

  private void scheduleClassroomInstsByDate(
      ClassroomSchedule classroomSchedule, LocalDateTime expiryDate, String orgSlug) {

    var classroomScheduleRequest =
        ClassroomScheduleRequest.builder()
            .title(classroomSchedule.getTitle())
            .meetingRoomId(
                Objects.nonNull(classroomSchedule.getMeetingRoom())
                    ? classroomSchedule.getMeetingRoom().getId()
                    : null)
            .startTime(dateTimeUtil.getOffSetTimeToEpoch(classroomSchedule.getMeetingStartTime()))
            .endTime(dateTimeUtil.getOffSetTimeToEpoch(classroomSchedule.getMeetingEndTime()))
            .dayOfWeek(classroomSchedule.getDayOfWeek())
            .startDate(convertIso8601ToEpoch(classroomSchedule.getStartDate()))
            .expiryDate(convertIso8601ToEpoch(classroomSchedule.getEndDate()))
            .build();

    classroomSchedule.setStartDate(classroomSchedule.getEndDate().plusDays(1));
    classroomSchedule.setEndDate(expiryDate);
    classroomSchedule
        .getClassroomScheduleInsts()
        .addAll(buildClassroomScheduleInst(classroomScheduleRequest, classroomSchedule, orgSlug));
    classroomSchedule.setStartDate(
        dateTimeUtil.convertEpochToIso8601Legacy(classroomScheduleRequest.getStartDate()));
    classroomSchedule.setClassroomScheduleInsts(classroomSchedule.getClassroomScheduleInsts());
    classroomScheduleRepository.save(classroomSchedule);
  }

  private void removescheduleClassroomInstsByDate(
      ClassroomSchedule classroomSchedule, LocalDateTime latestDate) {
    List<ClassroomScheduleInst> removableInsts =
        classroomSchedule.getClassroomScheduleInsts().stream()
            .filter(insts -> latestDate.isBefore(insts.getEndTime()))
            .toList();
    classroomSchedule.getClassroomScheduleInsts().removeAll(removableInsts);
    classroomSchedule.setEndDate(latestDate);
    classroomScheduleRepository.save(classroomSchedule);
  }

  public List<ClassroomScheduleResponse> buildScheduleResponses(
      List<ClassroomSchedule> classroomSchedules) {
    classroomSchedules.sort(Comparator.comparing(ClassroomSchedule::getEndDate).reversed());
    return classroomSchedules.stream()
        .map(
            schedule ->
                ClassroomScheduleResponse.builder()
                    .id(schedule.getId())
                    .title(schedule.getTitle())
                    .meetingRoomId(
                        Objects.nonNull(schedule.getMeetingRoom())
                            ? schedule.getMeetingRoom().getId()
                            : null)
                    .meetingRoomName(
                        Objects.nonNull(schedule.getMeetingRoom())
                            ? schedule.getMeetingRoom().getName()
                            : null)
                    .meetingRoomLink(
                        Objects.nonNull(schedule.getMeetingRoom())
                            ? schedule.getMeetingRoom().getHostLink()
                            : null)
                    .startDate(convertIso8601ToEpoch(schedule.getStartDate()))
                    .endDate(convertIso8601ToEpoch(schedule.getEndDate()))
                    .dayOfWeek(schedule.getDayOfWeek().name())
                    .meetingStartTime(
                        dateTimeUtil.getOffSetTimeToEpoch(schedule.getMeetingStartTime()))
                    .meetingEndTime(dateTimeUtil.getOffSetTimeToEpoch(schedule.getMeetingEndTime()))
                    .orgSlug(schedule.getOrgSlug())
                    .build())
        .toList();
  }

  private List<String> findBusyStudents(
      String orgSlug,
      Classroom classroom,
      List<ClassroomScheduleRequest> classroomScheduleRequests) {
    List<ClassroomSchedule> classroomSchedule =
        classroomScheduleRepository.findByOrgSlugAndDeletedAtIsNull(orgSlug);

    var studentIds = classroom.getStudents().stream().map(Student::getId).toList();
    var activeSchedules =
        new ArrayList<>(
            classroomSchedule.stream()
                .filter(schedule -> schedule.getEndDate().isAfter(LocalDateTime.now()))
                .toList());
    List<Student> scheduledStudents =
        new ArrayList<>(
            activeSchedules.stream()
                .map(ClassroomSchedule::getClassroom)
                .map(Classroom::getStudents)
                .flatMap(List::stream)
                .toList());
    var scheduledStudentIds =
        new ArrayList<>(scheduledStudents.stream().map(Student::getId).toList());

    var filteredStudents =
        new ArrayList<>(studentIds.stream().filter(scheduledStudentIds::contains).toList());

    List<ClassroomSchedule> entries;
    for (var request : classroomScheduleRequests) {
      var startTime = dateTimeUtil.convertEpochToIso8601Legacy(request.getStartTime());

      var endTime = dateTimeUtil.convertEpochToIso8601Legacy(request.getEndTime());
      entries =
          classroomScheduleRepository.checkScheduledByTimeForStudents(
              startTime.toLocalTime(),
              endTime.toLocalTime(),
              request.getDayOfWeek().toString(),
              filteredStudents);
      if (!entries.isEmpty()) {
        List<Student> conflictStudents =
            new ArrayList<>(
                entries.stream()
                    .map(ClassroomSchedule::getClassroom)
                    .map(Classroom::getStudents)
                    .flatMap(List::stream)
                    .toList());
        List<String> studentNames = new ArrayList<>();
        List<Student> studentDetails =
            studentRepository.findAllById(
                new ArrayList<>(conflictStudents.stream().map(Student::getId).toList()));
        studentDetails.stream()
            .map(Student::getUserInfo)
            .forEach(user -> studentNames.add(user.getFirstName() + "" + user.getLastName()));
        return studentNames;
      }
    }
    return new ArrayList<>();
  }
}
