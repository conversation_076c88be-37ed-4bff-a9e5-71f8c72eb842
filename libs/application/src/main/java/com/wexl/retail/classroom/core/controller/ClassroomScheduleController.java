package com.wexl.retail.classroom.core.controller;

import com.wexl.retail.classroom.core.dto.ClassroomScheduleInstResponse;
import com.wexl.retail.classroom.core.dto.ClassroomScheduleRequest;
import com.wexl.retail.classroom.core.dto.ClassroomScheduleUpdateRequest;
import com.wexl.retail.classroom.core.service.ClassroomScheduleInstService;
import com.wexl.retail.classroom.core.service.ClassroomScheduleService;
import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}")
public class ClassroomScheduleController {

  private final ClassroomScheduleService classrromScheduleService;
  private final ClassroomScheduleInstService classroomScheduleInstService;

  @IsOrgAdmin
  @ResponseStatus(HttpStatus.CREATED)
  @PostMapping("/classrooms/{classroomId}/schedules")
  public void scheduleClassroom(
      @PathVariable String orgSlug,
      @PathVariable long classroomId,
      @RequestBody List<ClassroomScheduleRequest> classroomScheduleRequests,
      @RequestParam(required = false, defaultValue = "false") boolean allowConflict) {
    classrromScheduleService.scheduleClassroom(
        orgSlug, classroomId, classroomScheduleRequests, allowConflict);
  }

  @IsOrgAdmin
  @PostMapping("/classrooms/{classroomId}/schedules/{scheduledId}")
  public void editScheduledClassroom(
      @PathVariable String orgSlug,
      @PathVariable("scheduledId") long scheduleId,
      @RequestBody ClassroomScheduleUpdateRequest classroomScheduleUpdateRequest) {

    classrromScheduleService.editScheduleClassroom(
        orgSlug, scheduleId, classroomScheduleUpdateRequest);
  }

  @IsOrgAdmin
  @GetMapping("/schedules/{scheduledId}/schedule-insts")
  public List<ClassroomScheduleInstResponse> getScheduleInsts(
      @PathVariable String orgSlug, @PathVariable("scheduledId") long scheduleId) {
    return classroomScheduleInstService.getScheduleInsts(orgSlug, scheduleId);
  }
}
