package com.wexl.retail.metrics;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.sql.Timestamp;

public interface WexlTestData {

  @JsonProperty("name")
  public String getName();

  @JsonProperty("date")
  Timestamp getDate();

  @JsonProperty("subject")
  String getSubject();

  @JsonProperty("chapter")
  String getChapter();

  @JsonProperty("no_of_questions")
  public Integer getNoOfQuestion();

  @JsonProperty("marksScored")
  public Integer getMarksScored();

  @JsonProperty("totalMarks")
  Integer getTotalMarks();

  @JsonProperty("percentage")
  Double getPercentage();

  @JsonProperty("gradeName")
  String getGradeName();
}
