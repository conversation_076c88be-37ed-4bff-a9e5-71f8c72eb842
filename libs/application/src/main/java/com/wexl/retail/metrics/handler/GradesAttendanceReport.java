package com.wexl.retail.metrics.handler;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class GradesAttendanceReport extends AbstractMetricHandler implements MetricHandler {

  @Override
  public String name() {
    return "attendance-by-grade";
  }

  @Override
  public List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {

    List<String> gradeList =
        Optional.ofNullable(genericMetricRequest.getInput().get(GRADE))
            .map(List.class::cast)
            .orElse(Collections.emptyList());
    String sessionType =
        Optional.ofNullable(genericMetricRequest.getInput().get(SESSION_TYPE))
            .map(Object::toString)
            .orElse(null);
    return erpAttendanceService.getGradeWiseAttendanceReport(
        org, genericMetricRequest.getTimePeriod(), gradeList, sessionType);
  }
}
