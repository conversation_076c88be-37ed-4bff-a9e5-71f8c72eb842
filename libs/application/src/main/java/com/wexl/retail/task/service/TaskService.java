package com.wexl.retail.task.service;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;
import static com.wexl.retail.task.domain.TaskStatus.*;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.AuthUtil;
import com.wexl.retail.classroom.core.dto.ClassroomMeetingStatus;
import com.wexl.retail.classroom.core.dto.TaskInstResponse;
import com.wexl.retail.classroom.core.dto.TaskResponse;
import com.wexl.retail.classroom.core.model.Classroom;
import com.wexl.retail.classroom.core.model.ClassroomSchedule;
import com.wexl.retail.classroom.core.model.ClassroomScheduleInst;
import com.wexl.retail.classroom.core.repository.ClassroomRepository;
import com.wexl.retail.classroom.core.repository.ClassroomScheduleInstRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.model.QuestionResponse;
import com.wexl.retail.mlp.service.MlpService;
import com.wexl.retail.mlp.service.VideoUrlEncryptor;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.ExamResponse;
import com.wexl.retail.student.exam.ExamService;
import com.wexl.retail.student.exam.school.SchoolExamService;
import com.wexl.retail.task.domain.Task;
import com.wexl.retail.task.domain.TaskInst;
import com.wexl.retail.task.domain.TaskSource;
import com.wexl.retail.task.domain.TaskStatus;
import com.wexl.retail.task.domain.TaskType;
import com.wexl.retail.task.dto.StudentTasksResponse;
import com.wexl.retail.task.dto.TaskActivityResponse;
import com.wexl.retail.task.dto.TaskRequest;
import com.wexl.retail.task.mapper.StudentTasksResponseMapper;
import com.wexl.retail.task.repository.TaskInstRepository;
import com.wexl.retail.task.repository.TaskRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.StrapiService;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class TaskService {

  private final TaskRepository taskRepository;

  private final TaskInstRepository taskInstRepository;

  private final ClassroomRepository classroomRepository;

  private final ClassroomScheduleInstRepository classroomScheduleInstRepository;

  private final StudentRepository studentRepository;

  private final AuthService authService;

  private final DateTimeUtil dateTimeUtil;

  private final UserService userService;

  private final UserRepository userRepository;
  private final ExamService examService;

  private final SchoolExamService schoolExamService;

  private final MlpService mlpService;

  private final StudentTasksResponseMapper studentTasksResponseMapper;
  private final TestDefinitionRepository testDefinitionRepository;
  private final VideoUrlEncryptor urlEncryptor;
  private final TeacherRepository teacherRepository;
  private final StorageService storageService;

  private final StrapiService strapiService;

  @Value("${app.defaultTaskInstStatus:false}")
  private String defaultTaskInstStatus;

  public List<Task> createTaskByRequests(List<TaskRequest> taskRequest, String orgSlug) {

    List<Task> tasks = new ArrayList<>();

    taskRequest.forEach(
        request -> {
          if (TaskType.ASSIGNMENT.name().equals(request.getTaskType())) {
            request
                .getAssignmentIds()
                .forEach(id -> tasks.add(buildTask(request, orgSlug, assignmentDetails(id))));
          } else if (TaskType.SCHOOLTEST.name().equals(request.getTaskType())) {
            request
                .getSchoolTestIds()
                .forEach(id -> tasks.add(buildTask(request, orgSlug, assignmentDetails(id))));
          } else {
            tasks.add(buildTask(request, orgSlug, null));
          }
        });
    return tasks;
  }

  private Task buildTask(TaskRequest request, String orgSlug, TestDefinition testDefinition) {
    Task task = new Task();
    task.setName(request.getName());
    task.setTestDefinition(testDefinition);
    task.setDescription(request.getDescription());
    task.setTaskType(TaskType.valueOf(request.getTaskType()));
    task.setTaskSource(TaskSource.CLASSROOM_ACTIVITY);
    task.setTeacher(authService.getTeacherDetails().getTeacherInfo());
    task.setClassroomScheduleInst(request.getClassroomScheduleInst());
    task.setTaskInsts(createTaskInsts(task, request));
    task.setStatus(Boolean.TRUE);
    task.setDueDate(dateTimeUtil.convertEpochToIso8601Legacy(request.getDueDate()));
    task.setChapterId(
        Objects.nonNull(request.getChapterDetail().getId())
            ? request.getChapterDetail().getId()
            : null);
    task.setChapterSlug(request.getChapterDetail().getSlug());
    task.setChapterName(request.getChapterDetail().getName());
    if (Objects.nonNull(request.getSubjectDetail())) {
      task.setSubjectId(request.getSubjectDetail().getId());
      task.setSubjectSlug(request.getSubjectDetail().getSlug());
      task.setSubjectName(request.getSubjectDetail().getName());
    }
    task.setSubtopicId(
        Objects.nonNull(request.getSubtopicDetail()) ? request.getSubtopicDetail().getId() : null);
    task.setSubtopicSlug(
        Objects.nonNull(request.getSubtopicDetail())
            ? request.getSubtopicDetail().getSlug()
            : null);
    task.setSubtopicName(
        Objects.nonNull(request.getSubtopicDetail())
            ? request.getSubtopicDetail().getName()
            : null);
    if (Objects.nonNull(request.getVideoDetails())) {
      task.setVideoSlug(request.getVideoDetails().getSlug());
      task.setAltVimeoLink(
          Objects.nonNull(request.getVideoDetails().getAltVimeoLink())
              ? urlEncryptor.convertPlain(request.getVideoDetails().getAltVimeoLink())
              : null);
      task.setVideoSource(request.getVideoDetails().getSource());
      task.setVideoTitle(request.getVideoDetails().getName());
    }
    task.setSynopsisSlug(
        Objects.nonNull(request.getSynopsisDetails())
            ? request.getSynopsisDetails().getSlug()
            : null);
    task.setSynopsisTitle(
        Objects.nonNull(request.getSynopsisDetails())
            ? request.getSynopsisDetails().getName()
            : null);
    task.setQuestionUuids(
        Objects.nonNull(request.getQuestionUuids()) ? request.getQuestionUuids() : null);
    task.setQuestionCount(
        Objects.nonNull(request.getQuestionCount()) ? request.getQuestionCount() : null);
    task.setQuestionsAssigneeMode(request.getQuestionsAssigneeMode());
    task.setOrgSlug(orgSlug);
    task.setBoardSlug(request.getBoardSlug());
    task.setGradeSlug(request.getGradeSlug());
    task.setGradeName(request.getGradeName());
    task.setBoardName(request.getBoardName());
    return task;
  }

  private TestDefinition assignmentDetails(Long assignmentId) {
    Optional<TestDefinition> testDefinition = testDefinitionRepository.findById(assignmentId);
    if (testDefinition.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.ClassroomValidity.Org");
    }
    return testDefinition.get();
  }

  private List<TaskInst> createTaskInsts(Task task, TaskRequest taskRequest) {

    List<TaskInst> taskInsts = new ArrayList<>();
    task.setTaskInsts(taskInsts);
    buildStudents(taskRequest)
        .forEach(
            student -> {
              TaskInst taskInst = new TaskInst();
              taskInst.setTask(task);
              taskInst.setStudent(student);
              taskInst.setCompletionStatus(getTaskInstStatus(taskRequest));
              taskInst
                  .getTask()
                  .getTaskInsts()
                  .forEach(ti -> updateTaskCompletionStatus(ti, taskRequest));
              taskInsts.add(taskInst);
            });
    return taskInsts;
  }

  private TaskStatus getTaskInstStatus(TaskRequest taskRequest) {
    if (ClassroomMeetingStatus.COMPLETED
            .name()
            .equals(taskRequest.getClassroomScheduleInst().getStatus().name())
        || Boolean.parseBoolean(defaultTaskInstStatus)) {
      return isRevision(taskRequest) ? COMPLETED : NOT_STARTED;
    }
    return DRAFT;
  }

  private boolean isRevision(TaskRequest taskRequest) {
    return TaskType.REVISION.name().equals(taskRequest.getTaskType());
  }

  public void updateTaskCompletionStatus(TaskInst taskInsts, TaskRequest taskRequest) {
    if (isRevision(taskRequest)) {
      handleRevisionTask(taskInsts, taskRequest);
    } else {
      handleOtherTasks(taskInsts, taskRequest);
    }
  }

  private void handleOtherTasks(TaskInst taskInsts, TaskRequest taskRequest) {
    if (ClassroomMeetingStatus.COMPLETED.equals(taskRequest.getClassroomScheduleInst().getStatus())
        || Boolean.parseBoolean(defaultTaskInstStatus)) {
      taskInsts.setCompletionStatus(NOT_STARTED);
    } else {
      taskInsts.setCompletionStatus(DRAFT);
    }
  }

  private void handleRevisionTask(TaskInst taskInsts, TaskRequest taskRequest) {
    if (ClassroomMeetingStatus.COMPLETED.equals(taskRequest.getClassroomScheduleInst().getStatus())
        || Boolean.parseBoolean(defaultTaskInstStatus)) {
      taskInsts.setCompletionStatus(COMPLETED);
    } else {
      taskInsts.setCompletionStatus(DRAFT);
    }
  }

  private List<Student> buildStudents(TaskRequest taskRequest) {
    Classroom classroom =
        classroomRepository
            .findById(taskRequest.getClassroomId())
            .orElseThrow(
                () ->
                    new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidClassroom"));
    if (Objects.isNull(taskRequest.getStudentIds())) {
      return classroom.getStudents();
    }
    var studentIds =
        classroom.getStudents().stream()
            .map(Student::getId)
            .filter(id -> taskRequest.getStudentIds().contains(id))
            .toList();
    return studentRepository.findAllById(studentIds);
  }

  public TaskResponse getTaskInstResponses(
      String orgSlug,
      Long classroomId,
      Long scheduleInstId,
      Long fromEpochDate,
      Long toEpochDate,
      int limit) {

    if (Objects.nonNull(scheduleInstId)) {
      return getTaskInstResponsesByScheduleInstId(scheduleInstId, orgSlug, limit);
    }
    if (Objects.isNull(classroomId)) {
      return getTaskInstResponseByAllClassrooms(orgSlug, fromEpochDate, toEpochDate, limit);
    }

    Classroom classroom =
        classroomRepository
            .findByIdAndOrgSlug(classroomId, orgSlug)
            .orElseThrow(
                () ->
                    new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidClassroom"));

    var scheduleInsts =
        classroom.getSchedules().stream()
            .map(ClassroomSchedule::getClassroomScheduleInsts)
            .flatMap(List::stream)
            .toList();
    if (scheduleInsts.isEmpty()) {
      return null;
    }

    return getTaskInstResponses(
        getFilteredIntsByDates(scheduleInsts, fromEpochDate, toEpochDate), limit);
  }

  private TaskResponse getTaskInstResponsesByScheduleInstId(
      Long scheduleInstId, String orgSlug, int limit) {

    var classroomScheduleInst =
        classroomScheduleInstRepository
            .findById(scheduleInstId)
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST,
                        "error.ClassroomScheduleValidity.Org",
                        new String[] {orgSlug}));
    return getTaskInstResponses(List.of(classroomScheduleInst), limit);
  }

  private List<ClassroomScheduleInst> getFilteredIntsByDates(
      List<ClassroomScheduleInst> scheduleInsts, Long fromEpochDate, Long toEpochDate) {

    if (Objects.isNull(fromEpochDate) && Objects.isNull(toEpochDate)) {
      return scheduleInsts.stream().toList();
    }
    LocalDate fromDate = dateTimeUtil.convertEpochToIso8601Legacy(fromEpochDate).toLocalDate();
    LocalDate toDate = dateTimeUtil.convertEpochToIso8601Legacy(toEpochDate).toLocalDate();

    return scheduleInsts.stream()
        .filter(inst -> inst.getEndTime().toLocalDate().isAfter(fromDate))
        .filter(inst -> inst.getEndTime().toLocalDate().isBefore(toDate))
        .toList();
  }

  private TaskResponse getTaskInstResponses(List<ClassroomScheduleInst> scheduleInsts, int limit) {

    var tasks =
        scheduleInsts.stream().map(ClassroomScheduleInst::getTasks).flatMap(List::stream).toList();

    var taskInsts =
        tasks.stream().map(Task::getTaskInsts).flatMap(List::stream).toList().stream()
            .filter(s -> s.getDeletedAt() == null)
            .toList();

    return TaskResponse.builder()
        .taskCount(((long) taskInsts.size()))
        .attemptedCount(
            taskInsts.stream()
                .filter(
                    taskInst ->
                        TaskStatus.COMPLETED.name().equals(taskInst.getCompletionStatus().name()))
                .count())
        .notAttemptedCount(
            taskInsts.stream()
                .filter(
                    taskInst ->
                        !TaskStatus.COMPLETED.name().equals(taskInst.getCompletionStatus().name()))
                .count())
        .practiceActivityCount(
            taskInsts.stream()
                .filter(
                    taskInst ->
                        TaskType.PRACTICE.name().equals(taskInst.getTask().getTaskType().name()))
                .count())
        .taskInstResponses(buildTaskInstResponses(taskInsts, limit))
        .build();
  }

  private List<TaskInstResponse> buildTaskInstResponses(List<TaskInst> taskInsts, int limit) {
    return taskInsts.stream()
        .map(
            inst ->
                TaskInstResponse.builder()
                    .taskInstId(inst.getId())
                    .studentName(userService.getNameByUserInfo(inst.getStudent().getUserInfo()))
                    .studentId(inst.getStudent().getId())
                    .classroomName(
                        inst.getTask()
                            .getClassroomScheduleInst()
                            .getClassroomSchedule()
                            .getClassroom()
                            .getName())
                    .gradeName(
                        Objects.nonNull(inst.getStudent().getSection().getGradeName())
                            ? inst.getStudent().getSection().getGradeName()
                            : null)
                    .taskType(inst.getTask().getTaskType().name())
                    .scheduledDate(
                        convertIso8601ToEpoch(
                            inst.getTask().getClassroomScheduleInst().getStartTime()))
                    .status(inst.getCompletionStatus().name())
                    .subject(inst.getTask().getSubjectName())
                    .chapter(inst.getTask().getChapterName())
                    .examId(inst.getExam() == null ? 0 : inst.getExam().getId())
                    .activityName(inst.getTask().getName())
                    .boardName(inst.getTask().getBoardName())
                    .boardSlug(inst.getTask().getBoardSlug())
                    .subtopicSlug(inst.getTask().getSubtopicSlug())
                    .subtopicName(inst.getTask().getSubtopicName())
                    .build())
        .sorted(Comparator.comparing(TaskInstResponse::getStatus))
        .limit(limit)
        .toList();
  }

  private TaskResponse getTaskInstResponseByAllClassrooms(
      String orgSlug, Long fromDate, Long toDate, int limit) {

    var classrooms = classroomRepository.findByOrgSlugAndDeletedAtIsNull(orgSlug);

    var scheduleInsts =
        classrooms.stream()
            .map(Classroom::getSchedules)
            .flatMap(List::stream)
            .map(ClassroomSchedule::getClassroomScheduleInsts)
            .flatMap(List::stream)
            .toList();

    if (scheduleInsts.isEmpty()) {
      return null;
    }

    return getTaskInstResponses(getFilteredIntsByDates(scheduleInsts, fromDate, toDate), limit);
  }

  public Task getTaskByIdAndOrg(Long taskId, String orgSlug) {
    Optional<Task> optionalTask = taskRepository.findByIdAndOrgSlug(taskId, orgSlug);

    if (optionalTask.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.Organization.Task", new String[] {orgSlug});
    }
    return optionalTask.get();
  }

  public ExamResponse startTaskExam(String orgSlug, Long taskId) {
    final Task task = getTaskByIdAndOrg(taskId, orgSlug);
    if (TaskType.ASSIGNMENT.name().equals(task.getTaskType().name())
        || TaskType.SCHOOLTEST.name().equals(task.getTaskType().name())) {
      return schoolExamService.createDirectAssignment(
          task.getTestDefinition().getId(), taskId, task.getTaskType().name());
    } else if (TaskType.PRACTICE.name().equals(task.getTaskType().name())) {
      return examService.createTaskPractice(task);
    } else {
      return examService.createTaskTestExam(task);
    }
  }

  public List<StudentTasksResponse> getStudentTasks(Long studentId, String status, Integer limit) {

    var taskInst = taskInstRepository.getStudentActivityByCompletionStatus(studentId);

    if (taskInst.isEmpty()) {
      return Collections.emptyList();
    }
    List<TaskInst> validTasks;

    taskInst.stream()
        .filter(
            ti ->
                TaskType.VIDEO.equals(ti.getTask().getTaskType())
                    && "S3".equals(ti.getTask().getVideoSource()))
        .forEach(
            ti ->
                ti.getTask()
                    .setVideoSlug(
                        storageService.generatePreSignedUrlForFetch(ti.getTask().getVideoSlug())));

    if (TaskStatus.COMPLETED.name().equals(status)) {
      validTasks =
          taskInst.stream()
              .filter(inst -> TaskStatus.COMPLETED.name().equals(inst.getCompletionStatus().name()))
              .filter(inst -> Objects.isNull(inst.getDeletedAt()))
              .toList();
      return validTasks.stream()
          .map(studentTasksResponseMapper::taskInstToStudentTasksResponse)
          .limit(limit)
          .toList();
    }
    if (Constants.PENDING_STATUS.equals(status)) {
      validTasks =
          taskInst.stream()
              .filter(
                  inst -> !TaskStatus.COMPLETED.name().equals(inst.getCompletionStatus().name()))
              .filter(inst -> Objects.isNull(inst.getDeletedAt()))
              .toList();
      return validTasks.stream()
          .map(studentTasksResponseMapper::taskInstToStudentTasksResponse)
          .limit(limit)
          .toList();
    }

    return taskInst.stream()
        .filter(inst -> Objects.isNull(inst.getDeletedAt()))
        .map(studentTasksResponseMapper::taskInstToStudentTasksResponse)
        .filter(inst -> !(TaskStatus.DRAFT.name().equals(inst.getStatus())))
        .limit(limit)
        .toList();
  }

  public void completeTaskInst(Long taskInstId) {
    Optional<TaskInst> optionalTaskInst = taskInstRepository.findById(taskInstId);
    if (optionalTaskInst.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.couldNotFindTaskInst");
    }

    final var taskInst = optionalTaskInst.get();

    taskInst.setCompletedAt(LocalDateTime.now());
    taskInst.setCompletionStatus(getTaskStatus(taskInst));
    taskInstRepository.save(taskInst);
  }

  private TaskStatus getTaskStatus(TaskInst taskInst) {
    if (TaskType.ASSIGNMENT.name().equals(taskInst.getTask().getTaskType().name())) {
      return TaskStatus.PENDING;
    }
    return TaskStatus.COMPLETED;
  }

  public QuestionResponse getTaskQuestions(String orgSlug, Long examId, String bearerToken) {

    Exam exam = examService.findById(examId);
    if (Objects.isNull(exam.getTaskId())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.TaskNotFound");
    }
    Task task =
        taskRepository
            .findByIdAndOrgSlug(exam.getTaskId(), orgSlug)
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST,
                        "error.InvalidTask",
                        new String[] {orgSlug}));
    if (task.getQuestionUuids().isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.QuestionsFind.Task",
          new String[] {task.getName()});
    }

    return mlpService.getQuestionsByUuidsAndSubjectSlug(
        task.getQuestionUuids(), task.getSubjectSlug(), bearerToken, Boolean.FALSE, Boolean.FALSE);
  }

  public List<Long> getActivityDates(String studentAuthId, int limit) {
    User user = userRepository.getUserByAuthUserId(studentAuthId);
    Student student = studentRepository.findByUserId(user.getId());

    var activityDates = taskRepository.getStudentActivityDates(student.getId(), limit);
    if (activityDates.isEmpty()) {
      new ArrayList<>();
    }
    return activityDates.stream().map(DateTimeUtil::getEpochFromStringDate).toList();
  }

  public List<TaskActivityResponse> getTeacherTaskActivityResponse(
      String teacherAuthUserId,
      String orgSlug,
      Long startTimeInEpoch,
      Long endTimeInEpoch,
      Long studentId,
      String subject,
      String chapter,
      String topic,
      TaskStatus taskStatus,
      Integer limit) {
    var teacher =
        userRepository.findByAuthUserIdAndOrganization(teacherAuthUserId, orgSlug).getTeacherInfo();
    List<TaskInst> taskInsts = new ArrayList<>();
    if (AuthUtil.isOrgAdmin(teacher.getUserInfo())) {
      taskInsts =
          taskInstRepository.getPendingCorrections(
              orgSlug,
              dateTimeUtil.convertEpochToIso8601(startTimeInEpoch),
              dateTimeUtil.convertEpochToIso8601(endTimeInEpoch),
              studentId,
              subject,
              chapter,
              topic,
              Objects.nonNull(taskStatus) ? taskStatus.name() : null,
              limit);
    } else {
      var teacherPendingCorrections =
          taskInstRepository.getTeacherPendingCorrections(
              teacher.getId(),
              dateTimeUtil.convertEpochToIso8601(startTimeInEpoch),
              dateTimeUtil.convertEpochToIso8601(endTimeInEpoch),
              studentId,
              subject,
              chapter,
              topic,
              Objects.nonNull(taskStatus) ? taskStatus.name() : null,
              limit);
      var studentIdList = getTeacherStudents(teacher, studentId, orgSlug);
      var newStudentTasks =
          taskInstRepository.getStudentsTasksInsts(
              dateTimeUtil.convertEpochToIso8601(startTimeInEpoch),
              dateTimeUtil.convertEpochToIso8601(endTimeInEpoch),
              studentIdList,
              subject,
              chapter,
              topic,
              Objects.nonNull(taskStatus) ? taskStatus.name() : null,
              limit);
      taskInsts.addAll(teacherPendingCorrections);
      taskInsts.addAll(newStudentTasks);
    }

    Stream<TaskActivityResponse> taskStream =
        taskInsts.stream()
            .distinct()
            .filter(inst -> Objects.nonNull(inst.getStudent().getUserInfo()))
            .map(inst -> constructTaskActivityResponse(inst, inst.getTask()));

    if (limit != null) {
      taskStream = taskStream.limit(limit);
    }

    return taskStream.toList();
  }

  private List<Long> getTeacherStudents(Teacher teacher, Long studentId, String orgSlug) {
    List<Long> studentList = new ArrayList<>();
    if (studentId != null) {
      studentList.add(studentId);
    } else {
      List<Teacher> teacherList = new ArrayList<>();
      teacherList.add(teacher);
      var classroomList =
          classroomRepository.findByTeachersInAndOrgSlugAndDeletedAtIsNull(teacherList, orgSlug);
      classroomList.forEach(
          classroom ->
              studentList.addAll(classroom.getStudents().stream().map(Student::getId).toList()));
    }
    return studentList;
  }

  private TaskActivityResponse constructTaskActivityResponse(TaskInst taskInst, Task task) {
    final Student student = taskInst.getStudent();
    final User userInfo = student.getUserInfo();
    final Exam exam = taskInst.getExam();

    Long examId = 0L;
    Float marksScored = 0F;
    Float totalMarks = 0F;

    if (exam != null) {
      examId = exam.getId();
      marksScored = exam.getMarksScored();
      totalMarks = exam.getTotalMarks();
    }

    return TaskActivityResponse.builder()
        .activityType(task.getTaskType())
        .taskId(task.getId())
        .taskInstId(taskInst.getId())
        .chapterName(task.getChapterName())
        .chapterSlug(task.getChapterSlug())
        .subtopicSlug(task.getSubtopicSlug())
        .subtopicName(task.getSubtopicName())
        .subjectName(task.getSubjectName())
        .subjectSlug(task.getSubjectSlug())
        .taskStatus(taskInst.getCompletionStatus())
        .taskCompletedDate(
            Objects.nonNull(taskInst.getCompletedAt())
                ? dateTimeUtil.convertIso8601ToEpoch(taskInst.getCompletedAt())
                : null)
        .taskDate(
            DateTimeUtil.convertIso8601ToEpoch(task.getClassroomScheduleInst().getStartTime()))
        .studentAuthUserId(userInfo.getAuthUserId())
        .studentId(student.getId())
        .studentName(userInfo.getFirstName() + " " + userInfo.getLastName())
        .studentGrade(student.getSection().getGradeName())
        .classroomCode(
            task.getClassroomScheduleInst().getClassroomSchedule().getClassroom().getName())
        .boardSlug(task.getBoardSlug())
        .boardName(task.getBoardName())
        .gradeSlug(task.getGradeSlug())
        .gradeName(task.getGradeName())
        .examId(examId)
        .isExamCorrected(isCorrected(taskInst, exam))
        .marksScored(marksScored)
        .totalMarks(totalMarks)
        .synopsisSlug(task.getSynopsisSlug())
        .altVimeoLink(task.getAltVimeoLink())
        .videoSlug(task.getVideoSlug())
        .build();
  }

  private Boolean isCorrected(TaskInst taskInst, Exam exam) {
    if (List.of(TaskType.STUDYMATERIAL, TaskType.VIDEO, TaskType.REVISION)
        .contains(taskInst.getTask().getTaskType())) {
      return taskInst.getCompletionStatus().equals(TaskStatus.COMPLETED);
    }
    return Objects.nonNull(exam) && exam.isCorrected();
  }
}
