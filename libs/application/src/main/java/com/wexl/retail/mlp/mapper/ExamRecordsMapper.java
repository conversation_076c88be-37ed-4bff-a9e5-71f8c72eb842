package com.wexl.retail.mlp.mapper;

import com.wexl.retail.mlp.dto.ExamRecords;
import com.wexl.retail.mlp.dto.ExamRecordsQueryResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ExamRecordsMapper {
  ExamRecordsMapper mapper = Mappers.getMapper(ExamRecordsMapper.class);

  @Mapping(target = "mlp", source = "queryResult.title")
  @Mapping(target = "examId", source = "queryResult.examId")
  ExamRecords examRecordsQueryResultToDto(ExamRecordsQueryResult queryResult);
}
