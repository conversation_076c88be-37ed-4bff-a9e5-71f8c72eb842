package com.wexl.retail.classroom.core.model;

import com.wexl.retail.classroom.core.dto.ClassRoomDto;
import com.wexl.retail.model.Model;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.organization.model.Organization;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.hibernate.annotations.Type;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "classrooms")
public class Classroom extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String name;

  @ManyToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinTable(
      name = "classroom_teachers",
      joinColumns = @JoinColumn(name = "classroom_id"),
      inverseJoinColumns = @JoinColumn(name = "teacher_id"))
  private List<Teacher> teachers;

  @ManyToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinTable(
      name = "classroom_students",
      joinColumns = @JoinColumn(name = "classroom_id"),
      inverseJoinColumns = @JoinColumn(name = "student_id"))
  private List<Student> students;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "org_id")
  private Organization organization;

  @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinColumn(name = "classroom_id")
  private List<ClassroomSchedule> schedules;

  @ManyToOne
  @JoinColumn(name = "parent_classroom_id")
  private Classroom parent;

  @Column(name = "org_slug")
  private String orgSlug;

  @Type(JsonType.class)
  @Column(name = "extensions", columnDefinition = "jsonb")
  private ClassRoomDto.Extensions extensions;
}
