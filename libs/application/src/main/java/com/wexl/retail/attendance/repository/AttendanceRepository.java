package com.wexl.retail.attendance.repository;

import com.wexl.retail.attendance.domain.Attendance;
import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface AttendanceRepository extends JpaRepository<Attendance, Long> {

  @Query(
      """
      select a from Attendance a where a.sectionSchedule.id=:meetingId and \
      a.meetingStartTime=:startTime and a.meetingEndTime=:endTime\
      """)
  Optional<Attendance> getAttendanceByMeetingId(
      long meetingId, Timestamp startTime, Timestamp endTime);

  @Modifying
  @Transactional
  @Query(
      value =
          """
          update attendance \
                  set metadata = (\
                      select jsonb_insert(\
                          (select metadata from attendance where id=:attendanceId),\
                          cast('{students, -1}' as text[]),\
                          cast(:studentJsonString as jsonb),\
                          true)\
                      ) \
                  where id=:attendanceId and (\
                     select count(metadata.id) \
                     from attendance a, \
                       jsonb_to_recordset(a.metadata->'students') as metadata(id bigint) \
                     where metadata.id = :studentId and a.id = :attendanceId) = 0\
          """,
      nativeQuery = true)
  void insertStudenInfoToAttendanceRecord(
      @Param("studentId") long studentId,
      @Param("attendanceId") long attendanceId,
      @Param("studentJsonString") String studentJsonString);

  @Query(
      value =
          """
                      select a.*
                                   from attendance a
                                   inner join section_schedule ss on a.section_schedule_id = ss.id
                                   inner join teacher_sections ts on ts.section_id = ss.section_id
                                   where ts.teacher_id = :teacherId and a.created_at > :fromDate""",
      nativeQuery = true)
  List<Attendance> getAttendanceBySections(long teacherId, Timestamp fromDate);
}
