package com.wexl.retail.util;

import com.wexl.retail.errorslog.ErrorsLog;
import com.wexl.retail.errorslog.ErrorsLogRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class ErrorLogUtils {
  private final ErrorsLogRepository errorsLogRepository;

  public void logError(String authUserId, String comments) {
    ErrorsLog errorsLog = new ErrorsLog();
    errorsLog.setAuthUserId(authUserId);
    errorsLog.setComments(comments);
    errorsLogRepository.save(errorsLog);
  }
}
