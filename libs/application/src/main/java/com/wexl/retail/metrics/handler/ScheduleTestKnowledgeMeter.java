package com.wexl.retail.metrics.handler;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ScheduleTestKnowledgeMeter extends AbstractMetricHandler {
  @Override
  public String name() {
    return "test-knowledge-meter";
  }

  @Override
  protected List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {

    String board =
        Optional.ofNullable(genericMetricRequest.getInput().get(BOARD))
            .map(String.class::cast)
            .orElse(null);
    String grade =
        Optional.ofNullable(genericMetricRequest.getInput().get(GRADE))
            .map(String.class::cast)
            .orElse(null);
    String subject =
        Optional.ofNullable(genericMetricRequest.getInput().get(SUBJECT))
            .map(String.class::cast)
            .orElse(null);

    return dacKmService.getOrgWiseKnowledgeReport(org, board, grade, subject);
  }
}
