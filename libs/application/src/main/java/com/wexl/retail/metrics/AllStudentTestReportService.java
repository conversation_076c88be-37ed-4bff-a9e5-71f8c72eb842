package com.wexl.retail.metrics;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.ExamRepository;
import com.wexl.retail.student.exam.SectionWiseData;
import com.wexl.retail.student.exam.competitive.processor.CompetitiveExamValidatorProcessor;
import com.wexl.retail.test.schedule.domain.ScheduleTestStudent;
import com.wexl.retail.test.schedule.repository.ScheduleTestRepository;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import com.wexl.retail.test.schedule.service.ScheduleTestService;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class AllStudentTestReportService {
  private final ScheduleTestRepository scheduleTestRepository;
  private final ExamRepository examRepository;
  private final ScheduleTestStudentRepository scheduleTestStudentRepository;
  private final ScheduleTestService scheduleTestService;
  private final CompetitiveExamValidatorProcessor competitiveExamValidatorProcessor;

  public List<GenericMetricResponse> leaderBoardReportDetails(Long testScheduleId) {
    List<GenericMetricResponse> genericMetricResponses = new ArrayList<>();
    var scheduleTest = scheduleTestRepository.findById(testScheduleId);
    if (scheduleTest.isEmpty()) {
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.TestSchedule");
    }
    var childTestScheduleIds =
        scheduleTestRepository.findChildTestScheduleIds(scheduleTest.get().getId());

    List<Long> scheduleTestIds = new ArrayList<>(childTestScheduleIds);
    scheduleTestIds.add(scheduleTest.get().getId());
    List<ScheduleTestStudent> scheduledTestStudentsResponse =
        scheduleTestStudentRepository.getAllStudentsByScheduledIds(scheduleTestIds);
    List<StudentsTestData> studentsTestData =
        scheduledTestStudentsResponse.stream()
            .map(
                scheduleTestStudent -> {
                  Optional<Exam> exam =
                      examRepository.getExamDetails(
                          scheduleTestStudent.getStudent().getStudentInfo().getId(),
                          scheduleTestStudent.getScheduleTest().getId());
                  var studentTestData =
                      StudentsTestData.builder()
                          .testName(scheduleTest.get().getTestDefinition().getTestName())
                          .studentName(
                              scheduleTestStudent.getStudent().getFirstName()
                                  + " "
                                  + scheduleTestStudent.getStudent().getLastName())
                          .userId(scheduleTestStudent.getStudent().getId())
                          .schoolName(
                              scheduleTestStudent.getStudent().getStudentInfo().getSchoolName())
                          .authUserId(scheduleTestStudent.getStudent().getAuthUserId())
                          .build();
                  var testDef = scheduleTest.get().getTestDefinition();
                  if (exam.isPresent()) {
                    int totalMarks =
                        competitiveExamValidatorProcessor.getTotalMarks(
                            testDef.getCategory(), exam.get().getTotalMarks());
                    studentTestData.setSections(buildSectionsData(exam.get().getExamAttributes()));
                    studentTestData.setTotalMarks(totalMarks);
                    studentTestData.setSecuredMarks(
                        scheduleTestService.countMarksScoredForExam(exam.get().getExamAnswers()));
                    studentTestData.setExamId(exam.get().getId());
                  } else {
                    studentTestData.setSections(null);
                    studentTestData.setTotalMarks(0L);
                    studentTestData.setExamId(null);
                  }
                  return studentTestData;
                })
            .toList();
    List<StudentsTestData> sortedList =
        studentsTestData.stream()
            .sorted(Comparator.comparing(StudentsTestData::getSecuredMarks).reversed())
            .toList();

    for (StudentsTestData testData : sortedList) {
      Map<String, Object> dataMap = new HashMap<>();
      dataMap.put("test_name", testData.getTestName());
      dataMap.put("student_name", testData.getStudentName());
      dataMap.put("user_id", testData.getUserId());
      dataMap.put("auth_user_id", testData.getAuthUserId());
      dataMap.put("school_name", testData.getSchoolName());
      dataMap.put("total_marks", testData.getTotalMarks());
      dataMap.put("secured_marks", String.format("%.1f", testData.getSecuredMarks()));
      dataMap.put("exam_id", testData.getExamId());
      dataMap.put("sections", testData.getSections());
      genericMetricResponses.add(GenericMetricResponse.builder().data(dataMap).build());
    }
    return genericMetricResponses;
  }

  private List<SectionWiseData.SectionWiseTestData> buildSectionsData(
      SectionWiseData.ExamAttributes buildSectionsData) {

    return buildSectionsData.sectionWiseTestData().stream()
        .map(
            sectionWiseTestData1 ->
                SectionWiseData.SectionWiseTestData.builder()
                    .sectionName(sectionWiseTestData1.sectionName())
                    .sectionId(sectionWiseTestData1.sectionId())
                    .noOfQuestions(sectionWiseTestData1.noOfQuestions())
                    .totalMarks(sectionWiseTestData1.totalMarks())
                    .marksScored(
                        Float.valueOf(String.format("%.1f", sectionWiseTestData1.marksScored())))
                    .build())
        .toList();
  }
}
