package com.wexl.retail.test.competitive;

import com.wexl.retail.test.competitive.validators.TestDefinitionValidator;
import com.wexl.retail.test.schedule.domain.ScheduleTestStudent;
import com.wexl.retail.test.schedule.domain.TestScheduleStudentAnswer;
import com.wexl.retail.test.school.domain.TestCategory;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.domain.TestDefinitionSection;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class TestDefinitionValidatorProcessor {

  private final List<TestDefinitionValidator> validators;

  public void validate(TestDefinition testDefinition) {
    TestCategory category = testDefinition.getCategory();

    validators.stream()
        .filter(validator -> validator.supports(category))
        .forEach(validator -> validator.validate(testDefinition));
  }

  public List<TestScheduleStudentAnswer> processOptionalQuestions(
      ScheduleTestStudent tss,
      List<TestScheduleStudentAnswer> tssa,
      List<TestDefinitionSection> sections) {
    TestCategory category = tss.getScheduleTest().getTestDefinition().getCategory();
    List<TestScheduleStudentAnswer> tssaResponse = new ArrayList<>();
    validators.stream()
        .filter(validator -> validator.supports(category))
        .forEach(
            validator -> tssaResponse.addAll(validator.processOptionalQuestions(tssa, sections)));
    return tssaResponse;
  }
}
