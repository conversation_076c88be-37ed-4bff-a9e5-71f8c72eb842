package com.wexl.retail.section.listener;

import com.wexl.retail.model.Student;
import com.wexl.retail.offlinetest.service.OfflineTestScheduleService;
import com.wexl.retail.section.publisher.AssociateStudentSectionEvent;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Slf4j
@AllArgsConstructor
@Component
public class AssociateStudentSectionEventListener
    implements ApplicationListener<AssociateStudentSectionEvent> {

  private final OfflineTestScheduleService offlineTestScheduleService;

  @Override
  public void onApplicationEvent(AssociateStudentSectionEvent associateStudentSectionEvent) {
    Object source = associateStudentSectionEvent.getSource();
    if (source instanceof Student student) {
      offlineTestScheduleService.migrateAssociateTestScheduleStudent(student);
    }
  }
}
