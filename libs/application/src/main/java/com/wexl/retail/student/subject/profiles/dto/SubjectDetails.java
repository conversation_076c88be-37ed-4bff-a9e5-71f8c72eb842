package com.wexl.retail.student.subject.profiles.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class SubjectDetails {

  private Long id;

  @JsonProperty("board_name")
  private String boardName;

  @JsonProperty("grade_name")
  private String gradeName;

  @JsonProperty("subject_name")
  private String subjectName;

  @JsonProperty("source_org")
  private String sourceOrgName;

  @JsonProperty("display_name")
  private String displayName;
}
