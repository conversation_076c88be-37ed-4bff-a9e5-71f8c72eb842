package com.wexl.retail.student.answer;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.content.model.Question;
import java.util.Objects;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ExamQuestionPractice {
  private long questionId;
  private String questionUuid;
  private long examId;
  private int selectedAnswer;
  private boolean isCorrect;
  private Question questionDetails;
  private String type;
  private String answer;

  @JsonProperty("elaborate_answer")
  private String elaborateAnswer;

  public String getType() {
    if (Objects.isNull(type)) {
      return "mcq";
    }
    return type;
  }
}
