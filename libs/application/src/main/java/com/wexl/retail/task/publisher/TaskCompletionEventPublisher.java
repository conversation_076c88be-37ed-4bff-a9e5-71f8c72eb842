package com.wexl.retail.task.publisher;

import com.wexl.retail.student.exam.Exam;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class TaskCompletionEventPublisher {

  private final ApplicationEventPublisher applicationEventPublisher;

  public void publishTaskCompletion(final Exam exam) {
    TaskCompletionEvent taskCompletionEvent = new TaskCompletionEvent(exam);
    applicationEventPublisher.publishEvent(taskCompletionEvent);
  }
}
