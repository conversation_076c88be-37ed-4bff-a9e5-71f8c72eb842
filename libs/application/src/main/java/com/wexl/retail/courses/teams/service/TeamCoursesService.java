package com.wexl.retail.courses.teams.service;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.courses.definition.service.CourseDefinitionService;
import com.wexl.retail.courses.enrollment.model.CourseEnrollmentMetadata;
import com.wexl.retail.courses.enrollment.model.CourseSchedule;
import com.wexl.retail.courses.enrollment.repository.CourseScheduleRepository;
import com.wexl.retail.courses.enrollment.service.CourseScheduleInstService;
import com.wexl.retail.courses.teams.model.ScheduleCourseDto;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.team.domain.Team;
import com.wexl.retail.team.event.TeamCourseScheduleMetaData;
import com.wexl.retail.team.repository.TeamRepository;
import com.wexl.retail.telegram.service.UserService;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class TeamCoursesService {

  private final CourseScheduleRepository courseScheduleRepository;
  private final CourseScheduleInstService courseScheduleInstService;
  private final CourseDefinitionService courseDefinitionService;
  private final UserService userService;
  private final TeamRepository teamRepository;
  private final AuthService authService;

  private final StudentRepository studentRepository;

  public void scheduleCourseToTeam(
      ScheduleCourseDto.ScheduleCourseRequest scheduleCourseRequest, String org, Long courseDefId) {
    var course =
        courseScheduleRepository.save(buildTeamCourses(scheduleCourseRequest, org, courseDefId));
    courseScheduleInstService.updateCourseScheduleStatusTimeBombEntry(course);
    courseScheduleInstService.enrollStudentsToCourse(
        course, scheduleCourseRequest.students(), courseDefId);
  }

  private CourseSchedule buildTeamCourses(
      ScheduleCourseDto.ScheduleCourseRequest scheduleCourseRequest,
      String orgSlug,
      Long courseDefId) {

    Optional<Team> optionalTeam = teamRepository.findById(scheduleCourseRequest.team().getTeamId());
    if (optionalTeam.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Team not Found");
    }
    Team team = optionalTeam.get();

    return CourseSchedule.builder()
        .courseDefinition(courseDefinitionService.findCourseDefinitionById(courseDefId))
        .endDate(
            Timestamp.valueOf(
                LocalDate.now().plusDays(scheduleCourseRequest.courseDuration()).atStartOfDay()))
        .startDate(Timestamp.valueOf(LocalDateTime.now()))
        .orgSlug(orgSlug)
        .teacherId(userService.findUserById(authService.getUserDetails().getId()))
        .teams(List.of(team))
        .metadata(
            CourseEnrollmentMetadata.builder()
                .grades(new ArrayList<>())
                .sections(new ArrayList<>())
                .team(scheduleCourseRequest.team())
                .build())
        .build();
  }

  public void scheduleCourseToNewlyOnboardedTeam(TeamCourseScheduleMetaData metaData) {

    List<CourseSchedule> cours = courseScheduleRepository.findAllById(metaData.getCourseIds());
    List<Student> students = studentRepository.findStudentListById(metaData.getStudentIds());
    List<Long> userIds = students.stream().map(Student::getUserInfo).map(User::getId).toList();
    cours.forEach(
        course -> {
          if (course
              .getMetadata()
              .getTeam()
              .getAssignToNewlyOnboardedStudents()
              .equals(Boolean.TRUE)) {
            courseScheduleInstService.enrollStudentsToCourse(
                course, userIds, course.getCourseDefinition().getId());
          }
        });
  }
}
