package com.wexl.retail.student.exam.revision.domain;

import com.wexl.retail.model.Student;
import com.wexl.retail.student.exam.Exam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
public class ExamRevisionRequest {

  private Student student;
  private Exam exam;
  private String questionUuid;
  private Boolean isCorrect;
}
