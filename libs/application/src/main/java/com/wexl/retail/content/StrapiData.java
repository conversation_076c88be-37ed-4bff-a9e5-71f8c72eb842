package com.wexl.retail.content;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.Data;

@Entity
@Data
@Table(name = "strapi_data")
public class StrapiData extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "strapi-data-sequence-generator")
  @SequenceGenerator(
      name = "strapi-data-sequence-generator",
      sequenceName = "strapi_data_seq",
      allocationSize = 1)
  private long id;

  private long chapterId;
  private String chapterName;

  private long subjectId;
  private String subjectName;

  private long boardId;
  private String boardName;

  private long gradeId;
  private String gradeName;

  private String boardSlug;
  private String chapterSlug;
  private String subjectSlug;
  private String gradeSlug;
}
