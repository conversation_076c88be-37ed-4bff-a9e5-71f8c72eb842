package com.wexl.retail.test.school.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.liveworksheet.dto.WorkSheetQuestionType;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class TestQuestionRequest {

  private long testDefinition;
  private String questionUuid;
  private String chapterSlug;
  private String subtopicSlug;
  protected String type;
  private Integer marks;

  @JsonProperty("negative_marks")
  private float negativeMarks;

  @JsonProperty("subject_slug")
  private String subjectSlug;

  @JsonProperty("test_definition_section_id")
  private Long testDefinitionSectionId;

  public String getType() {
    if (Objects.isNull(type)) {
      return "mcq";
    }
    return type;
  }

  @JsonProperty("mcq_answer")
  private Long mcqAnswer;

  @JsonProperty("nat_answer")
  private Float natAnswer;

  @JsonProperty("fbq_answer")
  private String fbqAnswer;

  @JsonProperty("yes_no")
  private Boolean yesNo;

  @JsonProperty("msq_answer")
  private List<Long> msqAnswer;

  @JsonProperty("pbq_answers")
  private PbqDto.Data pbqAnswers;

  @JsonProperty("amcq_answer")
  private Integer amcqAnswer;

  @JsonProperty("spch_answer")
  private String spchAnswer;

  @JsonProperty("ddfbq_answer")
  private QuestionDto.DdFbq ddFbqAnswer;

  private String question;

  @JsonProperty("work_sheet_question_type")
  private WorkSheetQuestionType questionType;

  private String answer;

  @JsonProperty("work_sheet_answer_type")
  private WorkSheetQuestionType answerType;
}
