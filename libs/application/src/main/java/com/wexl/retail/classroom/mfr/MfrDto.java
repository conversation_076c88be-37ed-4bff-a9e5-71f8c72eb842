package com.wexl.retail.classroom.mfr;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record MfrDto() {
  @Builder
  public record Response(
      @JsonProperty("student_name") String studentName,
      @JsonProperty("user_name") String authUserId,
      @JsonProperty("roll_no") String rollNo,
      @JsonProperty("board_name") String boardName,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("over_all_feedback") String overAllFeedBack,
      @JsonProperty("attendance_percentage") Double attendancePercentage,
      @JsonProperty("from_date") Long fromDateInEpoc,
      @JsonProperty("to_date") Long toDateInEpoc,
      @JsonProperty("subjects") List<Subjects> subjectsList) {}

  @Builder
  public record Subjects(
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("subject_remarks") String subjectRemarks,
      @JsonProperty("chapters") List<Chapters> chaptersList) {}

  @Builder
  public record Chapters(
      @JsonProperty("chapter_name") String chapterName,
      @JsonProperty("chapter_slug") String chapterSlug,
      @JsonProperty("test_percentage") String testPercentage,
      @JsonProperty("sub_topics") List<SubTopics> subTopicsList,
      @JsonProperty("chapter_remarks") String chapterRemarks) {}

  @Builder
  public record SubTopics(
      @JsonProperty("subtopic_name") String subTopicName,
      @JsonProperty("subtopic_slug") String subTopicSlug,
      @JsonProperty("revision_count") String revisionCount,
      @JsonProperty("total_task_given") Integer totalAssignmentsGiven,
      @JsonProperty("total_task_completed") Integer totalAssignmentsCompleted,
      @JsonProperty("maks_scored") String marksScored) {}
}
