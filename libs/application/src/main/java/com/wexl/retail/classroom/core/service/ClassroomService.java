package com.wexl.retail.classroom.core.service;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.AuthUtil;
import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.classroom.core.dto.*;
import com.wexl.retail.classroom.core.model.Classroom;
import com.wexl.retail.classroom.core.model.ClassroomSchedule;
import com.wexl.retail.classroom.core.model.ClassroomScheduleInst;
import com.wexl.retail.classroom.core.repository.ClassroomRepository;
import com.wexl.retail.classroom.core.repository.ClassroomScheduleInstRepository;
import com.wexl.retail.classroom.history.ClassroomHistory;
import com.wexl.retail.classroom.history.ClassroomHistoryRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.admin.StudentResponse;
import com.wexl.retail.organization.admin.teacher.TeacherResponse;
import com.wexl.retail.organization.handler.EntityHandler;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.services.StudentService;
import com.wexl.retail.task.dto.StudentScheduleResponse;
import com.wexl.retail.task.service.TaskService;
import com.wexl.retail.util.Status;
import jakarta.transaction.Transactional;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ClassroomService {

  private final ClassroomRepository classroomRepository;

  private final OrganizationRepository organizationRepository;
  private final TeacherRepository teacherRepository;
  private final StudentService studentService;

  private final AuthService authService;
  private final ClassroomScheduleInstService classroomScheduleInstService;

  private final ClassroomScheduleInstRepository classroomScheduleInstRepository;
  private final ClassroomScheduleService classroomScheduleService;

  private final TaskService taskService;
  private final UserRepository userRepository;
  private final StudentRepository studentRepository;
  private final DateTimeUtil dateTimeUtil;
  private final List<EntityHandler<Classroom>> classroomHandlers;
  private final ClassroomHistoryRepository classroomHistoryRepository;
  private final UserRoleHelper userRoleHelper;

  public void createClassroom(ClassroomRequest classroomRequest, String orgSlug) {
    verifyClassRoomByName(classroomRequest.getName(), orgSlug);
    final Classroom updatedClassroom =
        classroomRepository.save(buildClassroom(new Classroom(), classroomRequest, orgSlug, null));
    classroomHandlers.forEach(handler -> handler.postSave(updatedClassroom));
  }

  public void verifyClassRoomByName(String classroomName, String orgSlug) {
    var possibleClassroom = classroomRepository.findByNameAndOrgSlug(classroomName, orgSlug);
    if (possibleClassroom.isPresent()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.Classroom.Exists",
          new String[] {classroomName});
    }
  }

  public List<Teacher> getTeachersByIdsAndOrgSlug(String orgSlug, List<Long> teacherIds) {
    if (teacherIds.isEmpty()) {
      return Collections.emptyList();
    }
    var teachers = teacherRepository.findAllByIdsAndOrgSlug(orgSlug, teacherIds);

    if (teachers.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.TeacherUnauthorized.Org",
          new String[] {orgSlug});
    }
    return teachers;
  }

  public Classroom buildClassroom(
      Classroom classroom,
      ClassroomRequest classroomRequest,
      String orgSlug,
      Classroom parentClassroom) {
    classroom.setName(classroomRequest.getName());
    classroom.setOrgSlug(orgSlug);
    classroom.setOrganization(organizationRepository.findBySlug(orgSlug));
    classroom.setTeachers(getTeachersByIdsAndOrgSlug(orgSlug, classroomRequest.getTeacherIds()));
    classroom.setParent(parentClassroom);
    if (ObjectUtils.isNotEmpty(classroomRequest.getStudentIds())) {
      classroom.setStudents(
          studentService.getStudentsByIds(orgSlug, classroomRequest.getStudentIds()));
    }
    if (classroom.getExtensions() == null) {
      classroom.setExtensions(classroomRequest.getExtensions());
    }
    return classroom;
  }

  @Transactional
  public void editClassroom(ClassroomRequest classRoomRequest, String orgSlug, long classroomId) {

    Classroom classroom = getClassroomByIdAndOrgSlug(classroomId, orgSlug);
    classroom.getTeachers().clear();
    classroom.getStudents().clear();
    final Classroom updatedClassroom =
        classroomRepository.save(
            buildClassroom(classroom, classRoomRequest, orgSlug, classroom.getParent()));
    classroomHandlers.forEach(handler -> handler.postSave(updatedClassroom));
  }

  public Classroom getClassroomByIdAndOrgSlug(long classroomId, String orgSlug) {
    return classroomRepository
        .findByIdAndOrgSlug(classroomId, orgSlug)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST,
                    "error.ClassroomValidity.Org",
                    new String[] {orgSlug}));
  }

  @Transactional
  public void deleteClassroom(String orgSlug, long classroomId) {

    Classroom classroom = getClassroomByIdAndOrgSlug(classroomId, orgSlug);
    if (!classroom.getSchedules().isEmpty()) {
      checkSchedules(classroom);
    }
    classroomRepository.saveAll(softDeleteClassrooms(List.of(classroom)));
  }

  public List<Classroom> softDeleteClassrooms(List<Classroom> classrooms) {
    return classrooms.stream()
        .map(
            classroom -> {
              classroom.setDeletedAt(new Date(Timestamp.valueOf(LocalDateTime.now()).getTime()));
              return classroom;
            })
        .toList();
  }

  private void checkSchedules(Classroom classroom) {

    var unExpiredSchedules =
        classroom.getSchedules().stream()
            .map(ClassroomSchedule::getEndDate)
            .filter(date -> date.isAfter(LocalDateTime.now()))
            .toList();
    if (!unExpiredSchedules.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.ScheduledClassroom.Delete");
    }
  }

  public List<ClassroomResponse> getAllClassRoomResponses(
      String orgSlug, boolean isClassroomReport, boolean isTodayClassrooms) {

    var teacher = authService.getTeacherDetails();
    if (isTodayClassrooms) {
      return getClassroomsByDate(orgSlug);
    }
    if (isClassroomReport) {
      if (AuthUtil.isOrgAdmin(teacher)) {
        List<Classroom> classrooms =
            classroomRepository.findByOrgSlugAndDeletedAtIsNullOrderByCreatedAtDesc(orgSlug);
        return buildClassroomReportResponse(classrooms);
      }

      List<Classroom> classrooms =
          classroomRepository.findByTeachersInAndOrgSlugAndDeletedAtIsNullOrderByCreatedAtDesc(
              List.of(teacher.getTeacherInfo()), orgSlug);
      return buildClassroomReportResponse(classrooms);
    }
    if (AuthUtil.isOrgAdmin(teacher)) {
      return buildClassroomResponse(classroomRepository.findByOrgSlugAndDeletedAtIsNull(orgSlug));
    }

    return buildClassroomResponse(
        classroomRepository.findByTeachersInAndOrgSlugAndDeletedAtIsNull(
            List.of(teacher.getTeacherInfo()), orgSlug));
  }

  private List<ClassroomResponse> buildClassroomResponse(List<Classroom> classrooms) {
    if (Objects.isNull(classrooms) || classrooms.isEmpty()) {
      return Collections.emptyList();
    }

    return classrooms.stream()
        .map(
            classroom ->
                ClassroomResponse.builder()
                    .id(classroom.getId())
                    .teacherCount(classroom.getTeachers().size())
                    .studentCount(studentService.getActiveStudents(classroom.getStudents()).size())
                    .name(classroom.getName())
                    .extensions(
                        Objects.nonNull(classroom.getExtensions())
                            ? classroom.getExtensions()
                            : null)
                    .teacherName(
                        classroomScheduleInstService.buildTeacherNames(classroom.getTeachers()))
                    .slotCount(classroom.getSchedules().size())
                    .createdAt(
                        DateTimeUtil.convertIso8601ToEpoch(
                            classroom.getCreatedAt().toLocalDateTime()))
                    .build())
        .sorted(Comparator.comparing(ClassroomResponse::getCreatedAt).reversed())
        .toList();
  }

  public ClassroomDetails getAllClassRoomDetails(String orgSlug, Long classroomId) {

    var optionalClassroom = classroomRepository.findById(classroomId);
    if (optionalClassroom.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.ClassRoomValidity.ClassIdAndOrg",
          new String[] {Long.toString(classroomId), orgSlug});
    }
    var classroom = optionalClassroom.get();
    return ClassroomDetails.builder()
        .classroomName(classroom.getName())
        .teachers(getTeacherResponseByTeachers(classroom.getTeachers()))
        .students(getStudentResponsesByStudents(classroom.getStudents()))
        .schedules(classroomScheduleService.buildScheduleResponses(classroom.getSchedules()))
        .extensions(classroom.getExtensions())
        .build();
  }

  public List<TeacherResponse> getTeacherResponseByTeachers(List<Teacher> teachers) {
    return teachers.stream()
        .map(
            teacher ->
                TeacherResponse.builder()
                    .firstName(
                        Objects.nonNull(teacher.getUserInfo().getFirstName())
                            ? teacher.getUserInfo().getFirstName()
                            : "")
                    .lastName(
                        Objects.nonNull(teacher.getUserInfo().getLastName())
                            ? teacher.getUserInfo().getLastName()
                            : "")
                    .teacherId(teacher.getId())
                    .userRoles(userRoleHelper.getUserRolesFromUser(teacher.getUserInfo()))
                    .subjects(
                        Objects.nonNull(teacher.getMetadata())
                            ? teacher.getMetadata().getSubjects()
                            : null)
                    .build())
        .toList();
  }

  public List<StudentResponse> getStudentResponsesByStudents(List<Student> students) {
    return students.stream()
        .filter(
            student ->
                student.getDeletedAt() == null
                    && Objects.nonNull(student.getUserInfo())
                    && Objects.isNull(student.getUserInfo().getDeletedAt()))
        .map(
            student ->
                StudentResponse.builder()
                    .id(student.getId())
                    .firstName(
                        Objects.nonNull(student.getUserInfo().getFirstName())
                            ? student.getUserInfo().getFirstName()
                            : "")
                    .lastName(
                        Objects.nonNull(student.getUserInfo().getLastName())
                            ? student.getUserInfo().getLastName()
                            : "")
                    .section(
                        Objects.nonNull(student.getSection())
                            ? student.getSection().getName()
                            : null)
                    .gradeSlug(
                        Objects.nonNull(student.getSection())
                            ? student.getSection().getGradeSlug()
                            : null)
                    .gradeName(
                        Objects.nonNull(student.getSection())
                            ? student.getSection().getGradeName()
                            : null)
                    .userName(student.getUserInfo().getUserName())
                    .orgSlug(student.getUserInfo().getOrganization())
                    .orgName(
                        organizationRepository
                            .findBySlug(student.getUserInfo().getOrganization())
                            .getName())
                    .build())
        .toList();
  }

  public TaskResponse getTaskResponsesByClassroomId(
      String orgSlug, Long classroomId, Long fromDate, Long toDate, int limit) {

    return taskService.getTaskInstResponses(orgSlug, classroomId, null, fromDate, toDate, limit);
  }

  public List<Classroom> getClassroomsByIdsAndOrgSlug(List<Long> classroomId) {
    var classrooms = classroomRepository.findAllById(classroomId);

    if (classrooms.isEmpty()) {
      return Collections.emptyList();
    }
    return classrooms;
  }

  private List<ClassroomResponse> buildClassroomReportResponse(List<Classroom> classrooms) {
    if (Objects.isNull(classrooms) || classrooms.isEmpty()) {
      return Collections.emptyList();
    }
    List<ClassroomResponse> classroomResponseList = new ArrayList<>();
    for (Classroom classroom : classrooms) {
      var classroomResponse =
          classroom.getStudents().stream()
              .filter(student -> student.getUserInfo() != null)
              .map(
                  student ->
                      ClassroomResponse.builder()
                          .studentUserName(student.getUserInfo().getUserName())
                          .studentFirstName(student.getUserInfo().getFirstName())
                          .studentRollNumber(student.getRollNumber())
                          .studentParentName("")
                          .mobileNumber("")
                          .guardianDetails(
                              student.getUserInfo().getStudentInfo().getGuardians().stream()
                                  .map(
                                      guardian ->
                                          ClassroomResponse.GuardianMetaData.builder()
                                              .firstName(guardian.getFirstName())
                                              .lastName(guardian.getLastName())
                                              .email(guardian.getEmail())
                                              .mobileNumber(guardian.getMobileNumber())
                                              .relationType(guardian.getRelationType())
                                              .build())
                                  .toList())
                          .grade(student.getSection().getGradeName())
                          .name(classroom.getName())
                          .classroomId(classroom.getId())
                          .teacherName(
                              classroom.getTeachers().stream()
                                  .map(teacher -> teacher.getUserInfo().getFirstName())
                                  .toList())
                          .studentCount(
                              studentService.getActiveStudents(classroom.getStudents()).size())
                          .lastLogin(student.getUserInfo().getLastLogin())
                          .status(
                              Objects.nonNull(student.getDeletedAt())
                                  ? Status.INACTIVE
                                  : Status.ACTIVE)
                          .slotCount(classroom.getSchedules().size())
                          .sectionName(student.getSection().getName())
                          .sectionUuid(student.getSection().getUuid())
                          .classId(student.getClassId())
                          .boardId(student.getBoardId())
                          .id(student.getId())
                          .build())
              .toList();
      classroomResponseList.addAll(classroomResponse);
    }
    return classroomResponseList;
  }

  public List<Classroom> getGroupClassRoomsByParent(Classroom parentClassroom) {
    List<Classroom> classrooms = classroomRepository.findAllByParent(parentClassroom);
    if (ObjectUtils.isNotEmpty(classrooms)) {
      return classrooms;
    }
    return Collections.emptyList();
  }

  public List<Long> getClassroomDates(String studentAuthId, int limit) {
    User user = userRepository.getUserByAuthUserId(studentAuthId);
    Student student = studentRepository.findByUserId(user.getId());

    var classroomDates = classroomDates(student, limit);
    if (classroomDates.isEmpty()) {
      new ArrayList<>();
    }
    return classroomDates.stream().map(DateTimeUtil::getEpochFromStringDate).toList();
  }

  private List<String> classroomDates(Student student, int limit) {
    var classroomDates = classroomRepository.getStudentClassroomDates(student.getId(), limit);
    var studentClassRoomHistory = classroomHistoryRepository.findAllByStudent(student);
    if (studentClassRoomHistory != null) {
      var classRoomNames =
          studentClassRoomHistory.stream()
              .map(ClassroomHistory::getClassroomName)
              .distinct()
              .toList();
      var dates = classroomRepository.getClassroomDates(classRoomNames);
      classroomDates.addAll(dates);
    }
    return classroomDates.stream().distinct().toList();
  }

  public List<StudentScheduleResponse> getStudentClassroomsByDate(
      String studentAuthId, String orgSlug, Long epochDate, int limit) {
    User user = userRepository.getUserByAuthUserId(studentAuthId);
    Student student = studentRepository.findByUserId(user.getId());
    var date = dateTimeUtil.convertEpochToIso8601Legacy(epochDate).toLocalDate();
    return classroomScheduleInstService.getStudentSchedulesByDate(
        orgSlug, date.toString(), student.getId(), limit);
  }

  public List<StudentResponse> getClassroomStudents(String orgSlug) {
    try {
      User user = authService.getTeacherDetails();
      if (AuthUtil.isOrgAdmin(user)) {
        return getStudentResponsesByStudents(studentRepository.getClassroomStudents(orgSlug));
      }
      return getStudentResponsesByStudents(
          studentRepository.getClassroomStudentByTeacher(user.getTeacherInfo().getId(), orgSlug));
    } catch (Exception e) {
      log.error("Failed to get students", e);
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.StudentsGet.Failed");
    }
  }

  private List<ClassroomResponse> getClassroomsByDate(String orgSlug) {

    var classroomScheduleInsts =
        classroomScheduleInstRepository.findByOrgSlugAndDeletedAtIsNullAndStartTimeBetween(
            orgSlug,
            LocalDateTime.now().with(LocalTime.MIN),
            LocalDateTime.now().with(LocalTime.MAX));
    if (classroomScheduleInsts.isEmpty()) {
      log.info("There are no classrooms  on  :" + LocalDate.now());
      return Collections.emptyList();
    }

    return buildTodayClassroomResponse(classroomScheduleInsts);
  }

  private List<ClassroomResponse> buildTodayClassroomResponse(
      List<ClassroomScheduleInst> classroomScheduleInsts) {

    return classroomScheduleInsts.stream()
        .map(
            inst ->
                ClassroomResponse.builder()
                    .startTime(inst.getStartTime())
                    .id(inst.getClassroomSchedule().getClassroom().getId())
                    .name(inst.getClassroomSchedule().getClassroom().getName())
                    .orgSlug(inst.getOrgSlug())
                    .build())
        .toList();
  }
}
