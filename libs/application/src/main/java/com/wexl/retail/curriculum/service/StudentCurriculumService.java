package com.wexl.retail.curriculum.service;

import com.wexl.retail.content.model.Entity;
import com.wexl.retail.content.model.Grade;
import com.wexl.retail.content.model.Icon;
import com.wexl.retail.curriculum.dto.SubjectDetails;
import com.wexl.retail.model.EduBoard;
import com.wexl.retail.model.Subject;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.auth.OrgCurriculumRequest;
import com.wexl.retail.student.subject.profiles.service.SubjectProfilesService;
import com.wexl.retail.util.StrapiService;
import java.util.*;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Data
public class StudentCurriculumService {

  private final SubjectProfilesService subjectProfilesService;
  private final StrapiService strapiService;
  private final TeacherCurriculumService teacherCurriculumService;

  public List<EduBoard> getStudentCurriculum(User user) {
    List<OrgCurriculumRequest> orgCurriculumRequest =
        subjectProfilesService.buildCurriculumRequest(user);

    if (orgCurriculumRequest.isEmpty()) {
      return new ArrayList<>();
    }

    return transformBoards(orgCurriculumRequest);
  }

  private List<EduBoard> transformBoards(List<OrgCurriculumRequest> orgCurriculumRequest) {
    final Map<String, Entity> eduBoardsMapBySlug =
        strapiService.transformStrapiEntityToMapBySlug(strapiService.getAllBoards());
    final Map<String, Grade> gradesMapBySlug =
        strapiService.transformGradeEntityToMapBySlug(strapiService.getAllGrades());
    final Map<String, Entity> subjectsMapBySlug =
        strapiService.transformStrapiEntityToMapBySlug(strapiService.getAllSubjects());

    Set<String> boardSlugs =
        orgCurriculumRequest.stream()
            .map(OrgCurriculumRequest::getBoardSlug)
            .collect(Collectors.toSet());

    List<EduBoard> boardHierarchy = new ArrayList<>();
    boardSlugs.parallelStream()
        .forEach(
            boardSlug -> {
              EduBoard eduBoard = new EduBoard();
              eduBoard.setSlug(boardSlug);
              teacherCurriculumService.mapEduBoard(boardSlug, eduBoard, eduBoardsMapBySlug);
              Set<String> mappedGradeSlugs = getMappedGradeSlugs(boardSlug, orgCurriculumRequest);
              eduBoard.setOrgSlug(getMappedOrgSlug(boardSlug, orgCurriculumRequest));
              eduBoard.setGrades(
                  mapGradeToBoard(
                      mappedGradeSlugs,
                      boardSlug,
                      orgCurriculumRequest,
                      gradesMapBySlug,
                      subjectsMapBySlug));
              boardHierarchy.add(eduBoard);
            });

    return boardHierarchy;
  }

  private Set<String> getMappedGradeSlugs(
      String boardSlug, List<OrgCurriculumRequest> orgCurriculumRequest) {
    return orgCurriculumRequest.stream()
        .filter(r -> r.getBoardSlug().equals(boardSlug))
        .map(OrgCurriculumRequest::getGradeSlug)
        .collect(Collectors.toSet());
  }

  private String getMappedOrgSlug(
      String boardSlug, List<OrgCurriculumRequest> orgCurriculumRequest) {
    var orgSlug =
        orgCurriculumRequest.stream()
            .filter(r -> r.getBoardSlug().equals(boardSlug))
            .map(OrgCurriculumRequest::getOrgSlug)
            .findFirst();
    return orgSlug.orElse("");
  }

  public List<com.wexl.retail.model.Grade> mapGradeToBoard(
      Set<String> gradeSlugs,
      String boardSlug,
      List<OrgCurriculumRequest> orgCurriculumRequest,
      final Map<String, Grade> gradesMapBySlug,
      final Map<String, Entity> subjectsMapBySlug) {

    List<Grade> entities = new ArrayList<>();
    gradeSlugs.forEach(g -> entities.add(gradesMapBySlug.get(g)));
    List<com.wexl.retail.model.Grade> grades = new ArrayList<>();
    entities.forEach(
        entity ->
            grades.add(
                com.wexl.retail.model.Grade.builder()
                    .id(entity.getId())
                    .name(entity.getName())
                    .slug(entity.getSlug())
                    .orderId(entity.getOrder())
                    .subjects(
                        mapSubjectToGradeAndBoard(
                            getMappedSubjectSlugs(
                                boardSlug, entity.getSlug(), orgCurriculumRequest),
                            subjectsMapBySlug))
                    .build()));
    grades.sort(Comparator.comparing(com.wexl.retail.model.Grade::getOrderId));
    return grades;
  }

  public List<SubjectDetails> getMappedSubjectSlugs(
      String boardSlug, String gradeSlug, List<OrgCurriculumRequest> orgCurriculumRequest) {
    List<SubjectDetails> details = new ArrayList<>();
    orgCurriculumRequest.forEach(
        request -> {
          if ((Objects.equals(request.getGradeSlug(), gradeSlug))
              && (Objects.equals(request.getBoardSlug(), boardSlug))) {
            details.add(
                SubjectDetails.builder()
                    .displayName(request.getSubjectDisplayName())
                    .slug(request.getSubjectSlug())
                    .build());
          }
        });
    return details;
  }

  public List<Subject> mapSubjectToGradeAndBoard(
      List<SubjectDetails> subjectsSlugDisplayNameMap,
      final Map<String, Entity> entitiesMapBySlug) {
    List<String> subjectSlugs =
        subjectsSlugDisplayNameMap.stream().map(SubjectDetails::getSlug).toList();
    List<Subject> subjects = new ArrayList<>();
    subjectSlugs.forEach(
        subjectSlug -> {
          Entity entity = entitiesMapBySlug.get(subjectSlug);
          final String displayNameFromSlug =
              getDisplayNameFromSlug(subjectsSlugDisplayNameMap, subjectSlug);
          subjects.add(
              Subject.builder()
                  .id(entity.getId())
                  .name(
                      Objects.nonNull(displayNameFromSlug) ? displayNameFromSlug : entity.getName())
                  .slug(entity.getSlug())
                  .icons(entity.getIcons().parallelStream().map(Icon::getUrl).toList())
                  .build());
        });
    return subjects;
  }

  private String getDisplayNameFromSlug(List<SubjectDetails> subjectDetails, String slug) {
    final Optional<SubjectDetails> possibleSubjectDetails =
        subjectDetails.stream().filter(s -> s.getSlug().equals(slug)).findFirst();
    if (possibleSubjectDetails.isEmpty()) {
      return "";
    }
    return possibleSubjectDetails.get().getDisplayName();
  }
}
