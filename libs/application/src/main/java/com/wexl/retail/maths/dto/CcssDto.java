package com.wexl.retail.maths.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record CcssDto() {

  @Builder
  public record CcssResponse(List<CcssGradeResponse> ccssGradeResponse) {}

  @Builder
  public record CcssGradeResponse(
      Long id,
      String name,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("ccss_domains") List<CcssDomainResponse> ccssDomains) {}

  @Builder
  public record CcssDomainResponse(
      Long id,
      @JsonProperty("domain_name") String domainName,
      @JsonProperty("domain_slug") String domainSlug,
      @JsonProperty("ccss_clusters") List<CcssClusterResponse> ccssClusters) {}

  @Builder
  public record CcssClusterResponse(
      Long id,
      @JsonProperty("cluster_name") String clusterName,
      @JsonProperty("ccss_standards") List<CcssStandardResponse> ccssStandards) {}

  @Builder
  public record CcssStandards(
      @JsonProperty("ccss_standards") List<CcssStandardResponse> ccssStandardResponses) {}

  @Builder
  public record CcssStandardResponse(
      Long id,
      @JsonProperty("standard_name") String standardName,
      @JsonProperty("standard_slug") String standardSlug,
      @JsonProperty("cluster_id") Long clusterId,
      @JsonProperty("cluster_name") String clusterName,
      @JsonProperty("domain_id") Long domainId,
      @JsonProperty("domain_name") String domainName,
      @JsonProperty("domain_slug") String domainSlug,
      @JsonProperty("ccss_grade_name") String ccssGradeName,
      @JsonProperty("ccss_grade_slug") String ccssGradeSlug,
      @JsonProperty("ccss_grade_id") Long ccssGradeId) {}
}
