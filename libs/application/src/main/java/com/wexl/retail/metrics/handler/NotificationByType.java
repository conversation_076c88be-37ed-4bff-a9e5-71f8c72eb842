package com.wexl.retail.metrics.handler;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class NotificationByType extends AbstractMetricHandler {

  @Override
  public String name() {
    return "notifications-count-by-categories";
  }

  @Override
  protected List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    String gradeSlug =
        Optional.ofNullable(genericMetricRequest.getInput())
            .map(input -> (String) input.get("grade"))
            .orElse(null);
    if (gradeSlug != null) {
      return notificationsService.getNotificationByCategories(org, gradeSlug);
    } else {
      return Collections.emptyList();
    }
  }
}
