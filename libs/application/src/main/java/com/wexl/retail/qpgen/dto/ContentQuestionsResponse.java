package com.wexl.retail.qpgen.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class ContentQuestionsResponse {
  String question;
  String uuid;
  String explanation;
  Long answer;
  Long marks;
  Long category;

  @JsonProperty("question_complexity")
  Long questionComplexity;

  String questionCategoryName;
  String questionCategorySlug;
  String questionComplexityName;
  String questionComplexitySlug;
}
