package com.wexl.retail.team.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class TeamResponse {

  @JsonProperty("team_name")
  private String teamName;

  @JsonProperty("no_of_team_members")
  private long noOfTeamMembers;

  @JsonProperty("team_id")
  private long teamId;

  @JsonProperty("student_details")
  private List<TeamDetails> studentDetails;
}
