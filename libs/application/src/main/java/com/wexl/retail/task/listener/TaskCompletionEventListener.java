package com.wexl.retail.task.listener;

import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.task.publisher.TaskCompletionEvent;
import com.wexl.retail.task.service.TaskInstService;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@AllArgsConstructor
@Component
public class TaskCompletionEventListener implements ApplicationListener<TaskCompletionEvent> {

  private TaskInstService taskInstService;

  @Override
  public void onApplicationEvent(TaskCompletionEvent taskCompletionEvent) {
    Object source = taskCompletionEvent.getSource();
    if (source instanceof Exam exam
        && Objects.nonNull(exam.getRef())
        && Objects.nonNull(exam.getTaskId())) {
      taskInstService.updateTaskInst(exam);
    }
  }
}
