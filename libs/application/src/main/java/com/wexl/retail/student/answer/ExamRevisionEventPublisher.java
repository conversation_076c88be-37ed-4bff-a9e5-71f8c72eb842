package com.wexl.retail.student.answer;

import com.wexl.retail.student.exam.revision.domain.ExamRevisionRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
public class ExamRevisionEventPublisher {
  @Autowired ApplicationEventPublisher eventPublisher;

  public void publishEvent(final ExamRevisionRequest questionGeneration) {
    ExamRevisionEvent examRevisionEvent = new ExamRevisionEvent(questionGeneration);
    eventPublisher.publishEvent(examRevisionEvent);
  }
}
