package com.wexl.retail.erp.attendance.controller;

import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.commons.security.annotation.IsTeacherOrStudent;
import com.wexl.retail.erp.attendance.dto.*;
import com.wexl.retail.erp.attendance.service.ErpAttendanceService;
import com.wexl.retail.util.MobileAppUtil;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgId}/erp-attendance")
public class ErpAttendanceController {
  private final ErpAttendanceService attendanceService;

  @IsOrgAdmin
  @PostMapping("/initiate")
  @ResponseStatus(HttpStatus.CREATED)
  public void initiate(
      @RequestBody(required = false) FromDate.Request fromDate,
      @RequestHeader(HttpHeaders.USER_AGENT) String userAgent,
      @PathVariable String orgId) {
    if (!MobileAppUtil.requestComingFromMobileApp(userAgent)) {
      attendanceService.initiateCalendarData(orgId, fromDate);
    }
  }

  @IsOrgAdminOrTeacher
  @ResponseStatus(HttpStatus.CREATED)
  @PostMapping("/section/{sectionId}/attendance/{attendanceId}")
  public void addAttendance(
      @PathVariable Integer sectionId,
      @PathVariable Long attendanceId,
      @RequestBody AddAttendanceRequest addAttendanceRequest) {

    attendanceService.addAttendance(sectionId, attendanceId, addAttendanceRequest);
  }

  @IsOrgAdminOrTeacher
  @PutMapping("/section/{sectionId}/attendance/{attendanceId}")
  public void updateAfternoonAttendance(@RequestBody AddAttendanceRequest addAttendanceRequest) {

    attendanceService.updateAfternoonAttendance(addAttendanceRequest);
  }

  @IsOrgAdminOrTeacher
  @ResponseStatus(HttpStatus.CREATED)
  @PostMapping("/attendance/{attendanceId}/mark-holiday")
  public void markHoliday(
      @PathVariable Long attendanceId, @RequestBody MarkHolidayRequest markHolidayRequest) {

    attendanceService.markHoliday(attendanceId, markHolidayRequest);
  }

  @GetMapping("/section/{sectionId}/attendance")
  @IsOrgAdminOrTeacher
  public List<AttendanceResponse> getAttendanceByDate(
      @PathVariable Integer sectionId, @RequestParam Long fromDate, @RequestParam Long toDate) {
    return attendanceService.getAttendanceByDate(sectionId, fromDate, toDate);
  }

  @GetMapping("/attendance/{attendanceId}")
  public List<StudentAttendanceResponse> getAttendanceDetails(@PathVariable Long attendanceId) {
    return attendanceService.getAttendanceDetails(attendanceId);
  }

  @GetMapping("attendance/{attendanceId}/afternoon")
  public List<StudentAttendanceResponse> getAttendanceAfternoonDetails(
      @PathVariable Long attendanceId) {
    return attendanceService.getAttendanceAfternoonDetails(attendanceId);
  }

  @IsTeacherOrStudent
  @GetMapping("/student/{studentId}")
  public StudentAttendanceResponse getStudentAttendance(
      @PathVariable(name = "studentId") String userName,
      @PathVariable String orgId,
      @RequestParam(required = false, name = "fromDate") Long fromDate,
      @RequestParam(required = false, name = "toDate") Long toDate,
      @RequestParam(required = false, name = "sessionType") String sessionType) {
    return attendanceService.getStudentAttendance(orgId, userName, fromDate, toDate, sessionType);
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/teacher/{teacherAuthId}")
  public List<AttendanceSummaryResponse> getAttendanceSummary(
      @PathVariable String orgId,
      @PathVariable String teacherAuthId,
      @RequestParam(required = false) String section,
      @RequestParam(required = false, defaultValue = "100") int limit,
      @RequestParam(required = false) Long date,
      @RequestParam(required = false) List<String> grades,
      @RequestParam(required = true) List<String> boardSlugs,
      @RequestParam(required = false) String sessionType) {

    return attendanceService.getAttendanceSummary(
        orgId, teacherAuthId, grades, section, limit, date, boardSlugs, sessionType);
  }
}
