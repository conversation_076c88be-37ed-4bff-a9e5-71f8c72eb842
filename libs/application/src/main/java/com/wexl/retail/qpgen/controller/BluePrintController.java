package com.wexl.retail.qpgen.controller;

import com.wexl.retail.qpgen.dto.BluePrintDto;
import com.wexl.retail.qpgen.service.BluePrintService;
import com.wexl.retail.test.school.dto.QuestionDto;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}/qpgen-blueprints")
@RequiredArgsConstructor
public class BluePrintController {
  private final BluePrintService bluePrintService;

  @ResponseStatus(HttpStatus.CREATED)
  @PostMapping()
  public void saveBluePrint(
      @PathVariable String orgSlug, @RequestBody BluePrintDto.Request request) {
    bluePrintService.saveBluePrint(orgSlug, request);
  }

  @GetMapping()
  public List<BluePrintDto.Response> getAllBluePrintsByOrg(@PathVariable String orgSlug) {
    return bluePrintService.getAllBluePrintsByOrg(orgSlug);
  }

  @GetMapping("/{bluePrintId}")
  public BluePrintDto.Response getBluePrintById(
      @PathVariable String orgSlug, @PathVariable Long bluePrintId) {
    return bluePrintService.getBluePrintById(orgSlug, bluePrintId);
  }

  @PostMapping("/{bluePrintId}")
  public void saveBluePrintSection(
      @PathVariable String orgSlug,
      @RequestBody BluePrintDto.Section request,
      @PathVariable Long bluePrintId) {
    bluePrintService.saveBluePrintSection(orgSlug, request, bluePrintId);
  }

  @PostMapping("/{bluePrintId}/sections/{bluePrintSectionId}/instructions")
  public void addSectionInstruction(
      @PathVariable String orgSlug,
      @RequestBody BluePrintDto.InstructionsRequest request,
      @PathVariable Long bluePrintSectionId,
      @PathVariable Long bluePrintId) {
    bluePrintService.addInstructions(orgSlug, request, bluePrintSectionId, bluePrintId);
  }

  @GetMapping("/{bluePrintId}/sections/{bluePrintSectionId}/instructions")
  public BluePrintDto.InstructionsRequest addSectionInstruction(
      @PathVariable String orgSlug,
      @PathVariable Long bluePrintSectionId,
      @PathVariable Long bluePrintId) {
    return bluePrintService.getInstructions(orgSlug, bluePrintSectionId, bluePrintId);
  }

  @PutMapping("/{bluePrintId}/sections/{bluePrintSectionId}")
  public void editBluePrintSection(
      @PathVariable String orgSlug,
      @RequestBody BluePrintDto.Section request,
      @PathVariable Long bluePrintSectionId,
      @PathVariable Long bluePrintId) {
    bluePrintService.editBluePrintSection(orgSlug, request, bluePrintSectionId, bluePrintId);
  }

  @DeleteMapping("/{bluePrintId}")
  public void deleteBluePrint(@PathVariable String orgSlug, @PathVariable Long bluePrintId) {
    bluePrintService.deleteBluePrint(orgSlug, bluePrintId);
  }

  @DeleteMapping("/{bluePrintId}/sections/{bluePrintSectionId}")
  public void deleteBluePrintSection(
      @PathVariable String orgSlug,
      @PathVariable Long bluePrintId,
      @PathVariable Long bluePrintSectionId) {
    bluePrintService.deleteBluePrintSection(orgSlug, bluePrintId, bluePrintSectionId);
  }

  @GetMapping("/blueprint-config")
  public List<QuestionDto.QuestionTags> getBluePrintConfig(@PathVariable String orgSlug) {
    return bluePrintService.getBluePrintConfig(orgSlug);
  }
}
