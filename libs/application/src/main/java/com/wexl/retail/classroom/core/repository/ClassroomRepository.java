package com.wexl.retail.classroom.core.repository;

import com.wexl.retail.classroom.core.model.Classroom;
import com.wexl.retail.metrics.dto.StudentClassReportInterface;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface ClassroomRepository extends JpaRepository<Classroom, Long> {

  Optional<Classroom> findByNameAndOrgSlug(String name, String org);

  Optional<Classroom> findByIdAndOrgSlug(long classroomId, String orgSlug);

  List<Classroom> findByOrgSlugAndDeletedAtIsNull(String orgSlug);

  List<Classroom> findByOrgSlugAndDeletedAtIsNullOrderByCreatedAtDesc(String orgSlug);

  List<Classroom> findByTeachersInAndOrgSlugAndDeletedAtIsNullOrderByCreatedAtDesc(
      List<Teacher> teachers, String orgSlug);

  List<Classroom> findByTeachersInAndOrgSlugAndDeletedAtIsNull(
      List<Teacher> teachers, String orgSlug);

  List<Classroom> findByStudentsAndDeletedAtIsNullOrderByCreatedAtDesc(Student student);

  @Query(
      value =
          """
                  select distinct student_id from classroom_students cs
                  join students s on s.id = cs.student_id
                  join classrooms c on cs.classroom_id = c.id
                  where classroom_id in (:classroomIds)
                  and s.deleted_at is null and c.deleted_at is null
                  """,
      nativeQuery = true)
  List<Long> getStudentIdsByClassRoom(List<Long> classroomIds);

  List<Classroom> findAllByParent(Classroom parentClassroom);

  @Query(
      value =
          """
          select * from classrooms  where  parent_classroom_id in (
                    select id from classrooms  where org_slug = :orgSlug and deleted_at is NULL)
                    union
                    select * from classrooms where org_slug = :orgSlug  and deleted_at is NULL
                    order by org_slug desc""",
      nativeQuery = true)
  List<Classroom> getGroupClassrooms(String orgSlug);

  @Query(
      value =
          """
                  select distinct to_char(csi.start_time ,'yyyy-MM-dd') from classroom_students s join classrooms c on s.classroom_id = c.id
                  join classroom_schedules cs on cs.classroom_id = c.id
                  join classroom_schedule_inst csi on csi.classroom_schedule_id = cs.id
                  where s.student_id =:studentId order by to_char(csi.start_time ,'yyyy-MM-dd')  desc  limit :limit
                  """,
      nativeQuery = true)
  List<String> getStudentClassroomDates(Long studentId, int limit);

  @Query(
      value =
          """
                          select distinct to_char(csi.start_time ,'yyyy-MM-dd') from classroom_students
                          s join classrooms c on s.classroom_id = c.id
                          join classroom_schedules cs on cs.classroom_id = c.id
                          join classroom_schedule_inst csi on csi.classroom_schedule_id = cs.id
                          where s.student_id =:studentId and csi.start_time between :fDate and :tDate
                                  """,
      nativeQuery = true)
  List<String> getStudentClassroomsByDate(Long studentId, LocalDateTime fDate, LocalDateTime tDate);

  @Transactional
  @Modifying
  @Query(
      value =
          """
                delete FROM classroom_students where student_id = :studentId
                and classroom_id in (:classroomIds)
                """,
      nativeQuery = true)
  void deleteStudentInClassroom(Long studentId, List<Long> classroomIds);

  @Query(
      value =
          """
                                  select * from classrooms c where id = :classroomIds
                                          """,
      nativeQuery = true)
  Optional<Classroom> findById(List<Long> classroomIds);

  @Query(
      value =
          """
                                          select * from classrooms c where id in (:classroomIds)
                                                  """,
      nativeQuery = true)
  List<Classroom> getClassroomsByClassroomIds(List<Long> classroomIds);

  @Query(
      value =
          """
                  select c.* from applicant a
                  join classroom_students cs on cs.student_id=a.student_id
                  join classrooms c on c.id=cs.classroom_id
                  where a.id=:applicantId
                  """,
      nativeQuery = true)
  List<Classroom> getAllProductAndClassroom(Long applicantId);

  @Query(
      value =
          """
                                   select distinct to_char(csi.start_time ,'yyyy-MM-dd') from classroom_students s join classrooms c on s.classroom_id = c.id
                                                    join classroom_schedules cs on cs.classroom_id = c.id
                                                    join classroom_schedule_inst csi on csi.classroom_schedule_id = cs.id
                                                    where c.name in (:classRoomNames)
                                  """,
      nativeQuery = true)
  List<String> getClassroomDates(List<String> classRoomNames);

  @Query(
      value =
          """
                  select classroom, studentName, sum(presentCount) presentCount, sum(absentCount) absentCount, sum(notMarked) notMarked, count(*) as totalCount
                                                                   from
                                                                   (select c."name" classroom, CONCAT(u.first_name, u.last_name) AS studentName, siad.attendance_status status,
                                                                    case when siad.attendance_status = 'PRESENT' then 1 else 0 end presentCount,
                                                                    case when siad.attendance_status = 'ABSENT' then 1 else 0 end absentCount,
                                                                    case when siad.attendance_status = 'CLOSED' then 1 else 0 end notMarked
                                                                    from users u
                                                                    join students st on st.user_id = u.id
                                                                    join sections sc on sc.id = st.section_id
                                                                    join classroom_students cs on cs.student_id = st.id
                                                                    join classrooms c on c.id = cs.classroom_id
                                                                    join classroom_schedules css on css.classroom_id = c.id
                                                                    join (select * from classroom_schedule_inst where status in ('COMPLETED','NOT_STARTED','CLOSED')) csi on csi.classroom_schedule_id = css.id
                                                                    join schedule_inst_attendance sia on sia.classroom_schedule_inst_id = csi.id
                                                                    join schedule_inst_attendance_details siad on siad.student_id = st.id and siad.schedule_inst_attendance_id = sia.id
                                                                    where c.id in (:classroomId) and u.organization  = (:orgSlug)
                                                                    and csi.start_time BETWEEN (:fromDate) AND (:endDate)
                                                                    ) a
                                                                   group by classroom, studentName

                                  """,
      nativeQuery = true)
  List<StudentClassReportInterface> getStudentData(
      String orgSlug, Long classroomId, LocalDateTime fromDate, LocalDateTime endDate);

  List<Classroom> findByIdInAndOrgSlugAndDeletedAtIsNull(List<Long> ids, String orgSlug);
}
