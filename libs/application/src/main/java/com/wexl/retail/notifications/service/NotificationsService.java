package com.wexl.retail.notifications.service;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.AuthUtil;
import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.classroom.core.model.Classroom;
import com.wexl.retail.classroom.core.repository.ClassroomRepository;
import com.wexl.retail.classroom.core.service.ClassroomService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.Grade;
import com.wexl.retail.erp.attendance.domain.SectionAttendance;
import com.wexl.retail.erp.attendance.domain.SectionAttendanceDetails;
import com.wexl.retail.globalprofile.model.AppTemplate;
import com.wexl.retail.guardian.service.GuardianService;
import com.wexl.retail.messagetemplate.category.model.MessageTemplateCategory;
import com.wexl.retail.messagetemplate.category.repository.MessageTemplateCategoryRepository;
import com.wexl.retail.messagetemplate.model.MessageTemplate;
import com.wexl.retail.messagetemplate.repository.MessageTemplateRepository;
import com.wexl.retail.messagetemplate.service.MessageTemplateService;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.mlp.service.MlpService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.notification.service.EventNotificationService;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.dto.NotificationDto.NotificationRequest;
import com.wexl.retail.notifications.dto.NotificationMessageType;
import com.wexl.retail.notifications.model.*;
import com.wexl.retail.notifications.repository.NotificationLogRepository;
import com.wexl.retail.notifications.repository.NotificationRepository;
import com.wexl.retail.notifications.repository.SectionNotificationRepository;
import com.wexl.retail.notifications.repository.StudentNotificationRepository;
import com.wexl.retail.organization.handler.EntityHandler;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.domain.SectionStatus;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.services.StudentService;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.student.auth.StudentAuthService;
import com.wexl.retail.telegram.service.UserService;
import jakarta.transaction.Transactional;
import java.time.LocalDate;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class NotificationsService {

  private final NotificationRepository notificationRepository;
  private final SectionNotificationRepository sectionNotificationRepository;
  private final StudentNotificationRepository studentNotificationRepository;

  private final OrganizationRepository organizationRepository;

  private final ClassroomService classroomService;

  private final SectionService sectionservice;

  private final UserRepository userRepository;

  private final StudentService studentService;

  private final UserService userService;

  private final DateTimeUtil dateTimeUtil;

  private final AuthService authService;
  private final StorageService storageService;
  private final UserRoleHelper userRoleHelper;

  private final MessageTemplateService messageTemplateService;
  private final MessageTemplateRepository messageTemplateRepository;
  private final ClassroomRepository classroomRepository;
  private final TeacherRepository teacherRepository;
  private final GuardianService guardianService;
  private final StudentAuthService studentAuthService;
  private final ContentService contentService;
  private final SectionRepository sectionRepository;
  private final NotificationLogRepository notificationLogRepository;

  private final MessageTemplateCategoryRepository messageTemplateCategoryRepository;

  private final EventNotificationService eventNotificationService;

  private final MlpService mlpService;

  public final List<EntityHandler<Notification>> notificationHandlers;
  private static final String WEXL_INTERNAL = "wexl-internal";
  private final String present = "present";
  private final String absent = "absent";
  private static final String AFTERNOON = "afternoon";
  private static final String SMS_MESSAGE_TEMPLATE = "66ebf74cd6fc0552c172f9a2";

  public void createNotificationByTeacher(
      String orgSlug,
      NotificationDto.NotificationRequest notificationRequest,
      String teacherAuthId,
      boolean allClassRooms,
      Long date) {
    var notification = buildNotification(notificationRequest, orgSlug, teacherAuthId);

    saveNotification(notification, notificationRequest, orgSlug, allClassRooms);
    notificationHandlers.getFirst().postSave(notification);
    eventNotificationService.triggerNotification(notificationRequest, orgSlug, date);
  }

  public void createNotificationByTeacher(
      String orgSlug,
      NotificationDto.NotificationRequest notificationRequest,
      String teacherAuthId,
      boolean allClassRooms) {
    var notification = buildNotification(notificationRequest, orgSlug, teacherAuthId);

    saveNotification(notification, notificationRequest, orgSlug, allClassRooms);
    notificationHandlers.getFirst().postSave(notification);
    eventNotificationService.triggerNotification(notificationRequest, orgSlug, null);
  }

  public void triggerAttendanceNotification(
      SectionAttendance sectionAttendance, String sessionType, Long date) {
    try {
      List<Student> morningAbsentees = new ArrayList<>();
      List<Student> afterNoonAbsentees = new ArrayList<>();
      if (!sessionType.equals(AFTERNOON)) {
        morningAbsentees =
            sectionAttendance.getAttendanceDetails().stream()
                .filter(attendanceDetail -> absent.equals(attendanceDetail.getAttendanceStatus()))
                .map(SectionAttendanceDetails::getStudent)
                .toList();
      } else {
        afterNoonAbsentees =
            sectionAttendance.getAttendanceDetails().stream()
                .filter(
                    attendanceDetail ->
                        absent.equals(attendanceDetail.getAfternoonAttendanceStatus()))
                .map(SectionAttendanceDetails::getStudent)
                .toList();
      }
      var teacherUser = authService.getUserDetails();

      final MessageTemplate absenteesMessageTemplate =
          messageTemplateService.validateAndGetMessageTemplate(
              sectionAttendance.getOrg().getSlug(), "Absentees");
      if (absenteesMessageTemplate == null) {
        return;
      }
      if (!morningAbsentees.isEmpty()) {
        sendAbsenteeNotification(
            morningAbsentees,
            absenteesMessageTemplate,
            sectionAttendance,
            teacherUser.getAuthUserId(),
            date);
      } else if (!afterNoonAbsentees.isEmpty()) {
        var afternoonMsg =
            messageTemplateRepository.findBySmsDltTemplateIdAndOrgSlug(
                SMS_MESSAGE_TEMPLATE, sectionAttendance.getOrg().getSlug());
        sendAbsenteeNotification(
            afterNoonAbsentees,
            afternoonMsg.get(),
            sectionAttendance,
            teacherUser.getAuthUserId(),
            date);
      }
    } catch (Exception e) {
      log.error("Unable to trigger attendance notification. Message: [" + e.getMessage() + "]", e);
    }
  }

  private void sendAbsenteeNotification(
      List<Student> absenteeStudents,
      MessageTemplate absenteesMessageTemplate,
      SectionAttendance sectionAttendance,
      String authUserId,
      Long date) {
    NotificationRequest notificationRequest =
        NotificationRequest.builder()
            .notificationType(com.wexl.retail.notifications.dto.NotificationType.INDIVIDUAL)
            .message(absenteesMessageTemplate.getMessage())
            .title("Absentee Notification")
            .sectionUuids(null)
            .studentIds(absenteeStudents.stream().map(Student::getId).toList())
            .messageTemplateId(absenteesMessageTemplate.getId())
            .categoryId(absenteesMessageTemplate.getMessageTemplateCategory().getId())
            .build();
    createNotificationByTeacher(
        sectionAttendance.getOrg().getSlug(), notificationRequest, authUserId, false, date);
  }

  private void saveNotification(
      Notification notification,
      NotificationDto.NotificationRequest notificationRequest,
      String orgSlug,
      boolean allClassRooms) {
    if (NotificationType.CLASSROOM.name().equals(notificationRequest.notificationType().name())
        && Objects.nonNull(notificationRequest.classroomIds())) {
      buildClassroomNotification(notificationRequest, orgSlug, notification, allClassRooms);
    } else if (NotificationType.SECTION.name().equals(notificationRequest.notificationType().name())
        && Objects.nonNull(notificationRequest.sectionUuids())) {
      buildSectionNotification(notificationRequest, orgSlug, notification);
    } else if (NotificationType.GRADE.name().equals(notificationRequest.notificationType().name())
        && Objects.nonNull(notificationRequest.gradeSlug())) {
      buildGradeNotification(notificationRequest, orgSlug, notification);
    } else if (NotificationType.ORGANIZATION
            .name()
            .equals(notificationRequest.notificationType().name())
        && Objects.nonNull(notificationRequest.orgSlugs())) {
      buildOrgNotification(notificationRequest, orgSlug, notification);
    } else {
      var students =
          studentService.getStudentsByIdsAndOrgSlug(
              orgSlug, notificationRequest.studentIds(), false);
      if (!students.isEmpty()) {
        buildStudentNotification(students, notification, orgSlug);
        notification.setNotificationType(NotificationType.INDIVIDUAL);
      }
    }
    var user = authService.getUserDetails();
    if (userRoleHelper.isTeacher(user)) {
      List<Teacher> allTeachers = new ArrayList<>(teacherRepository.getAllAdminsByOrg(orgSlug));
      if (userRoleHelper.isManager(user)) {
        allTeachers.addAll(
            teacherRepository.getAllTeacherByOrgSlug(orgSlug, AppTemplate.TEACHER.toString()));
        buildTeacherNotification(allTeachers, notification, orgSlug);
      } else {
        buildTeacherNotification(allTeachers, notification, orgSlug);
      }
    } else if (AuthUtil.isStudent(user)) {
      buildTeacherNotification(List.of(notification.getCreatedBy()), notification, orgSlug);
    }
    notificationRepository.save(notification);
  }

  private void buildOrgNotification(
      NotificationRequest notificationRequest, String orgSlug, Notification notification) {
    var allSections =
        sectionRepository.findAllByOrganizationAndDeletedAtIsNullAndStatusOrderByName(
            orgSlug, SectionStatus.ACTIVE);
    var sectionUuids = allSections.stream().map(section -> section.getUuid().toString()).toList();
    var newNotificationRequest =
        NotificationRequest.builder()
            .sectionUuids(sectionUuids)
            .orgSlugs(notificationRequest.orgSlugs())
            .categoryId(notificationRequest.categoryId())
            .notificationType(notificationRequest.notificationType())
            .title(notificationRequest.title())
            .messageTemplateId(notificationRequest.messageTemplateId())
            .attachment(notificationRequest.attachment())
            .message(notificationRequest.message())
            .link(notificationRequest.link())
            .build();
    buildSectionNotification(newNotificationRequest, orgSlug, notification);
  }

  private void buildGradeNotification(
      NotificationDto.NotificationRequest notificationRequest,
      String orgSlug,
      Notification notification) {
    List<String> sections =
        sectionRepository
            .findAllByGradeSlugInAndOrganizationAndStatus(
                Collections.singletonList(notificationRequest.gradeSlug()),
                orgSlug,
                SectionStatus.ACTIVE)
            .stream()
            .map(section -> section.getUuid().toString())
            .toList();
    var students = studentService.getStudentsBySectionUuidsAndOrgSlug(sections, orgSlug);
    buildStudentNotification(students, notification, orgSlug);
    notification.setNotificationType(NotificationType.INDIVIDUAL);
    List<SectionNotification> sectionNotifications = new ArrayList<>();
    sections.forEach(
        sectionUuid ->
            sectionNotifications.add(
                SectionNotification.builder()
                    .notification(notification)
                    .section(sectionservice.findByUuid(sectionUuid))
                    .orgSlug(orgSlug)
                    .build()));
    notification.setSectionNotifications(sectionNotifications);
    notification.setGradeSlug(notificationRequest.gradeSlug());
  }

  private Notification buildNotification(
      NotificationDto.NotificationRequest notificationRequest,
      String orgSlug,
      String teacherAuthId) {
    var user = userRepository.getUserByAuthUserId(teacherAuthId);
    return Notification.builder()
        .title(notificationRequest.title())
        .message(notificationRequest.message())
        .attachments(notificationRequest.attachment())
        .link(notificationRequest.link())
        .notificationType(NotificationType.valueOf(notificationRequest.notificationType().name()))
        .organization(organizationRepository.findBySlug(orgSlug))
        .gradeSlug(notificationRequest.gradeSlug())
        .createdBy(user.getTeacherInfo())
        .orgSlug(orgSlug)
        .categoryId(notificationRequest.categoryId())
        .messageTemplate(
            notificationRequest.messageTemplateId() != null
                ? messageTemplateService.getMessageTemplate(
                    user.getOrganization(), notificationRequest.messageTemplateId())
                : null)
        .build();
  }

  private void buildSectionNotification(
      NotificationDto.NotificationRequest notificationRequest,
      String orgSlug,
      Notification notification) {
    List<SectionNotification> sectionNotifications = new ArrayList<>();

    if (Objects.nonNull(notificationRequest.studentIds())) {
      var students =
          studentService.getStudentsByIdsAndOrgSlug(
              orgSlug, notificationRequest.studentIds(), true);
      buildStudentNotification(students, notification, orgSlug);
    } else {

      var students =
          studentService.getStudentsBySectionUuidsAndOrgSlug(
              notificationRequest.sectionUuids(), orgSlug);
      buildStudentNotification(students, notification, orgSlug);
    }
    notificationRequest
        .sectionUuids()
        .forEach(
            sectionUuid ->
                sectionNotifications.add(
                    SectionNotification.builder()
                        .notification(notification)
                        .section(sectionservice.findByUuid(sectionUuid))
                        .orgSlug(orgSlug)
                        .build()));
    notification.setSectionNotifications(sectionNotifications);
  }

  private void buildClassroomNotification(
      NotificationDto.NotificationRequest notificationRequest,
      String orgSlug,
      Notification notification,
      boolean allClassRooms) {
    var classrooms =
        classroomService.getClassroomsByIdsAndOrgSlug(notificationRequest.classroomIds());
    List<ClassroomNotification> classroomNotifications = new ArrayList<>();

    if (Objects.nonNull(notificationRequest.studentIds())) {
      buildStudentNotification(
          studentService.getStudentsByIdsAndOrgSlug(
              orgSlug, notificationRequest.studentIds(), true),
          notification,
          orgSlug);
    } else if (allClassRooms) {
      var students =
          classroomRepository.getStudentIdsByClassRoom(notificationRequest.classroomIds());
      if (!students.isEmpty()) {
        buildStudentNotification(
            studentService.getStudentsByIdsAndOrgSlug(orgSlug, students, true),
            notification,
            orgSlug);
      }
    } else {
      classrooms.forEach(
          classroom -> buildStudentNotification(classroom.getStudents(), notification, orgSlug));
    }
    classrooms.forEach(
        classroom -> buildTeacherNotification(classroom.getTeachers(), notification, orgSlug));
    classrooms.forEach(
        classroom ->
            classroomNotifications.add(
                ClassroomNotification.builder()
                    .classroom(classroom)
                    .notification(notification)
                    .orgSlug(orgSlug)
                    .build()));
    notification.setClassroomNotifications(classroomNotifications);
  }

  private void buildStudentNotification(
      List<Student> students, Notification notification, String orgSlug) {
    MessageTemplate messageTemplate;
    var user = authService.getUserDetails();
    if (notification.getMessageTemplate() != null) {
      messageTemplate =
          messageTemplateService.getMessageTemplate(
              user.getOrganization(), notification.getMessageTemplate().getId());
    } else {
      messageTemplate = null;
    }

    List<StudentNotification> studentNotifications = new ArrayList<>();
    students.forEach(
        student ->
            studentNotifications.add(
                StudentNotification.builder()
                    .notification(notification)
                    .student(student)
                    .orgSlug(orgSlug)
                    .whatsappStatus(
                        messageTemplate != null
                            ? buildWhatsAppNotificationStatus(
                                messageTemplate.getWhatsAppTemplateId())
                            : NotificationMessageType.NONE)
                    .emailStatus(
                        messageTemplate != null
                            ? buildWhatsAppNotificationStatus(messageTemplate.getEmailTemplateId())
                            : NotificationMessageType.NONE)
                    .smsStatus(
                        messageTemplate != null
                            ? buildWhatsAppNotificationStatus(messageTemplate.getSmsDltTemplateId())
                            : NotificationMessageType.NONE)
                    .build()));
    notification.setStudentNotifications(studentNotifications);
  }

  private NotificationMessageType buildWhatsAppNotificationStatus(String templateId) {
    if (StringUtils.isNotEmpty(templateId)) {
      return NotificationMessageType.FALSE;
    }
    return NotificationMessageType.NONE;
  }

  public List<NotificationDto.StudentNotificationResponse> getStudentNotifications(
      String orgId, Long fromDateInEpoch, String appreciationMsg, Long limit) {

    LocalDate fromDate;

    if (Objects.nonNull(fromDateInEpoch)) {
      fromDate = dateTimeUtil.convertEpochToIso8601Legacy(fromDateInEpoch).toLocalDate();
    } else {
      fromDate = LocalDate.now().minusDays(365);
    }

    var studentId = authService.getStudentDetails().getStudentInfo().getId();
    var orgs =
        new ArrayList<>(
            organizationRepository.getParentOrgByChildOrgSlug(orgId).stream()
                .map(Organization::getSlug)
                .toList());
    orgs.add(orgId);
    orgs.add(WEXL_INTERNAL);
    var notifications =
        notificationRepository.getStudentNotificationsByIdAndOrgSlug(
            orgs, studentId, fromDate.toString(), appreciationMsg, limit);

    if (notifications.isEmpty()) {
      return Collections.emptyList();
    }

    return notifications.stream()
        .map(
            notification -> {
              var studentNotification =
                  notification.getStudentNotifications().stream()
                      .filter(sn -> sn.getStudent().getId() == studentId)
                      .findFirst();
              return NotificationDto.StudentNotificationResponse.builder()
                  .notificationId(notification.getId())
                  .title(notification.getTitle())
                  .message(notification.getMessage())
                  .attributes(
                      studentNotification.map(StudentNotification::getTestAttributes).orElse(null))
                  .teacherName(
                      userService.getNameByUserInfo(notification.getCreatedBy().getUserInfo()))
                  .type(notification.getNotificationType().name())
                  .attachment(convertAttachmentToS3Link(notification.getAttachments()))
                  .link(notification.getLink())
                  .messageTemplateCategory(
                      notification.getCategoryId() != null
                          ? messageTemplateService.getMessageTemplateCategory(
                              notification.getCreatedBy().getUserInfo().getOrganization(),
                              notification.getCategoryId())
                          : null)
                  .createdAt(convertIso8601ToEpoch(notification.getCreatedAt().toLocalDateTime()))
                  .build();
            })
        .toList();
  }

  public List<NotificationDto.StudentNotificationResponse> getStudentNotificationsfromTeacher(
      String orgSlug,
      Long fromDateInEpoch,
      String studentAuthId,
      String appreciationMsg,
      String academicYear,
      Long limit) {
    User user = guardianService.validateUser(studentAuthId);
    var student = studentAuthService.validateStudentByUser(user);
    Long userId;
    if (academicYear != null) {
      var academicData = contentService.getAcademicYearBySlug(orgSlug, academicYear);
      var assetName = academicData.getAssetName();

      var yearParts = assetName.split("-");
      var endYear = yearParts[1];

      var currentYear = LocalDate.now().getYear();

      if (endYear.equals(String.valueOf(currentYear))) {
        userId = student.getId();
      } else {
        userId = student.getPrevStudentId();
      }
    } else {
      userId = student.getId();
    }
    var orgs =
        new ArrayList<>(
            organizationRepository.getParentOrgByChildOrgSlug(orgSlug).stream()
                .map(Organization::getSlug)
                .toList());
    orgs.add(orgSlug);
    orgs.add(WEXL_INTERNAL);
    var notifications =
        notificationRepository.getStudentNotificationsByIdAndOrgSlug(
            orgs,
            userId,
            Objects.nonNull(fromDateInEpoch)
                ? dateTimeUtil.convertEpochToIso8601Legacy(fromDateInEpoch).toLocalDate().toString()
                : null,
            appreciationMsg,
            limit);
    if (notifications.isEmpty()) {
      return Collections.emptyList();
    }
    return buildStudentNotificationResponse(notifications);
  }

  private List<NotificationDto.StudentNotificationResponse> buildStudentNotificationResponse(
      List<Notification> notifications) {
    List<NotificationDto.StudentNotificationResponse> responses = new ArrayList<>();
    for (Notification notification : notifications) {
      responses.add(
          NotificationDto.StudentNotificationResponse.builder()
              .notificationId(notification.getId())
              .title(notification.getTitle())
              .message(notification.getMessage())
              .attributes(notification.getStudentNotifications().getFirst().getTestAttributes())
              .teacherName(userService.getNameByUserInfo(notification.getCreatedBy().getUserInfo()))
              .type(notification.getNotificationType().name())
              .attachment(convertAttachmentToS3Link(notification.getAttachments()))
              .messageTemplateCategory(
                  notification.getCategoryId() != null
                      ? messageTemplateService.getMessageTemplateCategory(
                          notification.getOrgSlug(), notification.getCategoryId())
                      : null)
              .link(notification.getLink())
              .createdAt(convertIso8601ToEpoch(notification.getCreatedAt().toLocalDateTime()))
              .build());
    }
    return responses;
  }

  private Notification getNotificationByIdAndOrgSlug(Long id, String orgSlug) {

    return notificationRepository
        .findByIdAndOrgSlug(id, orgSlug)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST,
                    "error.InvalidNotification",
                    new String[] {orgSlug}));
  }

  public void deleteNotification(String orgSlug, String teacherAuthId, Long id) {

    Teacher teacher =
        userRepository.findByAuthUserIdAndOrganization(teacherAuthId, orgSlug).getTeacherInfo();
    var notifications = notificationRepository.findAllByCreatedByAndOrgSlug(teacher, orgSlug);

    var notification = getNotificationByIdAndOrgSlug(id, orgSlug);

    var notificationIds = notifications.stream().map(Notification::getId).toList();

    if (!notificationIds.contains(notification.getId())) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.InvalidNotification", new String[] {orgSlug});
    }
    notificationRepository.delete(notification);
  }

  public List<NotificationDto.TeacherNotificationResponse> getTeacherAppreciationNotifications(
      String orgSlug, String teacherAuthId, int limit, String appreciationMessage) {
    Teacher teacher =
        userRepository.findByAuthUserIdAndOrganization(teacherAuthId, orgSlug).getTeacherInfo();

    List<Notification> notifications =
        notificationRepository.getTeacherAppreciationNotificationsByIdAndOrgSlug(
            orgSlug, teacher.getId(), limit, appreciationMessage);
    notifications.addAll(
        notificationRepository.getTeacherAppreciationNotificationsToAdmin(
            teacher.getId(), orgSlug, appreciationMessage));

    return buildAppreciationResponse(notifications, limit);
  }

  public List<NotificationDto.TeacherNotificationResponse> getTeacherNotifications(
      String orgSlug, String teacherAuthId, int limit, Boolean isAppreciation) {

    Teacher teacher =
        userRepository.findByAuthUserIdAndOrganization(teacherAuthId, orgSlug).getTeacherInfo();
    List<Notification> notifications =
        notificationRepository.getTeacherNotificationsByIdAndOrgSlug(
            orgSlug, teacher.getId(), limit);
    notifications.addAll(
        notificationRepository.getTeacherNotificationsToAdmin(teacher.getId(), orgSlug, limit));
    if (userRoleHelper.isManager(teacher.getUserInfo())) {
      notifications.addAll(notificationRepository.findAllByCreatedBy(teacher));
    }
    if (notifications.isEmpty()) {
      return Collections.emptyList();
    }
    if (Boolean.TRUE.equals(isAppreciation)) {
      return buildAppreciationResponse(notifications, limit);
    }
    return buildNotificationResponse(notifications, limit);
  }

  private List<NotificationDto.TeacherNotificationResponse> buildAppreciationResponse(
      List<Notification> notifications, int limit) {
    return notifications.stream()
        .map(
            notification -> {
              var sections =
                  notification.getSectionNotifications().stream()
                      .map(SectionNotification::getSection)
                      .toList();
              return NotificationDto.TeacherNotificationResponse.builder()
                  .notificationId(notification.getId())
                  .title(notification.getTitle())
                  .attachment(convertAttachmentToS3Link(notification.getAttachments()))
                  .link(notification.getLink())
                  .type(notification.getNotificationType().name())
                  .message(notification.getMessage())
                  .classroomName(
                      notification.getClassroomNotifications().stream()
                          .map(ClassroomNotification::getClassroom)
                          .map(Classroom::getName)
                          .toList())
                  .sectionIds(sections.stream().map(Section::getUuid).map(UUID::toString).toList())
                  .sectionName(sections.stream().map(Section::getName).toList())
                  .teacherNames(
                      userService.getNameByUserInfo(notification.getCreatedBy().getUserInfo()))
                  .createdAt(convertIso8601ToEpoch(notification.getCreatedAt().toLocalDateTime()))
                  .studentIds(
                      notification.getStudentNotifications().stream()
                          .map(StudentNotification::getStudent)
                          .map(Student::getId)
                          .toList())
                  .build();
            })
        .sorted(
            Comparator.comparing(NotificationDto.TeacherNotificationResponse::createdAt).reversed())
        .distinct()
        .limit(limit)
        .toList();
  }

  private List<NotificationDto.TeacherNotificationResponse> buildNotificationResponse(
      List<Notification> notifications, int limit) {
    return notifications.stream()
        .distinct()
        .map(
            notification -> {
              var sections =
                  notification.getSectionNotifications().stream()
                      .map(SectionNotification::getSection)
                      .filter(Objects::nonNull)
                      .toList();
              return NotificationDto.TeacherNotificationResponse.builder()
                  .notificationId(notification.getId())
                  .title(notification.getTitle())
                  .attachment(convertAttachmentToS3Link(notification.getAttachments()))
                  .link(notification.getLink())
                  .type(notification.getNotificationType().name())
                  .message(notification.getMessage())
                  .classroomName(
                      notification.getClassroomNotifications().stream()
                          .map(ClassroomNotification::getClassroom)
                          .map(Classroom::getName)
                          .toList())
                  .sectionIds(sections.stream().map(Section::getUuid).map(UUID::toString).toList())
                  .sectionName(sections.stream().map(Section::getName).toList())
                  .gradeSlug(notification.getGradeSlug())
                  .teacherNames(
                      userService.getNameByUserInfo(notification.getCreatedBy().getUserInfo()))
                  .createdAt(convertIso8601ToEpoch(notification.getCreatedAt().toLocalDateTime()))
                  .build();
            })
        .sorted(
            Comparator.comparing(NotificationDto.TeacherNotificationResponse::createdAt).reversed())
        .distinct()
        .limit(limit)
        .toList();
  }

  private List<String> convertAttachmentToS3Link(List<String> attachments) {
    if (Objects.nonNull(attachments)) {
      return attachments.stream().map(storageService::generatePreSignedUrlForFetch).toList();
    }
    return Collections.emptyList();
  }

  public NotificationDto.NotificationAttributes getNotificationById(
      String orgSlug, Long notificationId) {
    Notification notification;
    notification = getNotificationByIdAndOrgSlug(notificationId, orgSlug);
    User userInfo = notification.getCreatedBy().getUserInfo();
    List<NotificationDto.NotificationByIdResponse> notification1 =
        notification.getStudentNotifications().stream()
            .map(
                studentNotification -> {
                  Student student = studentNotification.getStudent();
                  User user = student.getUserInfo();
                  Section section = student.getSection();
                  return NotificationDto.NotificationByIdResponse.builder()
                      .userId(user.getAuthUserId())
                      .studentName(user.getFirstName() + " " + user.getLastName())
                      .smsStatus(studentNotification.getSmsStatus())
                      .emailStatus(studentNotification.getEmailStatus())
                      .whatsAppStatus(studentNotification.getWhatsappStatus())
                      .gradeName(section.getGradeName())
                      .gradeSlug(section.getGradeSlug())
                      .sectionName(section.getName())
                      .sectionUuid(section.getUuid().toString())
                      .build();
                })
            .toList();

    return NotificationDto.NotificationAttributes.builder()
        .title(notification.getTitle())
        .teacherName(userInfo.getFirstName() + " " + userInfo.getLastName())
        .notificationStudents(notification1)
        .build();
  }

  @Transactional
  public void editNotificationByTeacher(
      String orgSlug,
      NotificationDto.NotificationRequest notificationRequest,
      String teacherAuthId,
      Long notificationId) {

    Notification notification = getNotificationByIdAndOrgSlug(notificationId, orgSlug);
    sectionNotificationRepository.deleteAll(notification.getSectionNotifications());
    studentNotificationRepository.deleteAll(notification.getStudentNotifications());
    notification.getStudentNotifications().clear();
    notification.getClassroomNotifications().clear();
    notification.getSectionNotifications().clear();
    buildEditNotification(notificationRequest, notification);

    saveNotification(notification, notificationRequest, orgSlug, false);
  }

  private void buildEditNotification(
      NotificationDto.NotificationRequest notificationRequest, Notification notification) {
    notification.setTitle(notificationRequest.title());
    notification.setNotificationType(
        NotificationType.valueOf(notificationRequest.notificationType().name()));
    notification.setAttachments(notificationRequest.attachment());
    notification.setLink(notificationRequest.link());
    notification.setGradeSlug(notificationRequest.gradeSlug());
    notification.setMessage(notificationRequest.message());
  }

  public void buildTeacherNotification(
      List<Teacher> teachers, Notification notification, String orgSlug) {

    List<TeacherNotification> teacherNotifications = new ArrayList<>();
    teachers.forEach(
        teacher ->
            teacherNotifications.add(
                TeacherNotification.builder()
                    .notification(notification)
                    .teacher(teacher)
                    .orgSlug(orgSlug)
                    .build()));
    notification.setTeacherNotifications(teacherNotifications);
  }

  public void createNotificationByGrade(
      String orgSlug,
      NotificationDto.NotificationRequest notificationRequest,
      String teacherAuthId) {
    for (String gradeSlug : notificationRequest.gradeSlugs()) {
      var newNotificationRequest =
          NotificationDto.NotificationRequest.builder()
              .notificationType(notificationRequest.notificationType())
              .attachment(notificationRequest.attachment())
              .message(notificationRequest.message())
              .gradeSlug(gradeSlug)
              .categoryId(notificationRequest.categoryId())
              .classroomIds(notificationRequest.classroomIds())
              .studentIds(notificationRequest.studentIds())
              .link(notificationRequest.link())
              .title(notificationRequest.title())
              .messageTemplateId(notificationRequest.messageTemplateId())
              .sectionUuids(notificationRequest.sectionUuids())
              .build();
      createNotificationByTeacher(orgSlug, newNotificationRequest, teacherAuthId, false);
    }
  }

  public List<NotificationDto.NotificationLogResponse> getNotificationLogById(Long notificationId) {
    List<NotificationDto.NotificationLogResponse> response = new ArrayList<>();
    var notificationLogs = notificationLogRepository.findByNotificationId(notificationId);
    for (NotificationLog notificationLog : notificationLogs) {
      var student = studentService.getStudentById(notificationLog.getStudentId());
      response.add(
          NotificationDto.NotificationLogResponse.builder()
              .notificationId(notificationLog.getNotificationId())
              .studentId(notificationLog.getStudentId())
              .studentName(userService.getNameByUserInfo(student.getUserInfo()))
              .mobileNumber(notificationLog.getMobileNumber())
              .smsStatus(notificationLog.getSmsStatus())
              .whatsAppStatus(notificationLog.getWhatsAppStatus())
              .emailStatus(notificationLog.getEmailStatus())
              .rejected(notificationLog.getRejected())
              .rejectionReason(notificationLog.getRejectionReason())
              .build());
    }
    return response;
  }

  public void createNotificationByOrganization(
      NotificationRequest notificationRequest, String teacherAuthId) {
    for (String orgSlug : notificationRequest.orgSlugs()) {
      createNotificationByTeacher(orgSlug, notificationRequest, teacherAuthId, false);
    }
  }

  public List<GenericMetricResponse> getAllBranchNotificationCount(String orgSlug) {
    Organization organization = organizationRepository.findBySlug(orgSlug);
    List<Organization> childOrgs =
        organizationRepository.getAllChildOrgsByParentId(organization.getId());
    List<GenericMetricResponse> notificationsCount = new ArrayList<>();
    for (var childOrg : childOrgs) {
      Long countOfNotification =
          notificationRepository.getCountOfNotificationsByOrgSlug(childOrg.getSlug());
      Map<String, Object> summary = new HashMap<>();
      summary.put("org_name", childOrg.getName());
      summary.put("notification_count", countOfNotification);
      summary.put("org_slug", childOrg.getSlug());
      GenericMetricResponse metricResponse =
          GenericMetricResponse.builder().summary(summary).build();
      notificationsCount.add(metricResponse);
    }

    return notificationsCount;
  }

  public List<GenericMetricResponse> getNotificationCountByGrade(String orgSlug) {
    var organization = organizationRepository.findBySlug(orgSlug);
    List<String> gradeSlugs = getGradeSlugs();
    List<Map<String, Object>> notificationCountList = new ArrayList<>();
    for (String gradeSlug : gradeSlugs) {
      Grade grade = contentService.getGradeBySlug(gradeSlug);
      Long countOfNotificationsByGradeSlug =
          notificationRepository.getCountOfNotificationsByGradeSlug(orgSlug, gradeSlug);
      Map<String, Object> gradeCount = new HashMap<>();
      gradeCount.put("grade_name", grade.getName());
      gradeCount.put("grade_slug", grade.getSlug());
      gradeCount.put("count", countOfNotificationsByGradeSlug);
      notificationCountList.add(gradeCount);
    }
    Map<String, Object> summary = new HashMap<>();
    summary.put("organization_name", organization.getName());
    summary.put("notification_count", notificationCountList);
    return Collections.singletonList(GenericMetricResponse.builder().summary(summary).build());
  }

  public List<String> getGradeSlugs() {
    return Arrays.asList(
        "lkg", "ukg", "nur", "i", "ii", "iii", "iv", "v", "vi", "vii", "viii", "ix", "x", "xi",
        "xii");
  }

  public List<GenericMetricResponse> getNotificationByCategories(String orgSlug, String gradeSlug) {
    List<Map<String, Object>> notificationCountList = new ArrayList<>();
    List<MessageTemplateCategory> messageTemplateCategoryList =
        messageTemplateCategoryRepository.findAllByOrgSlug(orgSlug);
    for (MessageTemplateCategory messageTemplateCategory : messageTemplateCategoryList) {
      long countOfNotifications1 = 0;
      long countOfNotifications2 = 0;
      countOfNotifications1 =
          notificationRepository.getNotificationsCountByCategory(
              orgSlug, gradeSlug, messageTemplateCategory.getId());
      List<Long> messageTemplateIds =
          messageTemplateRepository.findByMessageTemplateCategory(messageTemplateCategory).stream()
              .map(MessageTemplate::getId)
              .toList();
      if (messageTemplateIds != null && !messageTemplateIds.isEmpty()) {
        countOfNotifications2 =
            notificationRepository.getNotificationsCountByTemplates(
                orgSlug, gradeSlug, messageTemplateIds);
      }
      Map<String, Object> typeCount = new HashMap<>();
      typeCount.put("type", messageTemplateCategory.getCategory());
      typeCount.put("count", Long.valueOf(countOfNotifications1 + countOfNotifications2));
      notificationCountList.add(typeCount);
    }

    Organization organization = organizationRepository.findBySlug(orgSlug);
    Map<String, Object> summary = new HashMap<>();
    summary.put("organization_name", organization.getName());
    summary.put("notification_count", notificationCountList);
    return Collections.singletonList(GenericMetricResponse.builder().summary(summary).build());
  }
}
