package com.wexl.retail.admin;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BasicUserInfo {
  private String email;
  private String username;
  private Long organization;

  @JsonProperty private Long id;

  @JsonProperty("first_name")
  private String firstName;

  @JsonProperty("last_name")
  private String lastName;

  @JsonProperty("auth_user_id")
  private String authUserId;

  @JsonProperty("deleted_at")
  private Timestamp deletedAt;
}
