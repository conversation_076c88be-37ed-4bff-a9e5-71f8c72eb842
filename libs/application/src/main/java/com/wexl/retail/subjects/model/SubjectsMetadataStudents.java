package com.wexl.retail.subjects.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "subject_metadata_students")
public class SubjectsMetadataStudents extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "student_id")
  private Long studentId;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "subject_metadata_id")
  private SubjectsMetaData subjectsMetaData;
}
