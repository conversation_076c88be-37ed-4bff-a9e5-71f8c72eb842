package com.wexl.retail.department.model;

import com.wexl.retail.department.dto.Status;
import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Entity
@Table(name = "departments")
@Data
@AllArgsConstructor
@RequiredArgsConstructor
public class Department extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String orgSlug;
  private String name;
  private Status status;
}
