package com.wexl.retail.interceptor;

import com.wexl.retail.monitoring.interceptor.EndpointInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Component
@RequiredArgsConstructor
public class InterceptorAppConfig implements WebMvcConfigurer {

  private final DeviceLoginInterceptor deviceLoginInterceptor;
  private final EndpointInterceptor endpointInterceptor;

  @Override
  public void addInterceptors(InterceptorRegistry registry) {
    registry.addInterceptor(deviceLoginInterceptor);
    registry.addInterceptor(endpointInterceptor);
  }
}
