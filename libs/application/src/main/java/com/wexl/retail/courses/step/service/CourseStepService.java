package com.wexl.retail.courses.step.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.storage.S3FileUploadResult;
import com.wexl.retail.courses.definition.service.CourseDefinitionService;
import com.wexl.retail.courses.module.service.CourseModuleService;
import com.wexl.retail.courses.step.dto.CourseStepRequest;
import com.wexl.retail.courses.step.dto.CourseStepResponse;
import com.wexl.retail.courses.step.dto.StepReorderRequest;
import com.wexl.retail.courses.step.model.CourseItem;
import com.wexl.retail.courses.step.model.CourseItemType;
import com.wexl.retail.courses.step.repository.CourseStepRepository;
import com.wexl.retail.storage.StorageService;
import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class CourseStepService {

  private final CourseItemFactory courseItemFactory;
  private final CourseModuleService courseModuleService;
  private final CourseStepRepository courseStepRepository;
  private final CourseDefinitionService courseDefinitionService;
  private final StorageService storageService;

  public List<CourseStepResponse> createCourseStep(
      List<CourseStepRequest> courseStepRequests, long moduleId) {
    var courseModule = courseModuleService.findCourseModuleById(moduleId);

    return courseStepRequests.stream()
        .map(
            request -> {
              if (request.getItemType().equals(CourseItemType.PAGE.toString())) {
                return courseItemFactory.createPageItem(courseModule, request);
              } else if (request.getItemType().equals(CourseItemType.ASSET.toString())) {
                return courseItemFactory.createAssetStep(courseModule, request);
              } else if (request.getItemType().equals(CourseItemType.SCHOOL_TEST.toString())) {
                return courseItemFactory.createTestDefStep(courseModule, request);
              } else if (request.getItemType().equals(CourseItemType.MOCK_TEST.toString())) {
                return courseItemFactory.createMockTestStep(courseModule, request);
              } else if (request.getItemType().equals(CourseItemType.SCORM.toString())) {
                return courseItemFactory.createScormStep(courseModule, request);
              } else if (request.getItemType().equals(CourseItemType.ASSIGNMENT.toString())) {
                return courseItemFactory.createAssignmentStep(courseModule, request);
              } else if (request.getItemType().equals(CourseItemType.CONCEPT_VIDEOS.toString())) {
                return courseItemFactory.createConceptVideosStep(courseModule, request);
              }
              throw new ApiException(
                  InternalErrorCodes.INVALID_REQUEST, "error.invalid.courseitemtype");
            })
        .toList();
  }

  public CourseItem findCourseStepById(long stepId) {
    return courseStepRepository
        .findById(stepId)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST,
                    "Course Step Not Found for %s".formatted(stepId)));
  }

  public CourseStepResponse updateCourseStep(CourseStepRequest request, long stepId) {
    var courseStep = findCourseStepById(stepId);
    courseDefinitionService.checkIfCurrentUserIsHavingPermission(
        courseStep.getCourseModule().getCourseDefinition().getOwner().getAuthUserId());
    checkIfCourseStepIsPublished(stepId, courseStep.getPublishedAt());

    if (request.getItemType().equals(CourseItemType.PAGE.toString())) {
      return courseItemFactory.updatePageItem(courseStep, request);
    } else if (request.getItemType().equals(CourseItemType.ASSET.toString())) {
      return courseItemFactory.updateAssetItem(courseStep, request);
    } else if (request.getItemType().equals(CourseItemType.SCHOOL_TEST.toString())) {
      return courseItemFactory.updateTestDefItem(courseStep, request);
    } else if (request.getItemType().equals(CourseItemType.MOCK_TEST.toString())) {
      return courseItemFactory.updateTestDefItem(courseStep, request);
    }

    return null;
  }

  public List<CourseStepResponse> getAllCourseStepsAssociatedToModule(long moduleId) {

    return courseStepRepository.getAllItemsAssociatedToCourseModule(moduleId).stream()
        .map(courseItemFactory::buildCourseStepResponse)
        .toList();
  }

  public void reorderSteps(StepReorderRequest request, long moduleId) {
    var courseSteps = courseStepRepository.getAllItemsAssociatedToCourseModule(moduleId);
    if (!Objects.equals(request.getStepIds().size(), courseSteps.size())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Steps count is different");
    }

    for (int i = 0; i < request.getStepIds().size(); i++) {
      for (var courseStep : courseSteps) {
        if (Objects.equals(courseStep.getId(), request.getStepIds().get(i))) {
          courseStep.setSequenceNumber(i + 1);
        }
      }
    }

    courseStepRepository.saveAll(courseSteps);
  }

  private void checkIfCourseStepIsPublished(long stepId, Timestamp publishedAt) {
    if (Objects.nonNull(publishedAt)) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "%s course step is published, clone this step in case of any modifications."
              .formatted(stepId));
    }
  }

  public S3FileUploadResult uploadScormFiles(CourseStepRequest courseStepRequests, long moduleId) {
    var courseModule = courseModuleService.findCourseModuleById(moduleId);

    String uploadPath =
        String.valueOf(courseItemFactory.createScormStep(courseModule, courseStepRequests));
    return S3FileUploadResult.builder()
        .url(storageService.generatePreSignedUrlForUpload(uploadPath))
        .build();
  }
}
