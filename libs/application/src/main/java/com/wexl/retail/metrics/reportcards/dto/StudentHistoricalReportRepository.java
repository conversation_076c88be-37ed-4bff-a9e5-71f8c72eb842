package com.wexl.retail.metrics.reportcards.dto;

import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface StudentHistoricalReportRepository
    extends JpaRepository<StudentHistoricalReport, Long> {
  @Query(
      value =
          """
                    select (shr.student_first_name|| ' ' || shr.student_last_name) as studentName,shrt.report_type as testName,shr.s3path as path,
                    shr.grade_name as gradeName,shrt.test_definition_id as offlineTestDefinition from student_historical_report shr
                    join student_historical_report_type shrt on shrt.id = shr.report_type_id
                    where shrt.org_slug = :orgSlug and shr.student_id = :studentId and shrt.test_definition_id = :offlineTestDefinitionId
                    """,
      nativeQuery = true)
  Optional<StudentHistoryReportInterface> getReportByOrgSlugAndReportTypeIdAndStudentId(
      String orgSlug, Long studentId, Long offlineTestDefinitionId);

  @Query(
      value =
          """
                  select (shr.student_first_name|| ' ' || shr.student_last_name) as studentName,shr.s3path as path,
                  shrt.report_type as testName,shr.grade_name as gradeName,shrt.test_definition_id as offlineTestDefinition
                  from student_historical_report shr
                  join student_historical_report_type shrt on shrt.id = shr.report_type_id
                  join academic_year ay on ay.id = shr.academic_year_id
                  where shr.grade_slug = :gradeSlug and ay.slug = :yearSlug
                    """,
      nativeQuery = true)
  List<StudentHistoryReportInterface> getReportByAcademicYearIdAndTestDefinitionIdAndGradeSlug(
      String yearSlug, String gradeSlug);
}
