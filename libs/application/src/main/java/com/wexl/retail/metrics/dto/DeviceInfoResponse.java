package com.wexl.retail.metrics.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@RequiredArgsConstructor
public class DeviceInfoResponse {

  private long id;

  @JsonProperty("user_name")
  private String userName;

  @JsonProperty("full_name")
  private String fullName;

  @JsonProperty("last_login")
  private String lastLogin;

  @JsonProperty("user_role")
  private String userRole;

  private String organization;
  private String grade;
  private String section;

  @JsonProperty("installed_mobile_app")
  private Boolean installedMobileApp;

  @JsonProperty("app_version")
  private String appVersion;
}
