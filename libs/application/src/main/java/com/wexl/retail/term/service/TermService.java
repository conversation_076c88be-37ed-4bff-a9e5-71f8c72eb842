package com.wexl.retail.term.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.term.dto.TermDto;
import com.wexl.retail.term.model.Term;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.model.TermAssessmentCategory;
import com.wexl.retail.term.model.TermAssessmentGrade;
import com.wexl.retail.term.repository.TermAssessmentCategoryRepository;
import com.wexl.retail.term.repository.TermAssessmentGradeRepository;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import com.wexl.retail.term.repository.TermRepository;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class TermService {
  private final TermRepository termRepository;
  private final TermAssessmentRepository termAssessmentRepository;
  private final TermAssessmentCategoryRepository termAssessmentCategoryRepository;
  private final TermAssessmentGradeRepository termAssessmentGradeRepository;

  public List<TermDto.TermDetails> getAllTerms(String gradeSlug, String boardSlug) {
    List<TermDto.TermDetails> termsDetails = new ArrayList<>();
    var gradeTermAssessments =
        termAssessmentGradeRepository.findAllByGradeSlugAndBoardSlug(gradeSlug, boardSlug);
    if (gradeTermAssessments.isEmpty()) {
      return Collections.emptyList();
    }
    var gradeAssessments =
        gradeTermAssessments.stream().map(TermAssessmentGrade::getTermAssessments).toList();
    var terms = gradeAssessments.stream().map(TermAssessment::getTerm).collect(Collectors.toSet());
    for (Term term : terms) {
      var commonAssessments =
          term.getTermAssessments().stream().filter(gradeAssessments::contains).toList();
      termsDetails.add(
          TermDto.TermDetails.builder()
              .termId(term.getId())
              .name(term.getName())
              .slug(term.getSlug())
              .termAssessments(buildTermAssessments(commonAssessments))
              .build());
    }
    return termsDetails.stream().sorted(Comparator.comparing(TermDto.TermDetails::termId)).toList();
  }

  private List<TermDto.TermAssessmentDetails> buildTermAssessments(
      List<TermAssessment> termAssessments) {
    List<TermDto.TermAssessmentDetails> termAssessmentDetails = new ArrayList<>();
    for (TermAssessment assessment : termAssessments) {
      termAssessmentDetails.add(
          TermDto.TermAssessmentDetails.builder()
              .assessmentId(assessment.getId())
              .assessmentName(assessment.getName())
              .assessmentSlug(assessment.getSlug())
              .seqNo(assessment.getSeqNumber())
              .build());
    }
    return termAssessmentDetails;
  }

  public void createAssessmentCategory(
      String orgSlug, Long assessmentId, TermDto.TermAssessmentCategoryRequest request) {
    var termAssessment = validateTermAssessment(assessmentId);
    termAssessmentCategoryRepository.save(
        TermAssessmentCategory.builder()
            .name(request.categoryName())
            .seqNo(request.seqNo())
            .termAssessment(termAssessment)
            .orgSlug(orgSlug)
            .build());
  }

  public List<TermDto.TermAssessmentCategoryDetails> getAssessmentCategories(
      String orgSlug, Long termAssessmentId) {
    List<TermDto.TermAssessmentCategoryDetails> response = new ArrayList<>();
    var termAssessment = validateTermAssessment(termAssessmentId);
    var assessmentCategories =
        termAssessmentCategoryRepository.findAllByOrgSlugAndTermAssessmentOrderBySeqNoDesc(
            orgSlug, termAssessment);
    if (assessmentCategories.isEmpty()) {
      return Collections.emptyList();
    }
    for (TermAssessmentCategory category : assessmentCategories) {
      response.add(
          TermDto.TermAssessmentCategoryDetails.builder()
              .id(category.getId())
              .categoryName(category.getName())
              .seqNo(category.getSeqNo())
              .assessmentName(category.getTermAssessment().getName())
              .termName(category.getTermAssessment().getTerm().getName())
              .build());
    }
    return response;
  }

  public void updateAssessmentCategory(
      Long assessmentCategoryId, TermDto.TermAssessmentCategoryRequest request) {
    var assessmentCategory =
        termAssessmentCategoryRepository
            .findById(assessmentCategoryId)
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, "error.AssessmentCategoryNotFound"));
    assessmentCategory.setName(request.categoryName());
    assessmentCategory.setSeqNo(request.seqNo());
    termAssessmentCategoryRepository.save(assessmentCategory);
  }

  public void deleteAssessmentCategory(Long assessmentCategoryId) {
    var category = validateAssessmentCategory(assessmentCategoryId);
    termAssessmentCategoryRepository.delete(category);
  }

  private TermAssessmentCategory validateAssessmentCategory(Long categoryId) {
    return termAssessmentCategoryRepository
        .findById(categoryId)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST, "error.AssessmentCategoryNotFound"));
  }

  public TermAssessment validateTermAssessment(Long assessmentId) {
    var termAssessment = termAssessmentRepository.findById(assessmentId);
    if (termAssessment.isPresent()) {
      return termAssessment.get();
    }
    throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.TermAssessmentNotFound");
  }
}
