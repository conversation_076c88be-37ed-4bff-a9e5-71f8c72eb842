package com.wexl.retail.products.service;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.courses.definition.model.CourseDefinition;
import com.wexl.retail.courses.definition.repository.CourseDefinitionRepository;
import com.wexl.retail.courses.enrollment.model.CourseEnrollmentMetadata;
import com.wexl.retail.courses.enrollment.model.CourseSchedule;
import com.wexl.retail.courses.enrollment.model.CourseScheduleInst;
import com.wexl.retail.courses.enrollment.repository.CourseScheduleInstRepository;
import com.wexl.retail.courses.enrollment.repository.CourseScheduleRepository;
import com.wexl.retail.courses.enrollment.service.CourseScheduleInstService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.products.dto.CourseDto;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.telegram.service.UserService;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ProductCourseEnrollmentService {

  private final AuthService authService;
  private final StudentRepository studentRepository;
  private final CourseDefinitionRepository courseDefinitionRepository;
  private final CourseScheduleRepository courseScheduleRepository;
  private final CourseScheduleInstService courseScheduleInstService;
  private final CourseScheduleInstRepository courseScheduleInstRepository;
  private final UserService userService;

  public CourseDto.CourseEnrollResponse enrollStudentsToProductCourses(
      String orgSlug, String studentAuthId, Long courseDefId) {
    User user = authService.getUserByAuthUserId(studentAuthId);
    Student student = studentRepository.findByUserId(user.getId());
    var courseDef = courseDefinitionRepository.findById(courseDefId);
    if (courseDef.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "");
    }
    var courseList = courseScheduleRepository.findByCourseDefinition(courseDef.get());
    if (courseList.isEmpty()) {
      saveCourseAndEnrollStudent(courseDef, student);
      return buildCourseEnrollResponse(student, courseDef.get());
    }
    var studentEnrolledCourses =
        courseScheduleInstRepository.findByOrgSlugAndStudentId(orgSlug, student.getUserInfo());
    if (studentEnrolledCourses.isEmpty()) {
      enrollStudentToCourse(courseList, courseDef, student);
    } else {
      var enrolledCourse =
          studentEnrolledCourses.stream().map(CourseScheduleInst::getCourseSchedule).toList();
      var newCourses =
          courseList.stream().filter(course -> !enrolledCourse.contains(course)).toList();
      enrollStudentToCourse(newCourses, courseDef, student);
    }
    return buildCourseEnrollResponse(student, courseDef.get());
  }

  private void saveCourseAndEnrollStudent(Optional<CourseDefinition> courseDef, Student student) {
    if (courseDef.isPresent()) {
      var course = courseScheduleRepository.save(buildCourse(courseDef));
      courseScheduleInstService.updateCourseScheduleStatusTimeBombEntry(course);
      enrollStudentToCourse(Collections.singletonList(course), courseDef, student);
    }
  }

  private void enrollStudentToCourse(
      List<CourseSchedule> courseSchedules, Optional<CourseDefinition> courseDef, Student student) {
    if (courseDef.isPresent()) {
      courseSchedules.forEach(
          courseSchedule ->
              courseScheduleInstService.enrollStudentsToCourse(
                  courseSchedule,
                  Collections.singletonList(student.getUserInfo().getId()),
                  courseDef.get().getId()));
    }
  }

  private CourseSchedule buildCourse(Optional<CourseDefinition> courseDef) {
    if (courseDef.isEmpty()) {
      return CourseSchedule.builder().build();
    }
    return CourseSchedule.builder()
        .courseDefinition(courseDef.get())
        .endDate(Timestamp.valueOf(LocalDateTime.now().plusDays(1000)))
        .startDate(Timestamp.valueOf(LocalDateTime.now()))
        .orgSlug(authService.getUserDetails().getOrganization())
        .teacherId(userService.findUserById(authService.getUserDetails().getId()))
        .metadata(
            CourseEnrollmentMetadata.builder()
                .sections(new ArrayList<>())
                .grades(new ArrayList<>())
                .build())
        .build();
  }

  public CourseDto.CourseEnrollResponse buildCourseEnrollResponse(
      Student student, CourseDefinition courseDef) {
    var course = courseScheduleRepository.findByCourseDefinition(courseDef);
    var courseEnrollment =
        courseScheduleInstRepository.getStudentEnrolledCourseById(
            student.getUserInfo().getId(), course.getFirst().getId());
    if (courseEnrollment == null) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.InvalidEnrollment ",
          new String[] {String.valueOf(course.getFirst().getId())});
    }
    return CourseDto.CourseEnrollResponse.builder()
        .courseId(course.getFirst().getId())
        .courseDefId(courseDef.getId())
        .build();
  }
}
