package com.wexl.retail.coding;

import com.wexl.retail.coding.dto.CodeSubmissionRequest;
import com.wexl.retail.coding.dto.judge.Request;
import com.wexl.retail.coding.dto.judge.Response;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsStudent;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

@RequiredArgsConstructor
@RestController
@RequestMapping("/orgs/{orgSlug}")
public class OnlineCodeSubmissionController {

  private final RestTemplate restTemplate;

  @IsStudent
  @PostMapping("/students/{authUserId}/code-submissions")
  public Response evaluateCodeSubmission(@RequestBody CodeSubmissionRequest codeSubmissionRequest) {

    int languageCode = getLanguageCode(codeSubmissionRequest.getLanguage());
    var request =
        Request.builder()
            .sourceCode(codeSubmissionRequest.getCode())
            .languageId(languageCode)
            .build();
    var responseEntity =
        restTemplate.exchange(
            "https://judge0-ce.p.rapidapi.com/submissions?base64_encoded=true&wait=true",
            HttpMethod.POST,
            getRequestEntity(request),
            Response.class);
    if (responseEntity.getStatusCode().is2xxSuccessful()) {
      return responseEntity.getBody();
    }
    throw new ApiException(
        InternalErrorCodes.SERVER_ERROR,
        "Unable to process request [" + responseEntity.getStatusCodeValue() + "]");
  }

  private int getLanguageCode(String language) {
    if ("java".equals(language)) {
      return 91;
    }
    if ("python2".equals(language)) {
      return 70;
    }
    if ("python3".equals(language)) {
      return 92;
    }
    if ("c".equals(language)) {
      return 50;
    }
    if ("c++".equals(language)) {
      return 52;
    }
    throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Unknown language");
  }

  private HttpEntity<Request> getRequestEntity(Request request) {
    var headers = new HttpHeaders();
    headers.add(HttpHeaders.CONTENT_TYPE, "application/json");
    headers.add("X-RapidAPI-Key", "**************************************************");
    headers.add("X-RapidAPI-Host", "judge0-ce.p.rapidapi.com");
    return new HttpEntity<>(request, headers);
  }
}
