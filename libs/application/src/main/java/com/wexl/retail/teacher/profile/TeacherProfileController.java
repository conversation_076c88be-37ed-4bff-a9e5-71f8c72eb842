package com.wexl.retail.teacher.profile;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsTeacher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/teacher/profile")
@IsTeacher
public class TeacherProfileController {
  @Autowired TeacherProfileService teacherProfileService;
  @Autowired AuthService authService;

  @GetMapping
  public TeacherProfileResponse getAllParentProfiles() throws ApiException {

    return teacherProfileService.getProfileDetails(authService.getTeacherDetails());
  }

  @PostMapping
  public TeacherProfileResponse submitProfile(@RequestBody TeacherProfileRequest profileRequest) {

    return teacherProfileService.updateProfile(profileRequest, authService.getTeacherDetails());
  }
}
