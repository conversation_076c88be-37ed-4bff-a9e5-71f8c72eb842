package com.wexl.retail.section.service;

import com.wexl.retail.model.Teacher;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.domain.TeacherSection;
import com.wexl.retail.section.repository.TeacherSectionRepository;
import java.sql.Timestamp;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class TeacherSectionService {

  @Autowired private TeacherSectionRepository teacherSectionRepository;

  public TeacherSection save(TeacherSection teacherSection) {
    return teacherSectionRepository.save(teacherSection);
  }

  public TeacherSection findByTeacherAndSection(Teacher teacher, Section section) {
    return teacherSectionRepository.findByTeacherAndSection(teacher, section);
  }

  public int addSectionIfRemovedAlready(long sectionId, long teacherId, Timestamp updatedAt) {
    return teacherSectionRepository.addSectionIfRemovedAlready(sectionId, teacherId, updatedAt);
  }

  public boolean isSectionMappedToTeacher(Teacher teacher, Section section) {
    final var teacherSection = teacherSectionRepository.findByTeacherAndSection(teacher, section);
    return !Objects.isNull(teacherSection);
  }
}
