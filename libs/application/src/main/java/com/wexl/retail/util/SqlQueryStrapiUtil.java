package com.wexl.retail.util;

import org.springframework.stereotype.Component;

/**
 * This is a legacy class to support sql queries from strapi. Eventually, we will delete this class
 * and move all the queries to the respective repositories.
 */
@Component
public class SqlQueryStrapiUtil {

  public static final String EXAM_COMPLETED_BY_STUDENT_FOR_TEACHER =
      """
          select e.created_at,
          (select subject_name from strapi_data where subject_id = e.subject_id limit 1 ) as subject_name,
          CASE
          when e.exam_type=4 then 'Scheduled Test'
          when e.exam_type=5 then 'School Test'
          when e.exam_type=6 then 'Worksheet'
          when e.exam_type=7 then 'Assignment'
          when e.exam_type=8 then 'Revision Exam'
          when e.exam_type=9 then 'Course Assignment'
          when e.exam_type=10 then 'Course Test'
          END exam_type,
          e.id,
          e.corrected,
          case\s
          when e.exam_type in (4,7,8,9,10) then
          (case when td.test_name is null then (
          select test_definitions.test_name from test_definitions inner join exams ex1 on test_definitions.id = ex1.test_definition_id where ex1.id = e.id)
          else (td.test_name) end)
          when e.exam_type=5 then (select test_definitions.test_name from test_definitions inner join exams ex1 on test_definitions.id = ex1.test_definition_id where ex1.id = e.id)
          when e.exam_type=6 then (select test_definitions.test_name from test_definitions inner join exams ex1 on test_definitions.id = ex1.test_definition_id where ex1.id = e.id)
          end test_name
          from exams e\s
          left join students s on s.id = e.student_id\s
          left join users u on u.id = s.user_id\s
          left join student_teachers st on u.id = st.student_id\s
          left join test_schedule ts on ts.id = e.schedule_test_id
          left join test_definitions td on td.id = ts.test_definition_id
          where\s
          e.is_completed = true
          and e.corrected = false
          and s.id = %s
          and st.deleted_at IS NULL\s
          and st.teacher_id = %s
          and e.deleted_at is NULL
          order by e.created_at desc
          """;

  public static final String EXAM_COMPLETED_BY_STUDENT_FOR_STUDENT =
      """
          select e.created_at,
          (select subject_name from exams where subject_id = e.subject_id limit 1 ) as subject_name,
          CASE
          when e.exam_type=4 then 'Scheduled Test'
          when e.exam_type=5 then 'School Test'
          when e.exam_type=6 then 'Worksheet'
          when e.exam_type=7 then 'Assignment'
          when e.exam_Type=9 then 'Course Assignment'
          when e.exam_type=10 then 'Course Test'
          END exam_type,
          e.id,
          e.corrected,
          case
          when e.exam_type in (4,7) then
          (case when td.test_name is null then (
          select test_definitions.test_name from test_definitions inner join exams ex1 on test_definitions.id = ex1.test_definition_id where ex1.id = e.id)
          else (td.test_name) end)
          when e.exam_type in (5,6,9,10) then (select test_definitions.test_name from test_definitions inner join exams ex1 on test_definitions.id = ex1.test_definition_id where ex1.id = e.id)
          end test_name
          from exams e
          left join students s on s.id = e.student_id
          left join users u on u.id = s.user_id
          left join test_schedule ts on ts.id = e.schedule_test_id
          left join test_definitions td on td.id = ts.test_definition_id
          where
          e.is_completed = true
          and e.corrected = false
          and e.deleted_at is NULL
          and u.id = %s
          """;
  public static final String ALL_TEACHER_STUDENT_LIST_BY_GRADE =
      """
            with student_count as
                           (select distinct u.id as student_id ,td.user_id as teacher_Id,null as deleted_at,ts.subject_slug
                    ,ts.section_id
                    from users u inner join students s on u.id=s.user_id
                    left JOIN teacher_subjects ts ON s.section_id = ts.section_id
                    JOIN teacher_details td ON ts.teacher_id = td.id
                        where u.deleted_at is null
                           )
                  select
                      distinct s.id, u.user_name, concat(u.first_name,' ',u.last_name) as first_name,
                               s.class_id,
                               (select grade_name from strapi_data where grade_id = s.class_id limit 1 ) as class_name,
                               s.roll_number as admission_number,
                               p.email, p.mobile_number,s.board_id,
                               st.teacher_id, st.deleted_at,
                               sec.uuid,
                               sec.name as sectionName
                  from student_count st
                           left join users u on u.id = st.student_id
                           left join users p on p.id = u.created_by
                           left join students s on u.id = s.user_id
                           inner join sections sec on sec.id = s.section_id
                  where s.class_id = %s and st.teacher_id = %s
          """;

  public static final String ALL_TEACHER_STUDENT_LIST =
      """
          select
          u.id, u.first_name, u.last_name,
          s.class_id,
          (select grade_name from strapi_data where grade_id = s.class_id limit 1 ) as class_name,
          p.email, p.mobile_number,
          st.teacher_id, st.deleted_at
          from student_teachers st
          left join users u on u.id = st.student_id
          left join users p on p.id = u.created_by
          left join students s on u.id = s.user_id
          where
          st.teacher_id = %s
          and st.deleted_at IS NULL
          """;

  public static final String ALL_GRADE_WITH_STUDENT_COUNT_FOR_TEACHER =
      """
          with student_count as
            (
                       select distinct u.id as student_id ,td.user_id as teacher_Id,null as deleted_at,ts.subject_slug
            ,ts.section_id
            from users u
            inner join students s on u.id=s.user_id
            left JOIN teacher_subjects ts ON s.section_id = ts.section_id
            JOIN teacher_details td ON ts.teacher_id = td.id
                  where u.deleted_at is null
                   )
          select distinct st.section_id, s.grade_id,
                          (select grade_name from strapi_data where grade_id = s.grade_id limit 1 ) as class_name,
                          s.name,
                          count (student_id)  over (partition by section_id ) from student_count st
                                                                                       left join sections s on s.id = st.section_id
                                                                                       left join users u on u.id = st.student_id
                                                                                       left join users p on p.id = u.created_by
          where
                      st.teacher_id = %s
            and u.active is not false
          group by student_id, section_id,s.grade_id, s.name
          """;

  public static final String GET_ALL_CONNECTED_STUDENT_BY_GRADE =
      """
          select
          u.id, u.first_name, u.last_name
          from student_teachers st
          left join users u on u.id = st.student_id
          left join users p on p.id = u.created_by
          left join students s on u.id = s.user_id
          where
          st.deleted_at IS NULL
          and s.class_id = (select grade_id from strapi_data where grade_slug = '%s' limit 1 )
          and st.teacher_id = %s
          """;

  public static final String GET_ALL_CONNECTED_STUDENT_BY_GRADE_AND_BOARD =
      """
          select
          u.id, u.first_name, u.last_name
          from student_teachers st
          left join users u on u.id = st.student_id
          left join users p on p.id = u.created_by
          left join students s on u.id = s.user_id
          where
          st.deleted_at IS NULL
          and s.class_id = (select grade_id from strapi_data where grade_slug = '%s' limit 1 )
          and s.board_id = (select board_id from strapi_data where board_slug = '%s' limit 1 )
          and st.teacher_id = %s
          """;

  public static final String STUDENT_ACTIVITY_ASSOCIATED_TO_TEACHER =
      """
          select e.id as exam_id, concat(u.first_name, ' ', u.last_name) as full_name,case
                    when e.exam_type = 1 then 'WeXL Practice'
                    when e.exam_type = 2 then 'WeXL Test'
                    when e.exam_type = 3 then 'assessment'
                    when e.exam_type = 4 then 'scheduled test'
                end as exam_type, sd.subject_name as subject, sd.chapter_name as chapter,
                e.sub_topic_id as subTopic, e.end_time as completed_at from teacher_details td join users u on u.id = td.user_id
          join teacher_sections ts on ts.teacher_id = td.id
          join exams e on e.section_id = ts.section_id
          left join strapi_data sd on sd.subject_id = e.subject_id and sd.chapter_id = e.chapter_id
          where  u.id=%s and e.is_completed is true and e.end_time > current_date - 7
          order by e.end_time
          """;

  public static final String ALL_SCHEDULED_TESTS_BY_TEACHER =
      """
          select ts.id as schedule_id,td.id as test_id, td.test_name as title, grd.grade_name as grade,
                sub.subject_name as subject, ts.start_date as scheduled_at
          from test_definitions td
                inner join test_schedule ts on td.id = ts.test_definition_id
                inner join (select distinct grade_name, grade_slug from strapi_data sd) grd on td.grade_slug = grd.grade_slug
                inner join (select distinct subject_name, subject_slug from strapi_data sd) sub on td.subject_slug = sub.subject_slug
          where round(date_part('epoch', ts.end_date)) > round( date_part( 'epoch', now() ) ) and td.teacher_id=%s
          order by ts.end_date asc;
          """;

  public static final String COUNT_OF_STUDENTS_ASSOCIATED_TO_TEACHER =
      """
          with count as (with student_count as  (
             SELECT s.user_id AS student_id,
                    td.user_id AS teacher_id,
                    null as deleted_at,
                    ts.subject_slug,
                    ts.section_id
             FROM students s
                      JOIN teacher_subjects ts ON s.section_id = ts.section_id
                      JOIN teacher_details td ON ts.teacher_id = td.id
             where s.deleted_at is null
             order by s.user_id desc )

          select distinct st.section_id, s.grade_id,
           (select grade_name from strapi_data where grade_id = s.grade_id limit 1 ) as class_name,
           s.name,
           count (student_id)  over (partition by section_id ) from student_count st
             left join sections s on s.id = st.section_id
             left join users u on u.id = st.student_id
             left join users p on p.id = u.created_by
          where
            st.teacher_id = %s
            and st.deleted_at IS NULL
            and u.active is not false
          group by student_id, section_id,s.grade_id, s.name)
          select sum(count) as count from count
          """;

  public static final String NO_OF_SCHEDULED_TESTS_BY_TEACHER =
      """
          select count(*)
          from test_schedule ts
          inner join test_definitions td on td.id = ts.test_definition_id
          where td.teacher_id=%s
          """;

  public static final String STUDENTS_WITH_ACTIVE_PLAN_ASSOCIATED_TO_TEACHER =
      """
          select count(*)
          from student_teachers st
          inner join students s on s.user_id = st.student_id
          inner join subscriptions sub on sub.student_id  = s.id
          where sub.plan_id != 1 and st.teacher_id=%s
          """;

  public static final String STUDENT_ACTIVITY_PAST_FEW_DAYS =
      """
          select e.id as exam_id,
                           concat(u.first_name, ' ', u.last_name) as full_name,
                           case
                               when e.exam_type = 1 then 'WeXL Practice'
                               when e.exam_type = 2 then 'WeXL Test'
                               when e.exam_type = 3 then 'WeXL Assessment'
                               when e.exam_type = 5 then td.test_name
                               when e.exam_type = 4 then tde.test_name
                               end as exam_type,
                           case
                               when e.exam_type = 1 then sd.subject_name
                               when e.exam_type = 2 then sd.subject_name
                               when e.exam_type = 3 then sd.subject_name
                               when e.exam_type = 4 then tde.subject_slug
                               when e.exam_type = 5 then td.subject_slug
                               else td.subject_slug
                               end as subject,
                            case
                             when e.exam_type = 1 then sd.chapter_name
                             when e.exam_type = 2 then sd.chapter_name
                             when e.exam_type = 3 then sd.chapter_name
                             when e.exam_type = 4 then ' '
                             when e.exam_type = 5 then ' '
                               else sd.chapter_name
                               end as chapter,
                           e.sub_topic_id as subTopic,
                           e.end_time as completed_at
                    from exams e
                             inner join students s on s.id = e.student_id
                             inner join users u on u.id = s.user_id
                             left join test_definitions td on td.id=e.test_definition_id
                             left join test_schedule ts on ts.id=e.schedule_test_id
                             left join test_definitions tde on tde.id=ts.test_definition_id
                             left join strapi_data sd on sd.subject_id = e.subject_id and sd.chapter_id = e.chapter_id
                    where e.exam_type in (1,2,3,4,5)
                    and e.is_completed is true and e.end_time > current_date - 7 and u.id=%s
                    order by e.end_time desc

          """;

  public static final String TEST_EVENT_QUERY =
      """
              select u.user_name as userName,
              e.chapter_name as chapter,
              e.subject_name as subject,
              e.subtopic_name as subTopic,
              s.board_name as board,
              concat(u.first_name, ' ', u.last_name) as fullName,
              u.organization as organization,
              s.grade_name as grade,
              e.corrected,
              case
              when e.exam_type = 1 then 'PRACTICE'
              when e.exam_type = 2 then 'TEST'
              when e.exam_type = 3 then 'ASSESSMENT'
              when e.exam_type = 4 then 'SCHEDULED_TEST'
              when e.exam_type = 5 then 'SCHOOL_TEST'
              when e.exam_type = 6 then 'WORKSHEET'
              when e.exam_type = 7 then 'ASSIGNMENT'
              when e.exam_type = 8 then 'REVISION'
              when e.exam_type = 9 then 'COURSE_ASSIGNMENT'
              when e.exam_type = 10 then 'COURSE_TEST'
              when e.exam_type = 11 then 'MOCK_TEST'
              END as type,
              e.subject_slug as subject_slug ,
              s.grade_slug as grade_slug,
              e.chapter_slug as chapter_slug,
              e.subtopic_slug as subtopic_slug,
              e.exam_difficulty_level_id as exam_difficulty_level_id,
              e.test_definition_id as test_definition_id,
              e.exam_type as exam_type,
              s.grade_id as grade_id,
              e.exam_difficulty_level_id as exam_difficulty_level_id,
              e.test_definition_id as test_definition_id
              from exams e
              inner join students s2 on e.student_id = s2.id
              inner join sections s on s.id = s2.section_id
              inner join users u on u.id = s2.user_id
          where e.id=%s
          """;
  public static final String STUDENT_ACTIVITY_ASSOCIATED_TO_PARENT =
      """
          select e.id as exam_id, concat(u.first_name, ' ', u.last_name) as full_name,u.auth_user_id as authUserId,
                 case
                     when e.exam_type = 1 then 'WeXL Practice'
                     when e.exam_type = 2 then 'WeXL Test'
                     when e.exam_type = 3 then 'assessment'
                     when e.exam_type = 4 then 'scheduled test'
                     end as exam_type, sd.subject_name as subject, sd.chapter_name as chapter,
                 e.sub_topic_id as subTopic, e.end_time as completed_at
          from users parent
                   inner join users u on parent.id=u.created_by
                   inner join students std on std.user_id = u.id
                   inner join exams e on e.student_id = std.id
                   left join strapi_data sd on sd.subject_id = e.subject_id and sd.chapter_id = e.chapter_id
          where e.is_completed is true and e.end_time > current_date - 7 and parent.id=%s
          order by e.end_time desc
          """;

  public static final String GET_ALL_CONNECTED_STUDENT =
      """
          select
          u.id, u.auth_user_id, u.first_name, u.last_name
          from student_teachers st
          left join users u on u.id = st.student_id
          left join users p on p.id = u.created_by
          left join students s on u.id = s.user_id
          where
          st.deleted_at IS NULL
          and st.teacher_id = %s
          """;

  public static final String PRACTICE_TEST_EXAM_COMPLETED_BY_STUDENT_FOR_TEACHER =
      """
          select e.created_at,
          (select subject_name from strapi_data where subject_id = e.subject_id limit 1 ) as subject_name,
          (select chapter_name from strapi_data where chapter_id = e.chapter_id limit 1 ) as chapter_name,
          CASE
           when e.exam_type =1 then 'Practice'
           when e.exam_type =2 then 'Test'
          END exam_type,
          e.id,
          COALESCE(((CAST(NULLIF(e.correct_answers, 0) AS DOUBLE PRECISION) / CAST(NULLIF(e.no_of_questions, 0) AS DOUBLE PRECISION)) * 100), 0) as score,
          e.corrected
          from exams e
          left join students s on s.id = e.student_id
          left join users u on u.id = s.user_id
          left join student_teachers st on u.id = st.student_id
          where
          e.is_completed = true
          and s.id = %s
          and st.deleted_at IS NULL
          and st.teacher_id = %s and e.no_of_questions >= 1
          and e.exam_type in (1,2)
          order by e.created_at desc
          """;

  public static final String PRACTICE_TEST_EXAM_COMPLETED_BY_STUDENT_FOR_STUDENT =
      """
          select e.created_at,
          (select subject_name from strapi_data where subject_id = e.subject_id limit 1 ) as subject_name,
          (select chapter_name from strapi_data where chapter_id = e.chapter_id limit 1 ) as chapter_name,
          CASE
           when e.exam_type =1 then 'Practice'
           when e.exam_type =2 then 'Test'
          END exam_type,
          e.id,
          COALESCE(((CAST(NULLIF(e.correct_answers, 0) AS DOUBLE PRECISION) / CAST(NULLIF(e.no_of_questions, 0) AS DOUBLE PRECISION)) * 100) , 0) as score,
          e.corrected
          from exams e
          left join students s on s.id = e.student_id
          left join users u on u.id = s.user_id
          where
          e.is_completed = true
          and u.id = %s and e.exam_type in (1,2) and e.no_of_questions > 4
          """;

  public String getSqlQuery(String key) {
    return switch (key) {
      case "EXAM_COMPLETED_BY_STUDENT_FOR_TEACHER" -> EXAM_COMPLETED_BY_STUDENT_FOR_TEACHER;
      case "EXAM_COMPLETED_BY_STUDENT_FOR_STUDENT" -> EXAM_COMPLETED_BY_STUDENT_FOR_STUDENT;
      case "ALL_TEACHER_STUDENT_LIST_BY_GRADE" -> ALL_TEACHER_STUDENT_LIST_BY_GRADE;
      case "ALL_TEACHER_STUDENT_LIST" -> ALL_TEACHER_STUDENT_LIST;
      case "ALL_GRADE_WITH_STUDENT_COUNT_FOR_TEACHER" -> ALL_GRADE_WITH_STUDENT_COUNT_FOR_TEACHER;
      case "GET_ALL_CONNECTED_STUDENT_BY_GRADE" -> GET_ALL_CONNECTED_STUDENT_BY_GRADE;
      case "GET_ALL_CONNECTED_STUDENT_BY_GRADE_AND_BOARD" ->
          GET_ALL_CONNECTED_STUDENT_BY_GRADE_AND_BOARD;
      case "STUDENT_ACTIVITY_ASSOCIATED_TO_TEACHER" -> STUDENT_ACTIVITY_ASSOCIATED_TO_TEACHER;
      case "ALL_SCHEDULED_TESTS_BY_TEACHER" -> ALL_SCHEDULED_TESTS_BY_TEACHER;
      case "COUNT_OF_STUDENTS_ASSOCIATED_TO_TEACHER" -> COUNT_OF_STUDENTS_ASSOCIATED_TO_TEACHER;
      case "NO_OF_SCHEDULED_TESTS_BY_TEACHER" -> NO_OF_SCHEDULED_TESTS_BY_TEACHER;
      case "STUDENTS_WITH_ACTIVE_PLAN_ASSOCIATED_TO_TEACHER" ->
          STUDENTS_WITH_ACTIVE_PLAN_ASSOCIATED_TO_TEACHER;
      case "STUDENT_ACTIVITY_PAST_FEW_DAYS" -> STUDENT_ACTIVITY_PAST_FEW_DAYS;
      case "TEST_EVENT_QUERY" -> TEST_EVENT_QUERY;
      case "STUDENT_ACTIVITY_ASSOCIATED_TO_PARENT" -> STUDENT_ACTIVITY_ASSOCIATED_TO_PARENT;
      case "GET_ALL_CONNECTED_STUDENT" -> GET_ALL_CONNECTED_STUDENT;
      case "PRACTICE_TEST_EXAM_COMPLETED_BY_STUDENT_FOR_TEACHER" ->
          PRACTICE_TEST_EXAM_COMPLETED_BY_STUDENT_FOR_TEACHER;
      case "PRACTICE_TEST_EXAM_COMPLETED_BY_STUDENT_FOR_STUDENT" ->
          PRACTICE_TEST_EXAM_COMPLETED_BY_STUDENT_FOR_STUDENT;
      default -> throw new IllegalArgumentException("Invalid key: " + key);
    };
  }
}
