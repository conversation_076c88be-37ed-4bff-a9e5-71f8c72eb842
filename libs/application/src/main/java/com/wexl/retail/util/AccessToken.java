package com.wexl.retail.util;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.model.LoginMethod;
import com.wexl.retail.model.User;
import com.wexl.retail.model.UserRole;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class AccessToken {

  public static final String TEMP_USER_NAME = "TEMP_USER_ID";
  public static final long TEMP_USER_ID = 0;

  public String generateTokenByRole(UserRole userRole, String orgSlug, AuthService authService) {
    User user = new User();
    user.setId(TEMP_USER_ID);
    user.setAuthUserId(TEMP_USER_NAME);
    user.setOrganization(orgSlug);
    return authService.generateAdminAccessToken(user, LoginMethod.SYSTEM_CREATED);
  }

  public String generateAdminToken(AuthService authService) {
    var user = new User();
    user.setId(TEMP_USER_ID);
    user.setAuthUserId(TEMP_USER_NAME);
    return authService.generateAdminAccessToken(user, LoginMethod.SYSTEM_CREATED);
  }
}
