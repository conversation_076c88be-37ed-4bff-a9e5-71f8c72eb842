package com.wexl.retail.student.subject.profiles.repository;

import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.student.subject.profiles.domain.SubjectProfiles;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface SubjectProfilesRepository extends JpaRepository<SubjectProfiles, Long> {

  List<SubjectProfiles> findAllByOrgAndStatusOrderByCreatedAtDesc(Organization org, Boolean status);

  Optional<SubjectProfiles> findByOrgAndId(Organization org, Long id);

  @Query(
      value =
          """
                      select distinct sp.id from subject_profiles sp
                      inner join subject_profiles_details spd on sp.id = spd.subject_profile_id
                      inner join orgs o on spd.source_org_id = o.id
                      where spd.board_slug = :board and spd.grade_slug = :grade and o.slug = :orgSlug  and sp.deleted_at is null order by sp.id""",
      nativeQuery = true)
  List<Long> getSubjectProfilesByBoardGradeOrg(String board, String grade, String orgSlug);

  @Query(
      value =
          """
                      SELECT sp.*
                      FROM public.student_subject_profiles ssp
                      inner join subject_profiles sp on ssp.subject_profile_id = sp.id
                      WHERE ssp.student_id = :studentId and ssp.deleted_at is null""",
      nativeQuery = true)
  List<SubjectProfiles> getSubjectProfileOfStudent(Long studentId);
}
