package com.wexl.retail.pdf.viewer.repository;

import com.wexl.retail.pdf.viewer.domain.Comment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

public interface CommentsRepository
    extends JpaRepository<Comment, Long>, JpaSpecificationExecutor<Comment> {

  @Query(value = "select nextval('public.\"comment_id_seq\"')", nativeQuery = true)
  long getNextValOfCommentIdSeq();
}
