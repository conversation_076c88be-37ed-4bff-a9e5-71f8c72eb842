package com.wexl.retail.documents.service;

import com.wexl.retail.classroom.core.model.Classroom;
import com.wexl.retail.classroom.core.repository.ClassroomRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.storage.S3FileUploadResult;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.documents.dto.ClassGroupType;
import com.wexl.retail.documents.dto.DocumentType;
import com.wexl.retail.documents.dto.DocumentsDto;
import com.wexl.retail.documents.model.Document;
import com.wexl.retail.documents.model.DocumentStudent;
import com.wexl.retail.documents.model.DocumentTeacher;
import com.wexl.retail.documents.repository.DocumentRepository;
import com.wexl.retail.documents.repository.DocumentStudentRepository;
import com.wexl.retail.documents.repository.DocumentTeacherRepository;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.ValidationUtils;
import java.util.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class DocumentService {

  private final DocumentRepository documentRepository;
  private final UserRepository userRepository;
  private final ClassroomRepository classroomRepository;
  private final StudentRepository studentRepository;
  private final TeacherRepository teacherRepository;
  private final SectionRepository sectionRepository;
  private final DocumentStudentRepository documentStudentRepository;
  private final DocumentTeacherRepository documentTeacherRepository;
  private final ValidationUtils validationUtils;
  private final StorageService storageService;

  public void saveDocuments(DocumentsDto.Request request, String orgSlug, String studentAuthId) {
    documentRepository.save(buildDocumentRequest(request, orgSlug, studentAuthId));
  }

  private Document buildDocumentRequest(
      DocumentsDto.Request request, String orgSlug, String studentAuthId) {
    var user = userRepository.findByAuthUserId(studentAuthId);
    if (user.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.StudentNotFound");
    }
    Document document = new Document();
    document.setChapterName(request.chapterName());
    document.setChapterSlug(request.chapterSlug());
    document.setType(request.documentType());
    document.setOrgSlug(orgSlug);
    document.setSubjectName(request.subjectName());
    document.setSubjectSlug(request.subjectSlug());
    document.setIsStudent(request.isStudent());
    document.setIsTeacher(request.isTeacher());
    document.setClassGroupId(request.classGroupId());
    document.setChapterName(request.chapterName());
    document.setClassGroupType(request.classGroupType());
    document.setFileType(request.fileType());
    document.setUploadedBy(studentAuthId);
    document.setPath(request.path());
    document.setTitle(request.title());
    document.setClassGroupName(request.classGroupName());
    document.setDocumentStudents(
        buildTeacherUploadedDocumentsForStudent(request, document, orgSlug));
    document.setDocumentTeachers(buildTeacherDocuments(request, document, orgSlug));

    return document;
  }

  private List<DocumentStudent> buildTeacherUploadedDocumentsForStudent(
      DocumentsDto.Request request, Document document, String orgSlug) {
    List<Student> studentList;
    switch (ClassGroupType.valueOf(request.classGroupType().toString())) {
      case CLASSROOM -> {
        studentList = getClassroomStudents(request.classGroupId(), orgSlug, request.studentIds());
        return buildStudents(document, studentList, orgSlug);
      }
      case SECTION -> {
        var studentIdList =
            userRepository.getStudentIdsOfSections(
                Collections.singletonList(request.classGroupId()), orgSlug);
        studentList = studentRepository.findAllById(studentIdList);
        return buildStudents(document, studentList, orgSlug);
      }
      case INDIVIDUAL -> {
        studentList = studentRepository.findAllById(request.studentIds());
        return buildStudents(document, studentList, orgSlug);
      }
    }
    return Collections.emptyList();
  }

  private List<Student> getClassroomStudents(
      Long classGroupId, String orgSlug, List<Long> studentIds) {
    List<Student> studentList = new ArrayList<>();
    if (classGroupId == null || !studentIds.isEmpty()) {
      return studentRepository.findAllById(studentIds);
    } else {
      var classroom = classroomRepository.findByIdAndOrgSlug(classGroupId, orgSlug);
      if (classroom.isEmpty()) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST,
            "error.ClassRoomValidity.ClassIdAndOrg",
            new String[] {Long.toString(classGroupId), orgSlug});
      }
      var classroomStudents = classroom.stream().map(Classroom::getStudents);
      classroomStudents.forEach(studentList::addAll);
      return studentList;
    }
  }

  private List<DocumentStudent> buildStudents(
      Document document, List<Student> studentList, String orgSlug) {
    List<DocumentStudent> documentStudents = new ArrayList<>();
    studentList.forEach(
        student ->
            documentStudents.add(
                DocumentStudent.builder()
                    .document(document)
                    .studentId(student.getId())
                    .orgSlug(orgSlug)
                    .build()));
    return documentStudents;
  }

  private List<DocumentTeacher> buildTeacherDocuments(
      DocumentsDto.Request request, Document document, String orgSlug) {
    List<Teacher> teacherList = new ArrayList<>();
    switch (ClassGroupType.valueOf(request.classGroupType().name())) {
      case CLASSROOM -> {
        List<Teacher> classroomTeachers =
            getClassroomTeachers(request.classGroupId(), request.studentIds().getFirst(), orgSlug);
        teacherList.addAll(classroomTeachers);
        var orgAdmins = getOrgAdmins(orgSlug);
        if (!orgAdmins.isEmpty()) {
          teacherList.addAll(orgAdmins);
        }
        return buildTeachers(document, teacherList, orgSlug);
      }
      case SECTION -> {
        var optionalSection = sectionRepository.findById(request.classGroupId());
        if (optionalSection.isEmpty()) {
          throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "error.SectionFind.Grade");
        }
        var section = optionalSection.get();
        var teacher = teacherRepository.getTeachersBySections((Set<Section>) section);
        return buildTeachers(document, teacher.stream().toList(), orgSlug);
      }
      case INDIVIDUAL -> {
        var teacher = teacherRepository.findTeacherByStudentIds(request.studentIds(), orgSlug);
        return buildTeachers(document, teacher, orgSlug);
      }
    }
    return Collections.emptyList();
  }

  private List<Teacher> getClassroomTeachers(Long classGroupId, Long studentId, String orgSlug) {
    List<Teacher> teacherList = new ArrayList<>();
    if (classGroupId == null) {
      var student = validationUtils.isStudentValid(studentId);
      var studentClassRooms =
          classroomRepository.findByStudentsAndDeletedAtIsNullOrderByCreatedAtDesc(student);
      studentClassRooms.forEach(classroom -> teacherList.addAll(classroom.getTeachers()));
      return teacherList;
    } else {
      Optional<Classroom> optionalClassroom;
      optionalClassroom = classroomRepository.findById(classGroupId);
      if (optionalClassroom.isEmpty()) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST,
            "error.ClassRoomValidity.ClassIdAndOrg",
            new String[] {Long.toString(classGroupId), orgSlug});
      }
      return optionalClassroom.get().getTeachers();
    }
  }

  private List<Teacher> getOrgAdmins(String orgSlug) {
    return teacherRepository.getAllAdminsByOrg(orgSlug);
  }

  private List<DocumentTeacher> buildTeachers(
      Document document, List<Teacher> teacherList, String orgSlug) {
    List<DocumentTeacher> documentTeachers = new ArrayList<>();
    var filterTeachers = teacherList.stream().distinct().toList();
    filterTeachers.forEach(
        teacher ->
            documentTeachers.add(
                DocumentTeacher.builder()
                    .document(document)
                    .teacherId(teacher.getId())
                    .orgSlug(orgSlug)
                    .build()));
    return documentTeachers;
  }

  public List<DocumentsDto.StudentDocumentResponse> getStudentDocuments(
      String studentAuthId,
      String orgSlug,
      List<String> subjectSlug,
      List<String> chapterSlug,
      List<DocumentType> documentType) {

    var user = validationUtils.isValidUser(studentAuthId);
    var student = user.getStudentInfo();
    var docTypeIds = getDocumentIds(documentType);
    var data =
        documentStudentRepository.getDocumentsByStudent(
            student.getId(), orgSlug, subjectSlug, chapterSlug, docTypeIds);
    return buildStudentDocumentResponse(data);
  }

  private List<Integer> getDocumentIds(List<DocumentType> documentType) {
    List<Integer> doctypes = new ArrayList<>();
    documentType.forEach(type -> doctypes.add(type.ordinal()));
    return doctypes;
  }

  private List<DocumentsDto.StudentDocumentResponse> buildStudentDocumentResponse(
      List<DocumentStudent> documentStudentList) {
    List<DocumentsDto.StudentDocumentResponse> responseList = new ArrayList<>();
    documentStudentList.forEach(
        documentStudent -> {
          var document = documentStudent.getDocument();
          responseList.add(
              DocumentsDto.StudentDocumentResponse.builder()
                  .documentType(document.getType())
                  .chapterName(document.getChapterName())
                  .title(document.getTitle())
                  .chapterSlug(document.getChapterSlug())
                  .isTeacher(document.getIsTeacher())
                  .isStudent(document.getIsStudent())
                  .classGroupId(document.getClassGroupId())
                  .classGroupName(document.getClassGroupName())
                  .classGroupType(document.getClassGroupType())
                  .fileType(document.getFileType())
                  .path(storageService.generatePreSignedUrlForFetch(document.getPath()))
                  .subjectSlug(document.getSubjectSlug())
                  .subjectName(document.getSubjectName())
                  .createdAt(
                      DateTimeUtil.convertIso8601ToEpoch(document.getCreatedAt().toLocalDateTime()))
                  .documentId(document.getId())
                  .build());
        });
    return responseList;
  }

  public List<DocumentsDto.TeacherDocumentResponse> getTeacherDocuments(
      String teacherAuthId,
      String orgSlug,
      List<String> subjectSlug,
      List<String> chapterSlug,
      List<DocumentType> documentType,
      List<String> classroomName,
      List<Long> studentId) {
    var user = validationUtils.isValidUser(teacherAuthId);
    var teacher = user.getTeacherInfo();
    var docTypeIds = getDocumentIds(documentType);
    var teacherDocuments =
        documentTeacherRepository.getDocumentsByTeacher(
            teacher.getId(),
            orgSlug,
            subjectSlug,
            chapterSlug,
            docTypeIds,
            classroomName,
            studentId);
    return buildTeacherDocumentResponse(teacherDocuments, studentId);
  }

  private List<DocumentsDto.TeacherDocumentResponse> buildTeacherDocumentResponse(
      List<DocumentTeacher> documentTeacherList, List<Long> studentIdList) {
    List<DocumentsDto.TeacherDocumentResponse> responseList = new ArrayList<>();
    documentTeacherList.forEach(
        documentTeacher -> {
          var document = documentTeacher.getDocument();
          var uploadedUser = validationUtils.isValidUser(document.getUploadedBy());
          responseList.add(
              DocumentsDto.TeacherDocumentResponse.builder()
                  .documentType(document.getType())
                  .chapterName(document.getChapterName())
                  .title(document.getTitle())
                  .chapterSlug(document.getChapterSlug())
                  .isTeacher(document.getIsTeacher())
                  .isStudent(document.getIsStudent())
                  .classGroupId(document.getClassGroupId())
                  .classGroupName(document.getClassGroupName())
                  .classGroupType(document.getClassGroupType())
                  .fileType(document.getFileType())
                  .path(storageService.generatePreSignedUrlForFetch(document.getPath()))
                  .subjectSlug(document.getSubjectSlug())
                  .subjectName(document.getSubjectName())
                  .uploadedAuthId(document.getUploadedBy())
                  .uploadedBy(uploadedUser.getFirstName() + " " + uploadedUser.getLastName())
                  .createdAt(
                      DateTimeUtil.convertIso8601ToEpoch(document.getCreatedAt().toLocalDateTime()))
                  .documentId(document.getId())
                  .studentDetails(
                      buildStudentDetails(document.getDocumentStudents(), studentIdList))
                  .build());
        });
    return responseList;
  }

  private List<DocumentsDto.StudentDetails> buildStudentDetails(
      List<DocumentStudent> documentStudents, List<Long> studentIdList) {
    List<DocumentsDto.StudentDetails> studentDetailsList = new ArrayList<>();
    documentStudents.forEach(
        documentStudent -> {
          var student = validationUtils.isStudentValid(documentStudent.getStudentId());
          var user = student.getUserInfo();
          if ((studentIdList.isEmpty() || studentIdList.contains(student.getId()))
              && user != null) {
            studentDetailsList.add(
                DocumentsDto.StudentDetails.builder()
                    .id(student.getId())
                    .authId(user.getAuthUserId())
                    .name(user.getFirstName() + " " + user.getLastName())
                    .build());
          }
        });
    return studentDetailsList;
  }

  public S3FileUploadResult uploadDocument(
      String orgSlug, DocumentsDto.UploadDocumentRequest uploadDocumentRequest, String authUserId) {
    UUID documentName = UUID.randomUUID();
    String uploadPath =
        createFileObjectKey(orgSlug, authUserId, documentName, uploadDocumentRequest);
    return S3FileUploadResult.builder()
        .url(storageService.generatePreSignedUrlForUpload(uploadPath))
        .path(uploadPath)
        .previewUrl(storageService.generatePreSignedUrlForFetch(uploadPath))
        .build();
  }

  public String createFileObjectKey(
      String orgSlug,
      String authUserId,
      UUID documentName,
      DocumentsDto.UploadDocumentRequest request) {
    return "%s/%s/%s/%s"
        .formatted(
            orgSlug, Constants.DOCUMENTS, authUserId, documentName + "." + request.extension());
  }

  public void editDocument(
      String authUserId, DocumentsDto.EditDocumentRequest editDocument, Long documentId) {
    var document = validationUtils.checkIfDocumentExist(documentId);
    validateDocument(document, authUserId);
    document.setTitle(editDocument.title());
    documentRepository.save(document);
  }

  public void deleteDocument(String authUserId, Long documentId) {
    var document = validationUtils.checkIfDocumentExist(documentId);
    validateDocument(document, authUserId);
    documentRepository.delete(document);
  }

  private void validateDocument(Document document, String authUserId) {
    if (!document.getUploadedBy().equals(authUserId)) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.Invalid.Document.owner",
          new String[] {authUserId});
    }
  }
}
