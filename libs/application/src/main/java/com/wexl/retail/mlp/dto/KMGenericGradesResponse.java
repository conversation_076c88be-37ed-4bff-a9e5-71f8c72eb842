package com.wexl.retail.mlp.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class KMGenericGradesResponse {
  @JsonProperty("attendance_meter")
  private List<KMSummaryResponse> attendanceMeterList;

  @JsonProperty("knowledge_meter")
  private List<KMSummaryResponse> knowledgeMeterList;
}
