package com.wexl.retail.zoom.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingEndedEventRequest extends ZoomMeetingEventRequest {

  @JsonProperty("payload")
  private MeetingEndedEventPayload payload;

  @Data
  public static class MeetingEndedEventPayload {

    @JsonProperty("object")
    private MeetingEndedEventObject object;

    @JsonProperty("account_id")
    private String accountId;
  }

  @Data
  @EqualsAndHashCode(callSuper = true)
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class MeetingEndedEventObject extends ZoomEventObject {

    @JsonProperty("end_time")
    private String endTime;
  }
}
