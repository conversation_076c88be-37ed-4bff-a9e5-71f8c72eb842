package com.wexl.retail.products.service;

import com.wexl.retail.courses.bundles.model.CourseBundles;
import com.wexl.retail.courses.bundles.repository.CourseBundleRepository;
import com.wexl.retail.courses.bundles.service.CourseBundleService;
import com.wexl.retail.courses.definition.model.CourseDefinition;
import com.wexl.retail.courses.definition.repository.CourseDefinitionRepository;
import com.wexl.retail.ecommerce.ProductDto;
import com.wexl.retail.ecommerce.ProductDto.Courses;
import com.wexl.retail.ecommerce.ProductDto.ProductResponse;
import com.wexl.retail.products.model.ProductCourses;
import com.wexl.retail.products.repository.ProductCourseRepository;
import com.wexl.retail.storage.StorageService;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ProductCourseService {

  private final ProductCourseRepository productCourseRepository;
  private final CourseDefinitionRepository courseDefinitionRepository;
  private final StorageService storageService;
  private final CourseBundleRepository courseBundleRepository;
  private final CourseBundleService courseBundleService;

  public ProductDto.ProductCourses getCoursesByProductId(
      ProductResponse productResponse, String orgSlug) {
    var productCourses =
        productCourseRepository.findByproductRefAndOrgSlug(productResponse.extRef(), orgSlug);
    if (productCourses == null) {
      return null;
    }
    var courses = getProductCourse(productCourses);
    var bundles = getCourseBundles(productCourses);

    return ProductDto.ProductCourses.builder()
        .productKm(bundles.isEmpty() && courses.isEmpty() ? 0.0 : buildProductKm(bundles, courses))
        .bundles(bundles)
        .courses(courses)
        .description(productResponse.description())
        .title(productResponse.title())
        .build();
  }

  private List<Courses> getProductCourse(ProductCourses productCourses) {
    if (productCourses.getCourseDefinitionId() == null) {
      return Collections.emptyList();
    }
    var courseDefinitions =
        courseDefinitionRepository.getByIdOrderByNameAsc(productCourses.getCourseDefinitionId());
    return buildProductCourse(courseDefinitions);
  }

  private List<ProductDto.Courses> buildProductCourse(List<CourseDefinition> courseDefinitions) {
    List<ProductDto.Courses> courses = new ArrayList<>();
    courseDefinitions.forEach(
        courseDefinition ->
            courses.add(
                ProductDto.Courses.builder()
                    .id(courseDefinition.getId())
                    .name(courseDefinition.getName())
                    .description(courseDefinition.getDescription())
                    .thumbNail(
                        courseDefinition.getImagePath() == null
                            ? courseDefinition.getImagePath()
                            : storageService.generatePreSignedUrlForFetch(
                                courseDefinition.getImagePath()))
                    .courseKm(buildCourseKm(courseDefinition))
                    .build()));

    return courses;
  }

  private List<ProductDto.Bundles> getCourseBundles(ProductCourses productCourses) {
    if (productCourses.getCourseBundleIds() == null) {
      return Collections.emptyList();
    }
    var courseBundles = courseBundleRepository.findAllById(productCourses.getCourseBundleIds());
    List<ProductDto.Bundles> bundles = new ArrayList<>();
    courseBundles.forEach(
        bundle ->
            bundles.add(
                ProductDto.Bundles.builder()
                    .name(bundle.getName())
                    .id(bundle.getId())
                    .thumbNail(
                        (bundle.getThumbnail() == null || bundle.getThumbnail().isEmpty())
                            ? null
                            : storageService.generatePreSignedUrlForFetch(bundle.getThumbnail()))
                    .bundleCourses(buildProductCourse(bundle.getCourseDefinitions()))
                    .bundleKm(buildCourseBundleKm(bundle))
                    .build()));
    return bundles;
  }

  private Double buildCourseKm(CourseDefinition courseDefinition) {
    return courseBundleService.buildCourseKm(courseDefinition);
  }

  private Double buildCourseBundleKm(CourseBundles bundle) {
    var courseBundle = courseBundleService.getCourseBundleById(bundle.getId());
    return courseBundle.courseBundleKm();
  }

  private Double buildProductKm(
      List<ProductDto.Bundles> bundles, List<ProductDto.Courses> courses) {
    double bundleKm = 0.0;
    double courseKm = 0.0;
    if (!bundles.isEmpty()) {
      var sum = bundles.stream().mapToDouble(ProductDto.Bundles::bundleKm).sum();
      long count = bundles.stream().filter(s -> s.bundleKm() != 0).count();
      bundleKm = sum == 0 ? 0.0 : sum / count;
    }
    if (!courses.isEmpty()) {
      var sum = courses.stream().mapToDouble(ProductDto.Courses::courseKm).sum();
      long count = courses.stream().filter(s -> s.courseKm() != 0).count();
      courseKm = sum == 0 ? 0.0 : sum / count;
    }
    int totalCount = (bundleKm != 0 ? 1 : 0) + (courseKm != 0 ? 1 : 0);
    if (totalCount != 0) {
      return (double) Math.round((courseKm + bundleKm) / totalCount);
    }
    return 0.0;
  }
}
