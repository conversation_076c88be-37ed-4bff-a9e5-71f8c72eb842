package com.wexl.retail.section.domain;

import com.wexl.retail.model.Model;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.schedules.domain.SectionSchedule;
import jakarta.persistence.*;
import java.util.List;
import java.util.UUID;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Where;

@Entity
@Table(name = "sections")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Where(clause = "deleted_at IS NULL")
public class Section extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "section-sequence-generator")
  @SequenceGenerator(
      name = "section-sequence-generator",
      sequenceName = "sections_seq",
      allocationSize = 1)
  private long id;

  private String name;

  @Enumerated(EnumType.STRING)
  private SectionStatus status;

  @Column(columnDefinition = "VARCHAR(500)")
  private String remarks;

  private UUID uuid = UUID.randomUUID();
  private String organization;
  private Integer gradeId;
  private String gradeName;
  private String gradeSlug;

  private String boardName;
  private String boardSlug;

  @OneToMany(mappedBy = "section", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @Getter(AccessLevel.PRIVATE)
  private List<SectionSchedule> sectionSchedule;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "teacher_id")
  private Teacher classTeacher;
}
