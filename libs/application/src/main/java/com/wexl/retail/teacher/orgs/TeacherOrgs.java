package com.wexl.retail.teacher.orgs;

import com.wexl.retail.model.Model;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.organization.model.Organization;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "teacher_orgs")
public class TeacherOrgs extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "teacher-orgs-generator")
  @SequenceGenerator(
      name = "teacher-orgs-generator",
      sequenceName = "teacher_orgs_seq",
      allocationSize = 1)
  private long id;

  @OneToOne
  @JoinColumn(name = "org_id")
  private Organization org;

  @OneToOne
  @JoinColumn(name = "child_org")
  private Organization childOrg;

  @OneToOne
  @JoinColumn(name = "teacher_id")
  private Teacher teacher;
}
