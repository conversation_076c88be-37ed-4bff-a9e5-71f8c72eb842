package com.wexl.retail.student.exam.revision.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
public class RevisionQuestionsCount {

  @JsonProperty("chapter_slug")
  private String chapterSlug;

  @JsonProperty("revision_questions_count")
  private Integer count;
}
