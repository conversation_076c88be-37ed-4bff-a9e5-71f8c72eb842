package com.wexl.retail.classroom.calendar.service;

import com.wexl.retail.classroom.core.dto.ScheduleInstAttendanceStatus;
import com.wexl.retail.classroom.core.repository.ClassroomRepository;
import com.wexl.retail.classroom.core.repository.ScheduleInstAttendanceDetailsRepository;
import com.wexl.retail.classroom.history.ClassroomHistory;
import com.wexl.retail.classroom.history.ClassroomHistoryRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.model.Student;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.task.domain.TaskInst;
import com.wexl.retail.task.domain.TaskStatus;
import com.wexl.retail.task.domain.TaskType;
import com.wexl.retail.task.repository.TaskInstRepository;
import com.wexl.retail.util.ValidationUtils;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class StudentCalendarService {
  private final UserRepository userRepository;

  private final TaskInstRepository taskInstRepository;

  private final DateTimeUtil dateTimeUtil;
  private final ScheduleInstAttendanceDetailsRepository scheduleInstAttendanceDetailsRepository;
  private final ClassroomRepository classroomRepository;
  private final ValidationUtils validationUtils;
  private final ClassroomHistoryRepository classroomHistoryRepository;

  public Map<String, List<String>> getStudentAllTasksResponse(
      String studentAuthId, Long fromDateInEpoch, Long toDateInEpoch) {
    var fromDate = dateTimeUtil.convertEpochToIso8601(fromDateInEpoch);
    var toDate = dateTimeUtil.convertEpochToIso8601(toDateInEpoch);
    var user = userRepository.findByAuthUserId(studentAuthId);
    if (user.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.StudentNotFound");
    }
    Student student = user.get().getStudentInfo();
    var taskInstList =
        taskInstRepository.getStudentActivitiesByTaskType(
            student.getId(), fromDate.with(LocalTime.MIN), toDate.with(LocalTime.MAX));
    return getChapterName(taskInstList, student.getId(), fromDate, toDate);
  }

  private Map<String, List<String>> getChapterName(
      List<TaskInst> taskInstList, long studentId, LocalDateTime fromDate, LocalDateTime toDate) {
    var classroomDatesList = getClassRoomDates(studentId, fromDate, toDate);
    HashMap<String, List<String>> map = new HashMap<>();
    classroomDatesList.forEach(
        date -> {
          var taskInsts =
              taskInstList.stream()
                  .filter(
                      x ->
                          x.getTask()
                              .getClassroomScheduleInst()
                              .getStartTime()
                              .toLocalDate()
                              .equals(date))
                  .toList();

          List<String> response = buildResponse(studentId, date, taskInsts);

          if (!response.isEmpty()) {
            map.put(date.toString(), response);
          }
        });
    return map;
  }

  private List<LocalDate> getClassRoomDates(
      long studentId, LocalDateTime fromDate, LocalDateTime toDate) {
    var classroomDates =
        classroomRepository.getStudentClassroomsByDate(studentId, fromDate, toDate);
    var student = validationUtils.isStudentValid(studentId);
    var studentClassRoomHistory = classroomHistoryRepository.findAllByStudent(student);
    if (studentClassRoomHistory != null) {
      var classRoomNames =
          studentClassRoomHistory.stream()
              .map(ClassroomHistory::getClassroomName)
              .distinct()
              .toList();
      var dates = classroomRepository.getClassroomDates(classRoomNames);
      classroomDates.addAll(dates);
    }
    List<String> distinctClassroomDates = classroomDates.stream().distinct().toList();
    List<LocalDate> classroomDatesList = new ArrayList<>();
    distinctClassroomDates.forEach(date -> classroomDatesList.add(LocalDate.parse(date)));
    return classroomDatesList;
  }

  private List<String> buildResponse(long studentId, LocalDate date, List<TaskInst> taskInsts) {
    List<String> response = new ArrayList<>();
    var attendanceStatus = getAttendanceStatus(studentId, date);
    if (attendanceStatus != null) {
      if (attendanceStatus.contains("PRESENT") && !taskInsts.isEmpty()) {
        var taskStatus = getTaskStatus(taskInsts);
        response.addAll(taskStatus);
      } else if (attendanceStatus.contains("NOT_MARKED")) {
        var status = getNotMarkedStatus(attendanceStatus, date, taskInsts);
        if (!status.isEmpty()) {
          response.add(status);
        }
      } else {
        response.add(attendanceStatus);
      }
      boolean containsOtherThanClosed =
          response.stream().anyMatch(element -> !element.equals("CLOSED"));
      if (containsOtherThanClosed) {
        response.removeIf(element -> element.equals("CLOSED"));
      }
    }
    return response;
  }

  private String getNotMarkedStatus(String status, LocalDate date, List<TaskInst> taskInsts) {
    if (date.isAfter(LocalDate.now())) {
      return !taskInsts.isEmpty() ? getTaskStatus(taskInsts).getFirst() : "";
    }
    return status;
  }

  private List<String> getTaskStatus(List<TaskInst> taskInsts) {
    List<String> response = new ArrayList<>();
    taskInsts.forEach(
        taskInst -> {
          var taskType = taskInst.getTask().getTaskType();
          var taskStatus = taskInst.getCompletionStatus();

          if (((taskType.equals(TaskType.REVISION)
                      && (taskStatus.equals(TaskStatus.COMPLETED)
                          || taskStatus.equals(TaskStatus.DRAFT)))
                  || taskType.equals(TaskType.PRACTICE)
                  || taskType.equals(TaskType.ASSIGNMENT)
                  || taskType.equals(TaskType.TEST))
              && (!response.contains(taskInst.getTask().getChapterName()))) {
            response.add(taskInst.getTask().getChapterName());
          }
        });
    return response;
  }

  private String getAttendanceStatus(Long studentId, LocalDate date) {
    var attendance =
        scheduleInstAttendanceDetailsRepository.findStudentAttendanceByDate(
            studentId, date.atStartOfDay(), date.plusDays(1).atStartOfDay());
    if (attendance.isEmpty()) {
      return ScheduleInstAttendanceStatus.NOT_MARKED.toString();
    }
    var attendanceStatus = attendance.getFirst().getAttendanceStatus();
    return attendanceStatus.toString();
  }
}
