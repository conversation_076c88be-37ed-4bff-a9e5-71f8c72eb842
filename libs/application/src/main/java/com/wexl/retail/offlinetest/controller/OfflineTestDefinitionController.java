package com.wexl.retail.offlinetest.controller;

import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.offlinetest.dto.OfflineTestScheduleDto;
import com.wexl.retail.offlinetest.service.OfflineTestScheduleService;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}/teachers/{teacherAuthId}")
@RequiredArgsConstructor
public class OfflineTestDefinitionController {

  private final OfflineTestScheduleService offlineTestScheduleService;

  @IsOrgAdmin
  @PostMapping("/offline-test-definition")
  public void saveOfflineTestDefinition(
      @RequestBody OfflineTestScheduleDto.Request request,
      @PathVariable String orgSlug,
      @PathVariable String teacherAuthId) {
    offlineTestScheduleService.saveOfflineTestDefinition(orgSlug, request, teacherAuthId);
  }

  @IsOrgAdmin
  @PutMapping("/offline-test-definition/{testDefinitionId}")
  public void updateOfflineTestDefinition(
      @PathVariable("testDefinitionId") Long testDefinitionId,
      @RequestBody OfflineTestScheduleDto.Request request,
      @PathVariable String orgSlug) {
    offlineTestScheduleService.updateOfflineTestDefinition(orgSlug, testDefinitionId, request);
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/offline-test-definition")
  public List<OfflineTestScheduleDto.Response> getOfflineTestDefinition(
      @PathVariable String teacherAuthId,
      @PathVariable String orgSlug,
      @RequestParam(value = "board_slug", required = false, defaultValue = "")
          List<String> boardSlug,
      @RequestParam(value = "grade_slug", required = false, defaultValue = "")
          List<String> gradeSlug,
      @RequestParam(value = "section_uuid", required = false, defaultValue = "")
          List<String> sectionUuid,
      @RequestParam(value = "subject_slug", required = false, defaultValue = "")
          List<String> subjectSlug,
      @RequestParam(value = "child_org", required = false) String childOrg) {
    if (Objects.nonNull(childOrg)) {
      return offlineTestScheduleService.getOfflineTestDefinition(
          teacherAuthId, boardSlug, gradeSlug, sectionUuid, subjectSlug, childOrg);
    }
    return offlineTestScheduleService.getOfflineTestDefinition(
        teacherAuthId, boardSlug, gradeSlug, sectionUuid, subjectSlug, orgSlug);
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/offline-test-definition/{offlineTestDefinitionId}")
  public OfflineTestScheduleDto.Response getOfflineTestSchedule(
      @PathVariable Long offlineTestDefinitionId) {
    return offlineTestScheduleService.getOfflineTestSchedule(offlineTestDefinitionId);
  }

  @PostMapping("/offline-test-schedules/{offlineTestScheduleId}:publish")
  public void publishOfflineTestSchedule(@PathVariable Long offlineTestScheduleId) {
    offlineTestScheduleService.publishOfflineTestSchedule(offlineTestScheduleId, true);
  }

  @PostMapping("/offline-test-schedules/{offlineTestScheduleId}:unpublish")
  public void unPublishOfflineTestSchedule(@PathVariable Long offlineTestScheduleId) {
    offlineTestScheduleService.publishOfflineTestSchedule(offlineTestScheduleId, false);
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/offline-test-schedules/{offlineTestScheduleId}/students")
  public OfflineTestScheduleDto.Response getOfflineTestScheduleStudents(
      @PathVariable Long offlineTestScheduleId) {
    return offlineTestScheduleService.getOfflineTestScheduleStudents(offlineTestScheduleId);
  }

  @IsOrgAdminOrTeacher
  @PostMapping("/offline-test-schedules/{offlineTestScheduleId}/students")
  public void saveOfflineTestScheduleStudents(
      @RequestBody OfflineTestScheduleDto.StudentsRequest studentsList,
      @PathVariable Long offlineTestScheduleId) {
    offlineTestScheduleService.saveOfflineTestScheduleStudents(offlineTestScheduleId, studentsList);
  }

  @IsOrgAdminOrTeacher
  @PostMapping("/offline-test-schedules/{offlineTestScheduleId}/students:refresh")
  @ResponseStatus(HttpStatus.ACCEPTED)
  public void refreshOfflineTestScheduleStudents(@PathVariable Long offlineTestScheduleId) {
    offlineTestScheduleService.refreshOfflineTestScheduleStudents(offlineTestScheduleId);
  }

  @IsOrgAdminOrTeacher
  @PutMapping("/offline-test-schedule/{offlineTestScheduleId}/subjects")
  public void updateOfflineTestSchedule(
      @PathVariable Long offlineTestScheduleId,
      @RequestBody OfflineTestScheduleDto.Subjects subjects) {
    offlineTestScheduleService.updateOfflineTestSchedule(offlineTestScheduleId, subjects);
  }

  @GetMapping("/offline-test-definition/{offlineTestDefinitionId}/subject")
  public List<OfflineTestScheduleDto.Subjects> getSubjectByOfflineTestDefinitionAndScheduleId(
      @PathVariable Long offlineTestDefinitionId,
      @RequestParam(required = false) Long offlineTestScheduleId) {
    return offlineTestScheduleService.getSubjectByDefinitionAndScheduleId(
        offlineTestDefinitionId, offlineTestScheduleId);
  }

  @DeleteMapping("/offline-test-schedule/{offlineTestScheduleId}/subject")
  public void deleteSubjectsByOfflineTestScheduleId(@PathVariable Long offlineTestScheduleId) {

    offlineTestScheduleService.deleteSubjectsByOfflineTestScheduleId(offlineTestScheduleId);
  }

  @PostMapping("/offline-test-definition/{offlineTestDefinitionId}/subject")
  public void addSubjectsToOfflineTestDefinition(
      @PathVariable Long offlineTestDefinitionId,
      @RequestBody OfflineTestScheduleDto.Subjects subject) {
    offlineTestScheduleService.addSubjectsToOfflineTestDefinition(offlineTestDefinitionId, subject);
  }

  @PostMapping("/offline-test-definition/{offlineTestDefinitionId}/notifications")
  @ResponseStatus(HttpStatus.OK)
  public void sendNotification(
      @PathVariable String orgSlug,
      @PathVariable String teacherAuthId,
      @PathVariable Long offlineTestDefinitionId) {
    offlineTestScheduleService.sendOfflineTestDefinitionNotifications(
        orgSlug, teacherAuthId, offlineTestDefinitionId);
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/offline-test-definition/{offlineTestDefinitionId}/attendance")
  public OfflineTestScheduleDto.AttendanceAndRemarksResponse getAttendance(
      @PathVariable Long offlineTestDefinitionId) {
    return offlineTestScheduleService.getAttendance(offlineTestDefinitionId);
  }

  @IsOrgAdminOrTeacher
  @PostMapping("/offline-test-definition/{offlineTestDefinitionId}/attendance")
  public void saveAttendance(
      @PathVariable Long offlineTestDefinitionId,
      @RequestBody OfflineTestScheduleDto.AttendanceAndRemarksRequest request) {
    offlineTestScheduleService.saveAttendance(offlineTestDefinitionId, request);
  }

  @GetMapping("/offline-test-definition/{offlineTestDefinitionId}/marks-list")
  public OfflineTestScheduleDto.MarksListResponse studentsMarksListResponse(
      @PathVariable Long offlineTestDefinitionId, @RequestParam String sectionUuid) {
    return offlineTestScheduleService.getStudentsMarksListResponse(
        offlineTestDefinitionId, sectionUuid);
  }

  @PostMapping("/offline-test-definition:migrate")
  public void migrateOfflineDefinition(@RequestParam("orgSlug") String orgSlug) {
    offlineTestScheduleService.migrateOfflineDefinition(orgSlug);
  }

  @PostMapping("/offline-test-definition/term-assessment-category:migration")
  public void migrateAssessmentCategoryInOfflineTestDef(@RequestParam("orgSlug") String orgSlug) {
    offlineTestScheduleService.migrateAssessmentCategoryInOfflineTestDef(orgSlug);
  }
}
