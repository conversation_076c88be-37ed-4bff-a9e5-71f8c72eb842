package com.wexl.retail.pdf.viewer.service;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.AuthUtil;
import com.wexl.retail.pdf.viewer.domain.Annotation;
import com.wexl.retail.pdf.viewer.domain.Comment;
import com.wexl.retail.pdf.viewer.domain.CommentsReviewHistory;
import com.wexl.retail.pdf.viewer.domain.DrawingPosition;
import com.wexl.retail.pdf.viewer.domain.HighlightTextRect;
import com.wexl.retail.pdf.viewer.domain.Setting;
import com.wexl.retail.pdf.viewer.dto.request.AnnotationRequest;
import com.wexl.retail.pdf.viewer.dto.request.CommentRequest;
import com.wexl.retail.pdf.viewer.dto.request.CommentsReviewHistoryRequest;
import com.wexl.retail.pdf.viewer.dto.request.DrawingPositionRequest;
import com.wexl.retail.pdf.viewer.dto.request.HighlightTextRectRequest;
import com.wexl.retail.pdf.viewer.dto.response.AnnotationResponse;
import com.wexl.retail.pdf.viewer.dto.response.CommentResponse;
import com.wexl.retail.pdf.viewer.dto.response.CommentsReviewHistoryResponse;
import com.wexl.retail.pdf.viewer.dto.response.DrawingPositionResponse;
import com.wexl.retail.pdf.viewer.dto.response.HighlightTextRectResponse;
import com.wexl.retail.pdf.viewer.dto.response.PdfAnnotationResponse;
import com.wexl.retail.pdf.viewer.repository.AnnotationRepository;
import com.wexl.retail.pdf.viewer.repository.CommentsRepository;
import com.wexl.retail.pdf.viewer.repository.CommentsReviewHistoryRepository;
import com.wexl.retail.pdf.viewer.repository.DrawingPositionsRepository;
import com.wexl.retail.pdf.viewer.repository.HighlightTextRectsRepository;
import io.jsonwebtoken.lang.Strings;
import java.util.ArrayList;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AnnotationService {

  public static final String INSERT = "insert";
  public static final String UPDATE = "update";
  public static final String DELETE = "delete";

  @Autowired CommentsRepository commentsRepository;
  @Autowired AnnotationRepository annotationRepository;
  @Autowired DrawingPositionsRepository drawingPositionsRepository;
  @Autowired HighlightTextRectsRepository highlightTextRectsRepository;
  @Autowired CommentsReviewHistoryRepository reviewHistoryRepository;
  @Autowired private AuthService authService;

  public List<AnnotationRequest> createAnnotation(List<AnnotationRequest> annotationRequests) {
    annotationRequests.forEach(
        annReq -> {
          if (INSERT.equals(annReq.getModified()) || UPDATE.equals(annReq.getModified())) {
            insertOrUpdateAnnotation(annReq);
          } else if (DELETE.equals(annReq.getModified()) && annReq.getId() > 0) {
            deleteAnnotation(annReq);
          }
        });
    return annotationRequests;
  }

  private void deleteAnnotation(AnnotationRequest annReq) {
    annotationRepository.deleteById(annReq.getId());
    annReq.setOldModified(DELETE);
    annReq.setModified("");
  }

  private void insertOrUpdateAnnotation(AnnotationRequest annReq) {
    var annotation = buildAnnotation(annReq);
    if (annReq.getId() <= 0 || INSERT.equals(annReq.getModified())) {
      insertAnnotation(annReq, annotation);
    } else if (annReq.getId() > 0 || UPDATE.equals(annReq.getModified())) {
      updateAnnotation(annReq, annotation);
    }
  }

  private void updateAnnotation(AnnotationRequest annReq, Annotation annotation) {
    annotation.setId(annReq.getId());
    annotation.setDrawingPositions(
        buildDrawingPositionsForUpdation(annReq.getDrawingPositions(), annotation.getId()));
    annotation.setHighlightTextRects(
        buildHighlightTextRectsForUpdation(annReq.getHighlightTextRects(), annotation.getId()));
    annotation.setComments(buildCommentsForUpdation(annReq.getComments(), annotation.getId()));

    annReq.setOldId(annReq.getId());
    annReq.setOldModified(UPDATE);
    annReq.setModified("");
    annotationRepository.save(annotation);
  }

  private List<Comment> buildCommentsForUpdation(
      List<CommentRequest> commentRequests, long annotationId) {
    var comments = new ArrayList<Comment>();

    for (CommentRequest commentRequest : commentRequests) {
      if (commentRequest.getParentId() != 0) {
        commentRequest.setParentId(commentRequests.getFirst().getId());
      }

      commentRequest.setAnnotationId(annotationId);

      if (commentRequest.getModified().equals(DELETE)) {
        commentsRepository.deleteById(commentRequest.getId());
        commentRequest.setOldModified(DELETE);
        commentRequest.setModified("");
      } else {
        insertOrUpdateComment(commentRequest);
        var comment = new Comment();
        BeanUtils.copyProperties(commentRequest, comment);
        comment.setReviewStatuses(
            buildCommentsReviewHistoriesForInsertion(
                comment.getId(), commentRequest.getReviewStatuses()));
        comments.add(comment);
      }
    }
    return comments;
  }

  private void insertOrUpdateComment(CommentRequest commentRequest) {
    commentRequest.setModified("");
    if (commentRequest.getId() <= 0 || INSERT.equals(commentRequest.getModified())) {
      commentRequest.setOldId(commentRequest.getId());
      commentRequest.setId(commentsRepository.getNextValOfCommentIdSeq());
      commentRequest.setModified("");
      commentRequest.setOldModified(INSERT);
    } else if (commentRequest.getId() > 0 || UPDATE.equals(commentRequest.getModified())) {
      commentRequest.setModified("");
      commentRequest.setOldModified(UPDATE);
    }
  }

  private List<HighlightTextRect> buildHighlightTextRectsForUpdation(
      List<HighlightTextRectRequest> highlightTextRectRequests, long annotationId) {

    var highlightTextRects = new ArrayList<HighlightTextRect>();

    highlightTextRectRequests.forEach(
        request -> {
          var highlightText = new HighlightTextRect();
          highlightText.setId(request.getId());
          highlightText.setAnnotationId(annotationId);
          highlightText.setCoordinate(
              String.valueOf(request.getOrigLeft())
                  + ','
                  + request.getOrigTop()
                  + ','
                  + request.getOrigWidth()
                  + ','
                  + request.getOrigHeight());
          highlightText.setDomRotateAngle(request.getDomRotateAngle());
          highlightTextRects.add(highlightText);
        });

    return highlightTextRects;
  }

  private List<DrawingPosition> buildDrawingPositionsForUpdation(
      List<DrawingPositionRequest> drawingPositionRequests, long annotationId) {

    return drawingPositionRequests.stream()
        .map(
            request -> {
              var dp = new DrawingPosition();
              dp.setId(request.getId());
              dp.setCoordinate(String.valueOf(request.getOrigX()) + ',' + request.getOrigY());
              dp.setAnnotationId(annotationId);
              return dp;
            })
        .toList();
  }

  private void insertAnnotation(AnnotationRequest annReq, Annotation annotation) {
    annotation.setId(annotationRepository.getNextValOfAnnotationIdSeq());
    annotation.setDrawingPositions(
        buildDrawingPositionsForInsertion(annReq.getDrawingPositions(), annotation.getId()));
    annotation.setHighlightTextRects(
        buildHighlightTextRectsForInsertion(annReq.getHighlightTextRects(), annotation.getId()));
    annotation.setComments(buildCommentsForInsertion(annReq.getComments(), annotation.getId()));

    annReq.setOldId(annReq.getId());
    annReq.setOldModified(INSERT);
    annReq.setId(annotation.getId());
    annReq.setModified("");
    annotationRepository.save(annotation);
  }

  private Annotation buildAnnotation(AnnotationRequest annReq) {
    var annotation = new Annotation();
    BeanUtils.copyProperties(annReq, annotation);
    annotation.setCoordinate(
        String.valueOf(annReq.getOrigX())
            + ','
            + annReq.getOrigY()
            + ','
            + annReq.getOrigW()
            + ','
            + annReq.getOrigH());

    String iconSrc;
    if (Strings.startsWithIgnoreCase(annReq.getIconSrc(), "data:image")) {
      iconSrc = annReq.getIconSrc();
    } else {
      String[] parts =
          annReq.getIconSrc() == null ? new String[] {""} : annReq.getIconSrc().split("/");
      iconSrc = lastPart(parts);
    }
    annotation.setIcon(iconSrc);

    return annotation;
  }

  public String lastPart(String[] self) {
    if (self.length == 0) {
      throw new NoSuchElementException("Cannot access last() element from an empty Array");
    } else {
      return self[self.length - 1];
    }
  }

  private List<Comment> buildCommentsForInsertion(
      List<CommentRequest> commentRequests, long annotationId) {

    var comments = new ArrayList<Comment>();
    var firstCommentId = 0L;

    for (int i = 0; i < commentRequests.size(); i++) {
      var comment = new Comment();
      var commentRequest = commentRequests.get(i);

      BeanUtils.copyProperties(commentRequest, comment);
      comment.setId(commentsRepository.getNextValOfCommentIdSeq());
      comment.setAnnotationId(annotationId);
      comment.setParentId(firstCommentId);

      commentRequest.setAnnotationId(annotationId);
      commentRequest.setParentId(firstCommentId);
      commentRequest.setOldId(commentRequest.getId());
      commentRequest.setId(comment.getId());

      if (i == 0) {
        firstCommentId = commentRequest.getId();
      }

      comment.setReviewStatuses(
          buildCommentsReviewHistoriesForInsertion(
              comment.getId(), commentRequest.getReviewStatuses()));

      comments.add(comment);
    }
    return comments;
  }

  private List<CommentsReviewHistory> buildCommentsReviewHistoriesForInsertion(
      long commentId, List<CommentsReviewHistoryRequest> reviewHistoryRequests) {

    var reviewStatuses = new ArrayList<CommentsReviewHistory>();
    for (CommentsReviewHistoryRequest reviewHistoryRequest : reviewHistoryRequests) {
      if (reviewHistoryRequest.getId() > 0) {
        continue;
      }

      reviewHistoryRequest.setCommentId(commentId);

      if (reviewHistoryRequest.getId() <= 0 || reviewHistoryRequest.getModified().equals(INSERT)) {
        var reviewStatus = new CommentsReviewHistory();
        reviewStatus.setId(reviewHistoryRepository.getNextValOfReviewHistoryIdSeq());
        reviewStatus.setCommentId(reviewHistoryRequest.getCommentId());
        reviewStatus.setStatus(reviewHistoryRequest.getStatus());
        reviewStatus.setReviewedBy(reviewHistoryRequest.getReviewedBy());
        reviewStatus.setDateReviewed(reviewHistoryRequest.getDateReviewed());

        reviewHistoryRequest.setOldId(reviewHistoryRequest.getId());
        reviewHistoryRequest.setId(reviewStatus.getId());
        reviewHistoryRequest.setOldModified(reviewHistoryRequest.getModified());
        reviewHistoryRequest.setModified("");

        reviewStatuses.add(reviewStatus);
      }
    }
    return reviewStatuses;
  }

  private ArrayList<HighlightTextRect> buildHighlightTextRectsForInsertion(
      List<HighlightTextRectRequest> highlightTextRectRequests, long annotationId) {

    var highlightTextRects = new ArrayList<HighlightTextRect>();

    highlightTextRectRequests.forEach(
        request -> {
          var highlightText = new HighlightTextRect();
          highlightText.setId(highlightTextRectsRepository.getNextValOfHighlightTextRectIdSeq());
          highlightText.setAnnotationId(annotationId);
          highlightText.setCoordinate(
              String.valueOf(request.getOrigLeft())
                  + ','
                  + request.getOrigTop()
                  + ','
                  + request.getOrigWidth()
                  + ','
                  + request.getOrigHeight());
          highlightText.setDomRotateAngle(request.getDomRotateAngle());
          highlightTextRects.add(highlightText);

          request.setAnnotationId(annotationId);
          request.setOldId(request.getId());
          request.setId(highlightText.getId());
        });

    return highlightTextRects;
  }

  private List<DrawingPosition> buildDrawingPositionsForInsertion(
      List<DrawingPositionRequest> drawingPositionRequests, long annotationId) {

    var drawingPositions = new ArrayList<DrawingPosition>();

    drawingPositionRequests.forEach(
        request -> {
          var dp = new DrawingPosition();
          dp.setId(drawingPositionsRepository.getNextValOfDrawingPositionIdSeq());
          dp.setCoordinate(String.valueOf(request.getOrigX()) + ',' + request.getOrigY());
          dp.setAnnotationId(annotationId);
          drawingPositions.add(dp);

          request.setOldId(request.getId());
          request.setId(dp.getId());
        });

    return drawingPositions;
  }

  public PdfAnnotationResponse getAnnotationsByDocId(String docId) {
    var setting = new Setting();
    setting.setKey("ANNOTATIONS_READ_ONLY");
    setting.setValue(AuthUtil.isTeacher(authService.getUserDetails()) ? "false" : "true");

    final List<AnnotationResponse> annotationResponses =
        this.annotationRepository.getAnnotationsByDocId(docId).stream()
            .map(this::buildAnnotationResponse)
            .toList();

    return PdfAnnotationResponse.builder()
        .annotations(annotationResponses)
        .settings(List.of(setting))
        .build();
  }

  private AnnotationResponse buildAnnotationResponse(Annotation annotation) {
    var annotationResponse = new AnnotationResponse();
    BeanUtils.copyProperties(annotation, annotationResponse);
    annotationResponse.setComments(buildCommentResponses(annotation.getComments()));
    annotationResponse.setDrawingPositions(
        buildDrawingPositionResponses(annotation.getDrawingPositions()));
    annotationResponse.setHighlightTextRects(
        buildHighlightTextRectResponses(annotation.getHighlightTextRects()));
    annotationResponse.setCalibrationValue(String.valueOf(annotation.getCalibrationValue()));
    annotationResponse.setReadOnly(Boolean.compare(annotation.getReadOnly(), false));
    return annotationResponse;
  }

  private ArrayList<HighlightTextRectResponse> buildHighlightTextRectResponses(
      List<HighlightTextRect> highlightTextRects) {
    return highlightTextRects.stream()
        .map(
            highlightTextRect -> {
              var highlightTextRectResponse = new HighlightTextRectResponse();
              BeanUtils.copyProperties(highlightTextRect, highlightTextRectResponse);
              return highlightTextRectResponse;
            })
        .collect(Collectors.toCollection(ArrayList::new));
  }

  private ArrayList<DrawingPositionResponse> buildDrawingPositionResponses(
      List<DrawingPosition> drawingPositions) {
    return drawingPositions.stream()
        .map(
            drawingPosition -> {
              var drawingPositionRespone = new DrawingPositionResponse();
              BeanUtils.copyProperties(drawingPosition, drawingPositionRespone);
              return drawingPositionRespone;
            })
        .collect(Collectors.toCollection(ArrayList::new));
  }

  private ArrayList<CommentResponse> buildCommentResponses(List<Comment> comments) {

    return comments.stream()
        .map(
            comment -> {
              var commentResponse = new CommentResponse();
              BeanUtils.copyProperties(comment, commentResponse);
              commentResponse.setReviewStatuses(
                  buildReviewStatusResponses(comment.getReviewStatuses()));
              return commentResponse;
            })
        .collect(Collectors.toCollection(ArrayList::new));
  }

  private ArrayList<CommentsReviewHistoryResponse> buildReviewStatusResponses(
      List<CommentsReviewHistory> commentsReviewHistories) {
    return commentsReviewHistories.stream()
        .map(
            reviewStatus -> {
              var reviewHistoryResponse = new CommentsReviewHistoryResponse();
              BeanUtils.copyProperties(reviewStatus, reviewHistoryResponse);
              return reviewHistoryResponse;
            })
        .collect(Collectors.toCollection(ArrayList::new));
  }
}
