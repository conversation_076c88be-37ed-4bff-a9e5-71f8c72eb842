package com.wexl.retail.qpgen.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.content.model.QuestionType;
import java.util.List;
import lombok.Builder;

public record BluePrintDto() {

  @Builder
  public record Request(
      String title,
      @JsonProperty("total_questions") Long totalQuestions,
      @JsonProperty("total_marks") Long totalMarks,
      @JsonProperty("sections_details") List<Section> sections) {}

  @Builder
  public record InstructionsRequest(String instructions) {}

  @Builder
  public record Section(
      @JsonProperty("section_id") Long sectionId,
      @JsonProperty("question_count") Long questionCount,
      String complexity,
      @JsonProperty("question_type") QuestionType questionType,
      Double marks,
      String tags,
      @JsonProperty("section_name") String sectionName) {}

  @Builder
  public record Sections(
      @JsonProperty("section_id") Long sectionId,
      String complexity,
      String category,
      @JsonProperty("question_type") QuestionType questionType,
      @JsonProperty("section_name") String sectionName) {}

  @Builder
  public record Response(
      Long id,
      String title,
      @JsonProperty("is_used_by_qpGen_Pro") Boolean isIdUsedByQpGenPro,
      @JsonProperty("created_at") Long createdAt,
      @JsonProperty("total_questions") Long totalQuestions,
      @JsonProperty("total_marks") Long totalMarks,
      @JsonProperty("sections_details") List<Section> sections) {}

  @Builder
  public record ContentRequest(
      String subjectSlug,
      String chapterName,
      String chapterSlug,
      String questionType,
      String questionCategory,
      String complexity) {}

  @Builder
  public record ContentRequestForQuestionTags(
      String subjectSlug,
      String chapterName,
      String chapterSlug,
      String questionType,
      String questionTags,
      String complexity) {}
}
