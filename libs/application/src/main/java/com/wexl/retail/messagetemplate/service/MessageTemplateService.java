package com.wexl.retail.messagetemplate.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.messagetemplate.category.model.MessageTemplateCategory;
import com.wexl.retail.messagetemplate.category.repository.MessageTemplateCategoryRepository;
import com.wexl.retail.messagetemplate.dto.MessageTemplateDto;
import com.wexl.retail.messagetemplate.dto.MessageType;
import com.wexl.retail.messagetemplate.model.MessageTemplate;
import com.wexl.retail.messagetemplate.repository.MessageTemplateRepository;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MessageTemplateService {

  @Autowired private MessageTemplateRepository messageTemplateRepository;
  @Autowired private MessageTemplateCategoryRepository messageTemplateCategoryRepository;

  private static final String INVALID_MESSAGE_TEMPLATE = "error.Invalid.MessageTemplate";
  private static final String INVALID_MESSAGE_TEMPLATE_CATEGORY =
      "error.Invalid.MessageTemplateCategory";

  public void createMessageTemplate(String orgSlug, MessageTemplateDto.Request request) {
    messageTemplateRepository.save(buildMessageTemplateRequest(orgSlug, request));
  }

  private MessageTemplate buildMessageTemplateRequest(
      String orgSlug, MessageTemplateDto.Request request) {
    var messageTemplateCategory =
        messageTemplateCategoryRepository
            .findById(request.messageTemplateCategoryId())
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, INVALID_MESSAGE_TEMPLATE_CATEGORY));
    return MessageTemplate.builder()
        .message(request.message())
        .type(request.type())
        .messageTemplateCategory(messageTemplateCategory)
        .orgSlug(orgSlug)
        .smsDltTemplateId(request.smsDltTemplateId())
        .whatsAppTemplateId(request.whatsappTemplateId())
        .emailTemplateId(request.emailTemplateId())
        .whatsAppTemplateId(request.whatsappTemplateId())
        .build();
  }

  public List<MessageTemplateDto.Response> getMessageTemplates(
      String orgSlug, Optional<MessageType> type) {
    var messageTemplateList =
        messageTemplateRepository.findAllByOrgSlugOrderByUpdatedAtDesc(orgSlug);
    if (type.isPresent()) {
      var messageTemplateByType =
          messageTemplateRepository.findAllByOrgSlugAndTypeOrderByUpdatedAtDesc(
              orgSlug, MessageType.fromValue(type.get().name()));
      return buildMessageTemplateResponse(messageTemplateByType);
    }
    return buildMessageTemplateResponse(messageTemplateList);
  }

  private List<MessageTemplateDto.Response> buildMessageTemplateResponse(
      List<MessageTemplate> messageTemplateList) {
    List<MessageTemplateDto.Response> messageTemplateResponse = new ArrayList<>();
    messageTemplateList.forEach(
        messageTemplate ->
            messageTemplateResponse.add(
                MessageTemplateDto.Response.builder()
                    .id(messageTemplate.getId())
                    .message(messageTemplate.getMessage())
                    .type(MessageType.fromValue(messageTemplate.getType().name()))
                    .messageTemplateCategory(
                        Objects.nonNull(messageTemplate.getMessageTemplateCategory())
                            ? messageTemplate.getMessageTemplateCategory().getCategory()
                            : null)
                    .smsDltTemplateId(messageTemplate.getSmsDltTemplateId())
                    .emailTemplateId(messageTemplate.getEmailTemplateId())
                    .whatsAppTemplateId(messageTemplate.getWhatsAppTemplateId())
                    .build()));
    return messageTemplateResponse;
  }

  public void editMessageTemplate(
      String orgSlug, Long templateId, MessageTemplateDto.Request request) {
    var messageTemplate = getMessageTemplate(orgSlug, templateId);
    var templateCategory = getMessageTemplateCategory(orgSlug, request.messageTemplateCategoryId());
    messageTemplate.setMessage(request.message());
    messageTemplate.setMessageTemplateCategory(templateCategory);
    messageTemplate.setType(MessageType.fromValue(request.type().name()));
    messageTemplate.setEmailTemplateId(request.emailTemplateId());
    messageTemplate.setWhatsAppTemplateId(request.whatsappTemplateId());
    messageTemplate.setSmsDltTemplateId(request.smsDltTemplateId());

    messageTemplateRepository.save(messageTemplate);
  }

  public MessageTemplate getMessageTemplate(String orgSlug, Long templateId) {
    var messageTemplate = messageTemplateRepository.findByOrgSlugAndId(orgSlug, templateId);
    if (messageTemplate.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, INVALID_MESSAGE_TEMPLATE);
    }
    return messageTemplate.get();
  }

  public MessageTemplate validateAndGetMessageTemplate(String orgSlug, String categoryName) {
    var possibleMessageTemplateCategory =
        messageTemplateCategoryRepository.findByOrgSlugAndCategory(orgSlug, categoryName);
    if (possibleMessageTemplateCategory.isEmpty()) {
      return null;
    }

    final List<MessageTemplate> byMessageTemplateCategory =
        messageTemplateRepository.findByMessageTemplateCategory(
            possibleMessageTemplateCategory.get());

    if (byMessageTemplateCategory.isEmpty()) {
      return null;
    }

    return byMessageTemplateCategory.getFirst();
  }

  public MessageTemplateCategory getMessageTemplateCategory(String orgSlug, Long categoryId) {
    var messageTemplateCategory =
        messageTemplateCategoryRepository.findByOrgSlugAndId(orgSlug, categoryId);
    if (messageTemplateCategory.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, INVALID_MESSAGE_TEMPLATE_CATEGORY);
    }
    return messageTemplateCategory.get();
  }
}
