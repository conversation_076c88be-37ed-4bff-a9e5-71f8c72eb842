package com.wexl.retail.otp;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.dto.MobileNumberLoginDto;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.email.TwilioSmsService;
import com.wexl.retail.generic.ProfileUtils;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service
@RequiredArgsConstructor
public class OtpService {

  private final RestTemplate restTemplate;

  private final ProfileUtils profileUtils;

  private final AuthService authService;
  private final TwilioSmsService twilioSmsService;
  private final OtpServiceLegacy otpServiceLegacy;
  private final OtpRepository otpRepository;

  @Value("${app.msg91.sendOtp}")
  private String sendOtpUrl;

  @Value("${app.msg91.verifyOtp}")
  private String verifyOtpUrl;

  @Value("${app.msg91.templateId}")
  private String msgTemplateId;

  @Value("${app.msg91.authkey}")
  private String msgAuthkey;

  public OtpResponse sendOtpByMsg91(String mobileNumber) {
    Otp otp = new Otp();
    try {
      if (!profileUtils.isDev()) {
        restTemplate.getForEntity(
            sendOtpUrl.formatted(msgTemplateId, mobileNumber, msgAuthkey), Object.class);
      }
      otp = otpServiceLegacy.saveOtp(mobileNumber);
      log.debug("OTP sent successfully to mobile number [ %s ]".formatted(mobileNumber));
    } catch (ApiException apiException) {

      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.SendOtpError", new String[] {mobileNumber});
    }
    return OtpResponse.builder()
        .otpId(otp.getId())
        .status(true)
        .message("OTP sent success")
        .build();
  }

  public MobileNumberLoginDto.MobileNumberLoginResponse verifyOtpByMsg91(
      MobileNumberLoginDto.MobileNumberLoginRequest mobileNumberLoginRequest, String userAgent) {
    try {
      if (profileUtils.isDev()) {
        return authService.signInWithMobileNumber(
            mobileNumberLoginRequest.mobileNumber(), mobileNumberLoginRequest, userAgent);
      }
      var mobileNumber =
          twilioSmsService.massageMobileNumber(mobileNumberLoginRequest.mobileNumber());
      String endPoint =
          verifyOtpUrl.formatted(mobileNumberLoginRequest.otp(), msgAuthkey, mobileNumber);
      ResponseEntity<Object> response = restTemplate.getForEntity(endPoint, Object.class);
      Map<String, Object> responseBody = (Map<String, Object>) response.getBody();
      if (responseBody == null) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidOtp");
      }
      if ((responseBody.get("type")).equals("error")) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST,
            "error.ErrorVerifyOtp",
            new String[] {mobileNumberLoginRequest.mobileNumber()});
      }
    } catch (ApiException apiException) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.InvalidOtp",
          new String[] {mobileNumberLoginRequest.mobileNumber()});
    }
    return authService.signInWithMobileNumber(
        mobileNumberLoginRequest.mobileNumber(), mobileNumberLoginRequest, userAgent);
  }

  public void validateMobileByOtp(String mobile, Long otpId) {
    if (!otpRepository.existsByIdAndTarget(otpId, mobile)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Mobile.NotMatching");
    }
  }
}
