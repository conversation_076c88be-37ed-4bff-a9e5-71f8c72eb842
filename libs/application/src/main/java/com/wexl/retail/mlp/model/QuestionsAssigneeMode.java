package com.wexl.retail.mlp.model;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum QuestionsAssigneeMode {
  AUTO("AUTO"),
  MANUAL("MANUAL");

  private final String value;

  public static QuestionsAssigneeMode fromValue(String value) {
    if (value == null || "".equals(value)) {
      throw new IllegalArgumentException("Value cannot be null or empty!");
    }

    for (QuestionsAssigneeMode enumEntry : QuestionsAssigneeMode.values()) {
      if (enumEntry.toString().equalsIgnoreCase(value)) {
        return enumEntry;
      }
    }

    throw new IllegalArgumentException("Do not understand the Type " + value);
  }

  @Override
  public String toString() {
    return this.value;
  }
}
