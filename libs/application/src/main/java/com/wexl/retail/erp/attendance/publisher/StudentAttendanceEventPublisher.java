package com.wexl.retail.erp.attendance.publisher;

import com.wexl.retail.erp.attendance.dto.AddAttendanceRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
public class StudentAttendanceEventPublisher {

  @Autowired private ApplicationEventPublisher applicationEventPublisher;

  public void publishStudentAttendance(AddAttendanceRequest request) {
    StudentAttendanceEvent studentAttendancesEvent = new StudentAttendanceEvent(request);
    applicationEventPublisher.publishEvent(studentAttendancesEvent);
  }
}
