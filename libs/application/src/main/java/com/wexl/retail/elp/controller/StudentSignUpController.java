package com.wexl.retail.elp.controller;

import com.wexl.retail.elp.dto.SignupDto;
import com.wexl.retail.elp.service.StudentSignupService;
import com.wexl.retail.otp.OtpResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RequiredArgsConstructor
@RestController
public class StudentSignUpController {

  private final StudentSignupService signupService;

  @PostMapping("/public/orgs/{orgSlug}/code:validation")
  public OtpResponse validateScratchCode(
      @PathVariable String orgSlug,
      @Valid @RequestBody SignupDto.CodeValidateRequest validateRequest) {
    return signupService.validateScratchCodeAndSendOtp(orgSlug, validateRequest);
  }

  @PostMapping("/public/orgs/{orgSlug}/students:signup")
  public void studentElpSignUp(
      @PathVariable String orgSlug, @Valid @RequestBody SignupDto.SignUpRequest elpSignUp) {
    signupService.signUpElpStudent(orgSlug, elpSignUp);
  }

  @PostMapping("/orgs/{orgSlug}/store/students:signup")
  public SignupDto.ElpStudentResponse storeStudentElpSignUp(
      @RequestBody SignupDto.ElpStudentRequest elpSignUp) {
    return signupService.storeStudentElpSignUp(elpSignUp);
  }
}
