package com.wexl.retail.mobile.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record MobileConfigDto() {
  @Builder
  public record PackageResponse(
      Long id,
      @JsonProperty("description") String description,
      @JsonProperty("package_name") String packageName,
      List<KeyValueResponse> data) {}

  @Builder
  public record KeyValueResponse(
      String key,
      @JsonProperty("key_id") Long keyId,
      @JsonProperty("value_id") Long valueId,
      String value,
      boolean status) {}
}
