package com.wexl.retail.student.teacher;

import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/students/{studentAuthId}/teacher-details")
public class StudentTeacherController {

  private final StudentTeacherService studentTeacherService;

  @GetMapping()
  public List<StudentTeacherDto.Response> getStudentTeacherDetails(
      @PathVariable String studentAuthId) {

    return studentTeacherService.getStudentTeacherDetails(studentAuthId);
  }
}
