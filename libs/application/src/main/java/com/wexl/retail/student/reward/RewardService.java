package com.wexl.retail.student.reward;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import java.util.List;
import java.util.logging.Logger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RewardService {
  final Logger logger = Logger.getLogger("StudentAnswerService");
  private final RewardRepository rewardRepository;
  private final RewardTransformer rewardTransformer;
  private final RewardTransactionRepository rewardTransactionRepository;

  /**
   * Constructor.
   *
   * @param rewardRepository @{@link RewardRepository}
   * @param rewardTransformer @{@link RewardTransformer}
   * @param rewardTransactionRepository @{@link RewardTransactionRepository}
   */
  public RewardService(
      RewardRepository rewardRepository,
      RewardTransformer rewardTransformer,
      RewardTransactionRepository rewardTransactionRepository) {
    this.rewardRepository = rewardRepository;
    this.rewardTransformer = rewardTransformer;
    this.rewardTransactionRepository = rewardTransactionRepository;
  }

  protected List<RewardTransactionResponse> findAll(int page, int size, Sort sort) {
    logger.info("Find All called");
    return rewardRepository.findAll(PageRequest.of(page, size, sort)).stream()
        .map(rewardTransformer::mapTransactionToResponse)
        .toList();
  }

  protected RewardTransactionResponse redeemReward(
      RewardTransactionRequest rewardTransactionRequest) {
    logger.info("Redeem Reward Called");
    var rewardTransaction = this.findById(rewardTransactionRequest.getPoints());
    rewardTransaction = rewardRepository.save(rewardTransaction);
    return rewardTransformer.mapTransactionToResponse(rewardTransaction);
  }

  protected RewardTransaction findById(long rewardId) {
    log.info("Find by RewardId called on " + rewardId);
    return rewardRepository
        .findById(rewardId)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST, "error.InvalidRewardTransaction"));
  }

  /**
   * Find by UserId.
   *
   * @param userId long
   * @return List of {@link RewardTransaction}
   */
  public List<RewardTransaction> findByUserId(long userId) {
    log.info("Find by UserId called on : " + userId);
    return rewardRepository.findByUserIdLastTen(
        userId, PageRequest.of(0, 10, Sort.by(Sort.Direction.ASC, "createdAt")));
  }

  public void addRewardTransaction(RewardTransaction rewardTransaction) {
    logger.info("Add RewardTransaction Called");
    rewardTransactionRepository.save(rewardTransaction);
  }
}
