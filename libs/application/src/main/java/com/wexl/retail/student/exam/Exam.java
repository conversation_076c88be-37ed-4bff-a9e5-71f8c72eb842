package com.wexl.retail.student.exam;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wexl.retail.courses.enrollment.model.CourseScheduleItemInst;
import com.wexl.retail.model.Model;
import com.wexl.retail.model.Student;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.student.answer.ExamAnswer;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.school.domain.TestDefinition;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@Entity
@Data
@Table(name = "exams")
@NoArgsConstructor
public class Exam extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "exam_id_seq")
  @SequenceGenerator(name = "exam_id_seq", allocationSize = 1)
  private long id;

  private Long examType;
  private long examDifficultyLevelId;
  private int noOfQuestions;
  private Timestamp startTime = Timestamp.from(Instant.now());
  private Timestamp endTime;
  private int allowedDuration;

  @ManyToOne(cascade = CascadeType.ALL)
  private Student student;

  private Long chapterId;
  private String chapterName;
  private String chapterSlug;
  private long subjectId;
  private String subjectName;
  private String subjectSlug;
  private boolean isCompleted;

  @Column(columnDefinition = "BOOLEAN DEFAULT TRUE")
  private boolean corrected = true;

  private boolean isAllChaptersSelected;
  private Long subTopicId;
  private String subtopicName;
  private String subtopicSlug;
  private Integer correctAnswers;
  private Integer assessmentId;
  private Float marksScored;
  private Float totalMarks;
  private Long taskId;
  private Long taskInstId;
  private String ref;
  private Long betSectionUnitLessonInstId;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "course_schedule_item_inst_id")
  private CourseScheduleItemInst courseScheduleItemInst;

  @ManyToOne(fetch = FetchType.LAZY)
  private ScheduleTest scheduleTest;

  @ManyToOne(fetch = FetchType.LAZY)
  private TestDefinition testDefinition;

  @OneToOne
  @JoinColumn(name = "section_id")
  private Section section;

  @Column(name = "negative_marks_scored")
  private Float negativeMarksScored = 0f;

  private Integer attemptedQuestionsCount;

  Exam(Long examType, Student student) {
    this.examType = examType;
    this.student = student;
    this.ref = UUID.randomUUID().toString();
    this.section = getStudentSection();
  }

  Exam(Long examType, Long taskId, Student student) {
    this.ref = UUID.randomUUID().toString();
    this.examType = examType;
    this.taskId = taskId;
    this.student = student;
  }

  Exam(Long examType, String ref, Student student) {
    this.examType = examType;
    this.student = student;
    this.ref = Objects.requireNonNullElse(ref, UUID.randomUUID().toString());
    this.section = getStudentSection();
  }

  Exam(Long examType, CourseScheduleItemInst courseScheduleItemInst, Student student) {
    this.examType = examType;
    this.student = student;
    this.ref = Objects.requireNonNullElse(ref, UUID.randomUUID().toString());
    this.courseScheduleItemInst = courseScheduleItemInst;
  }

  Exam(Long examType, Student student, Long lessonInstId) {
    this.ref = UUID.randomUUID().toString();
    this.examType = examType;
    this.betSectionUnitLessonInstId = lessonInstId;
    this.student = student;
  }

  public int getStudentClassId() {
    return student.getClassId();
  }

  public Long getStudentId() {
    return student.getId();
  }

  public String getStudentName() {
    return student.getUserInfo().getFirstName() + " " + student.getUserInfo().getLastName();
  }

  public Section getStudentSection() {
    return student.getSection();
  }

  public String getFirebaseToken() {
    return student.getUserInfo().getFirebaseToken();
  }

  @JsonIgnore
  @OneToMany(fetch = FetchType.LAZY, mappedBy = "exam", cascade = CascadeType.ALL)
  private List<ExamAnswer> examAnswers;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb")
  private SectionWiseData.ExamAttributes examAttributes;
}
