package com.wexl.retail.courses.enrollment.model;

import com.wexl.retail.courses.definition.model.CourseDefinition;
import com.wexl.retail.model.Model;
import com.wexl.retail.model.User;
import com.wexl.retail.team.domain.Team;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.sql.Timestamp;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

@EqualsAndHashCode(callSuper = true)
@Entity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "course_schedule")
public class CourseSchedule extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  @Column(name = "org_slug")
  private String orgSlug;

  @Enumerated(EnumType.STRING)
  private CourseScheduleStatus status;

  @ManyToOne private CourseDefinition courseDefinition;

  @ManyToOne
  @JoinColumn(name = "teacher_id")
  private User teacherId;

  @Column(name = "start_date")
  private Timestamp startDate;

  @Column(name = "end_date")
  private Timestamp endDate;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb")
  private CourseEnrollmentMetadata metadata;

  @ManyToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinTable(
      name = "course_schedule_teams",
      joinColumns = @JoinColumn(name = "course_schedule_id"),
      inverseJoinColumns = @JoinColumn(name = "team_id"))
  private List<Team> teams;
}
