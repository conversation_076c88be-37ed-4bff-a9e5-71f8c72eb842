package com.wexl.retail.task.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.task.domain.TaskStatus;
import com.wexl.retail.task.domain.TaskType;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class TaskActivityResponse {
  Long studentId;
  String studentName;
  String studentAuthUserId;
  String studentGrade;

  String classroomCode;
  String boardSlug;
  String gradeSlug;
  String boardName;
  String gradeName;
  TaskStatus taskStatus;
  TaskType activityType;
  Long taskDate;
  Long taskCompletedDate;
  String subjectName;
  String subjectSlug;
  String chapterName;
  String chapterSlug;
  String subtopicName;
  String subtopicSlug;

  Long testDefinitionId;
  Long examId;
  Boolean isExamCorrected;
  Float marksScored;
  Float totalMarks;
  String synopsisSlug;
  String altVimeoLink;
  String videoSlug;

  @JsonProperty("task_id")
  Long taskId;

  @JsonProperty("task_inst_id")
  Long taskInstId;
}
