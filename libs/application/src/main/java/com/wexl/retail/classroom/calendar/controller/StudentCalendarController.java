package com.wexl.retail.classroom.calendar.controller;

import com.wexl.retail.classroom.calendar.service.StudentCalendarService;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Valid
@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}")
public class StudentCalendarController {

  private final StudentCalendarService studentCalendarService;

  @GetMapping("/students/{studentAuthUserId}/student-calendar")
  public Map<String, List<String>> getFeedbackResponse(
      @PathVariable("studentAuthUserId") String studentAuthId,
      @RequestParam Long fromDateInEpoch,
      @RequestParam Long toDateInEpoch) {
    return studentCalendarService.getStudentAllTasksResponse(
        studentAuthId, fromDateInEpoch, toDateInEpoch);
  }
}
