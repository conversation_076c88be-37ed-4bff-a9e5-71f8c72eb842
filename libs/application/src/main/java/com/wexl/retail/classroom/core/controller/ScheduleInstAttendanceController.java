package com.wexl.retail.classroom.core.controller;

import com.wexl.retail.classroom.core.dto.ScheduleInstAttendanceRequest;
import com.wexl.retail.classroom.core.dto.ScheduleInstAttendanceResponse;
import com.wexl.retail.classroom.core.service.ScheduleInstAttendanceService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/schedule-insts")
public class ScheduleInstAttendanceController {
  private final ScheduleInstAttendanceService scheduleInstAttendanceService;

  @IsOrgAdminOrTeacher
  @PostMapping("/{id}/attendance")
  @ResponseStatus(HttpStatus.ACCEPTED)
  public void markAttendance(
      @PathVariable Long id,
      @PathVariable String orgSlug,
      @RequestBody ScheduleInstAttendanceRequest scheduleInstAttendanceRequest) {
    try {
      scheduleInstAttendanceService.markAttendance(id, orgSlug, scheduleInstAttendanceRequest);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.ClassroomSchedule.Update", e);
    }
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/{id}/attendance")
  public List<ScheduleInstAttendanceResponse> getAttendanceDetails(
      @PathVariable Long id, @PathVariable String orgSlug) {
    return scheduleInstAttendanceService.getAttendanceDetails(id, orgSlug);
  }
}
