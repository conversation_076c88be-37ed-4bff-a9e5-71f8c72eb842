package com.wexl.retail.zoom.domain;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class Event extends Model {

  private String uuid;

  @Column(name = "external_event_id")
  private String externalEventId;

  @Column(name = "event_type")
  @Enumerated(EnumType.STRING)
  private ZoomEventType eventType;

  @Column(name = "event_ts")
  private Timestamp eventTs;

  @Column(name = "account_id")
  private String accountId;

  @Column(name = "start_time")
  private Timestamp startTime;
}
