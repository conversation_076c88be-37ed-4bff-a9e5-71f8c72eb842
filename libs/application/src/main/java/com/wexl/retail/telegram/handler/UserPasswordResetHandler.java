package com.wexl.retail.telegram.handler;

import com.wexl.retail.auth.AuthUtil;
import com.wexl.retail.idp.UserIdpService;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.telegram.util.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

@Component
@Profile({"prod"})
public class UserPasswordResetHandler implements TelegramBotHandler {

  @Autowired UserRepository userRepository;
  @Autowired private UserIdpService userIdpService;

  @Override
  public String handleCommand(String[] params) {
    if (!isValid(params, 3)) {
      return Constants.INVALID_ARGS.formatted(getHelpText());
    }
    final String authUserId = params[1];
    final String newPassword = params[2];

    var user = userRepository.getUserByAuthUserId(params[1]);
    if (user == null) {
      return "User with [" + authUserId + "] not found";
    }

    userIdpService.adminSetUserPassword(authUserId, newPassword);

    if (AuthUtil.isStudent(user)) {
      return String.format(Constants.PASSWORD_RESET_SUCCESS, user.getUserName(), newPassword);
    } else if (AuthUtil.isTeacher(user)) {
      return String.format(Constants.PASSWORD_RESET_SUCCESS, user.getEmail(), newPassword);
    }

    return Constants.PASSWORD_RESET_FAILURE;
  }

  @Override
  public String getCommandName() {
    return "/set-password";
  }

  @Override
  public String getHelpText() {
    return "/set-password [username] [new-password]";
  }
}
