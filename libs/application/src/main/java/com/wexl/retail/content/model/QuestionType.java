package com.wexl.retail.content.model;

import com.wexl.retail.test.school.dto.QuestionDto;

@SuppressWarnings("rawtypes")
public enum QuestionType {
  MCQ("mcq", MultipleChoiceQuestion.class, MultipleChoiceQuestionWithAnswer.class),
  SUBJECTIVE("subjective", SubjectiveQuestion.class, SubjectiveQuestionWithAnswer.class),
  MSQ("msq", QuestionDto.Msq.class, QuestionDto.Msq.class),
  NAT("nat", QuestionDto.Nat.class, QuestionDto.Nat.class),
  YESNO("yesno", QuestionDto.YesNo.class, QuestionDto.YesNo.class),
  PBQ("pbq", QuestionDto.Pbq.class, QuestionDto.Pbq.class),
  FBQ("fbq", QuestionDto.Fbq.class, QuestionDto.Fbq.class),
  AMCQ("amcq", QuestionDto.Amcq.class, QuestionDto.Amcq.class),
  SPCH("spch", QuestionDto.Spch.class, QuestionDto.Spch.class),
  LIVE_WORKSHEET("live_worksheet", QuestionDto.WorkSheet.class, QuestionDto.WorkSheet.class),
  DDFBQ("ddfbq", QuestionDto.DdFbq.class, QuestionDto.DdFbq.class);

  QuestionType(
      final String type,
      final Class resultSetTypeWithOutAnswer,
      final Class resultSetTypeWithAnswer) {
    this.type = type;
    this.resultSetTypeWithOutAnswer = resultSetTypeWithOutAnswer;
    this.resultSetTypeWithAnswer = resultSetTypeWithAnswer;
  }

  private String type;
  private Class resultSetTypeWithOutAnswer;
  private Class resultSetTypeWithAnswer;

  public String getType() {
    return type;
  }

  public Class getResultSetTypeWithOutAnswer() {
    return resultSetTypeWithOutAnswer;
  }

  public Class getResultSetTypeWithAnswer() {
    return resultSetTypeWithAnswer;
  }

  public static QuestionType getByType(final String type) {
    for (QuestionType qt : values()) {
      if (qt.getType().equalsIgnoreCase(type)) {
        return qt;
      }
    }
    return MCQ;
  }
}
