package com.wexl.retail.term.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@RequiredArgsConstructor
@Builder
@Entity
@AllArgsConstructor
@Table(name = "term_assessment_categories")
public class TermAssessmentCategory extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String name;

  private String orgSlug;

  @JsonProperty("seq_no")
  private Long seqNo;

  @ManyToOne(fetch = FetchType.LAZY)
  private TermAssessment termAssessment;
}
