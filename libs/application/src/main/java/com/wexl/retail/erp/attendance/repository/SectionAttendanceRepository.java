package com.wexl.retail.erp.attendance.repository;

import com.wexl.retail.erp.attendance.domain.SectionAttendance;
import com.wexl.retail.erp.attendance.dto.StudentsAttendanceReport;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.section.domain.Section;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface SectionAttendanceRepository extends JpaRepository<SectionAttendance, Long> {

  @Query(
      value = "select * from section_attendance where date_id =:date and section_id = :sectionId",
      nativeQuery = true)
  SectionAttendance getSectionAttendanceDetails(Integer sectionId, Integer date);

  @Query(
      value =
          "select * from section_attendance where section_id = :sectionId and  date_id >=:fromDate and date_id <=:toDate",
      nativeQuery = true)
  List<SectionAttendance> findByDates(Integer sectionId, Integer fromDate, Integer toDate);

  @Query(
      value = "select * from section_attendance where section_id in (:sectionList)",
      nativeQuery = true)
  List<SectionAttendance> getSectionsData(List<Long> sectionList);

  @Query(
      value =
          """
                  select concat(u.first_name,' ',u.last_name) as fullName,se.name as sectionName,se.grade_name as gradeName,count(distinct sa.*) as totalWorkingDays,
                  SUM(CASE WHEN sad.attendance_status = 'absent' THEN 1 ELSE 0 END) AS absentDays,
                  SUM(CASE WHEN sad.attendance_status = 'present' THEN 1 ELSE 0 END) AS presentDays,
                  u.auth_user_id as authId,
                   s.roll_number as rollNumber,u.user_name as userName,s.class_roll_number as classRollNumber
                  from section_attendance sa
                  join sections se on sa.section_id  = se.id
                  join orgs o on o.id = sa.org_id
                  join section_attendance_details sad  on sad.section_attendance_id  = sa.id
                  join students s on s.id = sad.student_id
                  join users u on u.id = s.user_id
                  where is_holiday  = 0 and date_id  between (:fromDate) and (:toDate)
                  and o.slug =:orgSlug and (cast((:gradeSlugs) as varChar) is null or se.grade_slug in (:gradeSlugs))
                  and (cast((:sectionIds) as varChar) is null or se.id in (:sectionIds))
                  and (cast((:boardIds) as varChar) is null or s.board_id in (:boardIds))
                  group by fullName,sectionName,gradeName,authId,roll_number,user_name,classRollNumber
                  order by sectionName asc
                  """,
      nativeQuery = true)
  List<StudentsAttendanceReport> getStudentsAttendanceReport(
      String orgSlug,
      List<String> gradeSlugs,
      List<Long> sectionIds,
      List<Long> boardIds,
      Integer fromDate,
      Integer toDate);

  @Query(
      value =
          """
                          select concat(u.first_name,' ',u.last_name) as fullName,se.name as sectionName,se.grade_name as gradeName,count(distinct sa.*) as totalWorkingDays,
                          SUM(CASE WHEN sad.afternoon_attendance_status = 'absent' THEN 1 ELSE 0 END) AS absentDays,
                          SUM(CASE WHEN sad.afternoon_attendance_status = 'present' THEN 1 ELSE 0 END) AS presentDays,
                          u.auth_user_id as authId,
                           s.roll_number as rollNumber,u.user_name as userName,s.class_roll_number as classRollNumber
                          from section_attendance sa
                          join sections se on sa.section_id  = se.id
                          join orgs o on o.id = sa.org_id
                          join section_attendance_details sad  on sad.section_attendance_id  = sa.id
                          join students s on s.id = sad.student_id
                          join users u on u.id = s.user_id
                          where is_holiday  = 0 and date_id  between (:fromDate) and (:toDate)
                          and o.slug =:orgSlug and (cast((:gradeSlugs) as varChar) is null or se.grade_slug in (:gradeSlugs))
                          and (cast((:sectionIds) as varChar) is null or se.id in (:sectionIds))
                          and (cast((:boardIds) as varChar) is null or s.board_id in (:boardIds))
                          group by fullName,sectionName,gradeName,authId,roll_number,user_name,classRollNumber
                          order by sectionName asc
                          """,
      nativeQuery = true)
  List<StudentsAttendanceReport> getStudentsAfternoonAttendanceReport(
      String orgSlug,
      List<String> gradeSlugs,
      List<Long> sectionIds,
      List<Long> boardIds,
      Integer fromDate,
      Integer toDate);

  List<SectionAttendance> findBySectionAndOrgAndDateIdBetweenOrderByDateId(
      Section sectionId, Organization orgId, Integer fromDate, Integer endDate);
}
