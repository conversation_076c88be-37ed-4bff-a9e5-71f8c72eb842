package com.wexl.retail.thread.model;

import com.wexl.retail.model.Model;
import com.wexl.retail.model.User;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "thread_reply_comments")
public class ThreadReplyComments extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private Boolean isTeacher;
  private Boolean isStudent;
  private String reply;

  @ManyToOne
  @JoinColumn(name = "thread_reply_id")
  private ThreadReply threadReplies;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "parent_thread_reply_comment_id")
  private ThreadReplyComments parent;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "userId")
  private User userInfo;
}
