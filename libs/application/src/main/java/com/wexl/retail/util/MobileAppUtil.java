package com.wexl.retail.util;

import static com.wexl.retail.util.Constants.DART_USER_AGENT;

import lombok.Data;

@Data
public class MobileAppUtil {

  private MobileAppUtil() {}

  public static boolean requestComingFromMobileApp(String userAgent) {
    return (userAgent != null
        && (userAgent.startsWith(DART_USER_AGENT)
            || (userAgent.startsWith(Constants.MOBILE_USER_AGENT))));
  }
}
