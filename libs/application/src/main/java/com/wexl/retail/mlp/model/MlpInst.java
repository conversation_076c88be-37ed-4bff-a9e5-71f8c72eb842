package com.wexl.retail.mlp.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wexl.retail.model.Student;
import com.wexl.retail.student.exam.Exam;
import jakarta.persistence.*;
import java.sql.Timestamp;
import java.util.Date;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Entity
@Table(name = "mlp_inst", schema = "public")
@EntityListeners(AuditingEntityListener.class)
@Data
public class MlpInst {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne
  @JoinColumn(name = "mlp_id")
  private Mlp mlp;

  @OneToOne private Student student;

  @OneToOne private Exam exam;

  @Column(name = "video_status")
  @Enumerated(EnumType.STRING)
  private MlpItemStatus videoStatus = MlpItemStatus.NOT_STARTED;

  @Column(name = "synopsis_status")
  @Enumerated(EnumType.STRING)
  private MlpItemStatus synopsisStatus = MlpItemStatus.NOT_STARTED;

  @CreatedDate private Timestamp createdAt;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
  private Date deletedAt;

  @LastModifiedDate private Timestamp updatedAt;

  @Column(name = "knowledge_percentage")
  private Double knowledgePercentage;

  @Column(name = "attendance_percentage")
  private Double attendancePercentage;
}
