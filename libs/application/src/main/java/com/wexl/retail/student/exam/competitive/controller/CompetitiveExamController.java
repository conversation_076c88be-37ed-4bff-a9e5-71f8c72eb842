package com.wexl.retail.student.exam.competitive.controller;

import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.student.exam.competitive.dto.CompetitiveExamsDto;
import com.wexl.retail.student.exam.competitive.service.CompetitiveExamService;
import com.wexl.retail.test.schedule.dto.SimpleScheduleTestResponse;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}")
public class CompetitiveExamController {

  private final CompetitiveExamService competitiveExamService;

  @IsOrgAdminOrTeacher
  @GetMapping("/competitive-exams")
  public List<CompetitiveExamsDto.TeacherResponse> getCompetitiveExams(
      @PathVariable String orgSlug) {
    return competitiveExamService.getCompetitiveExams(orgSlug);
  }

  @IsOrgAdminOrTeacher
  @PostMapping("/competitive-exams")
  public SimpleScheduleTestResponse saveCompetitiveExams(
      @PathVariable String orgSlug, @RequestBody CompetitiveExamsDto.Request request) {
    return competitiveExamService.saveCompetitiveExams(orgSlug, request);
  }

  @IsOrgAdminOrTeacher
  @PostMapping("/competitive-exams:upload")
  public Map<String, String> upload(
      @PathVariable String orgSlug, @RequestBody CompetitiveExamsDto.UploadRequest request) {
    return competitiveExamService.upload(orgSlug, request);
  }
}
