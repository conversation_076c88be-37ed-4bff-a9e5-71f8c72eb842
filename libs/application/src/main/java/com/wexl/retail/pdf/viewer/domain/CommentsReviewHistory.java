package com.wexl.retail.pdf.viewer.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Entity
@Accessors(chain = true)
@Table(name = "pdf_comments_review_history")
public class CommentsReviewHistory implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Column(name = "id", updatable = false)
  private Long id;

  @Column(name = "comment_id", nullable = false)
  private Long commentId;

  @Column(name = "status", nullable = false)
  private String status;

  @Column(name = "reviewed_by", nullable = false)
  private String reviewedBy;

  @Column(name = "date_reviewed", nullable = false)
  private Timestamp dateReviewed;
}
