package com.wexl.retail.courses.definition.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Entity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(
    name = "course_org",
    uniqueConstraints = {@UniqueConstraint(columnNames = {"course_definition_id", "org_slug"})})
public class CourseOrg extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "course-org-sequence-generator")
  @SequenceGenerator(
      name = "course-org-sequence-generator",
      sequenceName = "course_org_seq",
      allocationSize = 1)
  private long id;

  @ManyToOne
  @JoinColumn(updatable = false)
  private CourseDefinition courseDefinition;

  @Column(name = "org_slug")
  private String orgSlug;
}
