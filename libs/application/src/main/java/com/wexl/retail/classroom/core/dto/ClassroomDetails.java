package com.wexl.retail.classroom.core.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.organization.admin.StudentResponse;
import com.wexl.retail.organization.admin.teacher.TeacherResponse;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ClassroomDetails {

  @JsonProperty("classroom_name")
  private String classroomName;

  private List<StudentResponse> students;

  private List<TeacherResponse> teachers;

  private List<ClassroomScheduleResponse> schedules;
  private ClassRoomDto.Extensions extensions;
}
