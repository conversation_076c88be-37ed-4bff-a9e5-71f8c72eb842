package com.wexl.retail.metrics.reportcards.dto;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.*;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "student_historical_report")
public class StudentHistoricalReport extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private Long userId;

  @Column(name = "student_first_name")
  private String studentFirstName;

  @Column(name = "student_last_name")
  private String studentLastName;

  private Long studentId;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "academic_year_id")
  private AcademicYear academicYearId;

  @Column(name = "grade_name")
  private String gradeName;

  @Column(name = "grade_slug")
  private String gradeSlug;

  @Column(name = "admission_number")
  private String admissionNumber;

  private String s3Path;
  private String description;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "report_type_id")
  private StudentHistoricalReportType reportTypeId;

  private String fatherName;
  private String dob;
}
