package com.wexl.retail.organization.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.model.EduBoard;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrganizationResponse {

  private String name;
  private String slug;
  private Boolean isPublisher;
  private Boolean isParent;
  private OrgMetaData metadata;
  private OrgParentAndPublisherResponse.ParentResponse parent;
  private OrgParentAndPublisherResponse.PublisherResponse publisher;
  private OrgParentResponse parentDetails;
  private String email;

  @JsonProperty("profile_id")
  private Long profileId;

  @JsonProperty("profile_name")
  private String profileName;

  private String mobileNumber;
  private List<EduBoard> eduBoardList;
  private String abbreviation;

  @JsonProperty("display_name")
  private String displayName;

  @JsonProperty("is_self_signup")
  private Boolean isSelfSignup;

  private String logo;
  private String theme;
  private String website;
  private String mobileLogo;
  private String studentUrl;
  private String logoutUrl;
  private Long orgProfile;
  private Boolean sendSms;
  private Boolean isCorporate;
}
