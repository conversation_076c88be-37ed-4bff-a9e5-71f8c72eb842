package com.wexl.retail.student.teacher;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record StudentTeacherDto() {

  @Builder
  public record Response(
      @JsonProperty("teacher_id") Long teacherId,
      @JsonProperty("teacher_user_id") String teacherAuthId,
      @JsonProperty("teacher_name") String teacherName,
      @JsonProperty("subject_slugs") List<String> subjectSlugs,
      String designation,
      String image,
      List<String> subjects,
      String degree) {}
}
