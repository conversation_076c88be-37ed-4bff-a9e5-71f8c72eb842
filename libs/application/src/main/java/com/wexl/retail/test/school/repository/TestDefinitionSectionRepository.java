package com.wexl.retail.test.school.repository;

import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.domain.TestDefinitionSection;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface TestDefinitionSectionRepository
    extends JpaRepository<TestDefinitionSection, Long> {

  long countByTestDefinition(TestDefinition testDefinition);

  List<TestDefinitionSection> findByTestDefinitionOrderById(TestDefinition testDefinition);
}
