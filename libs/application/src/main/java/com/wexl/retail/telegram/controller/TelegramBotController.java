package com.wexl.retail.telegram.controller;

import com.pengrad.telegrambot.BotUtils;
import com.pengrad.telegrambot.TelegramBot;
import com.pengrad.telegrambot.model.Message;
import com.pengrad.telegrambot.model.Update;
import com.pengrad.telegrambot.model.request.ParseMode;
import com.pengrad.telegrambot.request.SendMessage;
import com.wexl.retail.telegram.handler.TelegramBotHandler;
import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/bot")
public class TelegramBotController {

  private final Map<String, TelegramBotHandler> handlerMap = new HashMap<>();
  @Autowired private TelegramBot telegramBot;
  @Autowired private List<TelegramBotHandler> botHandlers;

  @Value("${app.telegram.chatId:2134}")
  private Long chatId;

  @PostConstruct
  public void initialize() {
    botHandlers.forEach(handler -> handlerMap.put(handler.getCommandName(), handler));
  }

  @PostMapping("/TLuV8VtihJFMWQBnWDYP/webhook")
  public void handleEvents(@RequestBody String event) {
    log.info(event);
    Update update = BotUtils.parseUpdate(event);
    if (update == null || update.message() == null) {
      log.debug("The update message is null. Do Nothing");
      return;
    }
    Message message = update.message();
    String text = message.text();
    log.info("Received a message [" + text + "]");
    String result = processCommand(message.text());
    log.info("Result [" + result + "]");
    SendMessage sendMessage = new SendMessage(chatId, result);
    sendMessage = sendMessage.parseMode(ParseMode.HTML);
    telegramBot.execute(sendMessage);
  }

  private String processCommand(String text) {
    if (StringUtils.isBlank(text)) {
      return "I do not understand!";
    }

    String[] commands = text.split(" ");
    if (!handlerMap.containsKey(commands[0])) {
      return "I do not understand!";
    }

    TelegramBotHandler handler = handlerMap.get(commands[0]);
    return handler.handleCommand(commands);
  }
}
