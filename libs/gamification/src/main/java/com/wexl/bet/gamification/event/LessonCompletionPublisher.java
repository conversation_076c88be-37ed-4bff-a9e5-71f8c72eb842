package com.wexl.bet.gamification.event;

import com.wexl.bet.gamification.dto.EventType;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class LessonCompletionPublisher {
  private final ApplicationEventPublisher applicationEventPublisher;

  public void publishEvent(final String authUserId, EventType eventType) {
    applicationEventPublisher.publishEvent(new LessonCompletionEvent(authUserId, eventType));
  }
}
