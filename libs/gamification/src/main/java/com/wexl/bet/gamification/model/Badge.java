package com.wexl.bet.gamification.model;

import com.wexl.retail.model.Model;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.hibernate.annotations.Type;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "badges")
public class Badge extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @NotNull
  @Column(nullable = false)
  private String name;

  @NotNull
  @Column(nullable = false)
  private String description;

  @NotNull
  @Column(nullable = false)
  private String category;

  @NotNull
  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb", nullable = false)
  private String criteria;

  @Column(name = "reward_points")
  private Integer rewardPoints;

  @Column(name = "reward_gems")
  private Integer rewardGems;
}
