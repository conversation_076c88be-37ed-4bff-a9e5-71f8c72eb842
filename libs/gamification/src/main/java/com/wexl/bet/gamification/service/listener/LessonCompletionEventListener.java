package com.wexl.bet.gamification.service.listener;

import com.wexl.bet.gamification.event.LessonCompletionEvent;
import com.wexl.bet.gamification.service.PointsService;
import com.wexl.bet.gamification.service.UserGameStatisticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class LessonCompletionEventListener implements ApplicationListener<LessonCompletionEvent> {
  private final UserGameStatisticsService userGameStatisticsService;
  private final PointsService pointsService;

  @Override
  public void onApplicationEvent(@NotNull LessonCompletionEvent event) {
    log.info("Handling the lesson completion event.  add 100 points to user");
    int lessonPoints = 100;
    userGameStatisticsService.addUserPoints(event.getEvent().authUserId(), lessonPoints);
    pointsService.addEvent(event.getEvent().authUserId());
  }
}
