package com.wexl.bet.gamification.service;

import com.wexl.bet.gamification.dto.EventDetail;
import com.wexl.bet.gamification.dto.EventType;
import com.wexl.bet.gamification.dto.GamificationDto;
import com.wexl.bet.gamification.event.LessonCompletionEvent;
import com.wexl.bet.gamification.model.Event;
import com.wexl.bet.gamification.model.LeagueLevel;
import com.wexl.bet.gamification.model.UserGameStatistics;
import com.wexl.bet.gamification.repository.EventsRepository;
import com.wexl.bet.gamification.repository.UserGameStatisticsRepository;
import com.wexl.retail.model.User;
import com.wexl.retail.student.profile.ProfileService;
import com.wexl.retail.util.ValidationUtils;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class PointsService {
  private final ApplicationEventPublisher applicationEventPublisher;
  private final UserGameStatisticsService userGameStatisticsService;
  private final EventsRepository eventListenerRepository;
  private final ValidationUtils validationUtils;
  private final ProfileService profileService;

  private final UserGameStatisticsRepository userGameStatisticsRepository;

  private static final int POINTS_FOR_SILVER = 3000;
  private static final int POINTS_FOR_GOLD = 6000;
  private static final int POINTS_FOR_RUBY = 9000;
  private static final int POINTS_FOR_AMETHYST = 12000;
  private static final int POINTS_FOR_EMERALD = 15000;

  @Scheduled(cron = "0 0 0 * * MON")
  @Transactional
  public void processWeeklyLeague() {
    updateUserRanks();
  }

  private void updateUserRanks() {
    List<UserGameStatistics> allUsers = userGameStatisticsRepository.findAll();
    for (LeagueLevel leagueLevel : LeagueLevel.values()) {
      List<UserGameStatistics> filteredList =
          allUsers.stream()
              .filter(x -> x.getLeagueLevel().equals(leagueLevel))
              .sorted(
                  Comparator.comparing(
                          (UserGameStatistics ugs) -> ugs.getTotalXp() - ugs.getLastWeekXp())
                      .reversed())
              .toList();

      List<UserGameStatistics> thisLeagueTop3 =
          filteredList.subList(0, Math.min(3, filteredList.size()));

      thisLeagueTop3.forEach(
          userGameStatistics -> {
            if (promoteUserLeague(userGameStatistics)) {
              userGameStatistics.setLeagueLevel(promoteUserLeagueLevel(userGameStatistics));
              userGameStatistics.setLastWeekXp(userGameStatistics.getTotalXp());
            }
          });
    }
    for (UserGameStatistics userGameStatistics : allUsers) {
      userGameStatistics.setLastWeekXp(userGameStatistics.getTotalXp());
    }
    userGameStatisticsRepository.saveAll(allUsers);
  }

  private boolean promoteUserLeague(UserGameStatistics userGameStatistics) {
    return (userGameStatistics.getTotalXp() - userGameStatistics.getLastWeekXp()) >= 3000
        && userGameStatistics.getLeagueLevel().ordinal() < LeagueLevel.EMERALD.ordinal();
  }

  private LeagueLevel promoteUserLeagueLevel(UserGameStatistics userGameStatistics) {
    return LeagueLevel.values()[userGameStatistics.getLeagueLevel().ordinal() + 1];
  }

  public void addEvent(String authUserId) {
    Event event = new Event();
    var user = userGameStatisticsService.isValidUser(authUserId);
    event.setEventType(EventType.LESSON_COMPLETED);
    event.setPointsAwarded(100);
    event.setUserId(user.getId());
    eventListenerRepository.save(event);
  }

  public void simulateDailyQuest(String authUserId) {
    EventDetail.EventObject event =
        new EventDetail.EventObject(authUserId, EventType.LESSON_COMPLETED);
    applicationEventPublisher.publishEvent(new LessonCompletionEvent(event));
    applicationEventPublisher.publishEvent(
        new EventDetail.LessonScoreEventObject(authUserId, "lesson1", 85));
    applicationEventPublisher.publishEvent(
        new EventDetail.LessonScoreEventObject(authUserId, "lesson2", 95));
    applicationEventPublisher.publishEvent(
        new EventDetail.LessonScoreEventObject(authUserId, "lesson3", 100));
  }

  @Transactional
  public GamificationDto.TopPerformersResponse getPerformersByLevel(
      String authUserId, LeagueLevel incomingLevel) {
    LeagueLevel level =
        incomingLevel == null
            ? userGameStatisticsRepository
                .findByUser(validationUtils.isValidUser(authUserId))
                .get()
                .getLeagueLevel()
            : incomingLevel;
    List<UserGameStatistics> topPerformersByLevel =
        userGameStatisticsRepository.findAllByLeagueLevel(level).stream()
            .sorted(
                Comparator.comparingInt(
                        (UserGameStatistics ugs) -> ugs.getTotalXp() - ugs.getLastWeekXp())
                    .reversed())
            .toList();
    AtomicInteger rank = new AtomicInteger(0);
    return GamificationDto.TopPerformersResponse.builder()
        .leagueLevel(level)
        .topPerformers(
            topPerformersByLevel.stream()
                .map(
                    userGameStatistics -> {
                      User user = userGameStatistics.getUser();
                      return GamificationDto.LeaguePointsResponse.builder()
                          .points(
                              userGameStatistics.getTotalXp() - userGameStatistics.getLastWeekXp())
                          .name(user.getFirstName() + " " + user.getLastName())
                          .rank(rank.incrementAndGet())
                          .level(userGameStatistics.getLeagueLevel().name())
                          .profileImageUrl(
                              Objects.isNull(user.getProfileImage())
                                  ? null
                                  : profileService.getProfileImageUrl(user.getProfileImage()))
                          .build();
                    })
                .toList())
        .build();
  }

  private LeagueLevel determineLevelFromPoints(int totalXp) {
    if (totalXp >= POINTS_FOR_EMERALD) return LeagueLevel.EMERALD;
    if (totalXp >= POINTS_FOR_AMETHYST) return LeagueLevel.AMETHYST;
    if (totalXp >= POINTS_FOR_RUBY) return LeagueLevel.RUBY;
    if (totalXp >= POINTS_FOR_GOLD) return LeagueLevel.GOLD;
    if (totalXp >= POINTS_FOR_SILVER) return LeagueLevel.SILVER;
    return LeagueLevel.BRONZE;
  }
}
