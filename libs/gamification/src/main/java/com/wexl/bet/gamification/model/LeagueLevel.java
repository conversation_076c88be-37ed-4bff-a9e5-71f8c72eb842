package com.wexl.bet.gamification.model;

public enum LeagueLevel {
  BRONZE(1, 0),
  SILVER(2, 3),
  GOLD(3, 6),
  RUBY(4, 9),
  AMETHYST(5, 12),
  EMERALD(6, 15);

  private final int level;
  private final int requiredTopThreeAppearances;

  LeagueLevel(int level, int requiredTopThreeAppearances) {
    this.level = level;
    this.requiredTopThreeAppearances = requiredTopThreeAppearances;
  }

  public int getLevel() {
    return level;
  }
}
