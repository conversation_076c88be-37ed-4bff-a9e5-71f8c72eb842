package com.wexl.bet.gamification.event;

import com.wexl.bet.gamification.dto.EventDetail;
import com.wexl.bet.gamification.dto.EventType;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class LessonCompletionEvent extends ApplicationEvent {
  private final EventDetail.EventObject event;

  public LessonCompletionEvent(Object source) {
    super(source);
    this.event = (EventDetail.EventObject) source;
  }

  public LessonCompletionEvent(String authUserId, EventType eventType) {
    super(new EventDetail.EventObject(authUserId, eventType));
    this.event = new EventDetail.EventObject(authUserId, eventType);
  }
}
