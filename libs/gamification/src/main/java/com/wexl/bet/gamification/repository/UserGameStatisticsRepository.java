package com.wexl.bet.gamification.repository;

import com.wexl.bet.gamification.model.LeagueLevel;
import com.wexl.bet.gamification.model.UserGameStatistics;
import com.wexl.retail.model.User;
import jakarta.transaction.Transactional;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface UserGameStatisticsRepository extends JpaRepository<UserGameStatistics, Long> {
  Optional<UserGameStatistics> findByUser(User user);

  @Modifying
  @Query(
      "UPDATE UserGameStatistics u SET u.totalXp = u.totalXp + :points, u.lastStreakDate = :today,u.currentStreak = :currentStreak"
          + " WHERE u.user = :user")
  @Transactional
  int updateTotalXpByUser(
      @Param("user") User user,
      @Param("points") int points,
      @Param("today") LocalDate today,
      @Param("currentStreak") int currentStreak);

  List<UserGameStatistics> findAllByLeagueLevel(LeagueLevel level);
}
