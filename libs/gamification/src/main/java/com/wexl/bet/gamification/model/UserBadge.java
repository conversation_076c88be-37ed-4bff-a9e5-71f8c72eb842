package com.wexl.bet.gamification.model;

import com.wexl.retail.model.Model;
import com.wexl.retail.model.User;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "user_badges")
public class UserBadge extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne
  @JoinColumn(name = "badge_id")
  private Badge badge;

  @ManyToOne
  @JoinColumn(name = "user_id")
  private User user;

  private LocalDateTime awardedAt;
}
