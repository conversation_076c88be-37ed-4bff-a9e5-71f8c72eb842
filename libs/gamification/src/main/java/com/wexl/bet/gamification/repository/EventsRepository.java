package com.wexl.bet.gamification.repository;

import com.wexl.bet.gamification.dto.EventType;
import com.wexl.bet.gamification.model.Event;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface EventsRepository extends JpaRepository<Event, Long> {
  List<Event> findAllByUserIdAndEventType(long userId, EventType lessonCompleted);

  @Query(
      value =
          """

                  SELECT name, total_xp as totalXp, user_id as userId, RANK() OVER (ORDER BY total_xp DESC, created_at ASC) AS rank
                                             FROM user_game_stats WHERE deleted_at IS NULL AND total_xp < 3000;""",
      nativeQuery = true)
  List<Events> getUserPoints();
}
