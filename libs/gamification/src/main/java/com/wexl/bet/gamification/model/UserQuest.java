package com.wexl.bet.gamification.model;

import com.wexl.bet.gamification.dto.QuestStatus;
import com.wexl.retail.model.Model;
import com.wexl.retail.model.User;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.hibernate.annotations.Type;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "user_quests")
public class UserQuest extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb", nullable = false)
  private String progress;

  @ManyToOne
  @JoinColumn(name = "quest_id")
  private Quest quest;

  @ManyToOne
  @JoinColumn(name = "user_id")
  private User user;

  @NotNull
  @Enumerated(EnumType.STRING)
  @Column(nullable = false)
  private QuestStatus status;

  @Column(name = "completed_at")
  private LocalDateTime completedAt;
}
