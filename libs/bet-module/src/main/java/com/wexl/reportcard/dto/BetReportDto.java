package com.wexl.reportcard.dto;

import lombok.Builder;

public record BetReportDto() {

  @Builder
  public record Header(
      String schoolName,
      String date,
      String sectionName,
      String isAdmissionTest,
      Long studentId,
      String studentName,
      String programme,
      String nationality,
      Long rollNo,
      String dob,
      String gender,
      String motherName,
      String fatherName,
      String countryOfRegion,
      String motherTongue,
      String imageUrl,
      String certificateId,
      String orgSlug,
      String qrCodeUrl,
      String logo) {}

  @Builder
  public record Body(
      String sectionListSize,
      String testLevel,
      String section1Name,
      String section1Grade,
      String section1Value,
      String section2Name,
      String section2Grade,
      String section2Value,
      String section3Name,
      String section3Grade,
      String section3Value,
      String section4Name,
      String section4Grade,
      String section4Value,
      Long Section1Questions,
      Long Section2Questions,
      Long Section3Questions,
      Long Section4Questions,
      Double scoreValue,
      String proficiencyValue,
      PronunciationAssessment speechAnalysis,
      String proctoringStatus) {}

  @Builder
  public record PronunciationAssessment(
      Long accuracyScore,
      Long pronunciationScore,
      Long completenessScore,
      Long fluencyScore,
      Double ieltsScore,
      Long pteScore,
      Long toeicScore,
      String cefrScore) {}
}
