package com.wexl.metricshandler;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.handler.AbstractMetricHandler;
import com.wexl.retail.metrics.handler.MetricHandler;
import com.wexl.sections.service.BetDashBoardService;
import java.util.List;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@EqualsAndHashCode(callSuper = true)
public class ContinueLearningDashBoard extends AbstractMetricHandler implements MetricHandler {
  private final BetDashBoardService dashboardService;

  @Override
  public String name() {
    return "continue-learning-dashboard";
  }

  @Override
  protected List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    return dashboardService.getContinueCoursesDashboard();
  }
}
