package com.wexl.metricshandler;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.handler.AbstractMetricHandler;
import com.wexl.retail.metrics.handler.MetricHandler;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BetExamPerformance extends AbstractMetricHandler implements MetricHandler {
  private final BetExamService betExamService;

  @Override
  public String name() {
    return "bet-exam-performance";
  }

  @Override
  protected List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    String authId = (String) genericMetricRequest.getInput().get(AUTHUSERID);
    String currentTssId = (String) genericMetricRequest.getInput().get(TEST_SCHEDULE_ID);
    String previousTestScheduleId = betExamService.getPreviousTestScheduleId(authId, currentTssId);

    List<GenericMetricResponse> currentGenericMetricResponses =
        betExamService.getBetExamAnalysis(authId, currentTssId);
    if (previousTestScheduleId == null) {
      return currentGenericMetricResponses;
    }
    List<GenericMetricResponse> previousGenericMetricResponses =
        betExamService.getBetExamAnalysis(authId, previousTestScheduleId);
    return betExamService.buildPreviousAndCurrentResponse(
        previousGenericMetricResponses, currentGenericMetricResponses);
  }
}
