package com.wexl.product.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.product.entity.ProductType;
import lombok.Builder;

public record ProductDto() {
  @Builder
  public record productResponse(
      @JsonProperty("product_id") Long id,
      @JsonProperty("cart_item_id") Long cartItemId,
      @JsonProperty("title") String title,
      @JsonProperty("description") String description,
      @JsonProperty("thumbnail") String thumbnail,
      @JsonProperty("price") Double price,
      @JsonProperty("status") String status,
      @JsonProperty("quantity") Long quantity,
      @JsonProperty("type") ProductType type) {}
}
