package com.wexl.product.controller;

import com.wexl.product.dto.ProductDto;
import com.wexl.product.service.ProductService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class ProductsController {

  private final ProductService productService;

  @GetMapping("/bet-exams/products")
  public List<ProductDto.productResponse> getAllProducts() {

    return productService.getAllProducts();
  }
}
