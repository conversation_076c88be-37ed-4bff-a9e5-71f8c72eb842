package com.wexl.product.repository;

import com.wexl.product.entity.Product;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.stereotype.Repository;

@Repository
@EnableJpaRepositories
public interface ProductsRepository extends JpaRepository<Product, Long> {
  public List<Product> findAllByDeletedAtIsNull();
}
