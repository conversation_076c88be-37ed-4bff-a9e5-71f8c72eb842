package com.wexl.product.service;

import com.wexl.product.dto.ProductDto;
import com.wexl.product.entity.Product;
import com.wexl.product.repository.ProductsRepository;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProductService {

  private final ProductsRepository productRepository;

  public List<ProductDto.productResponse> getAllProducts() {

    List<ProductDto.productResponse> productList = new ArrayList<>();

    var products = productRepository.findAllByDeletedAtIsNull();

    for (Product product : products) {

      productList.add(
          ProductDto.productResponse
              .builder()
              .id(product.getId())
              .title(product.getTitle())
              .type(product.getType())
              .description(product.getDescription())
              .price(product.getPrice())
              .status(product.getStatus())
              .thumbnail(product.getThumbnail())
              .build());
    }
    return productList;
  }
}
