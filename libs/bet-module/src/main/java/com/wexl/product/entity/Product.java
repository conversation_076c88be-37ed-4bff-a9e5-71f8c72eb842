package com.wexl.product.entity;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "products")
public class Product extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  @Column(name = "title")
  private String title;

  @Column(name = "description", columnDefinition = "TEXT")
  private String description;

  @Column(name = "thumbnail")
  private String thumbnail;

  @Column(name = "price")
  private Double price;

  @Column(name = "status")
  private String status;

  @Enumerated(EnumType.STRING)
  private ProductType type;
}
