package com.wexl.mytest;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;

import com.wexl.metricshandler.BetExamService;
import com.wexl.mytest.dto.ExamActivityDto;
import com.wexl.mytest.dto.ExamActivityResponse;
import com.wexl.order.dto.OrderDto;
import com.wexl.order.entity.Order;
import com.wexl.order.entity.OrderItem;
import com.wexl.order.entity.OrderItemStatus;
import com.wexl.order.entity.OrderStatus;
import com.wexl.order.repository.OrderItemRepository;
import com.wexl.order.repository.OrderRepository;
import com.wexl.practicetest.dto.BetPracticeTestDto;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.commons.util.MathUtil;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.repository.ReportCardTemplateRepository;
import com.wexl.retail.offlinetest.service.OfflineTestReportService;
import com.wexl.retail.proctoring.model.ProctoringSession;
import com.wexl.retail.proctoring.repository.ProctoringRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.services.StudentService;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.ExamRepository;
import com.wexl.retail.student.exam.ExamService;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.schedule.dto.TestStudentStatus;
import com.wexl.retail.test.schedule.repository.ScheduleTestRepository;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import com.wexl.retail.test.schedule.service.ScheduleTestService;
import com.wexl.retail.test.school.domain.TestType;
import com.wexl.retail.util.ValidationUtils;
import com.wexl.retail.v2.service.ScheduleTestStudentService;
import jakarta.validation.Valid;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class MyTestServices {
  private final ValidationUtils validationUtils;
  private final ScheduleTestService scheduleTestService;
  private final DateTimeUtil dateTimeUtil;
  private final AuthService authService;
  private final OrderRepository orderRepository;
  private final OrderItemRepository orderItemRepository;
  private final ScheduleTestRepository scheduleTestRepository;
  private final ScheduleTestStudentRepository scheduleTestStudentRepository;
  private final UserRepository userRepository;
  private final ScheduleTestStudentService scheduleTestStudentService;
  private final ExamRepository examRepository;
  private final ProctoringRepository proctoringRepository;
  private final ExamService examService;
  private final ReportCardTemplateRepository reportCardTemplateRepository;
  private final StudentService studentService;
  private final OfflineTestReportService offlineTestReportService;
  private final BetExamService betExamService;

  public BetPracticeTestDto.ExamResponse getMyTest(String orgSlug, String authUserId) {
    validationUtils.isOrgValid(orgSlug);
    User user = authService.getUserByAuthUserId(authUserId);
    List<Order> orders = orderRepository.findByUserAndStatus(user, OrderStatus.PAID);
    Map<Long, OrderItem> scheduleTestOrderItemMap =
        orders.stream()
            .flatMap(order -> order.getOrderItems().stream())
            .filter(
                orderItem ->
                    List.of(OrderItemStatus.PURCHASED, OrderItemStatus.IN_PROGRESS)
                        .contains(orderItem.getStatus()))
            .flatMap(
                orderItem -> {
                  if (orderItem.getItemDetails() != null
                      && orderItem.getItemDetails().testScheduleIds() != null) {
                    return orderItem.getItemDetails().testScheduleIds().stream()
                        .map(id -> new AbstractMap.SimpleEntry<>(id, orderItem));
                  }
                  return Stream.empty();
                })
            .collect(
                Collectors.toMap(
                    Map.Entry::getKey,
                    Map.Entry::getValue,
                    (existing, replacement) -> existing // handle duplicates if necessary
                    ));

    if (scheduleTestOrderItemMap.isEmpty()) {
      return BetPracticeTestDto.ExamResponse.builder()
          .examResponse(Collections.emptyList())
          .build();
    }

    List<ScheduleTest> scheduleTestList =
        scheduleTestRepository.findAllByIdIn(new ArrayList<>(scheduleTestOrderItemMap.keySet()));

    return BetPracticeTestDto.ExamResponse.builder()
        .examResponse(
            scheduleTestList.stream()
                .map(
                    scheduleTest -> {
                      var specificStudent =
                          scheduleTest.getScheduleTestStudent().stream()
                              .filter(student -> student.getEndTime() == null)
                              .findFirst();

                      if (specificStudent.isPresent()) {
                        var student = specificStudent.get();
                        var orderItem = scheduleTestOrderItemMap.get(scheduleTest.getId());

                        if (orderItem != null) {
                          return BetPracticeTestDto.Response.builder()
                              .testDefinitionId(scheduleTest.getTestDefinition().getId())
                              .scheduleTestId(scheduleTest.getId())
                              .startDate(convertIso8601ToEpoch(scheduleTest.getStartDate()))
                              .endDate(convertIso8601ToEpoch(scheduleTest.getEndDate()))
                              .status(
                                  scheduleTestService.getScheduledTestStatus(
                                      scheduleTest.getStartDate(), scheduleTest.getEndDate()))
                              .subjectName(null)
                              .testName(scheduleTest.getTestDefinition().getTestName())
                              .testType(TestType.MOCK_TEST.name())
                              .orderItemId(orderItem.getId())
                              .productId(orderItem.getProduct().getId())
                              .orderItemStatus(orderItem.getStatus())
                              .scheduleTestUuid(student.getUuid())
                              .build();
                        }
                      }
                      return null;
                    })
                .filter(Objects::nonNull)
                .toList())
        .build();
  }

  public void updateBetTestStatus(OrderDto.@Valid UpdateStatusRequest request, Long orderItemId) {
    var orderItem = validateOrderItemByScheduleTest(orderItemId, request.scheduleTestId());
    if (OrderItemStatus.COMPLETED.equals(orderItem.getStatus())) {
      return;
    }
    var scheduledTest = validationUtils.isTestScheduleValid(request.scheduleTestId());
    var tss =
        scheduleTestStudentRepository
            .findByScheduleTestAndStudent(scheduledTest, authService.getUserDetails())
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.TestSchedule"));
    if (TestStudentStatus.COMPLETED.name().equals(tss.getStatus())
        && List.of(OrderItemStatus.PURCHASED, OrderItemStatus.IN_PROGRESS)
            .contains(request.status())) {
      return;
    }
    orderItem.setStatus(request.status());
    orderItem.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
    orderItemRepository.save(orderItem);
  }

  private OrderItem validateOrderItemByScheduleTest(Long orderItemId, Long scheduleTestId) {
    var orderItem =
        orderItemRepository
            .findById(orderItemId)
            .orElseThrow(
                () ->
                    new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidOrderItem"));
    var noneMatch =
        orderItem.getItemDetails().testScheduleIds().stream()
            .noneMatch(s -> s.equals(scheduleTestId));
    if (noneMatch) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.TestSchedule");
    }
    return orderItem;
  }

  public ExamActivityDto.ActivityResponse findExamsByStudent(
      String orgSlug, String authUserId, List<Long> examTypes) {
    final var student = validationUtils.validateStudentByAuthId(authUserId, orgSlug);

    List<Exam> exams;
    if (!CollectionUtils.isEmpty(examTypes)) {
      exams =
          examRepository
              .findTop100ByStudentAndExamTypeInAndMarksScoredNotNullAndEndTimeNotNullOrderByEndTimeDesc(
                  student, examTypes);
    } else {
      exams =
          examRepository
              .findTop100ByStudentAndMarksScoredNotNullAndEndTimeNotNullOrderByEndTimeDesc(student);
    }
    if (CollectionUtils.isEmpty(exams)) {
      return ExamActivityDto.ActivityResponse.builder()
          .examActivityResponses(Collections.emptyList())
          .build();
    }
    var proctoringSessions =
        proctoringRepository.findByOrgSlugAndStudentId(orgSlug, student.getId());
    var proctoringSessionMap =
        proctoringSessions.stream()
            .filter(session -> Objects.nonNull(session.getExamId()))
            .collect(Collectors.groupingBy(ProctoringSession::getExamId));

    var examActivityResponses =
        exams.stream()
            .map(
                exam -> {
                  var sessions = proctoringSessionMap.get(exam.getId());
                  var response = new ExamActivityResponse();
                  if (!CollectionUtils.isEmpty(sessions)) {
                    response.setProctoringId(sessions.getFirst().getId());
                    response.setProctoringStatus(sessions.getFirst().getStatus());
                    response.setEmailSent(Boolean.TRUE.equals(sessions.getFirst().getEmailSent()));
                  }
                  response.setExamId(exam.getId());
                  response.setMarksScored(exam.getMarksScored());
                  response.setScheduleId(
                      exam.getScheduleTest() != null ? exam.getScheduleTest().getId() : null);
                  response.setCompletedAt(
                      DateTimeUtil.convertIso8601ToEpoch(exam.getEndTime().toLocalDateTime()));
                  response.setSubject(exam.getSubjectName());
                  response.setChapter(exam.getChapterName());
                  response.setPercentage(
                      MathUtil.calculatePercentage(exam.getMarksScored(), exam.getTotalMarks()));
                  response.setTotalMarks(exam.getTotalMarks());
                  response.setType(examService.getExamType(exam.getExamType()));
                  response.setSubtopic(exam.getSubtopicName());
                  response.setTestName(
                      exam.getTestDefinition() != null
                          ? exam.getTestDefinition().getTestName()
                          : null);
                  response.setCorrected(exam.isCorrected());
                  response.setBetExamAnalysis(
                      exam.getScheduleTest() != null
                          ? betExamService.getBetExamAnalysis(
                              exam.getStudent().getUserInfo().getAuthUserId(),
                              String.valueOf(exam.getScheduleTest().getId()))
                          : null);
                  return response;
                })
            .toList();
    return ExamActivityDto.ActivityResponse.builder()
        .examActivityResponses(examActivityResponses)
        .build();
  }
}
