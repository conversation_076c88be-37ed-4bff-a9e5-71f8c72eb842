package com.wexl.mytest.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.proctoring.model.ProctoringStatus;
import com.wexl.retail.student.exam.dto.ExamDto;
import lombok.*;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class ExamActivityResponse extends ExamDto {

  @JsonProperty("proctoring_id")
  private Long proctoringId;

  @JsonProperty("proctoring_status")
  private ProctoringStatus proctoringStatus;

  @JsonProperty("email_sent")
  private Boolean emailSent;
}
