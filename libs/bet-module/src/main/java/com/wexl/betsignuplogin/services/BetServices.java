package com.wexl.betsignuplogin.services;

import com.google.api.client.googleapis.auth.oauth2.GoogleIdToken;
import com.google.api.client.googleapis.auth.oauth2.GoogleIdTokenVerifier;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import com.wexl.betsignuplogin.dto.BetStudentSignUpDto;
import com.wexl.betsignuplogin.dto.BetStudentSignUpDto.LoginOAuthRequest;
import com.wexl.betsignuplogin.dto.BetStudentSignUpDto.OAuth2Request;
import com.wexl.betsignuplogin.dto.BetStudentSignUpDto.Request;
import com.wexl.betsignuplogin.dto.BetUserPasswordDto;
import com.wexl.cart.entity.Cart;
import com.wexl.cart.repository.CartRepository;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.dto.LoginRequest;
import com.wexl.retail.auth.dto.LoginResponse;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.email.EmailService;
import com.wexl.retail.model.LoginMethod;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.admin.StudentRequest;
import com.wexl.retail.proctoring.model.ProctoringSession;
import com.wexl.retail.proctoring.model.ProctoringStatus;
import com.wexl.retail.proctoring.repository.ProctoringRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.student.auth.StudentAuthService;
import com.wexl.retail.student.exam.ExamRepository;
import com.wexl.retail.test.schedule.repository.ScheduleTestRepository;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import com.wexl.retail.util.MobileAppUtil;
import com.wexl.retail.util.ValidationUtils;
import com.wexl.retail.v2.service.ScheduleTestStudentService;
import io.jsonwebtoken.Jwts;
import jakarta.transaction.Transactional;
import jakarta.validation.Valid;
import java.util.*;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@RequiredArgsConstructor
@Slf4j
public class BetServices {

  private final UserRepository userRepository;
  private final SectionService sectionService;
  private final StudentAuthService studentAuthService;
  private final AuthService authService;
  private final EmailService emailService;
  private final StudentRepository studentRepository;
  private final CartRepository cartRepository;
  private final PasswordEncoder passwordEncoder;
  private final ScheduleTestRepository scheduleTestRepository;
  private final ValidationUtils validationUtils;
  private final ScheduleTestStudentService scheduleTestStudentService;
  private final ExamRepository examRepository;
  private final ProctoringRepository proctoringRepository;
  private final ScheduleTestStudentRepository scheduleTestStudentRepository;

  public static final String ORG_SLUG = "bha215263";
  private static final String ALGORITHM = "AES";
  private static final String SECRETAESKEY = "mysecretaeskey12";

  @Value("${app.latestAcademicYear}")
  private String latestAcademicYear;

  @Value("${jwt.oauthTokenSecret}")
  private String oauthTokenSecret;

  @Value("${app.betDomainUrl}")
  private String baseUrl;

  @Value("${app.google.client-id}")
  private String googleClientId;

  @Transactional
  public LoginResponse betStudentSignUp(
      @Valid @RequestBody BetStudentSignUpDto.Request request, String userAgent) {

    var existingUser = userRepository.findByEmailWithIgnoreCase(request.email());
    if (!existingUser.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Email.Registered");
    }
    var studentSignupRequest = buildBetStudentRequest(request);
    var response = studentAuthService.createOrgStudent(studentSignupRequest, ORG_SLUG);
    var user = userRepository.findByEmailAndOrganization(response.getEmail(), ORG_SLUG);
    var student = studentRepository.findByUserInfo(user.get());
    user.get().setStudentInfo(student.get());
    var requestComingFromMobileApp = MobileAppUtil.requestComingFromMobileApp(userAgent);
    LoginResponse loginResponse =
        authService.signin(
            requestComingFromMobileApp,
            buildLoginRequest(request, user.get()),
            LoginMethod.USERNAME_PASSWORD);
    createCart(user.get());
    String activationLink = generateActivationLink(user.get().getAuthUserId());
    emailService.sendEmailAfterBetRegistration(activationLink, studentSignupRequest.getEmail());
    return loginResponse;
  }

  private void createCart(User user) {
    Cart cart = Cart.builder().user(user).build();
    cartRepository.save(cart);
  }

  private LoginRequest buildLoginRequest(BetStudentSignUpDto.Request request, User user) {
    return LoginRequest.builder()
        .appContext(request.appContext())
        .username(user.getAuthUserId())
        .password(request.password())
        .build();
  }

  public String generateActivationLink(String authUserId) {
    String encryptId = encrypt(authUserId, SECRETAESKEY);
    return baseUrl + "#/activation-links/" + encryptId;
  }

  private StudentRequest buildBetStudentRequest(BetStudentSignUpDto.Request request) {
    var userName = generateUserNameByUUID();
    Section sections = sectionService.getSectionByNameAndOrg("standard", ORG_SLUG);
    String firstName = request.email().substring(0, request.email().indexOf("@"));
    StudentRequest student = new StudentRequest();
    student.setFirstName(firstName);
    student.setLastName("");
    student.setUserName(userName);
    student.setEmail(request.email());
    student.setSchoolName("BET School");
    student.setAcademicYearSlug(latestAcademicYear);
    student.setGradeSlug(sections.getGradeSlug());
    student.setBoardSlug(sections.getBoardSlug());
    student.setSection(sections.getName());
    student.setParentFirstName("");
    student.setParentEmail("");
    student.setParentMobileNumber("");
    student.setPassword(request.password());
    return student;
  }

  private String generateUserNameByUUID() {
    UUID uuid = UUID.randomUUID();
    return uuid.toString();
  }

  public BetStudentSignUpDto.ActivationResponse activationLink(String activationKey) {

    try {
      String userName = decrypt(activationKey, SECRETAESKEY);
      var optionalUser = userRepository.findByAuthUserId(userName);
      if (optionalUser.isEmpty()) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.UserNotFound");
      }
      if (optionalUser.get().getEmailVerified() != null && optionalUser.get().getEmailVerified()) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.UserAlreadyActivated");
      }
      User user = optionalUser.get();
      user.setEmailVerified(true);
      userRepository.save(user);
      return new BetStudentSignUpDto.ActivationResponse(
          "Your account has been successfully activated.");
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage());
    }
  }

  public LoginResponse login(LoginRequest loginRequest, String userAgent) {
    if (ObjectUtils.isEmpty(loginRequest)
        || StringUtils.isEmpty(loginRequest.getUsername())
        || StringUtils.isEmpty(loginRequest.getPassword())
        || StringUtils.isEmpty(loginRequest.getAppContext())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.WrongCredentials");
    }
    var user = userRepository.findUserByEmail(loginRequest.getUsername());

    if (user.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.WrongCredentials");
    }
    validatePassword(user.get(), loginRequest.getPassword());
    loginRequest.setUsername(user.get().getAuthUserId());
    authService.validateDeviceDetails(user.get(), loginRequest.getAppContext());
    var requestComingFromMobileApp = MobileAppUtil.requestComingFromMobileApp(userAgent);
    return authService.signin(
        requestComingFromMobileApp, loginRequest, LoginMethod.USERNAME_PASSWORD);
  }

  public void validatePassword(User user, String password) {
    String encodedPassword = user.getPassword();

    if (!passwordEncoder.matches(password, encodedPassword)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidPassword");
    }
  }

  public String encrypt(String uuid, String secretKey) {
    try {
      SecretKey key = new SecretKeySpec(secretKey.getBytes(), ALGORITHM);
      Cipher cipher = Cipher.getInstance(ALGORITHM);
      cipher.init(Cipher.ENCRYPT_MODE, key);
      byte[] encryptedBytes = cipher.doFinal(uuid.getBytes());
      return Base64.getUrlEncoder().encodeToString(encryptedBytes);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidActivationKey", e);
    }
  }

  public String decrypt(String encryptedUuid, String secretKey) {
    try {
      SecretKey key = new SecretKeySpec(secretKey.getBytes(), ALGORITHM);
      Cipher cipher = Cipher.getInstance(ALGORITHM);
      cipher.init(Cipher.DECRYPT_MODE, key);
      byte[] decodedBytes = Base64.getUrlDecoder().decode(encryptedUuid);
      return new String(cipher.doFinal(decodedBytes));
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidActivationKey", e);
    }
  }

  public BetUserPasswordDto.PasswordResetResponse forgotPassword(
      BetStudentSignUpDto.ForgotPasswordRequest request) {
    User user =
        userRepository
            .findUserByEmail(request.email())
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.UserNotFound"));

    String passwordResetLink = generatePasswordResetLink(user.getAuthUserId());
    emailService.sendBetPasswordResetLink(passwordResetLink, user.getEmail());
    return BetUserPasswordDto.PasswordResetResponse.builder()
        .message("Password reset link sent to " + request.email())
        .build();
  }

  private String generatePasswordResetLink(String userId) {
    String encryptedData = encrypt(userId, SECRETAESKEY);
    return baseUrl + "#/reset-password/" + encryptedData;
  }

  public BetUserPasswordDto.PasswordResetResponse resetPassword(
      BetStudentSignUpDto.ResetPasswordRequest request, String resetLink) {

    try {
      String decryptedToken = decrypt(resetLink, SECRETAESKEY);
      User user =
          userRepository
              .findByAuthUserId(decryptedToken)
              .orElseThrow(
                  () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.StudentFind"));

      if (StringUtils.isEmpty(request.password())) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidPassword");
      }

      user.setPassword(passwordEncoder.encode(request.password()));
      userRepository.save(user);

      return BetUserPasswordDto.PasswordResetResponse.builder()
          .message("Password reset is successfully done")
          .build();

    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.PasswordResetFailed", e);
    }
  }

  @Transactional
  public LoginResponse loginOAuth(@Valid LoginOAuthRequest loginOAuthRequest) {
    final OAuth2Request oAuth2Request = parseGoogleIdToken(loginOAuthRequest.jwt());
    var possibleUser = userRepository.findByEmailAndOrganization(oAuth2Request.email(), ORG_SLUG);

    if (possibleUser.isPresent()) {
      var user = possibleUser.get();
      return authService.signInWithEmail(user);
    }

    Request request =
        Request.builder().email(oAuth2Request.email()).password("password@12345").build();
    var studentSignupRequest = buildBetStudentRequest(request);
    var response = studentAuthService.createOrgStudent(studentSignupRequest, ORG_SLUG);
    var possibleUserUpdated =
        userRepository.findByEmailAndOrganization(response.getEmail(), ORG_SLUG);
    if (possibleUserUpdated.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.UserNotFound");
    }
    final User user = possibleUserUpdated.get();
    user.setEmailVerified(true);
    user.setBetUserType(1L);
    userRepository.save(user);
    createCart(user);
    return authService.signInWithEmail(user);
  }

  private OAuth2Request parseGoogleIdToken(String jwt) {
    try {
      GoogleIdTokenVerifier verifier =
          new GoogleIdTokenVerifier.Builder(
                  GoogleNetHttpTransport.newTrustedTransport(), GsonFactory.getDefaultInstance())
              .setAudience(Collections.singletonList(googleClientId))
              .build();

      GoogleIdToken idToken = verifier.verify(jwt);
      if (idToken == null) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Invalid ID token.");
      }
      GoogleIdToken.Payload payload = idToken.getPayload();
      return OAuth2Request.builder()
          .name((String) payload.get("name"))
          .givenName((String) payload.get("given_name"))
          .familyName((String) payload.get("family_name"))
          .email(payload.getEmail())
          .isEmailVerified(payload.getEmailVerified())
          .build();

    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Token verification failed.", e);
    }
  }

  private OAuth2Request parseJwtToken(String jwt) {
    var claims =
        Jwts.parserBuilder()
            .setSigningKey(Base64.getDecoder().decode(oauthTokenSecret))
            .build()
            .parseClaimsJws(jwt)
            .getBody();

    return OAuth2Request.builder()
        .name(claims.get("name", String.class))
        .givenName(claims.get("given_name", String.class))
        .familyName(claims.get("family_name", String.class))
        .email(claims.get("email", String.class))
        .isEmailVerified(claims.get("email_verified", Boolean.class))
        .build();
  }

  public List<BetStudentSignUpDto.ExamResultResponse> getExamResults(String orgSlug) {
    validationUtils.isOrgValid(orgSlug);
    var scheduleTestStudentAndStatuses =
        scheduleTestStudentRepository.findTestScheduleTestsByOrgSlug(orgSlug);

    return scheduleTestStudentAndStatuses.stream()
        .map(
            test -> {
              Optional<User> userOptional = userRepository.findById(test.getStudentId());
              if (userOptional.isEmpty()) {
                return null;
              }
              User user = userOptional.get();

              ProctoringStatus proctoringStatus =
                  examRepository
                      .findLatestExamByScheduleTestIdAndStudentId(
                          test.getTestScheduleId(), user.getStudentInfo().getId())
                      .flatMap(
                          exam ->
                              proctoringRepository
                                  .findByExamIdOrderByEndTimeDesc(exam.getId())
                                  .stream()
                                  .findFirst())
                      .map(ProctoringSession::getStatus)
                      .orElse(null);

              return BetStudentSignUpDto.ExamResultResponse.builder()
                  .firstName(user.getFirstName())
                  .lastName(user.getLastName())
                  .emailId(user.getEmail())
                  .examDate(DateTimeUtil.convertIso8601ToEpoch(test.getStartDate()))
                  .mobileNumber(user.getMobileNumber())
                  .proctorStatus(proctoringStatus)
                  .testStatus(test.getTestStatus())
                  .scheduleTestId(test.getTestScheduleId())
                  .orgSlug(orgSlug)
                  .studentAuthId(user.getAuthUserId())
                  .build();
            })
        .filter(Objects::nonNull)
        .toList();
  }
}
