package com.wexl.practicetest.controller;

import com.wexl.practicetest.dto.BetPracticeTestDto;
import com.wexl.practicetest.services.BetPracticeTestServices;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RequiredArgsConstructor
@RestController
@RequestMapping("bet-exams/orgs/{orgSlug}")
public class BetPracticeTestController {

  private final BetPracticeTestServices betPracticeTestServices;

  @PostMapping("/students/{studentAuthId}/practice")
  public BetPracticeTestDto.Response betPracticeTest(
      @PathVariable String orgSlug, @PathVariable String studentAuthId) {

    return betPracticeTestServices.createPracticeTest(orgSlug, studentAuthId);
  }
}
