package com.wexl.practicetest.services;

import com.wexl.practicetest.dto.BetPracticeTestDto;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.schedule.domain.ScheduleTestMetadata;
import com.wexl.retail.test.schedule.dto.SimpleScheduleTestRequest;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import com.wexl.retail.test.schedule.service.ScheduleTestService;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.domain.TestType;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.util.ValidationUtils;
import java.time.Instant;
import java.util.Collections;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class BetPracticeTestServices {

  private final ValidationUtils validationUtils;
  private final TestDefinitionRepository testDefinitionRepository;
  private final UserRepository userRepository;
  private final ScheduleTestService scheduleTestService;
  private final ScheduleTestStudentRepository scheduleTestStudentRepository;
  private final DateTimeUtil dateTimeUtil;

  public BetPracticeTestDto.Response createPracticeTest(String orgSlug, String authUserId) {
    validationUtils.isOrgValid(orgSlug);
    var testDefinitionList =
        testDefinitionRepository.getBetTests("BET-LE-%", TestType.MOCK_TEST.name(), orgSlug);
    var user = userRepository.findByAuthUserId(authUserId);
    var testDefinitionData =
        testDefinitionList.stream()
            .filter(x -> x.getTestName().startsWith("BET-PRACTICE"))
            .findFirst();
    if (testDefinitionData.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.StudentTestNotFound");
    }

    var testDefinition = testDefinitionData.get();
    var scheduleRequest = buildScheduleTestRequest(testDefinition, user.get());
    var testSchedule =
        scheduleTestService.scheduleBetTest(testDefinition, scheduleRequest, user.get());

    return buildResponse(testSchedule, user.get());
  }

  private BetPracticeTestDto.Response buildResponse(ScheduleTest scheduleTest, User user) {

    var test =
        scheduleTestStudentRepository.findAllTestsForStudent(
            user.getId(), List.of(TestType.MOCK_TEST.name()));

    var currentTest =
        test.stream()
            .filter(x -> x.getScheduleTestId() == scheduleTest.getId())
            .findFirst()
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, "error.StudentTestNotFound"));

    return BetPracticeTestDto.Response.builder()
        .testDefinitionId(scheduleTest.getTestDefinition().getId())
        .scheduleTestId(scheduleTest.getId())
        .startDate(dateTimeUtil.convertIso8601ToEpoch(scheduleTest.getStartDate()))
        .endDate(dateTimeUtil.convertIso8601ToEpoch(scheduleTest.getEndDate()))
        .status(
            scheduleTestService.getScheduledTestStatus(
                scheduleTest.getStartDate(), scheduleTest.getEndDate()))
        .subjectName(null)
        .testName(scheduleTest.getTestDefinition().getTestName())
        .testState(currentTest.getTestState())
        .testType(TestType.MOCK_TEST.name())
        .scheduleTestUuid(currentTest.getScheduleTestUuid())
        .category(scheduleTest.getTestDefinition().getCategory().getValue())
        .build();
  }

  private SimpleScheduleTestRequest buildScheduleTestRequest(
      TestDefinition testDefinition, User user) {
    var student = user.getStudentInfo();
    long currentEpochMillis = Instant.now().toEpochMilli();
    long epochMillisAfterOneHour = currentEpochMillis + 3600000;

    return SimpleScheduleTestRequest.builder()
        .testDefinitionId(testDefinition.getId())
        .allStudents(false)
        .message("All the Best")
        .startDate(currentEpochMillis)
        .endDate(epochMillisAfterOneHour)
        .duration(60)
        .metadata(buildMetaData(student))
        .studentIds(Collections.singleton(student.getId()))
        .build();
  }

  private ScheduleTestMetadata buildMetaData(Student student) {
    var section = student.getSection();
    return ScheduleTestMetadata.builder()
        .board(section.getBoardSlug())
        .grade(section.getGradeSlug())
        .sections(Collections.singletonList(section.getName()))
        .build();
  }
}
