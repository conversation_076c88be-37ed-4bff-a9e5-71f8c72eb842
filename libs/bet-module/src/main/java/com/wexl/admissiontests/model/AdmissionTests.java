package com.wexl.admissiontests.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.model.Model;
import com.wexl.retail.test.schedule.dto.TestStudentStatus;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "admission_tests")
public class AdmissionTests extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String name;

  @JsonProperty("admission_no")
  private Long admissionNo;

  @JsonProperty("auth_user_id")
  private String authUserId;

  @JsonProperty("test_definition_id")
  private Long testDefinitionId;

  @JsonProperty("test_schedule_id")
  private Long testScheduleId;

  @JsonProperty("org_slug")
  private String orgSlug;

  @JsonProperty("grade_name")
  private String gradeName;

  @JsonProperty("grade_slug")
  private String gradeSlug;

  @JsonProperty("phone_number")
  private String phoneNumber;

  @JsonProperty("parent_name")
  private String parentName;

  @JsonProperty("school_name")
  private String schoolName;

  @JsonProperty("location")
  private String location;

  @JsonProperty("email_id")
  private String emailId;

  private TestStudentStatus status;
}
