package com.wexl.admissiontests.service;

import com.wexl.admissiontests.dto.AdmissionTestDto;
import com.wexl.admissiontests.model.AdmissionTests;
import com.wexl.admissiontests.repository.AdmissionTestRepository;
import com.wexl.reportcard.BetReportCard;
import com.wexl.reportcard.dto.BetReportDto;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.model.Grade;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.proctoring.model.ProctoringSession;
import com.wexl.retail.proctoring.model.ProctoringStatus;
import com.wexl.retail.proctoring.repository.ProctoringRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.student.exam.ExamRepository;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.schedule.domain.ScheduleTestMetadata;
import com.wexl.retail.test.schedule.dto.SimpleScheduleTestRequest;
import com.wexl.retail.test.schedule.dto.TestStudentStatus;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import com.wexl.retail.test.schedule.service.ScheduleTestService;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.domain.TestType;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.util.StrapiService;
import com.wexl.retail.util.ValidationUtils;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class AdmissionTestService {

  private final ValidationUtils validationUtils;
  private final TestDefinitionRepository testDefinitionRepository;
  private final ScheduleTestService scheduleTestService;
  private final DateTimeUtil dateTimeUtil;
  private final AdmissionTestRepository admissionTestRepository;
  private final ScheduleTestStudentRepository scheduleTestStudentRepository;
  private final StrapiService strapiService;
  private final BetReportCard betReportCard;
  private final UserRepository userRepository;
  private final ExamRepository examRepository;
  private final ProctoringRepository proctoringRepository;

  public AdmissionTestDto.Response createTest(
      String orgSlug, AdmissionTestDto.Request request, String studentAuthId) {

    validationUtils.isOrgValid(orgSlug);
    var testDefinitionList =
        testDefinitionRepository.getTestsByGradeAndOrgSlugAndType(TestType.MOCK_TEST.name());

    var testDefinitionData =
        testDefinitionList.stream()
            .filter(x -> x.getGradeSlug().equals(request.gradeSlug()))
            .sorted(Comparator.comparing(TestDefinition::getId).reversed())
            .toList();

    if (testDefinitionData.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "TestDefinition is not available");
    }

    var testDefinition = testDefinitionData.get(0);
    var user = validationUtils.isValidUser(studentAuthId);
    var scheduleRequest = buildScheduleTestRequest(testDefinition, user, request);
    var testSchedule = scheduleTestService.scheduleBetTest(testDefinition, scheduleRequest, user);
    var admissionTest =
        admissionTestRepository.save(
            buildAdmissionTest(orgSlug, testDefinition, testSchedule, request, studentAuthId));

    return buildResponse(admissionTest, testSchedule, user);
  }

  private AdmissionTestDto.Response buildResponse(
      AdmissionTests admissionTest, ScheduleTest scheduleTest, User user) {

    var test =
        scheduleTestStudentRepository.findAllTestsForStudent(
            user.getId(), List.of(TestType.MOCK_TEST.name()));

    var currentTest =
        test.stream()
            .filter(x -> x.getScheduleTestId() == scheduleTest.getId())
            .findFirst()
            .orElseThrow(
                () ->
                    new ApiException(InternalErrorCodes.INVALID_REQUEST, "Current test not found"));

    return AdmissionTestDto.Response.builder()
        .testDefinitionId(admissionTest.getTestDefinitionId())
        .scheduleTestId(admissionTest.getTestScheduleId())
        .startDate(dateTimeUtil.convertIso8601ToEpoch(scheduleTest.getStartDate()))
        .endDate(dateTimeUtil.convertIso8601ToEpoch(scheduleTest.getEndDate()))
        .status(
            scheduleTestService.getScheduledTestStatus(
                scheduleTest.getStartDate(), scheduleTest.getEndDate()))
        .subjectName(null)
        .testName(scheduleTest.getTestDefinition().getTestName())
        .testState(currentTest.getTestState())
        .testType(TestType.MOCK_TEST.name())
        .scheduleTestUuid(currentTest.getScheduleTestUuid())
        .category(scheduleTest.getTestDefinition().getCategory().getValue())
        .admissionTestId(admissionTest.getId())
        .build();
  }

  private SimpleScheduleTestRequest buildScheduleTestRequest(
      TestDefinition testDefinition, User user, AdmissionTestDto.Request request) {
    var student = user.getStudentInfo();

    return SimpleScheduleTestRequest.builder()
        .testDefinitionId(testDefinition.getId())
        .allStudents(false)
        .message("All the Best")
        .startDate(request.startTime())
        .endDate(request.endTime())
        .duration(90)
        .metadata(buildMetaData(student))
        .studentIds(Collections.singleton(student.getId()))
        .build();
  }

  private ScheduleTestMetadata buildMetaData(Student student) {
    var section = student.getSection();
    return ScheduleTestMetadata.builder()
        .board(section.getBoardSlug())
        .grade(section.getGradeSlug())
        .sections(Collections.singletonList(section.getName()))
        .build();
  }

  private AdmissionTests buildAdmissionTest(
      String orgSlug,
      TestDefinition testDefinition,
      ScheduleTest testSchedule,
      AdmissionTestDto.Request request,
      String studentAuthId) {

    return AdmissionTests.builder()
        .name(request.name())
        .admissionNo(Objects.nonNull(request.admissionNo()) ? request.admissionNo() : null)
        .testDefinitionId(testDefinition.getId())
        .testScheduleId(testSchedule.getId())
        .authUserId(studentAuthId)
        .phoneNumber(request.phoneNumber())
        .orgSlug(orgSlug)
        .status(TestStudentStatus.PENDING)
        .gradeName(request.gradeName())
        .gradeSlug(request.gradeSlug())
        .location(request.location())
        .parentName(request.parentName())
        .schoolName(request.schoolName())
        .emailId(request.emailId())
        .build();
  }

  public List<Grade> getGradesByTestDefinition() {
    var testDefinitionList =
        testDefinitionRepository.getTestsByGradeAndOrgSlugAndType(TestType.MOCK_TEST.name());

    var gradeSlug =
        testDefinitionList.stream().map(TestDefinition::getGradeSlug).distinct().toList();

    List<com.wexl.retail.content.model.Grade> allGrades = strapiService.getAllGrades();
    List<com.wexl.retail.content.model.Grade> grades =
        allGrades.stream().filter(grade -> gradeSlug.contains(grade.getSlug())).distinct().toList();

    return buildGrades(grades);
  }

  private List<Grade> buildGrades(List<com.wexl.retail.content.model.Grade> gradeEntities) {
    return gradeEntities.isEmpty()
        ? Collections.emptyList()
        : gradeEntities.stream()
            .map(
                grade -> {
                  return Grade.builder()
                      .id(grade.getId())
                      .slug(grade.getSlug())
                      .name(grade.getName())
                      .orderId(grade.getOrder())
                      .build();
                })
            .sorted(Comparator.comparing(Grade::getOrderId))
            .toList();
  }

  public List<AdmissionTestDto.AdmissionTestsResponse> getAdmissionTests(String orgSlug) {
    var org = validationUtils.isOrgValid(orgSlug);

    var admissionTestsList = admissionTestRepository.findAllByOrgSlug(orgSlug);
    if (admissionTestsList.isEmpty()) {
      return Collections.emptyList();
    }

    return admissionTestsList.stream()
        .filter(test -> test.getAdmissionNo() != null)
        .sorted(Comparator.comparing(AdmissionTests::getId).reversed())
        .map(
            test -> {
              var user = userRepository.findByAuthUserId(test.getAuthUserId()).orElseThrow();
              var testSchedule = scheduleTestService.validateTestSchedule(test.getTestScheduleId());

              BetReportDto.Body reportCard = null;
              if (Objects.equals(
                  testSchedule.getScheduleTestStudent().get(0).getStatus(), "COMPLETED")) {
                reportCard = betReportCard.buildBody(user.getStudentInfo(), testSchedule, null);
              }
              ProctoringStatus proctoringStatus =
                  examRepository
                      .findLatestExamByScheduleTestIdAndStudentId(
                          test.getTestScheduleId(), user.getStudentInfo().getId())
                      .flatMap(
                          exam ->
                              proctoringRepository
                                  .findByExamIdOrderByEndTimeDesc(exam.getId())
                                  .stream()
                                  .findFirst())
                      .map(ProctoringSession::getStatus)
                      .orElse(null);
              return AdmissionTestDto.AdmissionTestsResponse.builder()
                  .testDefinitionId(test.getTestDefinitionId())
                  .studentName(test.getName())
                  .studentAuthId(test.getAuthUserId())
                  .organizationName(org.getName())
                  .createdAt(
                      DateTimeUtil.convertIso8601ToEpoch(test.getCreatedAt().toLocalDateTime()))
                  .scheduleTestId(test.getTestScheduleId())
                  .gradeSlug(test.getGradeSlug())
                  .gradeName(test.getGradeName())
                  .phoneNumber(test.getPhoneNumber())
                  .examDate(DateTimeUtil.convertIso8601ToEpoch(testSchedule.getStartDate()))
                  .proctorStatus(proctoringStatus)
                  .testDetails(
                      reportCard == null
                          ? AdmissionTestDto.BetTestDetails.builder().build()
                          : buildBetSectionDetails(reportCard))
                  .id(test.getId())
                  .admissionNo(test.getAdmissionNo())
                  .build();
            })
        .toList();
  }

  private AdmissionTestDto.BetTestDetails buildBetSectionDetails(BetReportDto.Body reportCard) {
    return AdmissionTestDto.BetTestDetails.builder()
        .section1Name(reportCard.section1Name())
        .section1marks(reportCard.section1Value())
        .section2Name(reportCard.section2Name())
        .section2marks(reportCard.section2Value())
        .section3Name(reportCard.section3Name())
        .section3marks(reportCard.section3Value())
        .section4Name(reportCard.section4Name())
        .section4marks(reportCard.section4Value())
        .overAllScore(reportCard.scoreValue())
        .build();
  }
}
