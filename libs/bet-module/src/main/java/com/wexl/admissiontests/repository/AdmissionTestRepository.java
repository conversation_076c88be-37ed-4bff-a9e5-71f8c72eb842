package com.wexl.admissiontests.repository;

import com.wexl.admissiontests.model.AdmissionTests;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface AdmissionTestRepository extends JpaRepository<AdmissionTests, Long> {
  AdmissionTests findByTestScheduleId(Long testScheduleId);

  List<AdmissionTests> findAllByOrgSlug(String orgSlug);
}
