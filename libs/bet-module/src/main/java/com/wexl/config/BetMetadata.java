package com.wexl.config;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@Data
@AllArgsConstructor
@NoArgsConstructor
@ConfigurationProperties(prefix = "bet")
public class BetMetadata {
  private List<String> categories;
  private List<MetadataBetSection> sections;

  @Data
  public static class MetadataBetSection {
    private String name;
    private String outcome;
    private String seqNum;
    private String category;
    private List<MetadataBetUnit> units;
  }

  @Data
  public static class MetadataBetUnit {
    private String name;
    private String seqNum;
    private Long testDefinitionId;
    private List<MetadataBetLesson> lessons;
  }

  @Data
  public static class MetadataBetLesson {
    private String name;
    private String seqNum;
    private Long testDefinitionId;
  }
}
