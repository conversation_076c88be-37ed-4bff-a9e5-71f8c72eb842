package com.wexl.order.repository;

import com.wexl.order.entity.Order;
import com.wexl.order.entity.OrderStatus;
import com.wexl.retail.model.User;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.stereotype.Repository;

@Repository
@EnableJpaRepositories
public interface OrderRepository extends JpaRepository<Order, Long> {
  List<Order> findByUserAndStatus(User user, OrderStatus status);

  Optional<Order> findByOrderNumber(String orderNumber);

  List<Order> findAllByUserIdAndStatusNotIn(long userId, Collection<OrderStatus> status);

  Optional<Order> findByIdAndStatusIn(Long orderId, List<OrderStatus> orderStatus);
}
