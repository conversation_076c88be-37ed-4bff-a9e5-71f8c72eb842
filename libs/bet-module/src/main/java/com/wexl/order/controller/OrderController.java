package com.wexl.order.controller;

import com.wexl.order.dto.OrderDto;
import com.wexl.order.service.OrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/bet-exams")
@RequiredArgsConstructor
public class OrderController {

  private final OrderService orderService;

  @GetMapping("/students/{authUserId}/order-history")
  public OrderDto.OrderResponse getAllOrders(@PathVariable String authUserId) {
    return orderService.getAllStudentOrders(authUserId);
  }
}
