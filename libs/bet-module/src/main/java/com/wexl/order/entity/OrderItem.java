package com.wexl.order.entity;

import com.wexl.order.dto.OrderDto;
import com.wexl.product.entity.Product;
import com.wexl.retail.model.Model;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Type;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "order_items")
public class OrderItem extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "order_id")
  private Order order;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "product_id")
  private Product product;

  @Column(name = "quantity")
  private Long quantity;

  @Column(name = "unit_price")
  private double unitPrice;

  @Enumerated(EnumType.STRING)
  private OrderItemStatus status;

  @Type(JsonType.class)
  @Column(name = "item_details", columnDefinition = "jsonb")
  private OrderDto.OrderItemDetails itemDetails;
}
