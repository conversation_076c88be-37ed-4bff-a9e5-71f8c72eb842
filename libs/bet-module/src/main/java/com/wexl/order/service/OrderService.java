package com.wexl.order.service;

import com.wexl.order.dto.OrderDto;
import com.wexl.order.entity.Order;
import com.wexl.order.entity.OrderItem;
import com.wexl.order.entity.OrderStatus;
import com.wexl.order.repository.OrderItemRepository;
import com.wexl.order.repository.OrderRepository;
import com.wexl.product.dto.ProductDto;
import com.wexl.product.entity.Product;
import com.wexl.retail.guardian.service.GuardianService;
import com.wexl.retail.model.User;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@RequiredArgsConstructor
@Service
public class OrderService {

  private final GuardianService guardianService;
  private final OrderRepository orderRepository;
  private final OrderItemRepository orderItemRepository;

  public OrderDto.OrderResponse getAllStudentOrders(String authUserId) {

    User user = guardianService.validateUser(authUserId);
    List<Order> orders =
        orderRepository.findAllByUserIdAndStatusNotIn(
            user.getId(), Collections.singleton(OrderStatus.CREATED));

    List<OrderDto.OrderDetail> orderDetails = new ArrayList<>();

    for (Order order : orders) {

      List<OrderItem> orderItems = orderItemRepository.findAllByOrderId(order.getId());

      List<ProductDto.productResponse> productResponses =
          orderItems.stream().map(this::buildProductResponse).toList();

      double totalAmount =
          orderItems.stream().mapToDouble(item -> item.getUnitPrice() * item.getQuantity()).sum();

      OrderDto.OrderDetail orderDetail =
          OrderDto.OrderDetail.builder()
              .orderId(order.getId())
              .totalAmount(totalAmount)
              .status(order.getStatus())
              .productResponses(productResponses)
              .build();

      orderDetails.add(orderDetail);
    }

    return OrderDto.OrderResponse.builder().orderDetails(orderDetails).build();
  }

  public ProductDto.productResponse buildProductResponse(OrderItem orderItem) {
    Product product = orderItem.getProduct();
    return ProductDto.productResponse
        .builder()
        .id(product.getId())
        .title(product.getTitle())
        .price(product.getPrice())
        .type(product.getType())
        .description(product.getDescription())
        .quantity(orderItem.getQuantity())
        .build();
  }
}
