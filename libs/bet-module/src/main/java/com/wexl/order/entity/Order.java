package com.wexl.order.entity;

import com.wexl.retail.model.Model;
import com.wexl.retail.model.User;
import jakarta.persistence.*;
import java.util.List;
import lombok.*;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "orders")
public class Order extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "user_id")
  private User user;

  @Column(name = "discount_code")
  private String discountCode;

  @Column(name = "total_amount")
  private double totalAmount;

  @Column(name = "discount_amount")
  private Double discountAmount;

  @Column(name = "total_amount_with_discount")
  private Double totalAmountWithDiscount;

  @Enumerated(EnumType.STRING)
  private OrderStatus status;

  @Column(name = "order_number")
  private String orderNumber;

  private String rzPayOrderId;

  @OneToMany(mappedBy = "order", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  private List<OrderItem> orderItems;
}
