package com.wexl.order.repository;

import com.wexl.order.entity.Order;
import com.wexl.order.entity.OrderItem;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
@EnableJpaRepositories
public interface OrderItemRepository extends JpaRepository<OrderItem, Long> {

  void deleteByOrder(@Param("order") Order order);

  List<OrderItem> findAllByOrderId(Long orderId);
}
