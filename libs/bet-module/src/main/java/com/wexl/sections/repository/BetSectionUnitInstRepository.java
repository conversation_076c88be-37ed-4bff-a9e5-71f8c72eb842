package com.wexl.sections.repository;

import com.wexl.sections.models.BetSectionUnitInst;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface BetSectionUnitInstRepository extends JpaRepository<BetSectionUnitInst, Long> {

  List<BetSectionUnitInst> findAllByUserId(String authUserId);

  BetSectionUnitInst findByUserIdAndBetSectionUnitId(String authUserId, long id);

  @Query(
      value =
          """
          select bsui.* from  bet_section_unit_inst bsui
          join bet_section_units  bsu on bsu.id = bsui.bet_section_unit_id
          join bet_section_inst bsi on bsi.bet_section_id = bsu.bet_section_id
          where bsui.user_id = :authUserId and  bsi.user_id = :authUserId
          and bsu.grade_slug = :gradeSlug and bsu.bet_section_id = :betSectionId and bsi.bet_status in(0,2)
          order by bsui.created_at desc limit 1""",
      nativeQuery = true)
  Optional<BetSectionUnitInst> getStudentInProgressCourseLevel(
      String authUserId, long betSectionId, String gradeSlug);
}
