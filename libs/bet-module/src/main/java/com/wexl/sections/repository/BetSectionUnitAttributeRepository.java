package com.wexl.sections.repository;

import com.wexl.sections.models.BetSection;
import com.wexl.sections.models.BetSectionUnitAttributes;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface BetSectionUnitAttributeRepository
    extends JpaRepository<BetSectionUnitAttributes, Long> {

  Optional<BetSectionUnitAttributes> findByBetSectionAndGradeSlug(
      BetSection betSection, String gradeSlug);
}
