package com.wexl.sections.models;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "bet_section_unit_inst")
public class BetSectionUnitInst extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  @Column(name = "bet_section_unit_id")
  private long betSectionUnitId;

  private BetStatus betStatus;

  @Column(name = "user_id")
  private String userId;
}
