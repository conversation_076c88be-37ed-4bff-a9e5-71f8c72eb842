package com.wexl.sections.repository;

import com.wexl.sections.dto.GradeUserDto;
import com.wexl.sections.models.BetSection;
import com.wexl.sections.models.BetSectionUnit;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface BetSectionUnitRepository extends JpaRepository<BetSectionUnit, Long> {
  List<BetSectionUnit> findAllByBetSections(BetSection betSection);

  @Query(
      value =
          """
                  SELECT count(*)
                   FROM bet_section_units bsu
                   left join bet_section_unit_inst bsui on bsui.bet_section_unit_id  = bsu.id
                   WHERE bsu.grade_slug = :gradeSlug
                   AND bsui.user_id = :userId
                   AND bsu.bet_section_id = :sectionId
                   And bsu.bet_unit_category <> 1
                   """,
      nativeQuery = true)
  Long getAssignedUnitCounts(Long sectionId, String userId, String gradeSlug);

  BetSectionUnit findByIdAndBetSections(Long betSectionUnitId, BetSection betSection);

  @Query(
      value =
          """
                  SELECT bsu.grade_slug AS gradeSlug,
                                        bsui.user_id AS userId
                                 FROM bet_section_units bsu
                                 JOIN bet_section_unit_inst bsui ON bsui.bet_section_unit_id = bsu.id
                                 WHERE bsui.user_id IN :userAuthIds
                                   AND name != 'Level Test'
                                 GROUP BY (bsu.grade_slug,
                                           bsui.user_id);
                  """,
      nativeQuery = true)
  List<GradeUserDto> getUserIdAndGradeSlug(List<String> userAuthIds);
}
