package com.wexl.sections.models;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.util.List;
import lombok.*;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "bet_sections")
public class BetSection extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  private String name;

  @Column(name = "seq_num")
  private long seqNo;

  @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinColumn(name = "bet_section_id")
  private List<BetSectionUnit> betSectionUnits;

  @Column(name = "bet_section_category_id")
  private Long betSectionCategoryId;

  @Column(columnDefinition = "TEXT")
  private String outcome;

  @Column(name = "wexl_subject_name")
  private String wexlSubjectName;

  @Column(name = "wexl_subject_slug")
  private String wexlSubjectSlug;

  private String title;
  private String description;

  @Column(name = "image_path")
  private String imagePath;
}
