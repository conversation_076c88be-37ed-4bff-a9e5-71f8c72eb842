package com.wexl.sections.models;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "bet_section_unit_lessons")
public class BetSectionUnitLesson extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  private String name;

  @Column(name = "wexl_subtopic_slug")
  private String wexlSubtopicSlug;

  @Column(name = "seq_num")
  private long seqNo;

  @Column(name = "test_definition_id")
  private Long testDefinitionId;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "bet_section_unit_id")
  private BetSectionUnit betSectionUnits;

  private String wikiDocumentUuid;

  private String wikiCollectionUuid;
}
