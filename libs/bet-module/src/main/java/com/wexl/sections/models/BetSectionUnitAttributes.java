package com.wexl.sections.models;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "bet_section_unit_attributes")
public class BetSectionUnitAttributes extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  @ManyToOne
  @JoinColumn(name = "bet_section_id", nullable = false)
  private BetSection betSection;

  @Column(name = "grade_slug")
  private String gradeSlug;

  private String title;

  @Column(columnDefinition = "TEXT")
  private String description;
}
