package com.wexl.sections.models;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "bet_user_types")
public class BetUserType extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  private String type;

  @ManyToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinTable(
      name = "bet_user_type_section_categories",
      joinColumns = @JoinColumn(name = "bet_user_type_id"),
      inverseJoinColumns = @JoinColumn(name = "category_id"))
  private List<BetSectionCategory> categories = new ArrayList<>();
}
