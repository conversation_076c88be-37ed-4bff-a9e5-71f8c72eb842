package com.wexl.sections.models;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "bet_section_unit_lesson_inst")
public class BetSectionUnitLessonInst extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  private BetStatus betStatus;

  @Column(name = "start_date")
  private LocalDateTime startDate;

  @Column(name = "end_time")
  private LocalDateTime endTime;

  @Column(name = "exam_id")
  private Long examId;

  private Double score;

  @Column(name = "lesson_attempt_count")
  private Long lessonAttemptCount;

  @Column(name = "bet_section_unit_lesson_id")
  private Long betSectionUnitLessons;

  @Column(name = "test_definition_section_id")
  private Long testDefinitionSectionId;

  @Column(name = "user_id")
  private String userId;

  @Column(name = "seq_num")
  private Long seqNo;
}
