package com.wexl.sections.repository;

import com.wexl.sections.dto.LatestAttemptedLevelResult;
import com.wexl.sections.models.BetSectionUnitLessonInst;
import com.wexl.sections.models.BetStatus;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface BetSectionUnitLessonsInstRepository
    extends JpaRepository<BetSectionUnitLessonInst, Long> {
  List<BetSectionUnitLessonInst> findAllByUserId(String authUserId);

  BetSectionUnitLessonInst findByIdAndUserId(Long betSectionUnitLessonInstId, String authUserId);

  BetSectionUnitLessonInst findByUserIdAndExamIdAndId(
      String authUserId, Long examId, Long betSectionUnitLessonInstId);

  List<BetSectionUnitLessonInst> findAllByBetSectionUnitLessonsAndUserId(
      Long betSectionUnitLessons, String authUserId);

  @Query(
      value =
          """
                  SELECT
                                                                                                           bsuli.user_id as userId
                                                                                                       FROM
                                                                                                           bet_section_unit_lesson_inst bsuli
                                                                                                       WHERE
                                                                                                           to_char(bsuli.start_date, 'yyyy-MM-dd') = :date
                                                                                                           AND bsuli.user_id IN (
                                                                                                               SELECT email FROM users WHERE organization = :org
                                                                                                           )""",
      nativeQuery = true)
  Set<String> getActiveUserCountByOrgAndDate(String org, String date);

  @Query(
      value =
          """
          select bsuli.* from bet_section_unit_lesson_inst bsuli
          join bet_section_unit_lessons bsul on bsul.id = bsuli.bet_section_unit_lesson_id
          join bet_section_units bsu on bsu.id = bsul.bet_section_unit_id
          join bet_sections bs on  bs.id = bsu.bet_section_id
          where bs.wexl_subject_slug =:courseSlug  and  user_id  =:authUserId
          and bet_status =:status""",
      nativeQuery = true)
  List<BetSectionUnitLessonInst> getAttemptedLessonsByUserIdAndCourseSlug(
      String authUserId, String courseSlug, int status);

  List<BetSectionUnitLessonInst> findAllByAndUserId(String authUserId);

  Optional<BetSectionUnitLessonInst> findTopByUserIdAndBetStatusOrderByEndTimeDesc(
      String authUserId, BetStatus betStatus);

  @Query(
      value =
          """
          SELECT bs.id AS betSectionId , bsi.id AS betSectionInstId ,bs.wexl_subject_slug as subjectSlug , bs."name" as courseName,
          bsu.grade_name as gradeName,bsu.grade_slug as gradeSlug FROM bet_section_unit_lesson_inst bsuli
          JOIN bet_section_unit_lessons bsul ON bsul.id = bsuli.bet_section_unit_lesson_id
          JOIN bet_section_unit_inst bsui  ON bsui.bet_section_unit_id = bsul.bet_section_unit_id
          JOIN bet_section_units bsu ON bsu.id = bsui.bet_section_unit_id
          JOIN bet_section_inst bsi  ON bsi.bet_section_id = bsu.bet_section_id
          JOIN bet_sections bs  on bs.id = bsi.bet_section_id
          WHERE bsuli.user_id= :authUserId and bsui.user_id =:authUserId and bsi.user_id =:authUserId
          ORDER BY bsuli.exam_id DESC NULLS LAST limit 1""",
      nativeQuery = true)
  Optional<LatestAttemptedLevelResult> getLastestAttemptedLevel(String authUserId);

  @Query(
      value =
          """
          select count(bsuli.*) as examCompletedCount   from bet_section_unit_lesson_inst bsuli
            join  bet_section_unit_lessons bsul on bsul.id = bsuli.bet_section_unit_lesson_id
            join  bet_section_units  bsu  on bsul.bet_section_unit_id = bsu.id
            where bsu.bet_section_id = :betSectionId and grade_slug =:betGradeSlug and bsuli.user_id =:authUserId
            and bsuli.bet_status =:betStatus""",
      nativeQuery = true)
  Long getStudentExamCompletedByCourse(
      String authUserId, Long betSectionId, String betGradeSlug, int betStatus);

  @Query(
      value =
          """
          select count(bsu.*) from bet_section_unit_lessons bsul
          join bet_section_units bsu on bsu.id = bsul.bet_section_unit_id
          where bsu.bet_section_id = :betSectionId and bsu.grade_slug =:levelSlug""",
      nativeQuery = true)
  long getTotalLessonByCourseAndLevel(String levelSlug, Long betSectionId);
}
