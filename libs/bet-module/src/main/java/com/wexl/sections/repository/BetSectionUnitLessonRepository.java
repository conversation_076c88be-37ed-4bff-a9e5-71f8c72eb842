package com.wexl.sections.repository;

import com.wexl.sections.models.BetSectionUnit;
import com.wexl.sections.models.BetSectionUnitLesson;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface BetSectionUnitLessonRepository extends JpaRepository<BetSectionUnitLesson, Long> {
  List<BetSectionUnitLesson> findAllByBetSectionUnits(BetSectionUnit betSectionUnit);

  Long countByBetSectionUnitsIn(List<BetSectionUnit> betSectionUnits);

  BetSectionUnitLesson findByIdAndBetSectionUnits(Long id, BetSectionUnit betSectionUnit);

  @Query(
      value =
          """
                  select bsul.id as lessonId,bsu as unitId from bet_section_units bsu join bet_section_unit_lessons bsul
                   on bsul.bet_section_unit_id = bsu.id where grade_slug = :gradeSlug and bet_section_id =:sectionId
                   and bet_unit_category=1
                   """,
      nativeQuery = true)
  BetUnitAndLessonIds getLessonId(Long sectionId, String gradeSlug);
}
