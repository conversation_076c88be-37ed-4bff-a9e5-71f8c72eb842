package com.wexl.sections.repository;

import com.wexl.sections.models.BetSectionInst;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface BetSectionInstRepository extends JpaRepository<BetSectionInst, Long> {
  List<BetSectionInst> findAllByUserId(String authUserId);

  Optional<BetSectionInst> findByBetSectionAndUserId(long betSection, String authUserId);
}
