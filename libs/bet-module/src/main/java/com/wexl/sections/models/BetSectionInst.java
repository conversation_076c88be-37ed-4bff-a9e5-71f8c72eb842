package com.wexl.sections.models;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "bet_section_inst")
public class BetSectionInst extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  @Column(name = "bet_section_id")
  private long betSection;

  @Column(name = "user_id")
  private String userId;

  private BetStatus betStatus;
}
