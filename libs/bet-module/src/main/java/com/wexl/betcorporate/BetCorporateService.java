package com.wexl.betcorporate;

import static com.wexl.retail.test.schedule.service.ScheduleTestService.ACTIVE;

import com.wexl.bet.gamification.repository.UserGameStatisticsRepository;
import com.wexl.metricshandler.BetExamResult;
import com.wexl.reportcard.BetReportCard;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.curriculum.service.OrgSettingsService;
import com.wexl.retail.guardian.service.GuardianService;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.student.attributes.dto.StudentAttributeDto;
import com.wexl.retail.student.attributes.repository.StudentAttributeValueRepository;
import com.wexl.retail.student.attributes.service.StudentAttributeService;
import com.wexl.retail.student.auth.StudentAuthService;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.schedule.domain.ScheduleTestMetadata;
import com.wexl.retail.test.schedule.domain.ScheduleTestStudent;
import com.wexl.retail.test.schedule.dto.SimpleScheduleTestRequest;
import com.wexl.retail.test.schedule.repository.ScheduleTestRepository;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import com.wexl.retail.test.schedule.service.ScheduleTestService;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.dto.TestDetailsUser;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.ValidationUtils;
import com.wexl.retail.v2.service.ScheduleTestStudentService;
import com.wexl.sections.repository.BetSectionUnitLessonRepository;
import jakarta.validation.constraints.NotNull;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class BetCorporateService {
  private final OrgSettingsService orgSettingsService;
  private final OrganizationRepository organizationRepository;
  private final TestDefinitionRepository testDefinitionRepository;
  private final SectionRepository sectionRepository;
  private final UserRepository userRepository;
  private final StudentRepository studentRepository;
  private final StudentAttributeService studentAttributeService;
  private final ScheduleTestService scheduleTestService;
  private final StudentAttributeValueRepository studentAttributeValueRepository;
  private final ValidationUtils validationUtils;
  private final StudentAuthService studentAuthService;
  private final ScheduleTestRepository scheduleTestRepository;
  private final GuardianService guardianService;
  private final DateTimeUtil dateTimeUtil;
  private final ScheduleTestStudentRepository scheduleTestStudentRepository;
  private final ScheduleTestStudentService scheduleTestStudentService;
  private final BetSectionUnitLessonRepository sectionUnitLessonRepository;
  private final BetReportCard betReportCard;
  private final BetExamRepository examRepository;
  private final UserGameStatisticsRepository userGameStatisticsRepository;
  public static String WEXL_INTERNAL = "wexl-internal";
  public static String BET_BOARD = "bet";
  public static String BET_CORP = "BET-CE-%";
  public static String BRILLIANT_INSTITUTE_ORG = "bri645604";
  public static List<Long> BRILLIANT_INSTITUTE_CUSTOM_TEST_IDS = List.of(9203430L);

  public void initializeBet(BetInitializeRequest betInitializeRequest) {
    var org = orgSettingsService.validateOrganizaiton(betInitializeRequest.getOrgSlug());
    org.setIsCorporate(
        !Constants.BHARAT_ENGLISH_TEST_ORG.equals(betInitializeRequest.getOrgSlug()));
    organizationRepository.save(org);

    var testDefinitions =
        testDefinitionRepository.getTestDefinitionsByOrgAndBoard(
            "wexl-internal", betInitializeRequest.getBoardSlug(), "BET-CE-%", null);

    var sections =
        sectionRepository.getSectionsUsingGradeSlugsAndBoardSlugs(
            Collections.singletonList("stdg"), org.getSlug(), "bet");

    var students = studentRepository.getListStudentsBySections(sections);

    if (sections.isEmpty()) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "error.SectionNotFound");
    }

    if (testDefinitions.isEmpty() || students.isEmpty()) {
      return;
    }

    for (Student student : students) {
      var user = userRepository.findById(student.getUserInfo().getId()).orElseThrow();
      Map<String, String> attributes = new HashMap<>();
      int attributeIndex = 1;

      for (int cycle = 0; cycle < 5; cycle++) {
        for (TestDefinition td : testDefinitions) {
          var scheduleRequest =
              SimpleScheduleTestRequest.builder()
                  .testDefinitionId(td.getId())
                  .allStudents(false)
                  .message("All the Best")
                  .startDate(Instant.now().toEpochMilli())
                  .endDate(
                      LocalDateTime.now()
                          .plusMonths(6)
                          .atZone(ZoneId.systemDefault())
                          .toInstant()
                          .toEpochMilli())
                  .duration(120)
                  .metadata(
                      buildScheduleTestMetaData(sections.stream().map(Section::getName).toList()))
                  .studentIds(Collections.singleton(student.getId()))
                  .orgSlug(user.getOrganization())
                  .build();

          var response = scheduleTestService.scheduleBetTest(td, scheduleRequest, user);
          attributes.put("bet_corp_schedule_" + attributeIndex++, String.valueOf(response.getId()));
        }
      }
      var buildAttributes = StudentAttributeDto.Request.builder().attributes(attributes).build();
      studentAttributeService.saveStudentDefinitionAttributes(
          user.getAuthUserId(), user.getOrganization(), buildAttributes);
    }
  }

  public ScheduleTestMetadata buildScheduleTestMetaData(List<String> sections) {
    return ScheduleTestMetadata.builder().board("bet").grade("stdg").sections(sections).build();
  }

  public List<BetExam.Response> getBetCorpExams(String authId, String orgSlug) {
    var user = userRepository.findByAuthUserId(authId).orElseThrow();
    var org = orgSettingsService.validateOrganizaiton(orgSlug);
    if (Boolean.FALSE.equals(org.getIsCorporate())) {
      return Collections.emptyList();
    }
    return getUserBetCorpTests(user, orgSlug).stream()
        .collect(
            Collectors.toMap(
                TestDetailsUser::getTestName,
                testDefinition -> testDefinition,
                (existing, replacement) ->
                    "COMPLETED".equalsIgnoreCase(replacement.getStatus()) ? replacement : existing))
        .values()
        .stream()
        .map(
            testDefinition ->
                BetExam.Response.builder()
                    .title(testDefinition.getTestName())
                    .totalMarks(testDefinition.getTotalMarks())
                    .totalQuestions(testDefinition.getTotalQuestions())
                    .testId(testDefinition.getTestDefinitionId())
                    .status(testDefinition.getStatus())
                    .examId(testDefinition.getExamId())
                    .completionTime(
                        testDefinition.getCompletedDate() != null
                            ? dateTimeUtil.convertTimeStampToLong(testDefinition.getCompletedDate())
                            : null)
                    .scheduleId(
                        testDefinition.getScheduleId() != null
                            ? testDefinition.getScheduleId()
                            : null)
                    .tssUuid(testDefinition.getTssUuid())
                    .build())
        .toList();
  }

  private List<TestDetailsUser> getUserBetCorpTests(final User user, final String orgSlug) {
    if (BRILLIANT_INSTITUTE_ORG.equals(orgSlug)) {
      return testDefinitionRepository.getTestDefinitionsByOrgAndBoardAndUser(
          WEXL_INTERNAL,
          BET_BOARD,
          BET_CORP,
          null,
          user.getId(),
          !BRILLIANT_INSTITUTE_CUSTOM_TEST_IDS.isEmpty()
              ? BRILLIANT_INSTITUTE_CUSTOM_TEST_IDS
              : null);
    }
    var testDetailsUsers =
        testDefinitionRepository.getTestDefinitionsByOrgAndBoardAndUser(
            WEXL_INTERNAL, BET_BOARD, BET_CORP, null, user.getId(), null);

    return testDetailsUsers.stream()
        .filter(
            testDetailsUser ->
                !BRILLIANT_INSTITUTE_CUSTOM_TEST_IDS.contains(
                    testDetailsUser.getTestDefinitionId()))
        .toList();
  }

  private List<TestDetailsUser> getBetCorpTests(final String orgSlug) {
    if (BRILLIANT_INSTITUTE_ORG.equals(orgSlug)) {
      return testDefinitionRepository.getCorporateTestsByOrganizationAndBoardSlug(
          WEXL_INTERNAL,
          BET_BOARD,
          null,
          BET_CORP,
          !BRILLIANT_INSTITUTE_CUSTOM_TEST_IDS.isEmpty()
              ? BRILLIANT_INSTITUTE_CUSTOM_TEST_IDS
              : null);
    }
    var testDetailsUsers =
        testDefinitionRepository.getCorporateTestsByOrganizationAndBoardSlug(
            WEXL_INTERNAL, BET_BOARD, null, BET_CORP, null);

    return testDetailsUsers.stream()
        .filter(
            testDetailsUser ->
                !BRILLIANT_INSTITUTE_CUSTOM_TEST_IDS.contains(
                    testDetailsUser.getTestDefinitionId()))
        .toList();
  }

  public BetExam.ScheduleResponse scheduleBetExam(BetExam.Request request) {
    var user = guardianService.validateUser(request.authId());
    var testDefinition = validationUtils.validateTestDefinition(request.testDefinitionId());
    var scheduleTestStudent =
        scheduleTestStudentRepository.findByTestDefinitionAndStudentId(
            user.getId(), testDefinition.getId());
    if (scheduleTestStudent.isPresent()) {
      return validateBetCorpExam(scheduleTestStudent.get());
    }
    var scheduleTest = buildScheduleForStudent(user, testDefinition, new ScheduleTest());
    scheduleTest.setScheduleTestStudent(
        scheduleTestService.getScheduleTestStudents(
            Collections.singleton(user.getId()), scheduleTest));
    var scheduleTestById = scheduleTestRepository.save(scheduleTest);
    scheduleTestService.saveStudentScheduleTestAnswers(scheduleTestById);
    return BetExam.ScheduleResponse.builder()
        .testScheduleId(scheduleTest.getId())
        .tssUuid(scheduleTest.getScheduleTestStudent().getFirst().getUuid())
        .testDefinitionId(request.testDefinitionId())
        .build();
  }

  private BetExam.ScheduleResponse validateBetCorpExam(
      @NotNull ScheduleTestStudent scheduleTestStudent) {
    var scheduleTest = scheduleTestStudent.getScheduleTest();
    if (Constants.STARTED.equals(scheduleTestStudent.getStatus())
        || Constants.PENDING.equals(scheduleTestStudent.getStatus())) {
      return BetExam.ScheduleResponse.builder()
          .testScheduleId(scheduleTest.getId())
          .tssUuid(scheduleTestStudent.getUuid())
          .testDefinitionId(scheduleTest.getTestDefinition().getId())
          .build();
    }
    throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Test already attempted");
  }

  private ScheduleTest buildScheduleForStudent(
      User user, TestDefinition testDef, ScheduleTest scheduleTest) {

    scheduleTest.setStartDate(LocalDateTime.now());
    scheduleTest.setEndDate(LocalDateTime.now().plusMonths(6));
    scheduleTest.setStatus(ACTIVE);
    scheduleTest.setMessage("All The Best!");
    scheduleTest.setDuration(getDurationByOrgAndTest(user.getOrganization(), testDef.getId()));
    scheduleTest.setAllStudents(false);
    scheduleTest.setTestDefinition(testDef);
    scheduleTest.setMetadata(
        buildScheduleTestMetaData(
            Collections.singletonList(user.getStudentInfo().getSection().getName())));
    scheduleTest.setTeacher(scheduleTestService.getAdminTeacher());
    scheduleTest.setPublished("false");
    scheduleTest.setType(Constants.DEFAULT_EXAM_SCHEDULETYPE);
    scheduleTest.setOrgSlug(user.getStudentInfo().getUserInfo().getOrganization());
    return scheduleTest;
  }

  private int getDurationByOrgAndTest(String organization, Long testDefinitionId) {
    if (BRILLIANT_INSTITUTE_ORG.contains(organization) && testDefinitionId.equals(9203430L)) {
      return 15;
    }
    return 60;
  }

  public List<GenericMetricResponse> getCorporateMetrics(String orgSlug) {
    var organization = validationUtils.isOrgValid(orgSlug);
    var orgSlugs = getChildOrgs(organization);
    List<GenericMetricResponse> genericMetricResponses = new ArrayList<>();
    var totalLearners = userRepository.getStudentsCountOfOrg(orgSlugs);
    var attemptedStudentCount = scheduleTestStudentRepository.getAttemptedStudentCount(orgSlugs);
    Map<String, Object> summary = new HashMap<>();
    summary.put("total_learners", totalLearners);
    summary.put("active_leaners", attemptedStudentCount);
    summary.put("inactive_leaners", totalLearners - attemptedStudentCount);
    summary.put(
        "attempted_percentage",
        totalLearners > 0
            ? Math.round((attemptedStudentCount / (double) totalLearners) * 100.0)
            : 0.0);
    summary.put("categories_count", 4);
    summary.put("cerf_levels", 6);
    summary.put("total_lessons", sectionUnitLessonRepository.count());

    Map<String, Object> data = new HashMap<>();

    var betExamResults = examRepository.getInitialBetExamResult(orgSlugs);
    var initialBetResults = betExamResults.stream().filter(result -> result.getRn() == 1).toList();

    var averageBetScore =
        initialBetResults.stream().mapToDouble(BetExamResult::getMarkScored).average();

    var previousWeekInitialExamResults =
        initialBetResults.stream()
            .filter(
                result ->
                    LocalDateTime.now()
                        .minusWeeks(1)
                        .isBefore(result.getCompletedAt().toLocalDateTime()))
            .toList();

    var testDetails = getBetCorpTests(organization.getSlug());

    List<BetExamResult> maxRnResults =
        betExamResults.stream()
            .collect(Collectors.groupingBy(BetExamResult::getStudentId))
            .values()
            .stream()
            .flatMap(
                list -> list.stream().max(Comparator.comparingInt(BetExamResult::getRn)).stream())
            .toList();

    var averageBetResultScore = maxRnResults.stream().mapToInt(BetExamResult::getRn).average();

    var previousWeekBetResults =
        betExamResults.stream()
            .filter(
                result ->
                    LocalDateTime.now()
                        .minusWeeks(1)
                        .isBefore(result.getCompletedAt().toLocalDateTime()))
            .toList();

    data.put("average_bet_score", betReportCard.getBetScore(averageBetScore.orElse(0) * 100));
    data.put("learners_attempted", initialBetResults.size());
    data.put(
        "learners_Progression_percentage",
        !previousWeekInitialExamResults.isEmpty()
            ? Math.round(
                ((float) (initialBetResults.size() - previousWeekInitialExamResults.size())
                        / initialBetResults.size())
                    * 100)
            : 0);
    data.put("learners_not_attempted", totalLearners - initialBetResults.size());

    var userGameStatisticsDetails =
        userGameStatisticsRepository.getUserGameStatisticsDetails(
            organization.getSlug(), LocalDateTime.now());

    var previousWeekUserGameStats =
        userGameStatisticsRepository.getUserGameStatisticsDetails(
            organization.getSlug(), LocalDateTime.now().minusWeeks(1));

    data.put(
        "xp_points_learners_attempted",
        Constants.DECIMAL_FORMAT.format(userGameStatisticsDetails.getAverageXpPoints()));
    data.put("daily_streaks", userGameStatisticsDetails.getTotalCurrentStreak());
    data.put(
        "daily_streaks_progression",
        Constants.DECIMAL_FORMAT.format(
            ((userGameStatisticsDetails.getTotalCurrentStreak()
                        - previousWeekUserGameStats.getTotalCurrentStreak())
                    / userGameStatisticsDetails.getTotalCurrentStreak())
                * 100));
    data.put(
        "xp_points_attempted_percent_vs_lastweek",
        ((userGameStatisticsDetails.getAverageXpPoints()
                    - previousWeekUserGameStats.getAverageXpPoints())
                / userGameStatisticsDetails.getAverageXpPoints())
            * 100);
    data.put(
        "bet_practice_test_average",
        Double.valueOf(
            Constants.DECIMAL_FORMAT.format(
                (averageBetResultScore.orElse(0) / (testDetails.size() * maxRnResults.size()))
                    * 100)));
    data.put(
        "bet_practice_test_progression",
        previousWeekBetResults.isEmpty()
            ? 0
            : Double.parseDouble(
                Constants.DECIMAL_FORMAT.format(
                    ((float) (betExamResults.size() - previousWeekBetResults.size())
                            / betExamResults.size())
                        * 100)));

    genericMetricResponses.add(GenericMetricResponse.builder().summary(summary).data(data).build());
    return genericMetricResponses;
  }

  private List<String> getChildOrgs(Organization organization) {
    List<String> orgSlugs = new ArrayList<>();
    if (Boolean.TRUE.equals(organization.getIsParent())) {
      var childOrgs = organizationRepository.findByParentAndDeletedAtIsNull(organization);
      var childOrgSlugs = childOrgs.stream().map(Organization::getSlug).toList();
      orgSlugs.addAll(childOrgSlugs);
    }
    orgSlugs.add(organization.getSlug());
    return orgSlugs;
  }
}
