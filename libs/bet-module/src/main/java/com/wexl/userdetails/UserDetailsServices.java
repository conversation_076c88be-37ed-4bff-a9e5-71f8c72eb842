package com.wexl.userdetails;

import com.wexl.betsignuplogin.dto.BetStudentSignUpDto;
import com.wexl.cart.entity.Cart;
import com.wexl.cart.repository.CartRepository;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Addresses;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.admin.StudentDto;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.student.attributes.model.StudentAttributeDefinitionModel;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.student.attributes.repository.StudentAttributeDefinitionRepository;
import com.wexl.retail.student.attributes.repository.StudentAttributeValueRepository;
import com.wexl.retail.student.attributes.service.StudentAttributeService;
import com.wexl.retail.student.auth.StudentAuthService;
import com.wexl.retail.student.profile.ProfileService;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.schedule.domain.ScheduleTestStudent;
import com.wexl.retail.test.schedule.repository.ScheduleTestRepository;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.util.ValidationUtils;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserDetailsServices {

  private final AuthService authService;
  private final StudentAuthService studentAuthService;
  private final CartRepository cartRepository;
  private final UserRepository userRepository;
  private final ReportCardService reportCardService;
  private final StudentAttributeValueRepository studentAttributeValueRepository;
  private final TestDefinitionRepository testDefinitionRepository;
  private final ScheduleTestRepository scheduleTestRepository;
  private final ScheduleTestStudentRepository scheduleTestStudentRepository;
  private final StudentAttributeDefinitionRepository studentAttributeDefinitionRepository;
  private final StudentAttributeService studentAttributeService;
  private final ValidationUtils validationUtils;
  private final ProfileService profileService;

  public BetStudentSignUpDto.UserDetailsResponse getBetUserDetails() {
    String authUserId = authService.getUserDetails().getAuthUserId();
    User user = authService.getUserByAuthUserId(authUserId);
    Cart cart = cartRepository.findByUserAndDeletedAtIsNull(user);
    var organization = validationUtils.isOrgValid(user.getOrganization());
    var studentAttributes =
        studentAttributeValueRepository.findByStudentAndOrgSlug(
            user.getOrganization(), user.getStudentInfo().getId());

    var instNameAttributeValue =
        studentAttributes.stream()
            .filter(
                attribute -> "institute_name".equals(attribute.getAttributeDefinition().getName()))
            .findFirst();

    var filteredStudentAttributeValues =
        studentAttributes.stream()
            .filter(
                attribute -> !"institute_name".equals(attribute.getAttributeDefinition().getName()))
            .toList();

    String instName =
        instNameAttributeValue.isPresent() ? instNameAttributeValue.get().getValue() : null;

    Optional<StudentAttributeValueModel> betAssignedSchedules;
    Optional<ScheduleTest> scheduleTest = Optional.empty();
    Optional<ScheduleTestStudent> scheduleTestStudent = Optional.empty();
    if (!filteredStudentAttributeValues.isEmpty() && filteredStudentAttributeValues.size() != 0) {
      var filteredAttributes =
          studentAttributeValueRepository.findByStudentAndScheduleTestInAndStatus(
              user.getId(), "PENDING", "bet_corp_schedule_%");
      if (!filteredAttributes.isEmpty()) {
        betAssignedSchedules =
            reportCardService.getStudentAttributeValue(
                user.getStudentInfo(),
                filteredAttributes.getFirst().getAttributeDefinition().getName());
        scheduleTest =
            scheduleTestRepository.findById(Long.valueOf(betAssignedSchedules.get().getValue()));
        scheduleTestStudent =
            scheduleTest.get().getScheduleTestStudent().stream()
                .filter(tss -> tss.getStudent().equals(user))
                .findFirst();
      }
    }
    var isRetailBetOrg = validationUtils.isRetailBetOrg(organization.getSlug());
    var userAddresses = user.getAddresses();
    var responseBuilder =
        BetStudentSignUpDto.UserDetailsResponse.builder()
            .verificationStatus(Boolean.TRUE.equals(user.getEmailVerified()))
            .email(user.getEmail())
            .orgName(instName)
            .cartItemCount(
                (cart != null && cart.getCartItems() != null)
                    ? (long) cart.getCartItems().size()
                    : null)
            .cartId(cart != null ? cart.getId() : null)
            .firstName(user.getFirstName())
            .lastName(user.getLastName())
            .mobileNumber(user.getMobileNumber())
            .betCorporateSchedules(buildBetScheduleDetails(scheduleTest, scheduleTestStudent))
            .gender(user.getGender())
            .proctoringToggle(false)
            .profileImageUrl(
                Objects.isNull(user.getProfileImage())
                    ? null
                    : profileService.getProfileImageUrl(user.getProfileImage()))
            .proctoringEnabled(isRetailBetOrg);
    if (Objects.nonNull(userAddresses)) {
      responseBuilder
          .address(userAddresses.getLine1())
          .city(userAddresses.getCity())
          .state(userAddresses.getState())
          .country(userAddresses.getCountry());
    }
    return responseBuilder.build();
  }

  private BetStudentSignUpDto.BetScheduleDetails buildBetScheduleDetails(
      Optional<ScheduleTest> scheduleTestOpt,
      Optional<ScheduleTestStudent> scheduleTestStudentOpt) {

    if (scheduleTestStudentOpt.isEmpty() || scheduleTestOpt.isEmpty()) {
      return null;
    }

    ScheduleTestStudent scheduleTestStudent = scheduleTestStudentOpt.get();
    if (scheduleTestStudent.getEndTime() != null) {
      return null;
    }

    ScheduleTest scheduleTest = scheduleTestOpt.get();

    return BetStudentSignUpDto.BetScheduleDetails.builder()
        .scheduleId(scheduleTest.getId())
        .testDefinitionId(scheduleTest.getTestDefinition().getId())
        .tssUuid(scheduleTestStudent.getUuid())
        .build();
  }

  public void updateStudentPassword(StudentDto.UpdatePasswordRequest updatePasswordRequest) {
    User userByAuthUserId = authService.getUserDetails();
    studentAuthService.updateStudentPassword(
        userByAuthUserId.getOrganization(),
        userByAuthUserId.getAuthUserId(),
        updatePasswordRequest.newPassword());
  }

  public void updateUserDetails(String authUserId, StudentDto.UpdateUserDetailsRequest request) {
    var user = authService.getUserByAuthUserId(authUserId);
    StudentAttributeDefinitionModel instituteNameAttribute = null;

    instituteNameAttribute =
        studentAttributeDefinitionRepository.findByOrgSlugAndName(
            user.getOrganization(), "institute_name");
    if (Objects.nonNull(request.firstName())) {
      user.setFirstName(request.firstName());
    }
    if (Objects.nonNull(request.lastName())) {
      user.setLastName(request.lastName());
    }
    if (Objects.nonNull(request.mobileNumber())) {
      user.setMobileNumber(request.mobileNumber());
    }
    if (Objects.nonNull(request.gender())) {
      user.setGender(request.gender());
    }
    user.setCountryCode("+91");
    user.setAddresses(buildAddress(request, user));
    userRepository.save(user);
    createOrUpdateAttributeDefintionAndValue(
        instituteNameAttribute, user.getStudentInfo(), request.instituteName());
  }

  private void createOrUpdateAttributeDefintionAndValue(
      StudentAttributeDefinitionModel sadm, Student student, String instName) {
    if (sadm == null) {

      studentAttributeService.saveNewStudentAttributeDefinition(
          student.getUserInfo().getOrganization(), "institute_name", student, instName);
    } else {
      studentAttributeService.updateStudentAttributeValue(student, sadm, instName);
    }
  }

  private Addresses buildAddress(StudentDto.UpdateUserDetailsRequest request, User user) {
    var addresses = Objects.nonNull(user.getAddresses()) ? user.getAddresses() : new Addresses();
    addresses.setUser(user);

    if (Objects.nonNull(request.state())) {
      addresses.setState(request.state());
    }
    if (Objects.nonNull(request.country())) {
      addresses.setCountry(request.country());
    }
    if (Objects.nonNull(request.address())) {
      addresses.setLine1(request.address());
    }
    if (Objects.nonNull(request.city())) {
      addresses.setCity(request.city());
    }
    return addresses;
  }
}
