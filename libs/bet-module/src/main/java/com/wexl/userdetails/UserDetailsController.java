package com.wexl.userdetails;

import com.wexl.betsignuplogin.dto.BetStudentSignUpDto;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.organization.admin.StudentDto;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RequiredArgsConstructor
@RestController
@Slf4j
public class UserDetailsController {

  private final UserDetailsServices userDetailsServices;

  @IsStudent
  @GetMapping("/bet-exams/users/me")
  public BetStudentSignUpDto.UserDetailsResponse betUserDetails() {
    return userDetailsServices.getBetUserDetails();
  }

  @PostMapping("/bet-exams/users/{authUserId}/passwords")
  @ResponseStatus(HttpStatus.ACCEPTED)
  public void updateStudentPassword(
      @RequestBody @Valid StudentDto.UpdatePasswordRequest updatePasswordRequest) {
    try {
      userDetailsServices.updateStudentPassword(updatePasswordRequest);

    } catch (Exception exception) {
      log.error("Failed to update student information", exception);
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.StudentsUpdateInfo.Failed");
    }
  }

  @PutMapping("/bet-exams/users/{authUserId}")
  @ResponseStatus(HttpStatus.ACCEPTED)
  public void updateUserDetails(
      @PathVariable("authUserId") String authUserId,
      @RequestBody StudentDto.UpdateUserDetailsRequest request) {
    userDetailsServices.updateUserDetails(authUserId, request);
  }
}
