package com.wexl.speech.processor.speechace.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

public record SpeechAceResponse() {

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record Response(
      @JsonProperty("status") String status,
      @JsonProperty("short_message") String shortMessage,
      @JsonProperty("detail_message") String detailMessage,
      @JsonProperty("text_score") TextScore textScores,
      @JsonProperty("speech_score") SpeechAceSpeechScore speechScore,
      @JsonProperty("version") String version,
      @JsonProperty("request_id") String requestId,
      @JsonProperty("quota_remaining") Integer quotaRemaining) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record TextScore(
      @JsonProperty("text") String text,
      @JsonProperty("word_score_list") List<WordScore> scores,
      @JsonProperty("ielts_score") PronunciationScore ieltsScore,
      @JsonProperty("speechace_score") PronunciationScore speechAceScore,
      @JsonProperty("pte_score") PronunciationScore pteScore,
      @JsonProperty("toeic_score") PronunciationScore toeicScore,
      @JsonProperty("cefr_score") CefrScore cefrScore) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record SpeechAceSpeechScore(
      @JsonProperty("transcript") String transcript,
      @JsonProperty("word_score_list") List<WordScore> wordScoreList,
      @JsonProperty("relevance") Relevance relevance,
      @JsonProperty("ielts_score") PronunciationScore ieltsScore,
      @JsonProperty("speechace_score") PronunciationScore speechAceScore,
      @JsonProperty("pte_score") PronunciationScore pteScore,
      @JsonProperty("toeic_score") PronunciationScore toeicScore,
      @JsonProperty("grammar") Grammar grammar,
      @JsonProperty("vocab") Grammar vocab,
      @JsonProperty("coherence") Grammar coherence,
      @JsonProperty("fluency") Fluency fluency,
      @JsonProperty("asr_version") String asrVersion,
      @JsonProperty("cefr_score") CefrScore cefrScore,
      @JsonProperty("score_issue_list") List<ScoreIssueList> scoreIssueList) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record ScoreIssueList(
      @JsonProperty("short_message") String shortMessage,
      @JsonProperty("detail_message") String detailMessage) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record CefrScore(
      String pronunciation,
      String fluency,
      String grammar,
      String coherence,
      String vocab,
      String overall) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record Relevance(@JsonProperty("class") String classSting) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record Fluency(
      @JsonProperty("segment_metrics_list") List<SegmentMetrics> segmentMetricsList,
      @JsonProperty("overall_metrics") SegmentMetrics overAllMetrics,
      @JsonProperty("fluency_version") String fluencyVersion,
      @JsonProperty("ielts_subscore_version") String ieltsSubscoreVersion) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record SegmentMetrics(
      @JsonProperty("segment") List<Object> segment,
      @JsonProperty("duration") Float duration,
      @JsonProperty("articulation_length") Float articulationLength,
      @JsonProperty("syllable_count") Integer syllableCount,
      @JsonProperty("correct_syllable_count") Integer correctSyllableCount,
      @JsonProperty("correct_word_count") Integer correctWordCount,
      @JsonProperty("word_count") Integer wordCount,
      @JsonProperty("speech_rate") Float speechRate,
      @JsonProperty("articulation_rate") Float articulationRate,
      @JsonProperty("syllable_correct_per_minute") Float syllableCorrectPerMinute,
      @JsonProperty("word_correct_per_minute") Float wordCorrectPerMinute,
      @JsonProperty("all_pause_count") Integer allPauseCount,
      @JsonProperty("all_pause_duration") Float allPauseDuration,
      @JsonProperty("mean_length_run") Float meanLengthRun,
      @JsonProperty("max_length_run") Float maxLengthRun,
      @JsonProperty("all_pause_list") List<Object> allPauseList,
      @JsonProperty("ielts_score") PronunciationScore ieltsScore,
      @JsonProperty("pte_score") PronunciationScore pteScore,
      @JsonProperty("speechace_score") PronunciationScore speechaceScore,
      @JsonProperty("toeic_score") PronunciationScore toeicScore,
      @JsonProperty("cefr_score") CefrScore cefrScore) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record Grammar(
      @JsonProperty("overall_metrics") OverallMetrics overallMetrics,
      @JsonProperty("errors") List<Object> errors) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record OverallMetrics(
      @JsonProperty("length") Score length,
      @JsonProperty("lexical_diversity") Score lexicalDiversity,
      @JsonProperty("lexical_density") Score lexicalDensity,
      @JsonProperty("basic_connectives") Score basicConnectives,
      @JsonProperty("causal_connectives") Score causalConnectives,
      @JsonProperty("negative_connectives") Score negativeConnectives,
      @JsonProperty("grammatical_accuracy") Score grammaticalAccuracy,
      @JsonProperty("pronoun_density") Score pronounDensity,
      @JsonProperty("adverb_diversity") Score adverbDiversity,
      @JsonProperty("verb_diversity") Score verbDiversity,
      @JsonProperty("grammatical_range") Score grammaticalRange,
      @JsonProperty("word_sophistication") Score wordSophistication,
      @JsonProperty("word_specificity") Score wordSpecificity,
      @JsonProperty("academic_language_use") Score academicLanguageUse,
      @JsonProperty("collocation_commonality") Score collocationCommonality,
      @JsonProperty("idiomaticity") Score idiomaticity) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record Score(
      @JsonProperty("score") Integer score,
      @JsonProperty("level") String level,
      @JsonProperty("message") String message,
      @JsonProperty("examples") List<Object> examples,
      @JsonProperty("noun_phrase_complexity") Score nounPhraseComplexity,
      @JsonProperty("noun_phrase_variation") Score nounPhraseVariation,
      @JsonProperty("verb_construction_variation") Score verbConstructionVariation,
      @JsonProperty("adverb_modifier_variation") Score adverbModifierVariation) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record PronunciationScore(
      Float pronunciation,
      Float fluency,
      Float grammar,
      Float coherence,
      Float vocab,
      Float overall) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record WordScore(
      @JsonProperty("word") String word,
      @JsonProperty("quality_score") Double qualityScore,
      @JsonProperty("phone_score_list") List<PhoneScore> phoneScoreList,
      @JsonProperty("ending_punctuation") String endingPunctuation,
      @JsonProperty("syllable_score_list") List<SyllableScore> syllableScoreList) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record PhoneScore(
      @JsonProperty("phone") String phone,
      @JsonProperty("stress_level") String stressLevel,
      @JsonProperty("quality_score") Double qualityScore,
      @JsonProperty("sound_most_like") String soundMostLike,
      @JsonProperty("word_extent") List<Long> wordExtent,
      @JsonProperty("stress_score") Long stressScore,
      @JsonProperty("predicted_stress_level") Integer predictedStressLevel,
      @JsonProperty("extent") List<Long> extent) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record SyllableScore(
      @JsonProperty("phone_count") Integer phoneCount,
      @JsonProperty("stress_level") Integer stressLevel,
      @JsonProperty("letters") String letters,
      @JsonProperty("quality_score") Double qualityScore,
      @JsonProperty("stress_score") Float stressScore,
      @JsonProperty("extent") List<Long> extent,
      @JsonProperty("predicted_stress_level") Integer predictedStressLevel) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record DescriptiveFeedback(
      @JsonProperty("overall") String overall,
      @JsonProperty("pronunciation") String pronunciation,
      @JsonProperty("fluency") String fluency,
      @JsonProperty("grammar") String grammar,
      @JsonProperty("coherence") String coherence,
      @JsonProperty("vocab") String vocab) {}
}
