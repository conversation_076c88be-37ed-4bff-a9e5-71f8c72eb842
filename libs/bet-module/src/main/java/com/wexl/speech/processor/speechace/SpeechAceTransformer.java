package com.wexl.speech.processor.speechace;

import com.amazonaws.util.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.speech.dto.SpeechEvaluation;
import com.wexl.speech.processor.speechace.dto.SpeechAceResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class SpeechAceTransformer {
  @Value("classpath:speechace/descriptive_feedback.json")
  private Resource descriptiveFeedbackJson;

  public SpeechEvaluation.SpeechScore transformFromSpeechAceSpeechScore(
      SpeechAceResponse.SpeechAceSpeechScore speechAceSpeechScore) {
    List<SpeechEvaluation.WordScore> finalWordScoreList = null;
    if (speechAceSpeechScore.wordScoreList() != null) {
      var wordScoreList =
          speechAceSpeechScore.wordScoreList().stream().map(this::mapWordScore).toList();
      finalWordScoreList = addBadPauses(wordScoreList);
    }

    return new SpeechEvaluation.SpeechScore(
        speechAceSpeechScore.transcript(),
        finalWordScoreList,
        speechAceSpeechScore.ieltsScore() != null
            ? mapPronunciationScore(speechAceSpeechScore.ieltsScore())
            : null,
        speechAceSpeechScore.speechAceScore() != null
            ? mapPronunciationScore(speechAceSpeechScore.speechAceScore())
            : null,
        speechAceSpeechScore.pteScore() != null
            ? mapPronunciationScore(speechAceSpeechScore.pteScore())
            : null,
        speechAceSpeechScore.toeicScore() != null
            ? mapPronunciationScore(speechAceSpeechScore.toeicScore())
            : null,
        isIssueAvailable(speechAceSpeechScore.scoreIssueList()),
        mapGrammar(speechAceSpeechScore.grammar()),
        mapGrammar(speechAceSpeechScore.vocab()),
        mapGrammar(speechAceSpeechScore.coherence()),
        mapFluency(speechAceSpeechScore.fluency()),
        mapCefrScore(speechAceSpeechScore.cefrScore()),
        calculateBadPausesCount(finalWordScoreList),
        buildDescriptiveFeedback(speechAceSpeechScore.speechAceScore()),
        calculateAccuracy(speechAceSpeechScore.wordScoreList()));
  }

  private SpeechEvaluation.ScoreIssue isIssueAvailable(
      List<SpeechAceResponse.ScoreIssueList> scoreIssueLists) {
    if (scoreIssueLists == null || scoreIssueLists.isEmpty()) {
      return SpeechEvaluation.ScoreIssue.builder().available(false).build();
    }
    String detailedMessages =
        scoreIssueLists.stream()
            .map(SpeechAceResponse.ScoreIssueList::detailMessage)
            .filter(msg -> !StringUtils.isNullOrEmpty(msg))
            .collect(Collectors.joining(" "));
    return SpeechEvaluation.ScoreIssue.builder().available(true).message(detailedMessages).build();
  }

  private List<SpeechEvaluation.WordScore> addBadPauses(
      List<SpeechEvaluation.WordScore> wordScoreList) {
    if (wordScoreList == null || wordScoreList.isEmpty()) {
      return wordScoreList;
    }
    List<SpeechEvaluation.WordScore> result = new ArrayList<>();
    Long previousEnd = null;

    for (SpeechEvaluation.WordScore wordScore : wordScoreList) {
      List<Long> extent = wordScore.wordExtent();
      boolean badPause = false;
      if (extent != null && extent.size() == 2) {
        long start = extent.get(0);
        long end = extent.get(1);
        if (previousEnd != null) {
          long pause = start - previousEnd;
          if (pause > 30) {
            badPause = true;
          }
        }
        previousEnd = end;
      }
      // Assuming WordScore has a constructor or builder that accepts a badPause flag
      SpeechEvaluation.WordScore newWordScore =
          new SpeechEvaluation.WordScore(
              wordScore.word(),
              wordScore.qualityScore(),
              wordScore.wordExtent(),
              badPause, // set the badPause flag
              wordScore.phoneScoreList(),
              wordScore.endingPunctuation(),
              wordScore.syllableScoreList());
      result.add(newWordScore);
    }
    return result;
  }

  private String calculateAccuracy(List<SpeechAceResponse.WordScore> wordScores) {
    if (wordScores == null || wordScores.isEmpty()) {
      return "0";
    }
    long totalWords = wordScores.size();
    long goodWords =
        wordScores.stream()
            .filter(ws -> ws.qualityScore() != null && ws.qualityScore() > 79)
            .count();
    double accuracy = (double) goodWords / totalWords * 100;
    return String.format("%.0f", accuracy);
  }

  private Integer calculateBadPausesCount(List<SpeechEvaluation.WordScore> finalWordScoreList) {
    int count = 0;
    if (finalWordScoreList == null || finalWordScoreList.isEmpty()) {
      return count;
    }
    for (SpeechEvaluation.WordScore wordScore : finalWordScoreList) {
      if (Boolean.TRUE.equals(wordScore.badPause())) {
        count++;
      }
    }
    return count;
  }

  private SpeechEvaluation.CefrScore mapCefrScore(SpeechAceResponse.CefrScore cefrScore) {
    if (cefrScore == null) {
      return null;
    }

    return new SpeechEvaluation.CefrScore(
        cefrScore.pronunciation(),
        cefrScore.fluency(),
        cefrScore.grammar(),
        cefrScore.coherence(),
        cefrScore.vocab(),
        cefrScore.overall());
  }

  private SpeechEvaluation.Fluency mapFluency(SpeechAceResponse.Fluency fluency) {
    if (fluency == null) {
      return null;
    }

    List<SpeechEvaluation.SegmentMetrics> segmentMetricsList = null;
    if (fluency.segmentMetricsList() != null) {
      segmentMetricsList =
          fluency.segmentMetricsList().stream().map(this::mapSegmentMetrics).toList();
    }

    SpeechEvaluation.SegmentMetrics overallMetrics = null;
    if (fluency.overAllMetrics() != null) {
      overallMetrics = mapSegmentMetrics(fluency.overAllMetrics());
    }

    return new SpeechEvaluation.Fluency(
        segmentMetricsList,
        overallMetrics,
        fluency.fluencyVersion(),
        fluency.ieltsSubscoreVersion());
  }

  private SpeechEvaluation.SegmentMetrics mapSegmentMetrics(
      SpeechAceResponse.SegmentMetrics metrics) {
    if (metrics == null) {
      return null;
    }

    return new SpeechEvaluation.SegmentMetrics(
        metrics.segment(),
        metrics.duration(),
        metrics.articulationLength(),
        metrics.syllableCount(),
        metrics.correctSyllableCount(),
        metrics.correctWordCount(),
        metrics.wordCount(),
        metrics.speechRate(),
        metrics.articulationRate(),
        metrics.syllableCorrectPerMinute(),
        metrics.wordCorrectPerMinute(),
        metrics.allPauseCount(),
        metrics.allPauseDuration(),
        metrics.meanLengthRun(),
        metrics.maxLengthRun(),
        metrics.allPauseList(),
        metrics.ieltsScore() != null ? mapPronunciationScore(metrics.ieltsScore()) : null,
        metrics.pteScore() != null ? mapPronunciationScore(metrics.pteScore()) : null,
        metrics.speechaceScore() != null ? mapPronunciationScore(metrics.speechaceScore()) : null,
        metrics.toeicScore() != null ? mapPronunciationScore(metrics.toeicScore()) : null,
        metrics.cefrScore() != null ? mapCefrScore(metrics.cefrScore()) : null);
  }

  private SpeechEvaluation.Grammar mapGrammar(SpeechAceResponse.Grammar grammar) {
    if (grammar == null) {
      return null;
    }

    SpeechEvaluation.OverallMetrics overallMetrics = null;
    if (grammar.overallMetrics() != null) {
      overallMetrics = mapOverallMetrics(grammar.overallMetrics());
    }

    return new SpeechEvaluation.Grammar(overallMetrics, grammar.errors());
  }

  private SpeechEvaluation.OverallMetrics mapOverallMetrics(
      SpeechAceResponse.OverallMetrics metrics) {
    if (metrics == null) {
      return null;
    }

    return new SpeechEvaluation.OverallMetrics(
        mapScore(metrics.length()),
        mapScore(metrics.lexicalDiversity()),
        mapScore(metrics.lexicalDensity()),
        mapScore(metrics.basicConnectives()),
        mapScore(metrics.causalConnectives()),
        mapScore(metrics.negativeConnectives()),
        mapScore(metrics.grammaticalAccuracy()),
        mapScore(metrics.pronounDensity()),
        mapScore(metrics.adverbDiversity()),
        mapScore(metrics.verbDiversity()),
        mapScore(metrics.grammaticalRange()),
        mapScore(metrics.wordSophistication()),
        mapScore(metrics.wordSpecificity()),
        mapScore(metrics.academicLanguageUse()),
        mapScore(metrics.collocationCommonality()),
        mapScore(metrics.idiomaticity()));
  }

  private SpeechEvaluation.Score mapScore(SpeechAceResponse.Score score) {
    if (score == null) {
      return null;
    }

    return new SpeechEvaluation.Score(
        score.score(),
        score.level(),
        score.message(),
        score.examples(),
        mapScore(score.nounPhraseComplexity()),
        mapScore(score.nounPhraseVariation()),
        mapScore(score.verbConstructionVariation()),
        mapScore(score.adverbModifierVariation()));
  }

  private com.wexl.retail.speech.dto.SpeechEvaluation.WordScore mapWordScore(
      com.wexl.speech.processor.speechace.dto.SpeechAceResponse.WordScore source) {
    return new com.wexl.retail.speech.dto.SpeechEvaluation.WordScore(
        source.word(),
        source.qualityScore(),
        calculateWordExtent(source.syllableScoreList()),
        null,
        source.phoneScoreList() != null
            ? source.phoneScoreList().stream().map(this::mapPhoneScore).toList()
            : null,
        source.endingPunctuation(),
        source.syllableScoreList() != null
            ? source.syllableScoreList().stream().map(this::mapSyllableScore).toList()
            : null);
  }

  private List<Long> calculateWordExtent(List<SpeechAceResponse.SyllableScore> syllableScores) {
    if (syllableScores == null || syllableScores.isEmpty()) {
      return new ArrayList<>();
    }
    List<Long> wordExtent = new ArrayList<>();
    for (SpeechAceResponse.SyllableScore syllableScore : syllableScores) {
      if (syllableScore.extent() != null) {
        wordExtent.addAll(syllableScore.extent());
      }
    }
    wordExtent.sort(Long::compareTo);
    return List.of(wordExtent.getFirst(), wordExtent.getLast());
  }

  private com.wexl.retail.speech.dto.SpeechEvaluation.PhoneScore mapPhoneScore(
      com.wexl.speech.processor.speechace.dto.SpeechAceResponse.PhoneScore source) {
    return new com.wexl.retail.speech.dto.SpeechEvaluation.PhoneScore(
        source.phone(),
        source.stressLevel(),
        source.qualityScore(),
        source.soundMostLike(),
        source.wordExtent(),
        source.stressScore(),
        source.predictedStressLevel(),
        source.extent());
  }

  private com.wexl.retail.speech.dto.SpeechEvaluation.SyllableScore mapSyllableScore(
      com.wexl.speech.processor.speechace.dto.SpeechAceResponse.SyllableScore source) {
    return new com.wexl.retail.speech.dto.SpeechEvaluation.SyllableScore(
        source.phoneCount(),
        source.stressLevel(),
        source.letters(),
        source.qualityScore(),
        source.stressScore(),
        source.extent(),
        source.predictedStressLevel());
  }

  private SpeechEvaluation.PronunciationScore mapPronunciationScore(
      com.wexl.speech.processor.speechace.dto.SpeechAceResponse.PronunciationScore source) {
    return new SpeechEvaluation.PronunciationScore(
        source.pronunciation(),
        source.fluency(),
        source.grammar(),
        source.coherence(),
        source.vocab(),
        source.overall());
  }

  private SpeechEvaluation.DescriptiveFeedback buildDescriptiveFeedback(
      SpeechAceResponse.PronunciationScore source) {

    SpeechEvaluation.DescriptiveFeedbackJson descriptiveFeedback = getDescriptiveFeedbackJson();
    if (descriptiveFeedback == null) {
      return null;
    }
    try {
      return new SpeechEvaluation.DescriptiveFeedback(
          getFeedbackForOverall(descriptiveFeedback.overall(), source.overall()),
          geFeedbackString(descriptiveFeedback.fluency(), source.fluency()),
          geFeedbackString(descriptiveFeedback.vocab(), source.vocab()),
          geFeedbackString(descriptiveFeedback.grammar(), source.grammar()),
          geFeedbackString(descriptiveFeedback.pronunciation(), source.pronunciation()));
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.QuestionGenerationFailed");
    }
  }

  private SpeechEvaluation.DescriptiveFeedbackJson getDescriptiveFeedbackJson() {
    ObjectMapper objectMapper = new ObjectMapper();
    if (descriptiveFeedbackJson == null) {
      return null;
    }
    try {
      return objectMapper.readValue(
          descriptiveFeedbackJson.getInputStream(), SpeechEvaluation.DescriptiveFeedbackJson.class);
    } catch (IOException e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.QuestionGenerationFailed");
    }
  }

  private String geFeedbackString(SpeechEvaluation.Bands band, Float marks) {
    if (marks == null) {
      return null;
    }
    if (marks <= 0.5) {
      return band.band0();
    }
    if (marks <= 1.5) {
      return band.band1();
    }
    if (marks <= 2.5) {
      return band.band2();
    }
    if (marks <= 3.5) {
      return band.band3();
    }
    if (marks <= 4.5) {
      return band.band4();
    }
    if (marks <= 5.5) {
      return band.band5();
    }
    if (marks <= 6.5) {
      return band.band6();
    }
    if (marks <= 7.5) {
      return band.band7();
    }
    if (marks <= 8.5) {
      return band.band8();
    }
    return band.band9();
  }

  private String getFeedbackForOverall(SpeechEvaluation.OverAllBands overallBands, Float overall) {
    if (overall == null) {
      return null;
    }
    if (overall <= 2.5) {
      return getDescriptiveString(overallBands.band2());
    }
    if (overall <= 4.5) {
      return getDescriptiveString(overallBands.band4());
    }
    if (overall <= 5.5) {
      return getDescriptiveString(overallBands.band5());
    }
    if (overall <= 6.5) {
      return getDescriptiveString(overallBands.band6());
    }
    if (overall <= 7.5) {
      return getDescriptiveString(overallBands.band7());
    }
    if (overall <= 8.5) {
      return getDescriptiveString(overallBands.band8());
    }
    return getDescriptiveString(overallBands.band9());
  }

  @NotNull
  private static String getDescriptiveString(SpeechEvaluation.CefrScore band) {
    return band.pronunciation() + " " + band.fluency() + " " + band.vocab() + " " + band.grammar();
  }
}
