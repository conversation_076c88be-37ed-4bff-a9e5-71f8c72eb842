package com.wexl.speech.controller;

import com.wexl.retail.elp.dto.SpeechEvaluation.SpeechEvaluationRequest;
import com.wexl.retail.speech.SpeechService;
import com.wexl.retail.speech.dto.SpeechEvaluation.SpeechResponse;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class SpeechController {

  private final List<SpeechService> speechServices;

  @PostMapping("/orgs/{orgSlug}/speech-evaluation-demo")
  public SpeechResponse speechEvaluation(
      @PathVariable String orgSlug, @RequestBody SpeechEvaluationRequest speechEvaluationRequest) {
    var speechResponse =
        speechServices.getFirst().pronunciationAssessment(speechEvaluationRequest.reference());
    if (Objects.nonNull(speechResponse)) {
      return speechResponse;
    }
    return speechServices
        .getFirst()
        .pronunciationAssessment(
            speechEvaluationRequest.text(),
            speechEvaluationRequest.audioUrl(),
            speechEvaluationRequest.reference(),
            false)
        .speechResponse();
  }
}
