package com.wexl.speech.processor.azure.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record AzureSpeechResponse() {

  @Builder
  public record Request(String text, @JsonProperty("audio_url") String audioUrl) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record Response(
      @JsonProperty("Id") String id,
      @JsonProperty("RecognitionStatus") String recognitionStatus,
      @JsonProperty("DisplayText") String displayText,
      @JsonProperty("NBest") List<NBest> nbests) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  @Builder
  public record NBest(
      @JsonProperty("Confidence") String confidence,
      @JsonProperty("Lexical") String lexical,
      @JsonProperty("ITN") String itn,
      @JsonProperty("MaskedITN") String maskedItn,
      @JsonProperty("Display") String display,
      @JsonProperty("Words") List<NBestWord> words) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  @Builder
  public record NBestWord(
      @JsonProperty("Word") String word,
      @JsonProperty("PronunciationAssessment") NBestPronunciationAssessment pronunciationAssessment,
      @JsonProperty("Syllables") List<NBestSyllable> syllables,
      @JsonProperty("Phonemes") List<NBestPhoneme> phonemes) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record NBestPronunciationAssessment(
      @JsonProperty("AccuracyScore") Double accuracyScore,
      @JsonProperty("ErrorType") String errorType) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  @Builder
  public record NBestSyllable(
      @JsonProperty("Syllable") String syllable,
      @JsonProperty("PronunciationAssessment")
          NBestPronunciationAssessment pronunciationAssessment) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  @Builder
  public record NBestPhoneme(
      @JsonProperty("Phoneme") String phoneme,
      @JsonProperty("PronunciationAssessment")
          NBestPronunciationAssessment pronunciationAssessment) {}
}
