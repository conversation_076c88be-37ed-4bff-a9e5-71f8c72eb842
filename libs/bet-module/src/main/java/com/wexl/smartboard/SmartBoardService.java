package com.wexl.smartboard;

import static com.wexl.retail.util.Constants.WEXL_INTERNAL;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.elp.dto.ElpDto;
import com.wexl.retail.elp.service.ElpService;
import com.wexl.retail.model.Student;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.student.answer.ExamAnswer;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.ExamAnswerRepository;
import com.wexl.retail.student.exam.ExamRepository;
import com.wexl.retail.task.domain.Task;
import com.wexl.retail.task.domain.TaskInst;
import com.wexl.retail.task.domain.TaskStatus;
import com.wexl.retail.task.repository.TaskInstRepository;
import com.wexl.retail.task.service.TaskInstService;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.dto.QuestionDto;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.test.school.service.TestDefinitionService;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.ValidationUtils;
import com.wexl.retail.v2.service.ScheduleTestStudentService;
import com.wexl.sections.service.BetSectionService;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class SmartBoardService {

  private final ElpService elpService;

  private final SectionService sectionService;

  private final ValidationUtils validationUtils;

  private final TestDefinitionService testDefinitionService;

  private final TestDefinitionRepository testDefinitionRepository;

  private final TaskInstService taskInstService;

  private final TaskInstRepository taskInstRepository;

  private final ExamRepository examRepository;

  private final ExamAnswerRepository examAnswerRepository;

  private final BetSectionService betSectionService;

  private final ScheduleTestStudentService scheduleTestStudentService;

  public List<ElpDto.Chapter> getSmartBoardElp(String orgSlug, String gradeSlug, String boardSlug) {
    return elpService.getElps(orgSlug, gradeSlug, boardSlug);
  }

  public SmartBoardDto.SmartBoardElpQuestionResponse getElpQuestions(Long taskId) {
    var task = validationUtils.getTaskById(taskId);
    var testDefinition =
        testDefinitionRepository
            .findTop1ByTestNameAndOrganizationAndPublishedAtNotNullAndDeletedAtIsNull(
                task.getElpSlug(), WEXL_INTERNAL)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "Elp not found"));

    var questionResponse =
        testDefinitionService.getTestDefinitionQuestions(testDefinition.getId(), 1);

    return SmartBoardDto.SmartBoardElpQuestionResponse.builder()
        .questionResponse(questionResponse)
        .build();
  }

  public SmartBoardDto.SmartBoardAttemptResponse attemptElpQuestionByStudent(
      String orgSlug, SmartBoardDto.AttemptQuestionRequest request, Long taskId) {

    try {
      var validateRequest = request.betAnswerValidateRequest();

      var student = validationUtils.validateStudentByAuthId(request.studentAuthId(), orgSlug);
      var task = validationUtils.getTaskById(taskId);

      var exam = validateExamByTaskInst(student, task);

      var examAnswer =
          CollectionUtils.isEmpty(exam.getExamAnswers())
              ? new ExamAnswer()
              : exam.getExamAnswers().stream()
                  .filter(x -> x.getQuestionUuid().equals(validateRequest.questionUuid()))
                  .findFirst()
                  .orElse(new ExamAnswer());

      var latestExam = examRepository.save(exam);
      examAnswer.setExam(latestExam);
      var answerResponse =
          betSectionService.saveExamAnswerAndBuildResponse(
              validateRequest, examAnswer, orgSlug, latestExam);
      var examAnswers = examAnswerRepository.findByExam(latestExam);
      latestExam.setMarksScored(elpService.calculateMarkScored(examAnswers));
      latestExam.setTotalMarks(
          (float) examAnswers.stream().mapToDouble(ExamAnswer::getMarksPerQuestion).sum());
      var updatedTaskInst = validationUtils.getTaskInstById(latestExam.getTaskInstId());
      updatedTaskInst.setCompletionStatus(TaskStatus.PARTIALLY_COMPLETED);
      var savedExam = examRepository.save(latestExam);
      updatedTaskInst.setExam(savedExam);
      taskInstRepository.save(updatedTaskInst);
      return SmartBoardDto.SmartBoardAttemptResponse.builder()
          .examId(savedExam.getId())
          .validateAnswerResponse(answerResponse)
          .build();
    } catch (Exception e) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.SmartBoardElpAttempt",
          new String[] {e.getMessage()},
          e);
    }
  }

  private Exam validateExamByTaskInst(Student student, Task task) {
    var optionalTaskInst = taskInstRepository.findByStudentAndTaskAndDeletedAtIsNull(student, task);
    TaskInst taskInst =
        optionalTaskInst.orElseGet(
            () ->
                taskInstRepository.save(
                    TaskInst.builder()
                        .task(task)
                        .completionStatus(TaskStatus.PENDING)
                        .student(student)
                        .build()));
    var optionalExam = examRepository.findByStudentAndTaskId(student, task.getId());
    var exam = optionalExam.orElse(new Exam());
    if (optionalExam.isEmpty()) {
      exam.setStudent(student);
      exam.setTaskInstId(taskInst.getId());
      exam.setTaskId(task.getId());
      exam.setExamType(Constants.ELP_EXAM);
      exam.setChapterSlug(task.getChapterSlug());
      exam.setSubjectSlug(task.getSubjectSlug());
      exam.setSubtopicSlug(task.getSubtopicSlug());
      exam.setSubjectName(task.getSubjectName());
      exam.setTestDefinition(task.getTestDefinition());
      exam.setRef(UUID.randomUUID().toString());
    }
    return exam;
  }

  public QuestionDto.StudentResultsResponse getSmartBoardResult(Long examId) {
    var exam = validationUtils.findByExamId(examId);
    var taskInst = validationUtils.getTaskInstById(exam.getTaskInstId());
    var testDefinition =
        testDefinitionRepository
            .findTop1ByTestNameAndOrganizationAndPublishedAtNotNullAndDeletedAtIsNull(
                taskInst.getTask().getElpSlug(), WEXL_INTERNAL)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "Elp not found"));
    var questionResponse =
        testDefinitionService.getTestDefinitionQuestions(testDefinition.getId(), 1);
    Double speakingMarks = 0.0d;
    boolean isSpeaking = false;

    var task = validationUtils.getTaskById(taskInst.getTask().getId());

    if (StringUtils.equalsIgnoreCase("speaking", task.getSubtopicName())) {
      speakingMarks = scheduleTestStudentService.buildSpeakingMarks(taskInst);
      isSpeaking = true;
    }

    double totalMarksSecured =
        Double.parseDouble(
            String.format(
                "%.1f",
                isSpeaking
                    ? speakingMarks
                    : (exam.getMarksScored() == null || exam.getMarksScored() < 0)
                        ? 0.0
                        : exam.getMarksScored()));

    return QuestionDto.StudentResultsResponse.builder()
        .examId(exam.getId())
        .testDefinitionId(testDefinition.getId())
        .testName(testDefinition.getTestName())
        .gradeName(testDefinition.getGradeSlug())
        .noOfQuestions(testDefinition.getNoOfQuestions().longValue())
        .testDefinitionSection(
            buildTestDefinitionSectionResult(questionResponse, exam, testDefinition))
        .totalMarks(exam.getTotalMarks())
        .totalMarksSecured((float) totalMarksSecured)
        .totalMarks(exam.getTotalMarks())
        .percentageSecured(
            elpService.calculatePercentage((float) totalMarksSecured, exam.getTotalMarks()))
        .assetSlug(
            Objects.nonNull(testDefinition.getMetadata().getAssetSlug())
                ? testDefinition.getMetadata().getAssetSlug()
                : null)
        .build();
  }

  private List<QuestionDto.TestDefinitionSection> buildTestDefinitionSectionResult(
      QuestionDto.QuestionResponse questionResponse, Exam exam, TestDefinition testDefinition) {
    var questions =
        questionResponse.testDefinitionSectionResponses().stream()
            .map(QuestionDto.TestDefinitionSectionResponse::questions)
            .flatMap(Collection::stream)
            .toList();

    HashMap<String, ExamAnswer> resultMap = new HashMap<>();
    exam.getExamAnswers().stream()
        .filter(ExamAnswer::isAttempted)
        .forEach(examAnswer -> resultMap.put(examAnswer.getQuestionUuid(), examAnswer));

    var questionList =
        questions.stream().filter(question -> resultMap.containsKey(question.uuid())).toList();

    var results =
        scheduleTestStudentService.buildQuestionResult(
            questionList, exam, testDefinition.getTestDefinitionSections(), 1);

    return List.of(QuestionDto.TestDefinitionSection.builder().questionResults(results).build());
  }
}
