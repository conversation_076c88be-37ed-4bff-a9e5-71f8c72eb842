package com.wexl.smartboard;

import com.wexl.retail.elp.dto.ElpDto;
import com.wexl.retail.test.school.dto.QuestionDto;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}")
public class SmartBoardController {

  private final SmartBoardService smartBoardService;

  @GetMapping("/elp-smart-board")
  public List<ElpDto.Chapter> getSmartBoardElp(
      @PathVariable String orgSlug,
      @RequestParam("grade_slug") String gradeSlug,
      @RequestParam(required = false) String boardSlug) {
    return smartBoardService.getSmartBoardElp(orgSlug, gradeSlug, boardSlug);
  }

  @GetMapping("/tasks/{taskId}/elp-smart-board/questions")
  public SmartBoardDto.SmartBoardElpQuestionResponse getElpQuestions(
      @PathVariable("taskId") Long taskId) {
    return smartBoardService.getElpQuestions(taskId);
  }

  @PostMapping("/tasks/{taskId}/elp-smart-board:attempt")
  public SmartBoardDto.SmartBoardAttemptResponse AttemptElpQuestionToStudent(
      @PathVariable("orgSlug") String orgSlug,
      @RequestBody SmartBoardDto.AttemptQuestionRequest attemptQuestionRequest,
      @PathVariable("taskId") Long taskId) {
    return smartBoardService.attemptElpQuestionByStudent(orgSlug, attemptQuestionRequest, taskId);
  }

  @GetMapping("/exams/{examId}/elp-smart-board:results")
  public QuestionDto.StudentResultsResponse getSmartBoardResult(
      @PathVariable("examId") Long examId) {
    return smartBoardService.getSmartBoardResult(examId);
  }
}
