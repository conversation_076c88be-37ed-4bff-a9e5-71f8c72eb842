<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>wexl-lms-apps</artifactId>
        <groupId>com.wexl.bet</groupId>
        <version>2.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>libs</artifactId>
    <packaging>pom</packaging>
    <name>LibsDir</name>
    <properties>
        <maven.deploy.skip>false</maven.deploy.skip>
    </properties>

    <modules>
        <module>application</module>
        <module>commons</module>
        <module>ecommerce-api</module>
        <module>aws-cloud-driver</module>
        <module>ai-module</module>
        <module>bet-module</module>
        <module>gamification</module>
        <module>wiki-module</module>
    </modules>

</project>
