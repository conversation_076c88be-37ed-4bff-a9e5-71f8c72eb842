package com.wexl.retail.commons.exceptions.logger;

import com.wexl.retail.commons.exceptions.logger.dto.LogManagement;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@RequiredArgsConstructor
@Service
public class ParseableLogService {

  private final RestTemplate restTemplate;

  @Value("${app.parseable.baseUrl}")
  private String baseUrl;

  @Value("${app.parseable.streamName}")
  private String streamName;

  @Value("${app.parseable.username}")
  private String username;

  @Value("${app.parseable.password}")
  private String password;

  @Value("${app.parseable.enabled}")
  private boolean enabled;

  public void saveLog(LogManagement.Request request) {
    if (!enabled) {
      return;
    }
    String urlUpdated = "%s/api/v1/logstream/%s".formatted(baseUrl, streamName);

    final HttpHeaders httpHeaders = new HttpHeaders();
    httpHeaders.setBasicAuth(username, password);
    httpHeaders.setContentType(MediaType.APPLICATION_JSON);

    try {
      ResponseEntity<Void> responseEntity =
          restTemplate.postForEntity(
              urlUpdated, new HttpEntity<>(List.of(request), httpHeaders), Void.class);
      log.info("Parseable response: " + responseEntity.getStatusCode());
    } catch (Exception ex) {
      log.error("Some problem", ex);
    }
  }
}
