package com.wexl.retail.commons.core;

import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import javax.crypto.spec.SecretKeySpec;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

@Component
public class TokenGenerator {

  @Value("${jwt.tokenSecret}")
  private String tokenSecret;

  public static final String FIRST_NAME = "firstName";
  public static final String LAST_NAME = "lastName";
  public static final String MOBILE_NUMBER = "mobileNumber";
  public static final String IS_MOBILE = "mob";
  public static final String ORGANIZATION = "organization";
  public static final String ID = "id";
  public static final String SCOPE = "scope";
  public static final String EMAIL = "email";
  public static final String LOGIN_METHOD = "loginMethod";
  public static final String ROLES = "roles";

  public String generateAdminToken() {
    try {
      return getUserJwtBuilder().compact();
    } catch (Exception e) {
      throw new IllegalArgumentException("Failed to generate token", e);
    }
  }

  private JwtBuilder getUserJwtBuilder() {
    var tokenSignInKey =
        new SecretKeySpec(
            Base64.getDecoder().decode(tokenSecret), SignatureAlgorithm.HS256.getJcaName());
    var currentDate = Instant.now();
    return Jwts.builder()
        .claim(ID, 3)
        .claim(FIRST_NAME, "retail_service_user")
        .claim(LAST_NAME, "Bot")
        .claim(EMAIL, "<EMAIL>")
        .claim(ORGANIZATION, "wexl-internal")
        .claim(SCOPE, "3D_VIDEOS ACTIVITY_FEED")
        .claim(IS_MOBILE, false)
        .claim(LOGIN_METHOD, "bot")
        .claim(MOBILE_NUMBER, "9100000000")
        .claim(ROLES, List.of("ROLE_ITEACHER", "ROLE_ADMIN", "ROLE_ORG_ADMIN"))
        .setSubject("38f73cf8-a815-4ec2-a394-30af9af988aa")
        .setId("3")
        .setIssuedAt(Date.from(currentDate))
        .setExpiration(Date.from(currentDate.plus(2, ChronoUnit.DAYS)))
        .signWith(tokenSignInKey, SignatureAlgorithm.HS256);
  }

  public <T> HttpEntity<T> generateHttpEntity(T object) {
    String authorizationToken = "Bearer " + generateAdminToken();
    var headers = new HttpHeaders();
    headers.add(HttpHeaders.AUTHORIZATION, authorizationToken);
    return new HttpEntity<>(object, headers);
  }
}
