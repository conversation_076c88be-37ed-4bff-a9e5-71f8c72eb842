package com.wexl.retail.commons.locale;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver;

@Component
public class CustomLocaleResolver extends AcceptHeaderLocaleResolver {

  List<Locale> localeList = Arrays.asList(Locale.of("en"), Locale.of("es"));

  @Override
  public Locale resolveLocale(HttpServletRequest request) {
    String language = request.getHeader("Accept-Language");
    if (language == null || language.isEmpty()) {
      return Locale.getDefault();
    }

    List<Locale.LanguageRange> languageRanges = Locale.LanguageRange.parse(language);
    return Locale.lookup(languageRanges, localeList);
  }
}
