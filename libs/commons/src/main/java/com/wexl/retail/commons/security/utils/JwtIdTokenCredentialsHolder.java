package com.wexl.retail.commons.security.utils;

import static com.wexl.retail.commons.util.JwtConstants.*;

import com.wexl.retail.commons.util.CastUtil;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.security.core.context.SecurityContextHolder;

@Data
public class JwtIdTokenCredentialsHolder {
  private String accessToken;
  private int userId;

  private long studentId;
  private String authUserId;
  private int classId;
  private String organization;
  private boolean premiumSubscription;
  private String userEmail;
  private List<String> userRoles;
  private String firstName;
  private String lastName;
  private List<String> subjects;
  private List<String> sections;

  public JwtIdTokenCredentialsHolder() {
    var authentication = SecurityContextHolder.getContext().getAuthentication();
    if (authentication != null) {
      var claims = ((JwtAuthentication) authentication).getJwtClaimsSet();
      this.authUserId = claims.getSubject();
      this.userId = (int) claims.get(ID);

      if (claims.get("studentId") != null) {
        this.studentId = (int) claims.get("studentId");
      }
      this.firstName = (String) claims.get(FIRST_NAME);
      this.lastName = (String) claims.get(LAST_NAME);
      userRoles = CastUtil.castList(claims.get(ROLES), String.class);
      this.subjects = CastUtil.castList(claims.get(SUBJECTS), String.class);
      this.sections = CastUtil.castList(claims.get(SECTIONS), String.class);

      if (claims.containsKey(CLASS_ID)) {
        this.classId = (int) claims.get(CLASS_ID);
      }
      if (claims.containsKey(EMAIL)) {
        this.userEmail = (String) claims.get(EMAIL);
      }
      if (claims.containsKey(IS_PREMIUM)) {
        this.premiumSubscription = (boolean) claims.get(IS_PREMIUM);
      } else {
        this.premiumSubscription = false;
      }
      this.organization = (String) claims.get(ORGANIZATION);
    }
  }

  public boolean hasRole(String[] acceptedRoles) {
    if (ObjectUtils.isEmpty(userRoles)) {
      return false;
    }
    if (ObjectUtils.isEmpty(acceptedRoles)) {
      return true;
    }

    Optional<String> possibleRole =
        userRoles.stream().filter(role -> exists(role, acceptedRoles)).findFirst();
    return possibleRole.isPresent();
  }

  private boolean exists(String roleToCheck, String[] acceptedRoles) {
    return Arrays.asList(acceptedRoles).contains(roleToCheck);
  }
}
