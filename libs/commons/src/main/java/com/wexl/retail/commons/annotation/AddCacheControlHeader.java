package com.wexl.retail.commons.annotation;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class AddCacheControlHeader {
  private static final String CACHE_CONTROL = "Cache-Control";

  @Around("@annotation(CacheControl)")
  public Object addCacheHeader(ProceedingJoinPoint joinPoint) throws Throwable {
    Object proceed = joinPoint.proceed();
    if (!(proceed instanceof ResponseEntity)) {
      return proceed;
    }
    MethodSignature signature = (MethodSignature) joinPoint.getSignature();
    var annotation = AnnotationUtils.findAnnotation(signature.getMethod(), CacheControl.class);
    if (annotation == null) {
      return proceed;
    }

    var headers = new HttpHeaders();
    headers.add(CACHE_CONTROL, "public, max-age=" + annotation.value());
    return ResponseEntity.ok().headers(headers).body(((ResponseEntity<?>) proceed).getBody());
  }
}
