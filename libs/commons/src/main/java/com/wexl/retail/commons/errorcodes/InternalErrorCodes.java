package com.wexl.retail.commons.errorcodes;

import lombok.Getter;

public enum InternalErrorCodes {
  SERVER_ERROR("500", "could not process the request"),
  UN_AUTHORIZED("401", "Access Denied"),
  NO_RECORD_FOUND("404", "Invalid Input"),
  INVALID_REQUEST("400", "invalid input passed");

  @Getter private final String code;
  private final String description;

  InternalErrorCodes(String code, String description) {
    this.code = code;
    this.description = description;
  }
}
