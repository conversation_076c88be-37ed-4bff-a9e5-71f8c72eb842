package com.wexl.retail.commons.exceptions;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import lombok.Getter;

@Getter
public class ApiException extends RuntimeException {
  private static final long serialVersionUID = 1L;
  private boolean knownIssue;
  private final InternalErrorCodes errorCode;
  private final String errorMessage;
  private String[] args;

  public ApiException(InternalErrorCodes errorCode, String errorMessage) {

    super(errorMessage);
    this.errorCode = errorCode;
    this.errorMessage = errorMessage;
  }

  public ApiException(InternalErrorCodes errorCode, String errorMessage, String[] args) {
    super(errorMessage);
    this.errorCode = errorCode;
    this.errorMessage = errorMessage;
    this.args = args;
  }

  public ApiException(
      InternalErrorCodes errorCode,
      String errorMessage,
      String[] args,
      Exception causedByException) {
    super(errorMessage, causedByException);
    this.errorCode = errorCode;
    this.errorMessage = errorMessage;
    this.args = args;
  }

  public ApiException(
      InternalErrorCodes errorCode, String errorMessage, Exception causedByException) {
    super(errorMessage, causedByException);
    this.errorCode = errorCode;
    this.errorMessage = errorMessage;
  }

  public ApiException(InternalErrorCodes errorCode, String errorMessage, boolean knownIssue) {
    super(errorMessage);
    this.errorCode = errorCode;
    this.errorMessage = errorMessage;
    this.knownIssue = knownIssue;
  }

  public ApiException(
      InternalErrorCodes errorCode, String errorMessage, String[] args, boolean knownIssue) {
    super(errorMessage);
    this.errorCode = errorCode;
    this.errorMessage = errorMessage;
    this.args = args;
    this.knownIssue = knownIssue;
  }
}
