package com.wexl.retail.commons.security.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import org.springframework.security.access.prepost.PreAuthorize;

@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@PreAuthorize(
    """
    hasRole('ROLE_PARENT') or hasRole('ROLE_STUDENT') or hasRole('ROLE_ISTUDENT') \
    or hasRole('ROLE_ITEACHER') or hasRole('ROLE_TEACHER')\
    """)
public @interface IsNonAdmin {}
