package com.wexl.retail.commons.exceptions.handler;

import java.util.Map;
import java.util.Objects;
import lombok.Data;

@Data
public class GenericFailureResponse {

  private final String message;

  private final Map<String, String> reasons;

  @Override
  public int hashCode() {
    return Objects.hash(message, reasons);
  }

  @Override
  public String toString() {
    var sb = new StringBuilder();
    sb.append("class GenericFailureResponse {\n");

    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    reasons: ").append(toIndentedString(reasons)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (!(o instanceof GenericFailureResponse)) {
      return false;
    }

    GenericFailureResponse that = (GenericFailureResponse) o;

    if (getMessage() != null
        ? !getMessage().equals(that.getMessage())
        : that.getMessage() != null) {
      return false;
    }
    return getReasons() != null
        ? getReasons().equals(that.getReasons())
        : that.getReasons() == null;
  }
}
