package com.wexl.retail.commons.exceptions.handler;

import static com.wexl.retail.commons.util.JwtConstants.EMAIL;
import static com.wexl.retail.commons.util.JwtConstants.ORGANIZATION;
import static com.wexl.retail.commons.util.JwtConstants.SUB;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.exceptions.logger.ParseableLogService;
import com.wexl.retail.commons.exceptions.logger.dto.LogManagement.Request;
import com.wexl.retail.commons.locale.LocaleService;
import com.wexl.retail.commons.security.utils.JwtAuthentication;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.context.NoSuchMessageException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageConversionException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

@Slf4j
@ControllerAdvice
@RequiredArgsConstructor
public class ExceptionMessageHandler {

  private final LocaleService localeService;
  private final ParseableLogService logService;
  private static final String DESCRIPTION = "][Description: ";
  private static final String EVENT_CODE = "Event Code: ";
  private static final String EXCEPTION = "Exception: ";
  private static final String DETAILS = "Details: ";
  private static final String STACK_TRACE_ELEMENT = "StackTraceElement: ";

  private void logException(Exception ex) {
    InternalErrorCodes eventCode;
    var descriptionString = "";
    var detailsString = "";
    var exceptionString = "";

    if (ApiException.class.isAssignableFrom(ex.getClass())) {
      var serviceException = (ApiException) ex;

      eventCode = serviceException.getErrorCode();
      descriptionString = serviceException.getErrorMessage();
      exceptionString = serviceException.getClass().toString();

    } else {
      eventCode = InternalErrorCodes.SERVER_ERROR;
      descriptionString = ex.getMessage();
      exceptionString = ex.getClass().toString();
    }
    Throwable causedByException = ex.getCause();
    if ((causedByException) != null) {
      descriptionString = descriptionString + "=>" + causedByException.getMessage();
      exceptionString = exceptionString + "=>" + causedByException.getClass();
    }

    var email = getAuthenticatedUserDetails(EMAIL);
    var organization = getAuthenticatedUserDetails(ORGANIZATION);
    var subject = getAuthenticatedUserDetails(SUB);
    var identity = "%s::%s::%s".formatted(organization, email, subject);

    logService.saveLog(
        Request.builder()
            .message(descriptionString)
            .datetime(getCurrentLocalDateTimeStamp())
            .id(UUID.randomUUID().toString())
            .stackTrace(ExceptionUtils.getStackTrace(ex))
            .status(getHttpStatus(eventCode))
            .user(subject)
            .email(email)
            .orgSlug(organization)
            .build());

    log.error(
        "["
            + identity
            + DESCRIPTION
            + descriptionString
            + "],"
            + EVENT_CODE
            + eventCode
            + "],"
            + EXCEPTION
            + exceptionString
            + "],"
            + DETAILS
            + detailsString
            + "],"
            + STACK_TRACE_ELEMENT
            + ex.getStackTrace()[0].toString()
            + "]",
        ex);
  }

  private Integer getHttpStatus(InternalErrorCodes eventCode) {
    if (eventCode == null) {
      return 0;
    }
    return Integer.parseInt(eventCode.getCode());
  }

  public String getCurrentLocalDateTimeStamp() {
    return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
  }

  @ExceptionHandler({
    MethodArgumentNotValidException.class,
    MissingServletRequestParameterException.class
  })
  public ResponseEntity<GenericFailureResponse> handleArgumentValidationException(
      MethodArgumentNotValidException exception) {
    Map<String, String> errors = new HashMap<>();
    errors.put("error", "Bad Request");
    var response = new GenericFailureResponse("INVALID_INPUT", errors);
    logException(exception);
    return new ResponseEntity<>(response, new HttpHeaders(), HttpStatus.BAD_REQUEST);
  }

  @ExceptionHandler(Exception.class)
  protected ResponseEntity<GenericFailureResponse> handleGeneralException(
      Exception ex, Locale locale) {
    String convertedMessageCode = getLocaleMessage("error.serverError", null, locale);
    Map<String, String> errors = new HashMap<>();
    errors.put(InternalErrorCodes.SERVER_ERROR.toString(), convertedMessageCode);
    logException(ex);
    var response =
        new GenericFailureResponse(
            "Oops! Something went wrong on our end. Please try again later.", errors);
    return new ResponseEntity<>(response, new HttpHeaders(), HttpStatus.INTERNAL_SERVER_ERROR);
  }

  @ExceptionHandler(HttpMessageConversionException.class)
  protected ResponseEntity<GenericFailureResponse> handleRequestWithInvalidJson(
      HttpMessageConversionException ex) {
    Map<String, String> errors = new HashMap<>();
    errors.put(
        InternalErrorCodes.INVALID_REQUEST.toString(), "Request body missing or incorrect format");
    var response = new GenericFailureResponse("INVALID_INPUT", errors);
    return new ResponseEntity<>(response, new HttpHeaders(), HttpStatus.BAD_REQUEST);
  }

  @ExceptionHandler(ApiException.class)
  protected ResponseEntity<GenericFailureResponse> handleApiException(
      ApiException ex, Locale locale) {
    var convertedMessage = getLocaleMessage(ex.getMessage(), ex.getArgs(), locale);
    var response = new GenericFailureResponse(convertedMessage, new HashMap<>());

    if (InternalErrorCodes.INVALID_REQUEST.equals(ex.getErrorCode())) {
      logException(ex);
      return new ResponseEntity<>(response, new HttpHeaders(), HttpStatus.BAD_REQUEST);
    } else if (InternalErrorCodes.UN_AUTHORIZED.equals(ex.getErrorCode())) {
      logException(ex);
      response.getReasons().put(InternalErrorCodes.UN_AUTHORIZED.toString(), "Unauthorized Access");
      return new ResponseEntity<>(response, new HttpHeaders(), HttpStatus.UNAUTHORIZED);
    } else if (InternalErrorCodes.NO_RECORD_FOUND.equals(ex.getErrorCode())) {
      logException(ex);
      return new ResponseEntity<>(response, new HttpHeaders(), HttpStatus.NOT_FOUND);
    } else {
      logException(ex);
      response
          .getReasons()
          .put(InternalErrorCodes.SERVER_ERROR.toString(), "Could not process the request");
      return new ResponseEntity<>(response, new HttpHeaders(), HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  private String getLocaleMessage(String localeCode, Object[] args, Locale locale) {
    try {
      return localeService.convertMessage(localeCode, args, locale);
    } catch (NoSuchMessageException noSuchMessageException) {
      return localeCode;
    }
  }

  private String getAuthenticatedUserDetails(String name) {
    var authentication = SecurityContextHolder.getContext().getAuthentication();
    if (authentication != null
        && authentication.isAuthenticated()
        && authentication instanceof JwtAuthentication jwtAuthentication) {
      var claims = jwtAuthentication.getJwtClaimsSet();
      return (String) claims.get(name);
    }
    return "";
  }
}
