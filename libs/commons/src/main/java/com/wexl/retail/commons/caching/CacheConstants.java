package com.wexl.retail.commons.caching;

import java.time.Duration;
import org.springframework.stereotype.Component;

@Component
public class CacheConstants {
  public static final Duration SMALL = Duration.ofDays(1);
  public static final Duration MEDIUM = Duration.ofDays(2);
  public static final Duration LONG = Duration.ofDays(7);
  public static final String GRADES = "grades";
  public final String[] cacheNames = new String[] {GRADES};

  public String[] getCacheNames() {
    return cacheNames;
  }
}
