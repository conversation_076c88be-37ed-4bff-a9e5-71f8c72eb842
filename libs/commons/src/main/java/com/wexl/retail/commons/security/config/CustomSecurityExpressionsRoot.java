package com.wexl.retail.commons.security.config;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import java.io.Serializable;
import java.util.Collection;
import java.util.Set;
import lombok.Data;
import org.springframework.security.access.PermissionEvaluator;
import org.springframework.security.access.expression.method.MethodSecurityExpressionOperations;
import org.springframework.security.access.hierarchicalroles.RoleHierarchy;
import org.springframework.security.authentication.AuthenticationTrustResolver;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;

@Data
public class CustomSecurityExpressionsRoot implements MethodSecurityExpressionOperations {

  protected final Authentication authentication;
  private AuthenticationTrustResolver trustResolver;
  private RoleHierarchy roleHierarchy;
  private Set<String> roles;
  private String defaultRolePrefix = "ROLE_";
  private PermissionEvaluator permissionEvaluator;
  private Object filterObject;
  private Object returnObject;

  public CustomSecurityExpressionsRoot(Authentication authentication) {
    if (authentication == null) {
      throw new IllegalArgumentException("Authentication object cannot be null");
    }
    this.authentication = authentication;
  }

  @Override
  public final boolean hasAuthority(String authority) {
    throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, "method hasAuthority() not allowed");
  }

  @Override
  public final boolean hasAnyAuthority(String... authorities) {
    return hasAnyAuthorityName(null, authorities);
  }

  @Override
  public final boolean hasRole(String role) {
    return hasAnyRole(role);
  }

  @Override
  public final boolean hasAnyRole(String... roles) {
    return hasAnyAuthorityName(defaultRolePrefix, roles);
  }

  @Override
  public final Authentication getAuthentication() {
    return authentication;
  }

  @Override
  public final boolean permitAll() {
    return true;
  }

  @Override
  public final boolean denyAll() {
    return false;
  }

  @Override
  public final boolean isAnonymous() {
    return trustResolver.isAnonymous(authentication);
  }

  @Override
  public final boolean isAuthenticated() {
    return !isAnonymous();
  }

  @Override
  public final boolean isRememberMe() {
    return trustResolver.isRememberMe(authentication);
  }

  @Override
  public final boolean isFullyAuthenticated() {
    return !trustResolver.isAnonymous(authentication)
        && !trustResolver.isRememberMe(authentication);
  }

  @Override
  public boolean hasPermission(Object target, Object permission) {
    return permissionEvaluator.hasPermission(authentication, target, permission);
  }

  @Override
  public boolean hasPermission(Object targetId, String targetType, Object permission) {
    return permissionEvaluator.hasPermission(
        authentication, (Serializable) targetId, targetType, permission);
  }

  @Override
  public Object getFilterObject() {
    return this.filterObject;
  }

  @Override
  public void setFilterObject(Object obj) {
    this.filterObject = obj;
  }

  @Override
  public Object getReturnObject() {
    return this.returnObject;
  }

  @Override
  public void setReturnObject(Object obj) {
    this.returnObject = obj;
  }

  @Override
  public Object getThis() {
    return this;
  }

  private static String getRoleWithDefaultPrefix(String defaultRolePrefix, String role) {
    if ((defaultRolePrefix == null)
        || (defaultRolePrefix.length() == 0)
        || (role == null)
        || (role.startsWith(defaultRolePrefix))) {
      return role;
    }
    return defaultRolePrefix + role;
  }

  private boolean hasAnyAuthorityName(String prefix, String... roles) {
    final Set<String> roleSet = getAuthoritySet();

    for (final String role : roles) {
      final String defaultedRole = getRoleWithDefaultPrefix(prefix, role);
      if (roleSet.contains(defaultedRole)) {
        return true;
      }
    }

    return false;
  }

  private Set<String> getAuthoritySet() {
    if (roles == null) {
      Collection<? extends GrantedAuthority> userAuthorities = authentication.getAuthorities();

      if (roleHierarchy != null) {
        userAuthorities = roleHierarchy.getReachableGrantedAuthorities(userAuthorities);
      }

      roles = AuthorityUtils.authorityListToSet(userAuthorities);
    }

    return roles;
  }
}
