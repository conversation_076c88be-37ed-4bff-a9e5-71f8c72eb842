package com.wexl.retail.announcement.dto;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum AnnouncementType {
  PRACTICE("PRACTICE"),
  TEST("TEST"),
  VIDEO("VIDEO"),
  POST("POST"),
  SYNOPSIS("SYNOPSIS");

  private final String value;

  public static AnnouncementType fromValue(String value) {
    if (value == null || "".equals(value)) {
      throw new IllegalArgumentException("Value cannot be null or empty!");
    }

    for (AnnouncementType enumEntry : AnnouncementType.values()) {
      if (enumEntry.toString().equalsIgnoreCase(value)) {
        return enumEntry;
      }
    }

    throw new IllegalArgumentException("Do not understand the Type " + value);
  }

  @Override
  public String toString() {
    return this.value;
  }
}
