package com.wexl.retail.ecommerce;

import com.wexl.retail.ecommerce.dto.CommerceStudentDto.CourseStudentRequest;
import com.wexl.retail.ecommerce.dto.CommerceStudentDto.CourseStudentResponse;
import com.wexl.retail.ecommerce.dto.CommerceStudentDto.ElpStudentRequest;
import com.wexl.retail.ecommerce.dto.CommerceStudentDto.ElpStudentResponse;

public interface RetailService {

  ElpStudentResponse createElpStudent(ElpStudentRequest elpStudentRequest);

  CourseStudentResponse createCourseStudent(CourseStudentRequest courseStudentRequest);
}
