package com.wexl.retail.idp.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConfirmForgotPasswordRequest {

  @JsonProperty("username")
  private String userName;

  private String password;

  @JsonProperty("confirmation_code")
  private String confirmationCode;
}
