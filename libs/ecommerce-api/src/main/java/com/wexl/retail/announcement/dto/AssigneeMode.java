package com.wexl.retail.announcement.dto;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum AssigneeMode {
  ALL_STUDENTS("ALL_STUDENTS"),
  INDIVIDUAL_STUDENTS("INDIVIDUAL_STUDENTS");

  private final String value;

  public static AssigneeMode fromValue(String value) {
    if (value == null || "".equals(value)) {
      throw new IllegalArgumentException("Value cannot be null or empty!");
    }

    for (AssigneeMode enumEntry : AssigneeMode.values()) {
      if (enumEntry.toString().equalsIgnoreCase(value)) {
        return enumEntry;
      }
    }

    throw new IllegalArgumentException("Do not understand the Type " + value);
  }

  @Override
  public String toString() {
    return this.value;
  }
}
