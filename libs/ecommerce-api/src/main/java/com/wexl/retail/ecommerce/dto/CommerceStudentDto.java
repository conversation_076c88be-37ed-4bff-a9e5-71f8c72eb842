package com.wexl.retail.ecommerce.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;

public record CommerceStudentDto() {

  @Builder
  public record ElpStudentRequest(
      String customerId,
      @JsonProperty("first_name") String firstName,
      @JsonProperty("last_name") String lastName,
      String phone,
      String orgSlug,
      String email,
      String grade,
      String board,
      String env,
      String discountOrg,
      @JsonProperty("android_app_url") String androidAppUrl,
      @JsonProperty("web_app_url") String webAppUrl) {}

  @Builder
  public record ElpStudentResponse(String authUserId, String orgSlug, String orgName) {}

  @Builder
  public record CourseStudentRequest(
      String email,
      @JsonProperty("full_name") String fullName,
      @JsonProperty("phone_number") String phoneNumber,
      String env,
      String orgSlug,
      @JsonProperty("android_app_url") String androidAppUrl,
      @JsonProperty("web_app_url") String webAppUrl,
      @JsonProperty("ext_ref") String extRef,
      Long customerId) {}

  @Builder
  public record CourseStudentResponse(String authUserId) {}
}
