package com.wexl.retail.ai.dto;

import java.util.List;
import lombok.Builder;

public record ExamAnalysis() {
  @Builder
  public record PromptQuestionContent(Long questionNumber, int marks, String text, String answer) {}

  @Builder
  public record PromptAnswerContent(Long questionNumber, String answer) {}

  @Builder
  public record AiQuestionAnalysisResponse(Long question, int marks, String analysis) {}

  public record AiQuestionAnalysisResponseList(List<AiQuestionAnalysisResponse> response) {}

  @Builder
  public record AnswerReAnalysis(String answer, float marks, String annotatedAnswer) {}

  @Builder
  public record TestEnrichmentAiResponse(
      String concept, String summary, TestEnrichData data, String references) {}

  public record TestEnrichData(List<Mcq> mcqs) {}

  @Builder
  public record Mcq(
      String question,
      String option1,
      String option2,
      String option3,
      String option4,
      Long answer,
      String explanation) {}
}
