package com.wexl.retail.announcement.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AnnouncementRequest {
  @NotNull private String creatorUserId;
  @NotNull private String state;
  @NotNull private String assigneeMode;
  private IndividualStudentOptionRequest individualStudentOptions;
  private MaterialRequest material;

  @JsonIgnore private String studentAuthId;
  @JsonIgnore private String comment;
}
