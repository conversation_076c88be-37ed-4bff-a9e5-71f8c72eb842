package com.wexl.retail.announcement.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_EMPTY)
@Builder
public class MaterialInfoRequest {

  @JsonProperty("subTopic_slugs")
  private List<String> subTopicSlugs;

  @JsonProperty("chapter_slugs")
  private List<String> chapterSlugs;

  @JsonProperty("exam_level")
  private ExamLevel examLevel;

  private List<String> attachments;

  private List<String> videos;

  private List<String> links;

  private List<String> synopses;

  private String description;

  @JsonProperty("question_count")
  private Integer questionCount;

  private String refKey;

  @JsonProperty("video_details")
  private List<VideoDetailsRequest> videoDetails;

  public List<String> getUploads() {
    return Objects.isNull(this.attachments) ? Collections.emptyList() : this.attachments;
  }
}
