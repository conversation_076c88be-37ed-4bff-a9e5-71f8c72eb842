package com.wexl.retail.ai;

import com.wexl.retail.ai.dto.ExamAnalysis.AiQuestionAnalysisResponseList;
import com.wexl.retail.ai.dto.ExamAnalysis.PromptAnswerContent;
import com.wexl.retail.ai.dto.ExamAnalysis.PromptQuestionContent;
import java.util.List;

public interface AiQuestionAnalysis {
  AiQuestionAnalysisResponseList analyzeQuestions(
      List<PromptQuestionContent> promptQuestionContents,
      List<PromptAnswerContent> promptAnswerContents);
}
