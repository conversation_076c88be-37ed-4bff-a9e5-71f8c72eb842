package com.wexl.retail.notification.dto;

import java.util.Objects;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;

@Getter
@NoArgsConstructor
public enum NotificationEvent {
  CREATE_PARENT,
  CREATE_SUBSCRIPTION,
  ADD_POINTS,
  <PERSON><PERSON>IM_REWARD,
  CORRECTION_COMPLETED("CORRECTION_COMPLETED"),
  PRACTICE_COMPLETED("PRACTICE"),
  TEST_COMPLETED("TEST"),
  ASSESSMENT_COMPLETED("ASSESSMENT"),
  SCHEDULED_TEST_COMPLETED("SCHEDULED_TEST"),
  SCHOOL_TEST_COMPLETED("SCHOOL_TEST"),
  WORKSHEET_COMPLETED("WORKSHEET"),
  ASSIGNMENT_COMPLETED("ASSIGNMENT"),
  REVISION_COMPLETED("REVISION"),
  COURSE_ASSIGNMENT_COMPLETED("COURSE_ASSIGNMENT"),
  COURSE_TEST_COMPLETED("COURSE_TEST"),
  MOCK_TEST_COMPLETED("MOCK_TEST");
  private String type;

  NotificationEvent(String type) {
    this.type = type;
  }

  @SneakyThrows
  public static NotificationEvent getByExamType(String type) {
    for (NotificationEvent event : values()) {
      if (Objects.nonNull(event.getType()) && event.getType().equals(type)) {
        return event;
      }
    }
    return null;
  }
}
