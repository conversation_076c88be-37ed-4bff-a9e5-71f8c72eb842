package com.wexl.retail.announcement.dto;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum AnnouncementState {
  PUBLISHED("PUBLISHED"),
  DRAFT("DRAFT"),
  DELETED("DELETED");

  private final String value;

  public static AnnouncementState fromValue(String value) {
    if (value == null || "".equals(value)) {
      throw new IllegalArgumentException("Value cannot be null or empty!");
    }

    for (AnnouncementState enumEntry : AnnouncementState.values()) {
      if (enumEntry.toString().equalsIgnoreCase(value)) {
        return enumEntry;
      }
    }

    throw new IllegalArgumentException("Do not understand the Type " + value);
  }

  @Override
  public String toString() {
    return this.value;
  }
}
