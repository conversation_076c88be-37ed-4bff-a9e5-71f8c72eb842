package com.wexl.retail.speech;

import com.wexl.retail.speech.dto.SpeechEvaluation.SpeechResponse;
import com.wexl.retail.speech.dto.SpeechEvaluation.SpeechTaskResponse;

public interface SpeechService {
  SpeechTaskResponse pronunciationAssessment(
      String text, String audioUrl, String reference, Boolean isImpromptuSpeech);

  SpeechResponse pronunciationAssessment(String reference);

  SpeechResponse migrateSpeechTask(String speechRef, long speechTaskId);
}
