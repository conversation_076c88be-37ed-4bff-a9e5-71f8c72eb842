package com.wexl.retail.announcement.dto;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum ExamLevel {
  SCHOOL_LEVEL("SCHOOL_LEVEL"),
  EXPERT_LEVEL("EXPERT_LEVEL");

  private final String value;

  public static ExamLevel fromValue(String value) {
    if (value == null || "".equals(value)) {
      throw new IllegalArgumentException("Value cannot be null or empty!");
    }

    for (ExamLevel enumEntry : ExamLevel.values()) {
      if (enumEntry.toString().equalsIgnoreCase(value)) {
        return enumEntry;
      }
    }

    throw new IllegalArgumentException("Do not understand the Type " + value);
  }

  @Override
  public String toString() {
    return this.value;
  }
}
