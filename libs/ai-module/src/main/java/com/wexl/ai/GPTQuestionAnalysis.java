package com.wexl.ai;

import com.wexl.retail.ai.AiQuestionAnalysis;
import com.wexl.retail.ai.dto.ExamAnalysis;
import com.wexl.retail.ai.dto.ExamAnalysis.AiQuestionAnalysisResponseList;
import com.wexl.retail.ai.dto.ExamAnalysis.PromptAnswerContent;
import com.wexl.retail.ai.dto.ExamAnalysis.PromptQuestionContent;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;

@Component
@RequiredArgsConstructor
@Order(1)
public class GPTQuestionAnalysis implements AiQuestionAnalysis {

  private final EnglishTutor englishTutor;
  private final SpringTemplateEngine templateEngine;

  @Override
  public AiQuestionAnalysisResponseList analyzeQuestions(
      List<PromptQuestionContent> promptQuestionContents,
      List<PromptAnswerContent> promptAnswerContents) {
    String userPrompt = constructUserPrompt(promptQuestionContents, promptAnswerContents);
    return englishTutor.performEnglishAnalysis(userPrompt);
  }

  private String constructUserPrompt(
      List<PromptQuestionContent> promptQuestionContents,
      List<PromptAnswerContent> promptAnswerContents) {
    var context = new Context();
    context.setVariable("questions", promptQuestionContents);
    context.setVariable("answers", promptAnswerContents);
    return templateEngine.process("prompts/evaluation-user-prompt", context);
  }

  public ExamAnalysis.AnswerReAnalysis reAnalysis(ExamAnalysis.AnswerReAnalysis request) {
    var userPrompt = constructUserPromptForReAnalysis(request);
    return englishTutor.answerReAnalysis(userPrompt);
  }

  private String constructUserPromptForReAnalysis(ExamAnalysis.AnswerReAnalysis request) {
    var context = new Context();
    context.setVariable("answer", request);
    return templateEngine.process("prompts/evaluation-reanalysis-prompt", context);
  }
}
